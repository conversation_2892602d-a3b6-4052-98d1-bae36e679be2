# 数据库配置
DB_USER=admin
DB_PASSWORD=admin123
DATABASE_URL=postgresql://admin:admin123@localhost:5432/socialmedia_matrix

# Redis配置
REDIS_PASSWORD=redis123
REDIS_URL=redis://:redis123@localhost:6379/0

# 应用配置
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440
LOG_LEVEL=INFO
DEBUG=false

# 小红书配置
XHS_SIGN_SERVICE_URL=http://localhost:5005

# 许可证配置
LICENSE_CHECK_ENABLED=true
LICENSE_SERVER_URL=https://your-license-server.com

# 监控配置
SENTRY_DSN=https://<EMAIL>/project-id
PROMETHEUS_ENABLED=true
GRAFANA_PASSWORD=admin123

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# 文件上传配置
MAX_UPLOAD_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,xlsx,csv

# 安全配置
CORS_ORIGINS=http://localhost:3000,http://localhost:8000
RATE_LIMIT_PER_MINUTE=60

# 第三方服务配置
BITBROWSER_API_URL=http://127.0.0.1:54345
BITBROWSER_API_KEY=ca28ee5ca6de4d209182a83aa16a2044

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点
BACKUP_RETENTION_DAYS=30
