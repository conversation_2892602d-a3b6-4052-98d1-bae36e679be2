# BTX科技平台 - 生产环境配置

# 应用配置
NODE_ENV=production
PORT=3000
APP_NAME=BTX科技平台
APP_VERSION=1.0.0

# 数据库配置
DB_HOST=postgres
DB_PORT=5432
DB_NAME=btx_platform
DB_USER=btxuser
DB_PASSWORD=btx2024secure
DB_SSL=false

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=btx2024redis
REDIS_DB=0

# JWT配置
JWT_SECRET=btx2024jwt_super_secret_key_change_in_production
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=BTX科技平台 <<EMAIL>>

# 安全配置
CORS_ORIGIN=http://localhost:3000,https://yourdomain.com
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9464

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_KEYS=1000

# 第三方API配置
XIAOHONGSHU_API_KEY=your_xiaohongshu_api_key
DOUYIN_API_KEY=your_douyin_api_key
KUAISHOU_API_KEY=your_kuaishou_api_key

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# 性能配置
CLUSTER_WORKERS=auto
MAX_MEMORY=512m
GC_INTERVAL=300000
