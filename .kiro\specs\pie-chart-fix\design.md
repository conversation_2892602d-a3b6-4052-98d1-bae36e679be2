# Design Document

## Overview

This design document outlines the solution for fixing the incomplete pie chart display issue in the platform distribution visualization. The current implementation has mathematical calculation errors that result in incomplete or incorrectly rendered pie segments. The solution involves refactoring the pie chart generation logic to use precise mathematical calculations and proper SVG path construction.

## Architecture

### Current Architecture Issues
- Manual SVG path calculations with potential rounding errors
- Inconsistent angle-to-coordinate conversion
- Improper path closure leading to visual gaps
- Hard-coded positioning that doesn't account for edge cases

### Proposed Architecture
```
Data Layer (Platform Distribution Data)
    ↓
Calculation Engine (Mathematical Functions)
    ↓
SVG Generation Layer (Dynamic Path Creation)
    ↓
Interaction Layer (Hover Effects & Tooltips)
    ↓
Presentation Layer (Visual Rendering)
```

## Components and Interfaces

### 1. PieChartGenerator Class
**Purpose**: Core component responsible for generating accurate pie chart segments

**Key Methods**:
```javascript
class PieChartGenerator {
    constructor(data, config) {
        this.data = data;           // Platform distribution data
        this.config = config;       // Chart configuration (center, radius, etc.)
        this.svg = null;           // SVG element reference
    }

    // Generate complete pie chart
    generateChart() {
        this.validateData();
        this.clearSVG();
        this.createSegments();
        this.attachInteractions();
    }

    // Calculate precise segment coordinates
    calculateSegmentPath(startAngle, endAngle, radius, center) {
        // Mathematical calculations for accurate path generation
    }

    // Create individual pie segment
    createSegment(data, startAngle, endAngle) {
        // SVG path element creation with proper attributes
    }
}
```

### 2. MathUtils Module
**Purpose**: Utility functions for precise mathematical calculations

**Functions**:
```javascript
const MathUtils = {
    // Convert degrees to radians with high precision
    degreesToRadians(degrees) {
        return (degrees * Math.PI) / 180;
    },

    // Calculate arc endpoint coordinates
    calculateArcPoint(centerX, centerY, radius, angleInRadians) {
        return {
            x: centerX + radius * Math.cos(angleInRadians),
            y: centerY + radius * Math.sin(angleInRadians)
        };
    },

    // Determine if arc should use large-arc-flag
    isLargeArc(angleInDegrees) {
        return angleInDegrees > 180;
    },

    // Validate angle sum equals 360 degrees
    validateAngleSum(segments) {
        const total = segments.reduce((sum, segment) => sum + segment.angle, 0);
        return Math.abs(total - 360) < 0.001; // Allow for floating point precision
    }
};
```

### 3. InteractionManager Class
**Purpose**: Handle user interactions with pie chart segments

**Features**:
- Hover effects with smooth transitions
- Tooltip positioning and content
- Responsive behavior across screen sizes

### 4. Data Configuration
**Structure**:
```javascript
const pieChartConfig = {
    // Chart dimensions
    viewBox: { width: 200, height: 200 },
    center: { x: 100, y: 100 },
    radius: 70,
    
    // Visual properties
    strokeWidth: 0,
    hoverOpacity: 0.8,
    transitionDuration: '0.3s',
    
    // Starting position (12 o'clock)
    startAngle: -90,
    
    // Platform data
    platforms: [
        { name: '小红书', value: 45, color: '#ff2442' },
        { name: '抖音', value: 35, color: '#000000' },
        { name: '快手', value: 20, color: '#ff6600' }
    ]
};
```

## Data Models

### Platform Data Model
```javascript
interface PlatformData {
    name: string;           // Platform name (e.g., "小红书")
    value: number;          // Percentage value (0-100)
    color: string;          // Hex color code
    accounts?: number;      // Optional: number of accounts
    growth?: number;        // Optional: growth percentage
}
```

### Chart Configuration Model
```javascript
interface ChartConfig {
    viewBox: {
        width: number;
        height: number;
    };
    center: {
        x: number;
        y: number;
    };
    radius: number;
    startAngle: number;     // Starting angle in degrees
    strokeWidth: number;
    hoverOpacity: number;
    transitionDuration: string;
}
```

### Segment Path Model
```javascript
interface SegmentPath {
    startAngle: number;     // Start angle in degrees
    endAngle: number;       // End angle in degrees
    startPoint: {           // Arc start coordinates
        x: number;
        y: number;
    };
    endPoint: {             // Arc end coordinates
        x: number;
        y: number;
    };
    largeArcFlag: number;   // SVG large-arc-flag (0 or 1)
    pathData: string;       // Complete SVG path string
}
```

## Error Handling

### Data Validation
```javascript
function validatePieData(data) {
    // Check if data exists and is array
    if (!Array.isArray(data) || data.length === 0) {
        throw new Error('Pie chart data must be a non-empty array');
    }

    // Validate each platform entry
    data.forEach((platform, index) => {
        if (!platform.name || typeof platform.name !== 'string') {
            throw new Error(`Platform ${index}: name is required and must be string`);
        }
        
        if (typeof platform.value !== 'number' || platform.value < 0 || platform.value > 100) {
            throw new Error(`Platform ${index}: value must be number between 0-100`);
        }
        
        if (!platform.color || !/^#[0-9A-Fa-f]{6}$/.test(platform.color)) {
            throw new Error(`Platform ${index}: color must be valid hex color`);
        }
    });

    // Validate total percentage
    const total = data.reduce((sum, platform) => sum + platform.value, 0);
    if (Math.abs(total - 100) > 0.001) {
        console.warn(`Total percentage is ${total}%, expected 100%`);
    }
}
```

### SVG Element Validation
```javascript
function validateSVGElement(elementId) {
    const svg = document.getElementById(elementId);
    if (!svg) {
        throw new Error(`SVG element with id '${elementId}' not found`);
    }
    
    if (svg.tagName.toLowerCase() !== 'svg') {
        throw new Error(`Element '${elementId}' is not an SVG element`);
    }
    
    return svg;
}
```

### Mathematical Edge Cases
- Handle zero-value segments (skip rendering)
- Handle 100% single platform (render full circle)
- Handle floating-point precision issues
- Validate angle calculations don't exceed 360 degrees

## Testing Strategy

### Unit Tests
1. **Mathematical Functions**
   - Test degree-to-radian conversion accuracy
   - Test coordinate calculation precision
   - Test large arc flag determination
   - Test angle sum validation

2. **Path Generation**
   - Test SVG path string format
   - Test path closure correctness
   - Test segment boundary calculations
   - Test edge cases (0%, 100%, very small percentages)

3. **Data Validation**
   - Test invalid data handling
   - Test missing required fields
   - Test out-of-range values
   - Test color format validation

### Integration Tests
1. **Chart Rendering**
   - Test complete chart generation
   - Test segment visual accuracy
   - Test color application
   - Test responsive behavior

2. **User Interactions**
   - Test hover effects
   - Test tooltip positioning
   - Test smooth transitions
   - Test accessibility features

### Visual Regression Tests
1. **Chart Completeness**
   - Verify 360-degree coverage
   - Verify no gaps between segments
   - Verify correct proportions
   - Verify color accuracy

2. **Cross-browser Compatibility**
   - Test SVG rendering consistency
   - Test interaction behavior
   - Test performance across browsers

### Performance Tests
1. **Rendering Performance**
   - Measure chart generation time
   - Test with various data sizes
   - Monitor memory usage
   - Test animation smoothness

## Implementation Approach

### Phase 1: Core Mathematical Functions
1. Implement precise angle calculations
2. Create coordinate conversion utilities
3. Add data validation functions
4. Write comprehensive unit tests

### Phase 2: SVG Generation Engine
1. Refactor path generation logic
2. Implement proper path closure
3. Add segment creation methods
4. Test visual accuracy

### Phase 3: Interaction Layer
1. Enhance hover effects
2. Improve tooltip positioning
3. Add smooth transitions
4. Test user experience

### Phase 4: Integration & Testing
1. Integrate all components
2. Perform visual regression testing
3. Optimize performance
4. Document usage examples

## Technical Considerations

### Browser Compatibility
- SVG support: IE9+ (already supported by Electron)
- Math functions: Standard JavaScript (universal support)
- CSS transitions: Modern browsers (Electron uses Chromium)

### Performance Optimization
- Minimize DOM manipulations
- Use requestAnimationFrame for animations
- Cache calculated values where possible
- Optimize SVG path string generation

### Accessibility
- Add ARIA labels for screen readers
- Ensure keyboard navigation support
- Provide alternative text descriptions
- Maintain sufficient color contrast

### Maintainability
- Separate concerns (calculation, rendering, interaction)
- Use configuration objects for easy customization
- Add comprehensive documentation
- Follow consistent coding patterns