# 🚀 黑默科技 - Local API 测试说明

## ⚠️ 重要规范

**所有API接口都必须使用以下规范：**
- ✅ **请求方法**: POST
- ✅ **传参方式**: body传参，JSON格式
- ✅ **Content-Type**: application/json
- ❌ **不接受**: request url参数、formdata、string等类型传参方式

## 📮 Postman 调试配置

### 基本设置
1. **Method**: 选择 `POST`
2. **Headers**: 添加 `Content-Type: application/json`
3. **Body**: 选择 `raw` 类型，格式选择 `JSON`

### 服务器地址
```
Base URL: http://localhost:3000
```

## 🏥 健康检查API

### 1. 简化健康检查
```
POST /api/xiaohongshu/health/simple
Content-Type: application/json

Body:
{}

响应示例:
{
  "success": true,
  "timestamp": "2025-07-30T10:14:52.801Z",
  "uptime": 44,
  "status": "healthy"
}
```

### 2. 完整健康检查
```
POST /api/xiaohongshu/health
Content-Type: application/json

Body:
{}

响应示例:
{
  "success": true,
  "message": "Local Server 运行正常",
  "data": {
    "server": {...},
    "bitbrowser": {...},
    "services": {...}
  }
}
```

## 🌐 比特浏览器管理API

### 1. 获取浏览器列表
```
POST /api/xiaohongshu/bitbrowser/list
Content-Type: application/json

Body:
{
  "page": 1,
  "pageSize": 20
}

响应示例:
{
  "success": true,
  "message": "成功获取到 6 个浏览器实例",
  "data": {
    "browsers": [...],
    "targetBrowser": null,
    "targetBrowserId": "0d094596cb404282be3f814b98139c74",
    "total": 6
  }
}
```

### 2. 启动浏览器实例
```
POST /api/xiaohongshu/bitbrowser/start
Content-Type: application/json

Body:
{
  "browserId": "0d094596cb404282be3f814b98139c74"
}
// 或者使用空对象使用默认浏览器ID
{}

响应示例:
{
  "success": true,
  "message": "浏览器实例启动成功",
  "data": {
    "browserId": "0d094596cb404282be3f814b98139c74",
    "result": {...}
  }
}
```

### 3. 停止浏览器实例
```
POST /api/xiaohongshu/bitbrowser/stop
Content-Type: application/json

Body:
{
  "browserId": "0d094596cb404282be3f814b98139c74"
}
// 或者使用空对象使用默认浏览器ID
{}
```

### 4. 测试浏览器连接
```
POST /api/xiaohongshu/test-browser-connection
Content-Type: application/json

Body:
{}

响应示例:
{
  "success": true,
  "message": "比特浏览器连接成功，所有功能正常",
  "data": {
    "apiStatus": {...},
    "debugStatus": {...},
    "summary": {...}
  }
}
```

## 🧪 PowerShell 测试命令

### 简化健康检查
```powershell
Invoke-RestMethod -Uri 'http://localhost:3000/api/xiaohongshu/health/simple' -Method Post -ContentType 'application/json' -Body '{}'
```

### 获取浏览器列表
```powershell
$body = '{"page": 1, "pageSize": 20}'
Invoke-RestMethod -Uri 'http://localhost:3000/api/xiaohongshu/bitbrowser/list' -Method Post -ContentType 'application/json' -Body $body
```

### 启动浏览器
```powershell
Invoke-RestMethod -Uri 'http://localhost:3000/api/xiaohongshu/bitbrowser/start' -Method Post -ContentType 'application/json' -Body '{}'
```

## 📋 响应格式说明

### 成功响应
```json
{
  "success": true,
  "message": "操作成功描述",
  "data": {
    // 具体数据内容
  }
}
```

### 失败响应
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 🔧 调试建议

1. **先测试健康检查**: 确保服务器正常运行
2. **检查比特浏览器**: 确保比特浏览器已启动
3. **逐步测试**: 按照API顺序逐个测试
4. **查看日志**: 服务器控制台会显示详细的调试信息

## 📁 文件说明

- `postman-collection.json` - Postman导入文件
- `public/api-docs.html` - 在线API文档
- `public/health-check.html` - 健康检查页面
- `public/bitbrowser-manager.html` - 浏览器管理页面

## 🌐 Web界面访问

```
健康检查页面: http://localhost:3000/health-check.html
浏览器管理页面: http://localhost:3000/bitbrowser-manager.html
API文档页面: http://localhost:3000/api-docs.html
```

---

**建议流程**: 先用 Postman 调试接口，调通后再进行脚本开发调用 🚀
