# 黑默科技桌面端营销管理平台 - API 文档

## 📋 API 概览

本文档详细说明了桌面端应用后端提供的所有API接口。所有API都遵循RESTful设计原则，使用JSON格式进行数据交换。

### 基础信息
- **基础URL**: `http://localhost:3000/api`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **HTTP方法**: GET, POST, PUT, DELETE

### 标准响应格式
```json
{
  "success": true,        // 请求是否成功
  "data": {},            // 响应数据
  "message": "操作成功",  // 响应消息（可选）
  "error": null          // 错误信息（失败时）
}
```

## 🏠 健康检查 API

### GET /health
检查服务器运行状态

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600,
  "version": "1.0.0"
}
```

## 👥 账号管理 API (`/api/accounts`)

### GET /api/accounts
获取账号列表，支持分页和筛选

**查询参数**:
- `page` (number): 页码，默认1
- `limit` (number): 每页数量，默认20
- `status` (string): 状态筛选 (online/offline/pending)
- `group` (string): 分组筛选
- `platform` (string): 平台筛选 (小红书/抖音/微博)
- `search` (string): 搜索关键词

**响应示例**:
```json
{
  "success": true,
  "data": {
    "accounts": [
      {
        "id": "uuid-123",
        "username": "测试账号001",
        "platform": "小红书",
        "status": "online",
        "group": "default",
        "followers": 1250,
        "following": 156,
        "posts": 45,
        "likes": 2800,
        "createdAt": "2023-04-10T16:46:11Z",
        "lastLogin": "2023-04-10T16:46:11Z",
        "avatar": "https://via.placeholder.com/50"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 156,
      "pages": 8
    }
  }
}
```

### POST /api/accounts
创建新账号

**请求体**:
```json
{
  "username": "新账号名称",
  "platform": "小红书",
  "group": "default",
  "avatar": "头像URL"
}
```

### PUT /api/accounts/:id
更新账号信息

### DELETE /api/accounts/:id
删除账号

## 📊 数据总览 API (`/api/overview`)

### GET /api/overview/stats
获取平台整体统计数据

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalViews": ********,
    "totalLikes": 892000,
    "totalComments": 156000,
    "totalCollections": 89000,
    "viewsGrowth": 12.5,
    "likesGrowth": 8.3,
    "commentsGrowth": 15.2,
    "collectionsGrowth": 6.8,
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

### GET /api/overview/accounts
获取账号详细数据（包含作品信息）

## 🔍 系统监控 API (`/api/monitor`)

### GET /api/monitor/stats
获取系统监控数据

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalAccounts": 1234,
    "onlineAccounts": 856,
    "todayMessages": 2468,
    "successRate": 98.5,
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

### GET /api/monitor/charts
获取实时图表数据

## 💬 聊天功能 API (`/api/chat`)

### GET /api/chat/rooms
获取聊天室列表

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "general",
      "name": "综合讨论",
      "userCount": 15,
      "messageCount": 234
    }
  ]
}
```

### GET /api/chat/rooms/:roomId/messages
获取聊天室消息

### POST /api/chat/rooms/:roomId/messages
发送消息

### POST /api/chat/rooms/:roomId/join
加入聊天室

### POST /api/chat/rooms/:roomId/leave
离开聊天室

## 📨 消息管理 API (`/api/messages`)

### GET /api/messages/templates
获取消息模板列表

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid-123",
      "name": "欢迎消息模板",
      "content": "欢迎关注我们！我们会定期分享有价值的内容。",
      "type": "welcome",
      "createdAt": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

### POST /api/messages/templates
创建消息模板

### GET /api/messages/tasks
获取消息任务列表

### POST /api/messages/tasks
创建消息任务

## 📋 记录管理 API (`/api/records`)

### GET /api/records
获取发送记录

**查询参数**:
- `page` (number): 页码
- `limit` (number): 每页数量
- `status` (string): 状态筛选 (success/failed/pending)
- `startDate` (string): 开始日期
- `endDate` (string): 结束日期

**响应示例**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": "1",
        "taskId": "task-001",
        "taskName": "新用户欢迎消息",
        "accountId": "acc-001",
        "accountName": "测试账号001",
        "platform": "小红书",
        "targetUser": "user123",
        "messageContent": "欢迎关注我们！",
        "status": "success",
        "sentAt": "2024-01-15T08:30:00.000Z",
        "error": null,
        "retryCount": 0
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 500,
      "pages": 25
    }
  }
}
```

### GET /api/records/stats
获取发送统计数据

## 🔌 WebSocket 实时通信

应用使用Socket.IO进行实时通信，支持以下事件：

### 客户端事件
- `connect`: 连接成功
- `disconnect`: 连接断开
- `join-room`: 加入聊天室
- `leave-room`: 离开聊天室
- `send-message`: 发送消息

### 服务端事件
- `user-joined`: 用户加入
- `user-left`: 用户离开
- `new-message`: 新消息
- `system-notification`: 系统通知

## 🚨 错误代码

| 错误代码 | 说明 |
|---------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 📝 开发说明

### 添加新API
1. 在 `routes/` 目录创建新的路由文件
2. 在 `server.js` 中注册路由
3. 添加相应的注释和文档

### 测试API
可以使用以下工具测试API：
- Postman
- curl命令
- 浏览器开发者工具

### 示例curl命令
```bash
# 获取账号列表
curl -X GET "http://localhost:3000/api/accounts?page=1&limit=10"

# 创建新账号
curl -X POST "http://localhost:3000/api/accounts" \
  -H "Content-Type: application/json" \
  -d '{"username":"新账号","platform":"小红书","group":"default"}'
```

---

**版本**: 1.0.0  
**更新时间**: 2024年1月  
**维护者**: 黑默科技开发团队
