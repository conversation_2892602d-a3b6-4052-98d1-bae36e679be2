# 黑默科技桌面端营销管理平台 - 代码结构说明

## 📁 项目结构

```
heimo-tech-platform/
├── electron-main.js          # Electron主进程文件（桌面应用入口）
├── server.js                 # Node.js后端服务器
├── package.json              # 项目配置和依赖管理
├── CODE_STRUCTURE.md         # 代码结构说明文档
├── public/                   # 前端静态文件目录
│   ├── premium-index.html    # 桌面端主页面（已添加详细注释）
│   ├── premium-app.js        # 桌面端主要JavaScript逻辑（已添加详细注释）
│   └── premium-ui.css        # 桌面端样式文件（已添加详细注释）
└── routes/                   # 后端API路由文件（所有文件已添加详细注释）
    ├── accounts.js           # 账号管理API - 处理账号CRUD操作
    ├── overview.js           # 数据总览API - 提供统计数据
    ├── monitor.js            # 监控API - 系统状态监控
    ├── chat.js               # 聊天API - 聊天室功能
    ├── messages.js           # 消息API - 消息模板和任务管理
    └── records.js            # 记录API - 发送记录和日志
```

## 🚀 启动方式

### 方法1：直接启动桌面端（推荐）
```bash
npm run electron
```

### 方法2：开发模式（同时启动服务器和桌面端）
```bash
npm run electron-dev
```

### 方法3：仅启动后端服务器
```bash
npm start
```

## 🏗️ 核心文件说明

### 1. electron-main.js - 桌面应用主进程
- **作用**：Electron桌面应用的入口文件
- **主要功能**：
  - 创建桌面应用窗口
  - 启动内置Node.js服务器
  - 管理应用生命周期
  - 处理窗口事件

### 2. server.js - 后端服务器
- **作用**：为桌面端提供API服务和静态文件服务
- **主要功能**：
  - 提供HTTP服务器（端口3000）
  - 提供WebSocket实时通信
  - 处理API请求路由
  - 服务静态文件（HTML、CSS、JS）

### 3. premium-index.html - 桌面端主页面
- **作用**：桌面应用的用户界面
- **主要结构**：
  - 左侧导航栏（品牌logo + 功能菜单）
  - 右侧主内容区域（动态加载不同页面）
  - 账号分组管理界面（核心功能）

### 4. premium-app.js - 前端主要逻辑
- **作用**：桌面端的JavaScript核心逻辑
- **主要类**：
  - `PremiumApp`：主应用类，管理页面切换和基础功能
  - `GroupManager`：分组管理类，处理账号分组的所有操作

### 5. premium-ui.css - 样式文件
- **作用**：桌面端的所有样式定义
- **主要特点**：
  - 现代化设计风格
  - 响应式布局
  - 深色主题配色
  - 动画效果

## 🎯 核心功能模块

### 1. 账号分组管理（核心功能）
**位置**：`premium-app.js` 中的 `GroupManager` 类

**功能说明**：
- 支持三大平台独立分组：小红书、抖音、微博
- 每个平台可创建无限个自定义分组
- 账号只能在同平台的分组间移动
- 支持分组的增删改查操作

**主要方法**：
```javascript
// 创建新分组
createPlatformGroup(name, color, platform)

// 移动账号到分组
moveAccountToGroup(accountId, groupId)

// 删除分组
deleteGroup(groupId)

// 渲染分组列表
renderGroupList()
```

### 2. 数据管理功能
**位置**：`premium-index.html` 中的JavaScript函数

**功能说明**：
- 导出数据：将所有账号和分组数据导出为JSON文件
- 导入数据：从JSON文件导入数据
- 清空数据：删除所有本地存储数据
- 本地存储：自动保存数据到浏览器本地存储

**主要函数**：
```javascript
// 导出数据
exportData()

// 导入数据
importData()

// 清空数据
clearAllData()
```

### 3. 页面导航系统
**位置**：`premium-app.js` 中的 `PremiumApp` 类

**功能说明**：
- 左侧导航菜单管理
- 页面动态切换
- 导航状态管理

**主要方法**：
```javascript
// 加载页面
loadPage(page)

// 更新导航状态
updateNavigation(activePage)

// 初始化导航
initializeNavigation()
```

## 🔧 技术架构

### 前端技术栈
- **HTML5**：页面结构
- **CSS3**：样式和动画
- **JavaScript ES6+**：交互逻辑
- **Font Awesome**：图标库
- **Socket.IO Client**：实时通信

### 后端技术栈
- **Node.js**：运行环境
- **Express.js**：Web框架
- **Socket.IO**：WebSocket实时通信
- **CORS**：跨域支持

### 桌面端技术
- **Electron**：跨平台桌面应用框架
- **内置服务器**：集成Node.js服务器

## 💾 数据存储

### 本地存储
- **位置**：浏览器 localStorage
- **数据类型**：
  - 账号数据：`accountsData`
  - 分组数据：`groupsData`

### 数据格式
```javascript
// 账号数据格式
{
  id: 1,
  nickname: "账号名称",
  platform: "xiaohongshu", // xiaohongshu/douyin/weibo
  group: "xhs-beauty",     // 所属分组ID
  status: "online",        // online/offline/pending
  // ... 其他字段
}

// 分组数据格式
{
  id: "xhs-beauty",
  name: "小红书-美妆博主",
  color: "#ff69b4",
  count: 5,
  platform: "xiaohongshu"
}
```

## 🎨 界面设计

### 设计理念
- **专业性**：企业级应用的严谨设计
- **易用性**：直观的操作流程
- **现代感**：符合当前设计趋势
- **功能性**：突出核心功能

### 色彩方案
- **主色调**：深色主题 (#1a1a1a)
- **强调色**：蓝色 (#4F46E5)
- **平台色**：
  - 小红书：#ff2442
  - 抖音：#000000
  - 微博：#e6162d

### 布局结构
- **左侧导航**：固定宽度，垂直菜单
- **右侧内容**：自适应宽度，动态内容
- **响应式**：适配不同屏幕尺寸

## 🔍 新建分组按钮位置

### 位置1：页面顶部
- **位置**：账号管理页面顶部右侧
- **样式**：蓝色按钮，带加号图标
- **功能**：弹出平台选择对话框

### 位置2：平台区域
- **位置**：每个平台标题旁边
- **样式**：对应平台颜色的按钮
- **功能**：直接为该平台创建分组

## 📝 开发说明

### 添加新功能
1. 在 `premium-app.js` 中添加相应的类或方法
2. 在 `premium-index.html` 中添加UI元素
3. 在 `premium-ui.css` 中添加样式
4. 如需后端支持，在 `routes/` 目录添加API路由

### 调试方法
1. 使用浏览器开发者工具
2. 查看控制台日志
3. 检查网络请求
4. 使用Electron开发者工具

### 常见问题
1. **端口占用**：确保3000端口未被占用
2. **权限问题**：确保有文件读写权限
3. **依赖问题**：运行 `npm install` 安装依赖

---

**开发者**：黑默科技  
**版本**：1.0.0  
**更新时间**：2024年
