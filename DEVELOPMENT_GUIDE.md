# 黑默科技桌面端营销管理平台 - 开发指南

## 🚀 快速开始

### 环境要求
- **Node.js**: 16.0+ 
- **npm**: 8.0+
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 安装依赖
```bash
# 克隆项目（如果从Git仓库）
git clone <repository-url>
cd heimo-tech-platform

# 安装依赖
npm install
```

### 启动应用
```bash
# 方法1：直接启动桌面端（推荐）
npm run electron

# 方法2：开发模式（同时启动服务器和桌面端）
npm run electron-dev

# 方法3：仅启动后端服务器
npm start
```

## 🏗️ 项目架构

### 技术栈
- **前端**: HTML5, CSS3, JavaScript ES6+
- **后端**: Node.js, Express.js
- **桌面端**: Electron
- **实时通信**: Socket.IO
- **样式**: 原生CSS（无框架依赖）
- **图标**: Font Awesome

### 架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Electron      │    │   Express.js    │    │   Static Files  │
│   主进程         │◄──►│   后端服务器     │◄──►│   HTML/CSS/JS   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   桌面窗口       │    │   API路由       │    │   前端界面       │
│   窗口管理       │    │   数据处理       │    │   用户交互       │
│   生命周期       │    │   Socket.IO     │    │   分组管理       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📂 目录结构详解

### 根目录文件
- `electron-main.js`: Electron主进程，负责创建窗口和启动服务器
- `server.js`: Express服务器，提供API和静态文件服务
- `package.json`: 项目配置，包含依赖和脚本命令

### public/ 目录（前端文件）
- `premium-index.html`: 主页面，包含完整的UI结构
- `premium-app.js`: 前端逻辑，包含PremiumApp和GroupManager类
- `premium-ui.css`: 样式文件，采用现代化设计系统

### routes/ 目录（后端API）
- `accounts.js`: 账号管理相关API
- `overview.js`: 数据总览相关API
- `monitor.js`: 系统监控相关API
- `chat.js`: 聊天功能相关API
- `messages.js`: 消息管理相关API
- `records.js`: 记录管理相关API

## 🔧 开发工作流

### 1. 添加新功能
```bash
# 1. 创建功能分支（如果使用Git）
git checkout -b feature/new-feature

# 2. 修改代码
# 3. 测试功能
npm run electron

# 4. 提交代码
git add .
git commit -m "feat: 添加新功能"
```

### 2. 修改前端界面
1. **修改HTML结构**: 编辑 `public/premium-index.html`
2. **添加样式**: 编辑 `public/premium-ui.css`
3. **添加交互逻辑**: 编辑 `public/premium-app.js`
4. **测试界面**: 刷新桌面应用或重启

### 3. 添加后端API
1. **创建路由文件**: 在 `routes/` 目录创建新文件
2. **注册路由**: 在 `server.js` 中添加路由注册
3. **测试API**: 使用Postman或curl测试
4. **更新文档**: 在API文档中添加新接口说明

### 4. 调试方法
```bash
# 查看服务器日志
# 服务器启动后会在控制台显示日志

# 查看前端日志
# 在桌面应用中按F12打开开发者工具

# 查看网络请求
# 在开发者工具的Network标签页查看API请求
```

## 🎨 UI开发规范

### 设计原则
1. **一致性**: 使用统一的颜色、字体、间距
2. **可访问性**: 确保良好的对比度和键盘导航
3. **响应式**: 适配不同屏幕尺寸
4. **性能**: 优化CSS和JavaScript性能

### 颜色系统
```css
/* 主色调 */
--primary-500: #475569;
--primary-700: #1e293b;

/* 功能色 */
--success: #16a34a;    /* 成功 */
--warning: #ea580c;    /* 警告 */
--error: #dc2626;      /* 错误 */
--info: #6366f1;       /* 信息 */

/* 平台色 */
--xiaohongshu: #ff2442;  /* 小红书 */
--douyin: #000000;       /* 抖音 */
--weibo: #e6162d;        /* 微博 */
```

### CSS命名规范
```css
/* 使用BEM命名法 */
.block {}                    /* 块 */
.block__element {}           /* 元素 */
.block--modifier {}          /* 修饰符 */

/* 示例 */
.nav-item {}                 /* 导航项 */
.nav-item__icon {}           /* 导航项图标 */
.nav-item--active {}         /* 激活状态的导航项 */
```

## 🔌 API开发规范

### RESTful设计
```javascript
// 资源命名使用复数
GET    /api/accounts      // 获取账号列表
POST   /api/accounts      // 创建账号
GET    /api/accounts/:id  // 获取单个账号
PUT    /api/accounts/:id  // 更新账号
DELETE /api/accounts/:id  // 删除账号
```

### 响应格式
```javascript
// 成功响应
{
  "success": true,
  "data": {},
  "message": "操作成功"
}

// 错误响应
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE"
}
```

### 错误处理
```javascript
// 在路由中添加错误处理
router.get('/api/accounts', (req, res) => {
  try {
    // 业务逻辑
    res.json({ success: true, data: result });
  } catch (error) {
    console.error('获取账号列表失败:', error);
    res.status(500).json({ 
      success: false, 
      error: '服务器内部错误' 
    });
  }
});
```

## 🧪 测试指南

### 功能测试
1. **账号管理**: 测试创建、编辑、删除、移动分组
2. **分组管理**: 测试创建分组、删除分组、重命名
3. **数据导入导出**: 测试JSON文件的导入导出
4. **界面交互**: 测试所有按钮和菜单功能

### 性能测试
1. **启动时间**: 应用启动到可用的时间
2. **响应时间**: API请求的响应时间
3. **内存使用**: 长时间运行的内存占用
4. **CPU使用**: 正常操作的CPU占用

### 兼容性测试
1. **操作系统**: Windows, macOS, Linux
2. **屏幕分辨率**: 1920x1080, 1366x768等
3. **浏览器内核**: Chromium版本兼容性

## 📦 打包发布

### 构建应用
```bash
# 构建所有平台
npm run build

# 构建特定平台
npm run build:win    # Windows
npm run build:mac    # macOS
npm run build:linux  # Linux
```

### 发布流程
1. **更新版本号**: 修改 `package.json` 中的版本号
2. **构建应用**: 运行构建命令
3. **测试安装包**: 在目标平台测试安装和运行
4. **发布**: 上传到发布平台或分发给用户

## 🐛 常见问题

### 端口占用
```bash
# 检查端口占用
netstat -ano | findstr :3000

# 杀死占用进程
taskkill /PID <进程ID> /F
```

### 依赖问题
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules
npm install
```

### Electron问题
```bash
# 重新安装Electron
npm uninstall electron
npm install electron --save-dev
```

## 📚 学习资源

### 官方文档
- [Electron官方文档](https://www.electronjs.org/docs)
- [Express.js官方文档](https://expressjs.com/)
- [Socket.IO官方文档](https://socket.io/docs/)

### 推荐教程
- [Electron快速入门](https://www.electronjs.org/docs/tutorial/quick-start)
- [Node.js API参考](https://nodejs.org/api/)
- [CSS Grid布局指南](https://css-tricks.com/snippets/css/complete-guide-grid/)

---

**维护者**: 黑默科技开发团队  
**最后更新**: 2024年1月  
**版本**: 1.0.0
