# 🚀 黑默科技桌面端营销管理平台

> 专业的社交媒体营销管理桌面应用，支持小红书、抖音、微博三大平台的账号分组管理

## ✨ 核心特性

- 🎯 **三大平台支持**: 小红书、抖音、微博独立分组管理
- 📁 **智能分组**: 自定义分组，账号精细化管理
- 💾 **数据管理**: 支持数据导入导出，本地存储
- 🎨 **现代界面**: 企业级设计，深色主题
- ⚡ **实时通信**: WebSocket支持，数据实时更新
- 🖥️ **桌面应用**: 基于Electron，跨平台支持

## 🎯 快速开始

### 安装依赖
```bash
npm install
```

### 启动应用
```bash
# 启动桌面端应用（推荐）
npm run electron

# 开发模式
npm run electron-dev
```

### 应用截图
启动后您将看到：
- 左侧导航栏：包含数据总览、账号管理等功能
- 右侧主界面：默认显示账号分组管理页面
- 顶部操作栏：新建分组、数据导入导出等功能

## 📁 项目结构

```
heimo-tech-platform/
├── 📄 electron-main.js      # Electron主进程（桌面应用入口）
├── 🌐 server.js             # Node.js后端服务器
├── 📦 package.json          # 项目配置文件
├── 📚 文档文件/
│   ├── CODE_STRUCTURE.md    # 代码结构详细说明
│   ├── API_DOCUMENTATION.md # API接口文档
│   └── DEVELOPMENT_GUIDE.md # 开发指南
├── 🎨 public/               # 前端文件（已添加详细注释）
│   ├── premium-index.html   # 主页面
│   ├── premium-app.js       # 核心JavaScript逻辑
│   └── premium-ui.css       # 样式文件
└── 🔌 routes/               # 后端API路由（已添加详细注释）
    ├── accounts.js          # 账号管理API
    ├── overview.js          # 数据总览API
    ├── monitor.js           # 系统监控API
    ├── chat.js              # 聊天功能API
    ├── messages.js          # 消息管理API
    └── records.js           # 记录管理API
```

## 🎯 核心功能

### 1. 账号分组管理
- ✅ 三大平台独立分组（小红书、抖音、微博）
- ✅ 自定义分组创建和管理
- ✅ 账号在同平台分组间移动
- ✅ 分组颜色和名称自定义
- ✅ 分组账号数量统计

### 2. 数据管理
- ✅ JSON格式数据导出
- ✅ 数据文件导入
- ✅ 本地存储自动保存
- ✅ 数据清空功能
- ✅ 数据备份和恢复

### 3. 用户界面
- ✅ 现代化设计风格
- ✅ 深色主题配色
- ✅ 响应式布局
- ✅ 动画效果
- ✅ 直观的操作流程

## 🔧 技术栈

### 前端技术
- **HTML5**: 页面结构
- **CSS3**: 样式和动画（原生CSS，无框架依赖）
- **JavaScript ES6+**: 交互逻辑
- **Font Awesome**: 图标库

### 后端技术
- **Node.js**: 运行环境
- **Express.js**: Web框架
- **Socket.IO**: 实时通信

### 桌面端技术
- **Electron**: 跨平台桌面应用框架

## 📖 详细文档

- 📋 [代码结构说明](CODE_STRUCTURE.md) - 详细的代码结构和功能说明
- 🔌 [API接口文档](API_DOCUMENTATION.md) - 完整的API接口说明
- 🛠️ [开发指南](DEVELOPMENT_GUIDE.md) - 开发环境搭建和开发规范

## 🚀 使用指南

### 新建分组
1. 点击页面顶部的"新建分组"按钮
2. 选择平台（小红书/抖音/微博）
3. 输入分组名称和选择颜色
4. 点击"创建"完成

### 移动账号
1. 在账号列表中找到要移动的账号
2. 点击账号操作区的"分组"按钮
3. 选择目标分组（只显示同平台分组）
4. 确认移动

### 数据管理
- **导出数据**: 点击"导出数据"按钮，下载JSON文件
- **导入数据**: 点击"导入数据"按钮，选择JSON文件
- **清空数据**: 点击"清空数据"按钮，确认后清空所有数据

## 🔍 新建分组按钮位置

### 方法1：页面顶部按钮
- **位置**: 账号管理页面顶部右侧
- **样式**: 蓝色按钮，带加号图标
- **功能**: 弹出平台选择对话框

### 方法2：平台专用按钮
- **位置**: 每个平台标题旁边
- **样式**: 对应平台颜色的"+ 新建分组"按钮
- **功能**: 直接为该平台创建分组

## 🛠️ 开发环境

### 环境要求
- Node.js 16.0+
- npm 8.0+
- Windows 10+ / macOS 10.15+ / Ubuntu 18.04+

### 开发命令
```bash
npm start          # 仅启动后端服务器
npm run electron   # 启动桌面端应用
npm run electron-dev # 开发模式（推荐）
npm run build      # 构建应用安装包
```

## 📝 代码注释

所有代码文件都已添加详细的中文注释：

- ✅ **JavaScript文件**: 类、方法、变量都有详细说明
- ✅ **HTML文件**: 页面结构和功能区域注释
- ✅ **CSS文件**: 样式分类和设计说明
- ✅ **API路由**: 接口功能和参数说明

## 🎨 界面预览

### 主要功能区域
1. **左侧导航栏**: 
   - 品牌Logo和名称
   - 功能模块导航（数据总览、账号管理等）

2. **账号分组管理页面**:
   - 顶部操作栏（新建分组、数据管理）
   - 分组列表（按平台分类显示）
   - 账号列表（支持筛选和搜索）

3. **分组卡片**:
   - 平台颜色标识
   - 分组名称和描述
   - 账号数量统计
   - 编辑和删除操作

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证

## 👥 开发团队

**黑默科技** - 专业的营销管理解决方案提供商

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
