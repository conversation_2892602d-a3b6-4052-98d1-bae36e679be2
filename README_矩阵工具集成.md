# 🎯 小红书评论爬取模块 - 矩阵工具集成

## 📋 概述

这是一个专为矩阵工具设计的小红书评论爬取模块，基于比特浏览器API + Selenium实现，可以高效爬取小红书评论数据。

## 🚀 核心功能

- ✅ **比特浏览器API集成** - 自动管理浏览器窗口
- ✅ **智能评论爬取** - 自动滚动加载所有评论
- ✅ **数据结构化** - 提取用户名、时间、内容、点赞数等
- ✅ **兼职信息分析** - 自动识别和分析兼职相关评论
- ✅ **联系信息提取** - 提取微信、QQ等联系方式
- ✅ **多格式导出** - 支持JSON、CSV、Excel格式
- ✅ **模块化设计** - 易于集成到现有工具

## 📦 文件结构

```
├── xiaohongshu_scraper_module.py    # 核心爬取模块
├── test_scraper_module.py           # 功能测试脚本
├── matrix_integration_example.py    # 矩阵工具集成示例
├── matrix_config.json               # 配置文件（自动生成）
└── README_矩阵工具集成.md           # 本文档
```

## 🔧 快速集成

### 1. 基本集成

```python
from xiaohongshu_scraper_module import XiaohongshuScraperModule

# 初始化模块
scraper = XiaohongshuScraperModule(
    api_url="http://127.0.0.1:56906",
    output_dir="./scraped_data"
)

# 查找19号浏览器窗口
browser = scraper.find_browser_by_seq(19)

# 打开浏览器
open_result = scraper.open_browser(browser['id'])
debug_port = open_result['http'].split(':')[-1]

# 连接Selenium
scraper.connect_selenium(debug_port)

# 爬取评论
result = scraper.scrape_comments(target_url)

# 保存数据
filepath = scraper.save_to_file(result)

# 清理资源
scraper.cleanup()
```

### 2. 矩阵工具集成

```python
from matrix_integration_example import MatrixToolIntegration

# 创建矩阵工具实例
matrix = MatrixToolIntegration("matrix_config.json")

# 运行爬取任务
result = matrix.run()

# 获取汇总报告
summary = result['summary']
print(f"成功任务: {summary['successfulTasks']}")
print(f"总评论数: {summary['totalComments']}")
```

## ⚙️ 配置说明

### 配置文件 (matrix_config.json)

```json
{
  "bitbrowser": {
    "api_url": "http://127.0.0.1:56906",
    "target_browser_seq": 19,
    "auto_open": true,
    "auto_close": false
  },
  "scraping": {
    "output_directory": "./matrix_scraped_data",
    "target_comments": 1472,
    "max_scroll_attempts": 1000,
    "scroll_delay": 0.6,
    "click_delay": 0.4
  },
  "targets": [
    {
      "platform": "xiaohongshu",
      "name": "漫娴学姐兼职帖",
      "url": "https://www.xiaohongshu.com/explore/67af69ee000000002a003a157",
      "keywords": ["兼职", "聊天员", "陪玩", "求带", "日入", "赚钱"],
      "enabled": true
    }
  ],
  "analysis": {
    "extract_job_info": true,
    "extract_contact_info": true,
    "generate_statistics": true,
    "export_formats": ["json", "csv", "excel"]
  }
}
```

## 📊 数据结构

### 评论数据结构

```json
{
  "id": 1,
  "userId": "ABC123DEF456",
  "username": "用户名",
  "content": "评论内容",
  "time": "12-25",
  "likes": 5,
  "replyCount": 2,
  "isAuthor": false,
  "isPinned": false,
  "extractedInfo": {
    "source": "time",
    "originalLength": 150
  }
}
```

### 完整结果结构

```json
{
  "noteInfo": {
    "id": "67af69ee000000002a003a157",
    "title": "笔记标题",
    "author": "漫娴学姐 招暑假工版",
    "totalCommentCount": 1472,
    "url": "https://www.xiaohongshu.com/explore/..."
  },
  "comments": [...],
  "jobAnalysis": {
    "totalJobComments": 856,
    "jobPercentage": 58.15,
    "contactInfo": [...],
    "topKeywords": [...]
  },
  "extractStats": {
    "totalTextLength": 245678,
    "successfulExtractions": 1456,
    "extractionMethods": ["bitbrowser-api-selenium"]
  },
  "extractTime": "2024-12-25T10:30:00"
}
```

## 🧪 测试功能

### 运行基本测试

```bash
python test_scraper_module.py
# 选择 1 - 基本功能测试（需要实际浏览器）
```

### 运行集成演示

```bash
python test_scraper_module.py
# 选择 2 - 矩阵工具集成示例（仅演示）
```

### 运行完整矩阵工具

```bash
python matrix_integration_example.py
```

## 🔍 核心API

### XiaohongshuScraperModule 类

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| `list_browsers()` | 获取浏览器列表 | page, page_size | List[Dict] |
| `find_browser_by_seq()` | 根据序号查找浏览器 | seq | Dict |
| `open_browser()` | 打开浏览器窗口 | browser_id, args | Dict |
| `close_browser()` | 关闭浏览器窗口 | browser_id | bool |
| `connect_selenium()` | 连接Selenium | debug_port | bool |
| `scrape_comments()` | 爬取评论 | target_url | Dict |
| `save_to_file()` | 保存数据 | data, filename | str |
| `cleanup()` | 清理资源 | - | None |

### MatrixToolIntegration 类

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| `initialize_scraper()` | 初始化爬取器 | - | bool |
| `setup_browser()` | 设置浏览器 | - | bool |
| `execute_scraping_tasks()` | 执行爬取任务 | - | List[Dict] |
| `analyze_job_comments()` | 分析兼职评论 | comments, keywords | Dict |
| `generate_summary_report()` | 生成汇总报告 | results | Dict |
| `run()` | 运行完整流程 | - | Dict |

## 🛠️ 集成到现有矩阵工具

### 步骤1: 添加依赖

```python
# requirements.txt
selenium>=4.0.0
requests>=2.25.0
```

### 步骤2: 导入模块

```python
from xiaohongshu_scraper_module import XiaohongshuScraperModule
```

### 步骤3: 集成到工具流程

```python
class YourMatrixTool:
    def __init__(self):
        self.xiaohongshu_scraper = XiaohongshuScraperModule(
            api_url="http://127.0.0.1:56906",
            output_dir="./your_output_dir"
        )
    
    def scrape_xiaohongshu_comments(self, target_url, browser_seq=19):
        """集成小红书评论爬取功能"""
        try:
            # 查找浏览器
            browser = self.xiaohongshu_scraper.find_browser_by_seq(browser_seq)
            
            # 打开浏览器
            open_result = self.xiaohongshu_scraper.open_browser(browser['id'])
            debug_port = open_result['http'].split(':')[-1]
            
            # 连接Selenium
            self.xiaohongshu_scraper.connect_selenium(debug_port)
            
            # 爬取评论
            result = self.xiaohongshu_scraper.scrape_comments(target_url)
            
            # 保存数据
            filepath = self.xiaohongshu_scraper.save_to_file(result)
            
            return {
                'success': True,
                'data': result,
                'filepath': filepath
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
            
        finally:
            self.xiaohongshu_scraper.cleanup()
```

## 📈 性能优化

- **并发处理**: 支持多个浏览器窗口同时爬取
- **智能重试**: 自动处理网络异常和页面加载问题
- **内存优化**: 及时清理资源，避免内存泄漏
- **速度控制**: 可调节滚动和点击延迟，避免被检测

## 🔒 注意事项

1. **合规使用**: 请遵守小红书的使用条款和robots.txt
2. **频率控制**: 建议设置合理的延迟，避免过于频繁的请求
3. **数据保护**: 妥善处理用户数据，保护隐私
4. **异常处理**: 做好异常处理，确保程序稳定性

## 🆘 故障排除

### 常见问题

1. **无法连接比特浏览器API**
   - 检查API地址是否正确
   - 确保比特浏览器正在运行

2. **Selenium连接失败**
   - 确保浏览器窗口已启用调试模式
   - 检查调试端口是否正确

3. **评论提取不完整**
   - 增加滚动次数和延迟时间
   - 检查页面是否完全加载

4. **数据保存失败**
   - 检查输出目录权限
   - 确保磁盘空间充足

## 📞 技术支持

如有问题或建议，请联系开发团队或查看相关文档。

---

**🎯 这个模块已经准备好集成到你的矩阵工具中！**
