#!/usr/bin/env node

/**
 * 🎯 精确笔记数据采集器
 * 专门针对小红书页面结构优化，提高数据采集准确性
 */

const axios = require('axios');
const WebSocket = require('ws');

class AccurateNotesCollector {
    constructor() {
        this.debugPort = null;
    }

    // 🎯 精确采集方法
    async collectAccurateData() {
        console.log('🎯 开始精确采集小红书数据...\n');

        try {
            // 1. 获取调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 目标页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 先分析页面结构
            console.log('🔍 分析页面结构...');
            const pageStructure = await this.analyzePage(tab);
            console.log('📊 页面结构分析完成\n');

            // 4. 基于页面结构精确采集
            console.log('📊 开始精确数据采集...');
            const accurateData = await this.performAccurateCollection(tab, pageStructure);
            
            return accurateData;

        } catch (error) {
            console.error('❌ 精确采集失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取调试端口
    async getCurrentDebugPort() {
        try {
            const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 10000
            });

            if (response.data.success && response.data.data && response.data.data.result) {
                const httpInfo = response.data.data.result.http;
                if (httpInfo) {
                    this.debugPort = httpInfo.split(':')[1];
                    console.log(`✅ 调试端口: ${this.debugPort}`);
                    return;
                }
            }

            const commonPorts = [63524, 51859, 58222, 9222];
            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到端口: ${port}`);
                    return;
                } catch (error) {
                    // 继续尝试
                }
            }

            throw new Error('未找到可用端口');
        } catch (error) {
            throw new Error(`获取端口失败: ${error.message}`);
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
            timeout: 5000
        });

        return response.data.find(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.url.includes('creator.xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );
    }

    // 🔍 分析页面结构
    async analyzePage(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const analyzeScript = `
                        (function() {
                            const analysis = {
                                pageType: '',
                                noteSelectors: [],
                                interactionSelectors: [],
                                titleSelectors: [],
                                imageSelectors: [],
                                linkSelectors: []
                            };

                            // 判断页面类型
                            if (window.location.href.includes('/user/profile/')) {
                                analysis.pageType = 'user_profile';
                            } else if (window.location.href.includes('/creator.xiaohongshu.com')) {
                                analysis.pageType = 'creator_platform';
                            } else {
                                analysis.pageType = 'other';
                            }

                            // 查找所有可能的笔记容器
                            const possibleSelectors = [
                                '.note-item', '.feed-item', '.note-card', '.card-item',
                                '[class*="note"]', '[class*="feed"]', '[class*="card"]',
                                '[class*="item"]', '[class*="post"]', '[class*="content"]'
                            ];

                            possibleSelectors.forEach(selector => {
                                const elements = document.querySelectorAll(selector);
                                if (elements.length > 0) {
                                    analysis.noteSelectors.push({
                                        selector: selector,
                                        count: elements.length,
                                        sample: elements[0] ? elements[0].className : ''
                                    });
                                }
                            });

                            // 查找互动元素（点赞、收藏等）
                            const interactionTexts = ['赞', '点赞', '❤️', '👍', '收藏', '💖', '评论', '💬'];
                            interactionTexts.forEach(text => {
                                const elements = Array.from(document.querySelectorAll('*')).filter(el => 
                                    el.textContent.includes(text) && el.children.length === 0
                                );
                                if (elements.length > 0) {
                                    analysis.interactionSelectors.push({
                                        text: text,
                                        count: elements.length,
                                        sample: elements[0] ? elements[0].className : ''
                                    });
                                }
                            });

                            // 统计页面元素
                            analysis.stats = {
                                totalDivs: document.querySelectorAll('div').length,
                                totalImages: document.querySelectorAll('img').length,
                                totalLinks: document.querySelectorAll('a').length,
                                totalSpans: document.querySelectorAll('span').length
                            };

                            return analysis;
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: analyzeScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const analysis = message.result.result.value;
                        console.log('📊 页面分析结果:');
                        console.log(`   页面类型: ${analysis.pageType}`);
                        console.log(`   找到 ${analysis.noteSelectors.length} 种笔记选择器`);
                        console.log(`   找到 ${analysis.interactionSelectors.length} 种互动选择器`);
                        console.log(`   页面元素: ${analysis.stats.totalDivs} divs, ${analysis.stats.totalImages} images`);
                        
                        ws.close();
                        resolve(analysis);
                    }
                } catch (error) {
                    reject(error);
                }
            });

            ws.on('error', reject);
            setTimeout(() => {
                ws.close();
                reject(new Error('页面分析超时'));
            }, 15000);
        });
    }

    // 📊 执行精确采集
    async performAccurateCollection(tab, pageStructure) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const collectScript = this.buildAccurateCollectionScript(pageStructure);
                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: collectScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        console.log('✅ 精确数据采集成功');
                        
                        const processedData = {
                            ...result,
                            tabId: tab.id,
                            pageStructure: pageStructure,
                            dataSource: 'accurate_collection',
                            extractionMethod: 'structure_based_extraction'
                        };
                        
                        ws.close();
                        resolve(processedData);
                    }
                } catch (error) {
                    reject(error);
                }
            });

            ws.on('error', reject);
            setTimeout(() => {
                ws.close();
                reject(new Error('精确采集超时'));
            }, 30000);
        });
    }

    // 📝 构建精确采集脚本
    buildAccurateCollectionScript(pageStructure) {
        return `
            (function() {
                try {
                    const result = {
                        timestamp: new Date().toISOString(),
                        url: window.location.href,
                        title: document.title,
                        notes: [],
                        debug: {
                            pageStructure: ${JSON.stringify(pageStructure)},
                            selectors: []
                        }
                    };

                    // 基于页面结构分析选择最佳选择器
                    const bestNoteSelector = ${JSON.stringify(pageStructure.noteSelectors)}.length > 0 ? 
                        ${JSON.stringify(pageStructure.noteSelectors)}[0].selector : null;

                    let noteElements = [];
                    
                    if (bestNoteSelector) {
                        noteElements = document.querySelectorAll(bestNoteSelector);
                        result.debug.selectors.push('使用分析得出的最佳选择器: ' + bestNoteSelector);
                    }

                    // 如果没找到，使用备用策略
                    if (noteElements.length === 0) {
                        const fallbackSelectors = [
                            'section[class*="note"]',
                            'div[class*="note"]',
                            'article',
                            '.note-item',
                            '.feed-item'
                        ];

                        for (const selector of fallbackSelectors) {
                            noteElements = document.querySelectorAll(selector);
                            if (noteElements.length > 0) {
                                result.debug.selectors.push('使用备用选择器: ' + selector);
                                break;
                            }
                        }
                    }

                    // 最后的通用策略
                    if (noteElements.length === 0) {
                        const allDivs = document.querySelectorAll('div');
                        noteElements = Array.from(allDivs).filter(div => {
                            // 更严格的筛选条件
                            const hasImage = div.querySelector('img');
                            const hasText = div.textContent.trim().length > 20;
                            const hasLink = div.querySelector('a[href*="/explore/"]') || div.querySelector('a[href*="/discovery/"]');
                            const notTooNested = div.querySelectorAll('div').length < 20; // 避免选择过于复杂的容器
                            
                            return hasImage && hasText && hasLink && notTooNested;
                        });
                        result.debug.selectors.push('使用通用策略，找到 ' + noteElements.length + ' 个元素');
                    }

                    console.log('找到笔记元素数量:', noteElements.length);

                    // 精确提取每个笔记的信息
                    noteElements.forEach((element, index) => {
                        try {
                            const noteData = {
                                index: index + 1,
                                id: element.id || 'note_' + index,
                                debug: {
                                    className: element.className,
                                    tagName: element.tagName
                                }
                            };

                            // 精确提取标题 - 多种策略
                            const titleStrategies = [
                                () => element.querySelector('h1, h2, h3, h4, h5, h6')?.textContent?.trim(),
                                () => element.querySelector('[class*="title"]')?.textContent?.trim(),
                                () => element.querySelector('.note-title')?.textContent?.trim(),
                                () => element.querySelector('a[href*="/explore/"]')?.textContent?.trim(),
                                () => {
                                    const textNodes = Array.from(element.querySelectorAll('*')).filter(el => 
                                        el.children.length === 0 && 
                                        el.textContent.trim().length > 10 && 
                                        el.textContent.trim().length < 100
                                    );
                                    return textNodes[0]?.textContent?.trim();
                                }
                            ];

                            for (const strategy of titleStrategies) {
                                const title = strategy();
                                if (title && title.length > 5) {
                                    noteData.title = title;
                                    break;
                                }
                            }

                            // 精确提取图片
                            const images = element.querySelectorAll('img');
                            noteData.images = [];
                            images.forEach(img => {
                                if (img.src && 
                                    !img.src.includes('data:') && 
                                    !img.src.includes('avatar') &&
                                    !img.src.includes('icon') &&
                                    img.src.includes('xhscdn.com')) {
                                    noteData.images.push({
                                        src: img.src,
                                        alt: img.alt || '',
                                        width: img.width || 0,
                                        height: img.height || 0
                                    });
                                }
                            });

                            // 精确提取互动数据 - 改进的策略
                            noteData.interactions = { likes: 0, collects: 0, comments: 0 };
                            
                            // 查找所有包含数字的元素
                            const allElements = element.querySelectorAll('*');
                            allElements.forEach(el => {
                                const text = el.textContent.trim();
                                const number = parseInt(text);
                                
                                if (!isNaN(number) && number >= 0 && text === number.toString()) {
                                    const parent = el.parentElement;
                                    const grandParent = parent ? parent.parentElement : null;
                                    const context = (parent?.textContent || '') + ' ' + (grandParent?.textContent || '');
                                    const className = el.className + ' ' + (parent?.className || '');
                                    
                                    // 更精确的匹配
                                    if (context.includes('赞') || context.includes('❤️') || context.includes('👍') || 
                                        className.includes('like') || className.includes('heart')) {
                                        noteData.interactions.likes = Math.max(noteData.interactions.likes, number);
                                    } else if (context.includes('收藏') || context.includes('💖') || 
                                              className.includes('collect') || className.includes('favorite')) {
                                        noteData.interactions.collects = Math.max(noteData.interactions.collects, number);
                                    } else if (context.includes('评论') || context.includes('💬') || 
                                              className.includes('comment')) {
                                        noteData.interactions.comments = Math.max(noteData.interactions.comments, number);
                                    }
                                }
                            });

                            // 精确提取链接
                            const linkElement = element.querySelector('a[href*="/explore/"]') || 
                                              element.querySelector('a[href*="/discovery/"]') ||
                                              element.querySelector('a[href*="xiaohongshu.com"]');
                            if (linkElement && linkElement.href) {
                                noteData.link = linkElement.href;
                            }

                            // 提取发布时间
                            const timeElements = element.querySelectorAll('*');
                            timeElements.forEach(el => {
                                const text = el.textContent.trim();
                                if (text.match(/\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}-\\d{2}/)) {
                                    noteData.publishTime = text;
                                }
                            });

                            // 只添加有效的笔记
                            if (noteData.title || noteData.images.length > 0 || noteData.link) {
                                result.notes.push(noteData);
                            }

                        } catch (error) {
                            console.error('处理笔记', index, '时出错:', error.message);
                        }
                    });

                    // 统计信息
                    result.summary = {
                        totalNotes: result.notes.length,
                        totalImages: result.notes.reduce((sum, note) => sum + note.images.length, 0),
                        totalLikes: result.notes.reduce((sum, note) => sum + (note.interactions.likes || 0), 0),
                        totalCollects: result.notes.reduce((sum, note) => sum + (note.interactions.collects || 0), 0),
                        totalComments: result.notes.reduce((sum, note) => sum + (note.interactions.comments || 0), 0),
                        notesWithTitles: result.notes.filter(note => note.title).length,
                        notesWithImages: result.notes.filter(note => note.images.length > 0).length,
                        notesWithLinks: result.notes.filter(note => note.link).length
                    };

                    return result;
                    
                } catch (error) {
                    return {
                        error: error.message,
                        stack: error.stack,
                        timestamp: new Date().toISOString()
                    };
                }
            })();
        `;
    }
}

// 🧪 测试精确采集
async function testAccurateCollection() {
    const collector = new AccurateNotesCollector();
    
    try {
        const data = await collector.collectAccurateData();
        
        console.log('\n🎯 精确采集结果:');
        console.log('=' * 60);
        console.log(`📝 总笔记数: ${data.summary.totalNotes}`);
        console.log(`🖼️  总图片数: ${data.summary.totalImages}`);
        console.log(`👍 总点赞数: ${data.summary.totalLikes}`);
        console.log(`💖 总收藏数: ${data.summary.totalCollects}`);
        console.log(`💬 总评论数: ${data.summary.totalComments}`);
        console.log(`📝 有标题的笔记: ${data.summary.notesWithTitles}`);
        console.log(`🖼️  有图片的笔记: ${data.summary.notesWithImages}`);
        console.log(`🔗 有链接的笔记: ${data.summary.notesWithLinks}\n`);
        
        // 显示前5篇笔记的详细信息
        const displayNotes = data.notes.slice(0, 5);
        displayNotes.forEach((note, index) => {
            console.log(`📝 笔记 ${index + 1}:`);
            console.log(`   标题: ${note.title || '无标题'}`);
            console.log(`   图片数量: ${note.images.length}`);
            console.log(`   👍 点赞: ${note.interactions.likes}`);
            console.log(`   💖 收藏: ${note.interactions.collects}`);
            console.log(`   💬 评论: ${note.interactions.comments}`);
            if (note.publishTime) {
                console.log(`   📅 发布时间: ${note.publishTime}`);
            }
            if (note.link) {
                console.log(`   🔗 链接: ${note.link}`);
            }
            console.log('');
        });
        
        if (data.notes.length > 5) {
            console.log(`... 还有 ${data.notes.length - 5} 篇笔记`);
        }
        
        return data;
        
    } catch (error) {
        console.error('❌ 精确采集失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    testAccurateCollection().catch(console.error);
}

module.exports = { AccurateNotesCollector, testAccurateCollection };
