#!/usr/bin/env node

/**
 * 🚀 高级评论采集器
 * 基于研究结果，采用多策略组合采集1472条评论
 */

const axios = require('axios');
const WebSocket = require('ws');

class AdvancedCommentsCollector {
    constructor() {
        this.debugPort = null;
        this.collectedComments = new Set(); // 防重复
        this.totalTarget = 1472; // 目标评论数
    }

    // 🚀 主要采集方法
    async collectAllComments() {
        console.log('🚀 启动高级评论采集器...\n');
        console.log(`🎯 目标: 采集 ${this.totalTarget} 条评论\n`);

        try {
            // 1. 获取调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 当前页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 执行多策略采集
            console.log('🔄 执行多策略评论采集...');
            const commentsData = await this.executeMultiStrategyCollection(tab);
            
            return commentsData;

        } catch (error) {
            console.error('❌ 高级采集失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取调试端口
    async getCurrentDebugPort() {
        try {
            const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 10000
            });

            if (response.data.success && response.data.data && response.data.data.result) {
                const httpInfo = response.data.data.result.http;
                if (httpInfo) {
                    this.debugPort = httpInfo.split(':')[1];
                    console.log(`✅ 调试端口: ${this.debugPort}`);
                    return;
                }
            }

            const commonPorts = [63524, 51859, 58222, 9222];
            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到端口: ${port}`);
                    return;
                } catch (error) {
                    // 继续尝试
                }
            }

            throw new Error('未找到可用端口');
        } catch (error) {
            throw new Error(`获取端口失败: ${error.message}`);
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
            timeout: 5000
        });

        return response.data.find(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.url.includes('creator.xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );
    }

    // 🔄 执行多策略采集
    async executeMultiStrategyCollection(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ 高级采集WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const advancedCollectionScript = this.buildAdvancedCollectionScript();
                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: advancedCollectionScript,
                            returnByValue: true
                        }
                    }));
                }, 2000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            console.log('✅ 高级采集成功');
                            console.log(`📊 采集进度: ${result.data.summary.totalComments}/${this.totalTarget} (${Math.round(result.data.summary.totalComments/this.totalTarget*100)}%)`);
                            ws.close();
                            resolve(result.data);
                        } else {
                            console.log('❌ 高级采集失败:', result.error || '未知错误');
                            ws.close();
                            reject(new Error(result.error || '高级采集失败'));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理采集结果失败:', error.message);
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ 高级采集WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('高级采集超时'));
            }, 300000); // 5分钟超时
        });
    }

    // 📝 构建高级采集脚本
    buildAdvancedCollectionScript() {
        return `
            (async function() {
                try {
                    console.log('🚀 开始高级评论采集...');
                    
                    const commentsData = {
                        noteUrl: window.location.href,
                        noteTitle: document.title.replace(' - 小红书', '').trim(),
                        timestamp: new Date().toISOString(),
                        comments: [],
                        summary: {
                            totalComments: 0,
                            totalReplies: 0,
                            totalLikes: 0,
                            collectionStrategy: 'advanced-multi-strategy'
                        }
                    };

                    // 策略1: 强制展开所有"更多"按钮
                    console.log('📋 策略1: 展开所有"更多"按钮...');
                    await expandAllMoreButtons();

                    // 策略2: 深度滚动加载
                    console.log('📜 策略2: 深度滚动加载...');
                    await performDeepScrolling();

                    // 策略3: 智能评论提取
                    console.log('🔍 策略3: 智能评论提取...');
                    await extractAllComments();

                    // 展开所有"更多"按钮的函数
                    async function expandAllMoreButtons() {
                        const moreButtonTexts = [
                            '查看更多评论', '展开更多', '加载更多', '更多评论', '显示更多',
                            '查看全部评论', '展开全部', '查看更多回复', '展开回复', '查看回复',
                            'more', '更多', '展开', '查看全部', '显示回复', '展开 ', '条回复'
                        ];

                        let totalClicked = 0;
                        let round = 0;
                        
                        while (round < 50) { // 最多50轮
                            round++;
                            let clickedInThisRound = 0;
                            
                            console.log(\`🔘 第\${round}轮按钮点击...\`);
                            
                            for (const buttonText of moreButtonTexts) {
                                // 查找所有可能的按钮元素
                                const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                                    const text = el.textContent.trim();
                                    const isVisible = el.offsetParent !== null;
                                    const isClickable = el.tagName === 'BUTTON' || 
                                                      el.tagName === 'A' ||
                                                      el.onclick || 
                                                      el.className.includes('btn') ||
                                                      el.className.includes('button') ||
                                                      el.className.includes('more') ||
                                                      el.className.includes('expand') ||
                                                      el.style.cursor === 'pointer' ||
                                                      getComputedStyle(el).cursor === 'pointer';
                                    
                                    return isVisible && isClickable && (
                                        text.includes(buttonText) ||
                                        text === buttonText ||
                                        (buttonText === '条回复' && /\\d+条回复/.test(text))
                                    );
                                });
                                
                                for (const el of elements) {
                                    try {
                                        // 滚动到元素可见
                                        el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                        await new Promise(resolve => setTimeout(resolve, 100));
                                        
                                        // 点击元素
                                        el.click();
                                        clickedInThisRound++;
                                        totalClicked++;
                                        
                                        // 等待内容加载
                                        await new Promise(resolve => setTimeout(resolve, 500));
                                        
                                    } catch (e) {
                                        // 忽略点击错误
                                    }
                                }
                            }
                            
                            console.log(\`   点击了 \${clickedInThisRound} 个按钮\`);
                            
                            // 如果这轮没有点击任何按钮，说明已经展开完毕
                            if (clickedInThisRound === 0) {
                                console.log('✅ 所有按钮已展开完毕');
                                break;
                            }
                            
                            // 等待页面更新
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }
                        
                        console.log(\`🎉 总共点击了 \${totalClicked} 个"更多"按钮\`);
                    }

                    // 深度滚动的函数
                    async function performDeepScrolling() {
                        const maxScrolls = 100;
                        let scrollCount = 0;
                        let lastHeight = document.body.scrollHeight;
                        let noChangeCount = 0;
                        
                        while (scrollCount < maxScrolls && noChangeCount < 5) {
                            scrollCount++;
                            
                            // 滚动到底部
                            window.scrollTo(0, document.body.scrollHeight);
                            await new Promise(resolve => setTimeout(resolve, 1000));
                            
                            // 检查页面高度是否有变化
                            const currentHeight = document.body.scrollHeight;
                            if (currentHeight === lastHeight) {
                                noChangeCount++;
                            } else {
                                noChangeCount = 0;
                                lastHeight = currentHeight;
                            }
                            
                            console.log(\`📜 滚动 \${scrollCount}/\${maxScrolls}, 页面高度: \${currentHeight}px\`);
                        }
                        
                        console.log('✅ 深度滚动完成');
                    }

                    // 提取所有评论的函数
                    async function extractAllComments() {
                        // 使用多种选择器策略
                        const strategies = [
                            // 策略A: 基于class的选择器
                            () => document.querySelectorAll('[class*="comment-item"], [class*="comment-content"]'),
                            // 策略B: 基于头像的选择器
                            () => {
                                const avatars = document.querySelectorAll('img[src*="avatar"]');
                                return Array.from(avatars).map(img => img.closest('div')).filter(div => div);
                            },
                            // 策略C: 基于时间的选择器
                            () => {
                                const timeElements = Array.from(document.querySelectorAll('*')).filter(el => {
                                    const text = el.textContent.trim();
                                    return /\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}-\\d{2}/.test(text);
                                });
                                return timeElements.map(el => el.closest('div')).filter(div => div);
                            }
                        ];

                        const allCommentElements = new Set();
                        
                        // 执行所有策略
                        for (let i = 0; i < strategies.length; i++) {
                            try {
                                const elements = strategies[i]();
                                console.log(\`策略\${String.fromCharCode(65+i)}: 找到 \${elements.length} 个元素\`);
                                
                                Array.from(elements).forEach(el => {
                                    if (el && el.textContent && el.textContent.trim().length > 10) {
                                        allCommentElements.add(el);
                                    }
                                });
                            } catch (e) {
                                console.log(\`策略\${String.fromCharCode(65+i)} 执行失败:, e.message\`);
                            }
                        }

                        console.log(\`🔍 总共找到 \${allCommentElements.size} 个独特的评论元素\`);

                        // 提取评论详细信息
                        Array.from(allCommentElements).forEach((element, index) => {
                            try {
                                const commentData = extractCommentData(element, index + 1);
                                if (commentData && commentData.content) {
                                    commentsData.comments.push(commentData);
                                }
                            } catch (e) {
                                console.log(\`提取评论 \${index + 1} 失败: \${e.message}\`);
                            }
                        });
                    }

                    // 提取单个评论数据的函数
                    function extractCommentData(element, id) {
                        const commentData = {
                            id: id,
                            username: '',
                            avatar: '',
                            content: '',
                            publishTime: '',
                            likes: 0,
                            replies: [],
                            isReply: false,
                            replyTo: '',
                            level: 0
                        };

                        // 提取用户名
                        const usernameSelectors = [
                            '.username', '.user-name', '.nickname', 
                            '[class*="user"]', '[class*="name"]', '[class*="author"]'
                        ];
                        
                        for (const selector of usernameSelectors) {
                            const usernameEl = element.querySelector(selector);
                            if (usernameEl && usernameEl.textContent.trim()) {
                                const username = usernameEl.textContent.trim();
                                if (username.length > 0 && username.length < 50 && !username.includes('天前')) {
                                    commentData.username = username;
                                    break;
                                }
                            }
                        }

                        // 如果没找到用户名，从文本中智能提取
                        if (!commentData.username) {
                            const textNodes = Array.from(element.querySelectorAll('*')).filter(el => 
                                el.children.length === 0 && 
                                el.textContent.trim().length > 1 && 
                                el.textContent.trim().length < 30 &&
                                !el.textContent.includes('天前') &&
                                !el.textContent.includes('小时前') &&
                                !el.textContent.includes('分钟前')
                            );
                            if (textNodes.length > 0) {
                                commentData.username = textNodes[0].textContent.trim();
                            }
                        }

                        // 提取头像
                        const avatarImg = element.querySelector('img[src*="avatar"]') || 
                                        element.querySelector('img[class*="avatar"]') ||
                                        element.querySelector('img');
                        if (avatarImg && avatarImg.src) {
                            commentData.avatar = avatarImg.src;
                        }

                        // 提取评论内容
                        const allTexts = Array.from(element.querySelectorAll('*')).map(el => 
                            el.textContent.trim()
                        ).filter(text => 
                            text.length > 10 && text.length < 2000 &&
                            !text.includes('天前') && !text.includes('小时前') &&
                            !text.includes('点赞') && !text.includes('回复') &&
                            !text.includes('更多') && !text.includes('展开')
                        );
                        
                        if (allTexts.length > 0) {
                            // 选择最长的文本作为评论内容
                            commentData.content = allTexts.reduce((a, b) => a.length > b.length ? a : b);
                        }

                        // 提取发布时间
                        const timePatterns = [
                            /\\d+天前/, /\\d+小时前/, /\\d+分钟前/, /昨天/, /前天/,
                            /\\d{2}-\\d{2}/, /\\d{4}-\\d{2}-\\d{2}/
                        ];
                        
                        const allText = element.textContent;
                        for (const pattern of timePatterns) {
                            const match = allText.match(pattern);
                            if (match) {
                                commentData.publishTime = match[0];
                                break;
                            }
                        }

                        // 提取点赞数
                        const likeElements = Array.from(element.querySelectorAll('*')).filter(el => {
                            const text = el.textContent.trim();
                            const context = el.parentElement ? el.parentElement.textContent : '';
                            return /^\\d+$/.test(text) && (
                                context.includes('赞') || context.includes('❤️') || 
                                el.className.includes('like')
                            );
                        });
                        
                        if (likeElements.length > 0) {
                            commentData.likes = parseInt(likeElements[0].textContent.trim()) || 0;
                        }

                        return commentData;
                    }

                    // 计算统计信息
                    commentsData.summary.totalComments = commentsData.comments.length;
                    commentsData.summary.totalReplies = commentsData.comments.filter(c => c.isReply).length;
                    commentsData.summary.totalLikes = commentsData.comments.reduce((sum, c) => sum + c.likes, 0);

                    console.log(\`🎉 高级采集完成! 采集到 \${commentsData.summary.totalComments} 条评论\`);

                    return { success: true, data: commentsData };
                    
                } catch (error) {
                    return { 
                        success: false, 
                        error: error.message,
                        stack: error.stack 
                    };
                }
            })();
        `;
    }
}

// 🧪 测试高级采集
async function testAdvancedCollection() {
    const collector = new AdvancedCommentsCollector();
    
    try {
        const commentsData = await collector.collectAllComments();
        
        console.log('\n🎉 高级采集结果:');
        console.log('=' * 60);
        console.log(`📝 笔记标题: ${commentsData.noteTitle}`);
        console.log(`🔗 笔记链接: ${commentsData.noteUrl}`);
        console.log(`💬 总评论数: ${commentsData.summary.totalComments}`);
        console.log(`↩️  回复数: ${commentsData.summary.totalReplies}`);
        console.log(`👍 总点赞数: ${commentsData.summary.totalLikes}`);
        console.log(`📊 采集进度: ${Math.round(commentsData.summary.totalComments/1472*100)}%`);
        
        return commentsData;
        
    } catch (error) {
        console.error('❌ 高级采集测试失败:', error.message);
    }
}

// 💾 保存高级采集数据
async function saveAdvancedCommentsData() {
    const fs = require('fs');
    const collector = new AdvancedCommentsCollector();

    try {
        console.log('💾 开始高级采集并保存评论数据...\n');

        const commentsData = await collector.collectAllComments();

        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const jsonFileName = `advanced-comments-${timestamp}.json`;
        const txtFileName = `advanced-comments-${timestamp}.txt`;

        // 保存JSON数据
        fs.writeFileSync(jsonFileName, JSON.stringify(commentsData, null, 2), 'utf8');
        console.log(`✅ JSON数据已保存: ${jsonFileName}`);

        // 生成可读报告
        const report = generateAdvancedCommentsReport(commentsData);
        fs.writeFileSync(txtFileName, report, 'utf8');
        console.log(`✅ 可读报告已保存: ${txtFileName}`);

        return { jsonFile: jsonFileName, txtFile: txtFileName, data: commentsData };

    } catch (error) {
        console.error('❌ 保存高级采集数据失败:', error.message);
        throw error;
    }
}

// 📝 生成高级评论报告
function generateAdvancedCommentsReport(data) {
    const lines = [];

    lines.push('🚀 小红书笔记高级评论采集报告');
    lines.push('=' * 60);
    lines.push(`📅 采集时间: ${data.timestamp}`);
    lines.push(`📝 笔记标题: ${data.noteTitle}`);
    lines.push(`🔗 笔记链接: ${data.noteUrl}`);
    lines.push(`🔧 采集策略: ${data.summary.collectionStrategy}`);
    lines.push('');

    lines.push('📊 采集统计:');
    lines.push(`💬 总评论数: ${data.summary.totalComments}`);
    lines.push(`↩️  回复数: ${data.summary.totalReplies}`);
    lines.push(`👍 总点赞数: ${data.summary.totalLikes}`);
    lines.push(`📈 采集进度: ${Math.round(data.summary.totalComments/1472*100)}% (目标1472条)`);
    lines.push('');

    lines.push('💬 评论详情:');
    lines.push('-' * 60);

    data.comments.forEach((comment, index) => {
        lines.push(`\n💬 评论 ${index + 1}:`);
        lines.push(`👤 用户: ${comment.username || '匿名'}`);
        lines.push(`📄 内容: ${comment.content || '无内容'}`);
        lines.push(`👍 点赞: ${comment.likes}`);
        lines.push(`📅 时间: ${comment.publishTime || '未知'}`);
        lines.push(`🔗 头像: ${comment.avatar || '无'}`);

        if (comment.isReply) {
            lines.push(`↩️  回复给: ${comment.replyTo}`);
            lines.push(`📊 层级: ${comment.level}`);
        }

        if (comment.replies && comment.replies.length > 0) {
            lines.push(`💭 回复数: ${comment.replies.length}`);
            comment.replies.forEach((reply, replyIndex) => {
                lines.push(`   💭 回复 ${replyIndex + 1}: ${reply.username} - ${reply.content}`);
            });
        }
    });

    lines.push('\n' + '=' * 60);
    lines.push('📅 报告生成时间: ' + new Date().toLocaleString('zh-CN'));

    return lines.join('\n');
}

// 运行测试
if (require.main === module) {
    const args = process.argv.slice(2);
    if (args.includes('--save')) {
        saveAdvancedCommentsData().then(result => {
            console.log('\n🎉 高级采集数据保存完成!');
            console.log(`📁 JSON文件: ${result.jsonFile}`);
            console.log(`📄 文本报告: ${result.txtFile}`);
        }).catch(console.error);
    } else {
        testAdvancedCollection().catch(console.error);
    }
}

module.exports = { AdvancedCommentsCollector, testAdvancedCollection, saveAdvancedCommentsData };
