#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 API比特浏览器评论爬取器
使用比特浏览器API自动获取调试端口，然后爬取所有评论
"""

import time
import json
import re
import os
import requests
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

class ApiBitBrowserScraper:
    def __init__(self):
        self.output_dir = './scraped_data'
        self.ensure_output_dir()
        
        self.config = {
            'target_comments': 1472,
            'max_scroll_attempts': 1000,
            'scroll_delay': 0.6,
            'click_delay': 0.4,
            'bitbrowser_api': 'http://127.0.0.1:56906'  # 比特浏览器API地址
        }
        
        self.driver = None
        
    def ensure_output_dir(self):
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def get_bitbrowser_ports(self):
        """通过比特浏览器API获取所有调试端口"""
        print("🔍 通过比特浏览器API获取调试端口...")
        
        api_url = f"{self.config['bitbrowser_api']}/browser/ports"
        
        try:
            response = requests.post(api_url, json={}, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    ports_data = data['data']
                    ports = list(ports_data.values())
                    print(f"✅ 获取到 {len(ports)} 个调试端口:")
                    for browser_id, port in ports_data.items():
                        print(f"   🌐 浏览器 {browser_id[:8]}... -> 端口 {port}")
                    return [int(port) for port in ports]
                else:
                    print("❌ API返回数据格式错误")
                    return []
            else:
                print(f"❌ API请求失败，状态码: {response.status_code}")
                return []
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到比特浏览器API，请确保比特浏览器正在运行")
            return []
        except Exception as e:
            print(f"❌ API请求异常: {e}")
            return []
    
    def test_debug_port(self, port):
        """测试调试端口是否可用"""
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 尝试连接
            test_driver = webdriver.Chrome(options=chrome_options)
            test_driver.quit()
            return True
        except Exception:
            return False
    
    def connect_to_bitbrowser(self):
        """连接到比特浏览器"""
        print("🔗 连接到比特浏览器...")
        
        # 首先通过API获取端口
        api_ports = self.get_bitbrowser_ports()
        
        if not api_ports:
            print("💡 API获取端口失败，请手动输入端口号")
            return self.manual_connect()
        
        # 测试每个端口
        for port in api_ports:
            print(f"🔍 尝试连接端口 {port}...")
            
            try:
                chrome_options = Options()
                chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                self.driver = webdriver.Chrome(options=chrome_options)
                
                print(f"✅ 端口 {port} 连接成功!")
                
                # 检查当前页面
                current_url = self.driver.current_url
                print(f"📄 当前页面: {current_url}")
                
                if 'xiaohongshu.com' not in current_url:
                    print("💡 请手动导航到小红书评论页面...")
                    print("完成后按回车继续...")
                    input()
                
                return True
                
            except Exception as e:
                print(f"❌ 端口 {port} 连接失败: {str(e)}")
                continue
        
        print("❌ 所有API端口都连接失败，请手动输入端口号")
        return self.manual_connect()
    
    def manual_connect(self):
        """手动输入端口连接"""
        print("\n💡 如何查找调试端口:")
        print("   1. 在比特浏览器中右键点击窗口")
        print("   2. 选择'检查'或'开发者工具'")
        print("   3. 在地址栏中查看端口号")
        print("   4. 或者在比特浏览器设置中查看调试端口")
        
        while True:
            try:
                port = input("\n请输入调试端口号 (例如: 9222): ").strip()
                if not port:
                    continue
                
                port = int(port)
                print(f"🔍 尝试连接端口 {port}...")
                
                chrome_options = Options()
                chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                self.driver = webdriver.Chrome(options=chrome_options)
                
                print(f"✅ 端口 {port} 连接成功!")
                
                # 检查当前页面
                current_url = self.driver.current_url
                print(f"📄 当前页面: {current_url}")
                
                if 'xiaohongshu.com' not in current_url:
                    print("💡 请手动导航到小红书评论页面...")
                    print("完成后按回车继续...")
                    input()
                
                return True
                
            except ValueError:
                print("❌ 请输入有效的端口号")
            except Exception as e:
                print(f"❌ 连接失败: {str(e)}")
                print("请尝试其他端口号")
    
    def scroll_to_comments(self):
        """滚动到评论区域"""
        print("🎯 定位评论区域...")
        
        try:
            # 查找评论区域的多种方式
            comment_selectors = [
                "span:contains('条评论')",
                "div:contains('评论')",
                "[class*='comment']",
                "[class*='Comment']"
            ]
            
            for selector in comment_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if '条评论' in element.text or '评论' in element.text:
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
                            print("✅ 找到评论区域")
                            time.sleep(2)
                            return
                except:
                    continue
            
            # 如果没找到，滚动到页面中部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.6);")
            print("💡 滚动到页面中部")
            time.sleep(2)
            
        except Exception as e:
            print(f"⚠️ 滚动到评论区域失败: {e}")
    
    def count_comments(self):
        """统计当前页面的评论数量"""
        try:
            page_text = self.driver.find_element(By.TAG_NAME, "body").text
            
            # 多种计数方式
            time_count = len(re.findall(r'\d{2}-\d{2}', page_text))
            reply_count = len(re.findall(r'\d+回复', page_text))
            keyword_count = len(re.findall(r'求带|宝子|学姐|兼职|聊天员', page_text)) // 2
            
            return max(time_count, reply_count, keyword_count)
        except:
            return 0
    
    def click_load_more(self):
        """点击加载更多按钮"""
        load_more_texts = ['加载更多', '展开更多', '查看更多', '更多评论', '展开', '更多']
        
        for text in load_more_texts:
            try:
                elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{text}')]")
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        element.click()
                        return True
            except:
                continue
        return False
    
    def perform_scroll(self, scroll_index):
        """执行滚动操作"""
        strategies = [
            lambda: self.driver.execute_script("window.scrollBy(0, 300);"),
            lambda: self.driver.execute_script("window.scrollBy(0, 500);"),
            lambda: self.driver.execute_script("window.scrollBy(0, 800);"),
            lambda: self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        ]
        
        strategy_index = scroll_index % len(strategies)
        strategies[strategy_index]()
    
    def scrape_all_comments(self):
        """爬取所有评论"""
        print("🚀 开始爬取所有评论...")
        print(f"🎯 目标: {self.config['target_comments']} 条评论")
        
        # 滚动到评论区域
        self.scroll_to_comments()
        
        current_count = 0
        previous_count = 0
        stable_count = 0
        total_scrolls = 0
        total_clicks = 0
        
        for i in range(self.config['max_scroll_attempts']):
            print(f"📜 滚动 {i + 1}/{self.config['max_scroll_attempts']}")
            
            # 统计评论数量
            current_count = self.count_comments()
            print(f"   💬 当前评论数: {current_count}")
            
            # 检查是否达到目标
            if current_count >= self.config['target_comments'] * 0.95:
                print(f"🎉 接近目标！{current_count}/{self.config['target_comments']}")
                break
            
            # 检查进度
            if current_count == previous_count:
                stable_count += 1
                print(f"   ⏸️ 稳定 {stable_count} 次")
            else:
                new_comments = current_count - previous_count
                print(f"   📈 新增: {new_comments} 条")
                stable_count = 0
                previous_count = current_count
            
            # 点击加载更多
            if stable_count >= 3:
                print("   🔄 点击加载更多...")
                if self.click_load_more():
                    total_clicks += 1
                    stable_count = 0
                    print(f"   ✅ 点击成功，总计: {total_clicks} 次")
            
            # 执行滚动
            self.perform_scroll(i)
            total_scrolls += 1
            
            # 如果长时间稳定，停止
            if stable_count >= 20:
                print("   ⏹️ 长时间无新内容，停止")
                break
            
            # 等待
            time.sleep(self.config['scroll_delay'])
            
            # 每100次输出进度
            if i % 100 == 0 and i > 0:
                progress = round((current_count / self.config['target_comments']) * 100)
                print(f"📊 进度: {progress}% ({current_count}/{self.config['target_comments']})")
                print(f"   📜 滚动: {total_scrolls} 次")
                print(f"   🔄 点击: {total_clicks} 次")
        
        print(f"✅ 滚动完成！最终评论数: {current_count}")
        return current_count, total_scrolls, total_clicks

    def extract_comments_from_page(self):
        """从页面提取评论"""
        print("🧠 开始提取评论...")

        try:
            page_text = self.driver.find_element(By.TAG_NAME, "body").text
        except:
            print("❌ 无法获取页面文本")
            return []

        # 提取基本信息
        note_info = {
            'id': '',
            'title': '',
            'author': '',
            'totalCommentCount': 0
        }

        # 提取笔记ID
        current_url = self.driver.current_url
        url_match = re.search(r'explore/([a-f0-9]+)', current_url)
        if url_match:
            note_info['id'] = url_match.group(1)

        # 提取标题
        try:
            note_info['title'] = self.driver.title.replace(' - 小红书', '')
        except:
            note_info['title'] = ''

        # 提取评论总数
        comment_count_match = re.search(r'(\d+)\s*条评论', page_text)
        if comment_count_match:
            note_info['totalCommentCount'] = int(comment_count_match.group(1))

        # 提取作者
        if '漫娴学姐' in page_text:
            note_info['author'] = '漫娴学姐 招暑假工版'

        # 解析评论
        comments = self.parse_comments(page_text)

        return {
            'noteInfo': note_info,
            'comments': comments,
            'extractStats': {
                'totalTextLength': len(page_text),
                'successfulExtractions': len(comments),
                'extractionMethods': ['api-selenium']
            },
            'extractTime': datetime.now().isoformat()
        }

    def parse_comments(self, page_text):
        """解析评论"""
        print("🔍 开始解析评论...")

        all_comments = []

        # 时间模式解析
        time_pattern = r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)'
        time_matches = list(re.finditer(time_pattern, page_text))

        for i, match in enumerate(time_matches):
            time_str = match.group(1)
            time_index = match.start()

            start_index = max(0, time_index - 200)
            end_index = time_matches[i + 1].start() if i + 1 < len(time_matches) else time_index + 1000

            comment_text = page_text[start_index:min(len(page_text), end_index)].strip()

            if 20 < len(comment_text) < 2000:
                comment = self.create_comment(comment_text, len(all_comments) + 1, 'time')
                if comment:
                    all_comments.append(comment)

        # 关键词模式解析
        keywords = ['求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯', 'Carina', '广州']

        for keyword in keywords:
            for match in re.finditer(keyword, page_text):
                start_index = max(0, match.start() - 150)
                end_index = min(len(page_text), match.start() + 800)

                comment_text = page_text[start_index:end_index].strip()

                if 15 < len(comment_text) < 1500:
                    comment = self.create_comment(comment_text, len(all_comments) + 1, 'keyword')
                    if comment:
                        all_comments.append(comment)

        # 去重
        unique_comments = self.deduplicate_comments(all_comments)

        print(f"✅ 解析完成，提取到 {len(unique_comments)} 条评论")
        return unique_comments

    def create_comment(self, text, comment_id, source):
        """创建评论对象"""
        comment = {
            'id': comment_id,
            'userId': '',
            'username': '',
            'content': '',
            'time': '',
            'likes': 0,
            'replyCount': 0,
            'isAuthor': '作者' in text,
            'isPinned': '置顶' in text,
            'extractedInfo': {
                'source': source,
                'originalLength': len(text)
            }
        }

        # 提取时间
        time_match = re.search(r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)', text)
        if time_match:
            comment['time'] = time_match.group(1)

        # 提取用户名
        user_patterns = [
            r'^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}',
            r'([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带',
            r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})'
        ]

        for pattern in user_patterns:
            match = re.search(pattern, text)
            if match:
                comment['username'] = match.group(1).strip()
                break

        # 清理内容
        content = text
        content = re.sub(r'\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前', '', content)
        content = re.sub(r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+', '', content)
        content = re.sub(r'作者|置顶评论|回复|赞|仅自己可见', '', content)
        if comment['username']:
            content = content.replace(comment['username'], '')
        content = re.sub(r'\s+', ' ', content).strip()

        comment['content'] = content

        # 提取数字信息
        like_match = re.search(r'(\d+)\s*赞', text)
        if like_match:
            comment['likes'] = int(like_match.group(1))

        reply_match = re.search(r'(\d+)\s*回复', text)
        if reply_match:
            comment['replyCount'] = int(reply_match.group(1))

        # 提取用户ID
        user_id_match = re.search(r'小红薯([A-F0-9]{8,})', text)
        if user_id_match:
            comment['userId'] = user_id_match.group(1)

        return comment if len(comment['content']) >= 5 else None

    def deduplicate_comments(self, comments):
        """去重评论"""
        unique = []
        seen = set()

        for comment in comments:
            key = comment['content'][:30]
            if key not in seen:
                seen.add(key)
                unique.append(comment)

        # 重新分配ID
        for index, comment in enumerate(unique):
            comment['id'] = index + 1

        return unique

    def save_results(self, result, scroll_count, click_count):
        """保存结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"api_comments_{result['noteInfo']['id']}_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)

        # 添加统计信息
        result['scrapingStats'] = {
            'totalScrolls': scroll_count,
            'totalClicks': click_count,
            'scrapingMethod': 'api-selenium'
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"💾 数据已保存: {filepath}")
        return filepath

    def display_results(self, result):
        """显示结果"""
        print('\n🎉 API爬取完成！')
        print('📊 最终统计:')
        print(f"   📝 笔记ID: {result['noteInfo']['id']}")
        print(f"   📝 笔记标题: {result['noteInfo']['title']}")
        print(f"   👤 笔记作者: {result['noteInfo']['author']}")
        print(f"   🎯 目标评论数: {result['noteInfo']['totalCommentCount']}")
        print(f"   💬 实际提取数: {len(result['comments'])}")

        if result['noteInfo']['totalCommentCount'] > 0:
            completion_rate = round((len(result['comments']) / result['noteInfo']['totalCommentCount']) * 100)
            print(f"   📈 完成度: {completion_rate}%")

        print(f"   📄 页面文本长度: {result['extractStats']['totalTextLength']}")

        if result['comments']:
            print('\n👥 评论预览:')
            for i, comment in enumerate(result['comments'][:10]):
                username = comment['username'] or '匿名'
                time_str = comment['time'] or '未知时间'
                content = comment['content'][:80] + '...' if len(comment['content']) > 80 else comment['content']
                print(f"   {i + 1}. {username} ({time_str}): {content}")

            # 兼职相关统计
            job_keywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱']
            job_comments = [c for c in result['comments'] if any(keyword in c['content'] for keyword in job_keywords)]

            if job_comments:
                job_percentage = round((len(job_comments) / len(result['comments'])) * 100)
                print(f"\n💼 兼职相关评论: {len(job_comments)}/{len(result['comments'])} ({job_percentage}%)")

    def run(self):
        """运行爬虫"""
        try:
            print("🎯 API比特浏览器评论爬取器启动")
            print("=" * 50)

            # 连接到比特浏览器
            if not self.connect_to_bitbrowser():
                print("❌ 连接失败，退出程序")
                return

            # 爬取评论
            current_count, total_scrolls, total_clicks = self.scrape_all_comments()

            # 提取评论
            result = self.extract_comments_from_page()

            # 保存结果
            filepath = self.save_results(result, total_scrolls, total_clicks)

            # 显示结果
            self.display_results(result)

            print(f"\n✅ 爬取完成！数据已保存到: {filepath}")

        except KeyboardInterrupt:
            print("\n👋 用户中断操作")
        except Exception as e:
            print(f"\n❌ 程序异常: {e}")
        finally:
            if self.driver:
                print("🔒 关闭浏览器连接...")
                try:
                    self.driver.quit()
                except:
                    pass

if __name__ == "__main__":
    scraper = ApiBitBrowserScraper()
    scraper.run()
