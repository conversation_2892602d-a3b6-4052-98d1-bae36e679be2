#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 比特浏览器API诊断工具
详细诊断API连接问题
"""

import requests
import json

def test_api_endpoints():
    """测试不同的API端点"""
    api_base = "http://127.0.0.1:56906"
    
    print("🔍 测试API端点可访问性...")
    
    endpoints = [
        "/",
        "/browser",
        "/browser/list", 
        "/browser/ports",
        "/api",
        "/status"
    ]
    
    for endpoint in endpoints:
        try:
            url = f"{api_base}{endpoint}"
            response = requests.get(url, timeout=5)
            print(f"   GET {endpoint}: {response.status_code} - {response.text[:100]}")
        except Exception as e:
            print(f"   GET {endpoint}: 连接失败 - {e}")

def test_different_auth_methods(token):
    """测试不同的认证方法"""
    api_base = "http://127.0.0.1:56906"
    
    print(f"🔍 测试不同认证方法...")
    
    # 方法1: X-API-KEY header
    headers1 = {'X-API-KEY': token}
    
    # 方法2: Authorization header
    headers2 = {'Authorization': f'Bearer {token}'}
    
    # 方法3: Authorization Basic
    headers3 = {'Authorization': f'Basic {token}'}
    
    # 方法4: 在URL参数中
    url_with_token = f"{api_base}/browser/ports?token={token}"
    
    # 方法5: 在请求体中
    body_with_token = {'token': token}
    
    methods = [
        ("X-API-KEY header", headers1, {}),
        ("Authorization Bearer", headers2, {}),
        ("Authorization Basic", headers3, {}),
        ("URL parameter", {}, {}),
        ("Request body", {}, body_with_token)
    ]
    
    for method_name, headers, body in methods:
        try:
            if method_name == "URL parameter":
                response = requests.get(url_with_token, timeout=5)
            else:
                response = requests.post(f"{api_base}/browser/ports", 
                                       headers=headers, json=body, timeout=5)
            
            print(f"   {method_name}: {response.status_code} - {response.text[:100]}")
            
            if response.status_code == 200:
                print(f"   ✅ {method_name} 认证成功!")
                return method_name, headers, body
                
        except Exception as e:
            print(f"   {method_name}: 异常 - {e}")
    
    return None, None, None

def check_api_documentation():
    """检查API文档端点"""
    api_base = "http://127.0.0.1:56906"
    
    print("🔍 查找API文档...")
    
    doc_endpoints = [
        "/docs",
        "/swagger", 
        "/api-docs",
        "/help",
        "/info"
    ]
    
    for endpoint in doc_endpoints:
        try:
            url = f"{api_base}{endpoint}"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ 找到文档: {endpoint}")
                print(f"      内容预览: {response.text[:200]}")
        except:
            pass

def main():
    print("🎯 比特浏览器API诊断工具")
    print("=" * 50)
    
    # 1. 测试基本连接
    test_api_endpoints()
    
    # 2. 检查文档
    check_api_documentation()
    
    # 3. 获取Token进行认证测试
    print("\n" + "=" * 50)
    print("💡 请提供API Token进行认证测试:")
    print("   1. 在比特浏览器中打开Local API页面")
    print("   2. 如果Token显示为过期，点击'重新生成'")
    print("   3. 复制新生成的完整Token")
    
    token = input("\n请输入API Token: ").strip()
    
    if token:
        print(f"\n🔍 使用Token进行认证测试: {token[:20]}...")
        
        # 4. 测试不同认证方法
        success_method, headers, body = test_different_auth_methods(token)
        
        if success_method:
            print(f"\n🎉 找到有效的认证方法: {success_method}")
            
            # 5. 尝试获取实际数据
            print("🔍 尝试获取浏览器数据...")
            
            try:
                api_base = "http://127.0.0.1:56906"
                
                if success_method == "URL parameter":
                    response = requests.get(f"{api_base}/browser/list?token={token}", timeout=10)
                else:
                    list_payload = {"page": 0, "pageSize": 50}
                    response = requests.post(f"{api_base}/browser/list", 
                                           headers=headers, json=list_payload, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ 成功获取浏览器列表!")
                    
                    if data.get('success') and data.get('data'):
                        browsers = data['data']
                        print(f"   📱 浏览器数量: {len(browsers)}")
                        
                        # 查找19号窗口
                        for browser in browsers:
                            name = browser.get('name', '')
                            browser_id = browser.get('id', '')
                            if '19' in name or '19' in browser_id:
                                print(f"   🎯 找到19号窗口: {name} (ID: {browser_id})")
                    
                    # 保存成功的配置
                    config = {
                        'api_base': api_base,
                        'api_token': token,
                        'auth_method': success_method,
                        'headers': headers,
                        'body': body,
                        'test_result': data
                    }
                    
                    with open('working_api_config.json', 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=2, ensure_ascii=False)
                    
                    print(f"💾 工作配置已保存到 working_api_config.json")
                    
                else:
                    print(f"   ❌ 获取数据失败: {response.text}")
                    
            except Exception as e:
                print(f"   ❌ 获取数据异常: {e}")
        else:
            print("\n❌ 所有认证方法都失败")
            print("💡 可能的解决方案:")
            print("   1. 在比特浏览器中重新生成API Token")
            print("   2. 检查Local API是否正确启用")
            print("   3. 重启比特浏览器")
            print("   4. 检查防火墙设置")
    else:
        print("⚠️ 未提供Token，跳过认证测试")

if __name__ == "__main__":
    main()
