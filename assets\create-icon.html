<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黑默科技 - 图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .icon-preview {
            margin: 20px 0;
        }
        
        .icon-sizes {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .icon-size {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        
        .icon-canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        
        button {
            background: linear-gradient(135deg, #1a1a1a, #0a0a0a);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        button:hover {
            background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
        }
        
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 黑默科技图标生成器</h1>
        <p>为Windows桌面应用生成.ico格式图标</p>
        
        <div class="icon-preview">
            <canvas id="mainCanvas" width="256" height="256" class="icon-canvas"></canvas>
        </div>
        
        <div class="icon-sizes">
            <div class="icon-size">
                <canvas id="icon16" width="16" height="16" class="icon-canvas"></canvas>
                <span>16x16</span>
            </div>
            <div class="icon-size">
                <canvas id="icon32" width="32" height="32" class="icon-canvas"></canvas>
                <span>32x32</span>
            </div>
            <div class="icon-size">
                <canvas id="icon48" width="48" height="48" class="icon-canvas"></canvas>
                <span>48x48</span>
            </div>
            <div class="icon-size">
                <canvas id="icon64" width="64" height="64" class="icon-canvas"></canvas>
                <span>64x64</span>
            </div>
            <div class="icon-size">
                <canvas id="icon128" width="128" height="128" class="icon-canvas"></canvas>
                <span>128x128</span>
            </div>
        </div>
        
        <button onclick="generateIcon()">🎯 生成图标</button>
        <button onclick="downloadPNG()">📥 下载PNG</button>
        
        <div class="instructions">
            <h3>📋 使用说明：</h3>
            <ol>
                <li>点击"生成图标"按钮创建黑默科技logo</li>
                <li>点击"下载PNG"获取PNG格式文件</li>
                <li>使用在线工具将PNG转换为ICO格式：
                    <ul>
                        <li><a href="https://convertio.co/png-ico/" target="_blank">Convertio</a></li>
                        <li><a href="https://www.icoconverter.com/" target="_blank">ICO Converter</a></li>
                    </ul>
                </li>
                <li>将生成的icon.ico文件放入assets文件夹</li>
                <li>重新构建Electron应用</li>
            </ol>
        </div>
    </div>

    <script>
        function drawLogo(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 100;
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 设置背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#1a1a1a');
            gradient.addColorStop(1, '#0a0a0a');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // 设置线条样式
            ctx.strokeStyle = 'white';
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // 外层立方体框架
            ctx.lineWidth = 2.5 * scale;
            ctx.beginPath();
            ctx.moveTo(15 * scale, 25 * scale);
            ctx.lineTo(50 * scale, 10 * scale);
            ctx.lineTo(85 * scale, 25 * scale);
            ctx.lineTo(85 * scale, 75 * scale);
            ctx.lineTo(50 * scale, 90 * scale);
            ctx.lineTo(15 * scale, 75 * scale);
            ctx.closePath();
            ctx.stroke();
            
            // 内层立方体
            ctx.lineWidth = 2 * scale;
            ctx.beginPath();
            ctx.moveTo(25 * scale, 32 * scale);
            ctx.lineTo(50 * scale, 20 * scale);
            ctx.lineTo(75 * scale, 32 * scale);
            ctx.lineTo(75 * scale, 68 * scale);
            ctx.lineTo(50 * scale, 80 * scale);
            ctx.lineTo(25 * scale, 68 * scale);
            ctx.closePath();
            ctx.stroke();
            
            // 立体连接线
            ctx.lineWidth = 1.5 * scale;
            ctx.beginPath();
            // 顶部连接
            ctx.moveTo(15 * scale, 25 * scale);
            ctx.lineTo(25 * scale, 32 * scale);
            ctx.moveTo(50 * scale, 10 * scale);
            ctx.lineTo(50 * scale, 20 * scale);
            ctx.moveTo(85 * scale, 25 * scale);
            ctx.lineTo(75 * scale, 32 * scale);
            // 底部连接
            ctx.moveTo(15 * scale, 75 * scale);
            ctx.lineTo(25 * scale, 68 * scale);
            ctx.moveTo(50 * scale, 90 * scale);
            ctx.lineTo(50 * scale, 80 * scale);
            ctx.moveTo(85 * scale, 75 * scale);
            ctx.lineTo(75 * scale, 68 * scale);
            ctx.stroke();
            
            // Y形结构
            ctx.lineWidth = 2.5 * scale;
            ctx.beginPath();
            ctx.moveTo(50 * scale, 20 * scale);
            ctx.lineTo(50 * scale, 50 * scale);
            ctx.moveTo(35 * scale, 38 * scale);
            ctx.lineTo(50 * scale, 50 * scale);
            ctx.lineTo(65 * scale, 38 * scale);
            ctx.stroke();
            
            // 内部曲线装饰
            ctx.lineWidth = 1.5 * scale;
            ctx.beginPath();
            ctx.moveTo(30 * scale, 40 * scale);
            ctx.quadraticCurveTo(50 * scale, 35 * scale, 70 * scale, 40 * scale);
            ctx.moveTo(30 * scale, 60 * scale);
            ctx.quadraticCurveTo(50 * scale, 55 * scale, 70 * scale, 60 * scale);
            ctx.stroke();
            
            // 蓝色科技点
            ctx.fillStyle = '#4F46E5';
            ctx.beginPath();
            ctx.arc(50 * scale, 32 * scale, 2.5 * scale, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        function generateIcon() {
            // 生成不同尺寸的图标
            const sizes = [16, 32, 48, 64, 128, 256];
            
            sizes.forEach(size => {
                const canvasId = size === 256 ? 'mainCanvas' : `icon${size}`;
                const canvas = document.getElementById(canvasId);
                if (canvas) {
                    drawLogo(canvas, size);
                }
            });
        }
        
        function downloadPNG() {
            const canvas = document.getElementById('mainCanvas');
            const link = document.createElement('a');
            link.download = 'heimo-tech-icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 页面加载时自动生成图标
        window.onload = function() {
            generateIcon();
        };
    </script>
</body>
</html>
