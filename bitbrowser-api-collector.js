#!/usr/bin/env node

/**
 * 🌐 比特浏览器API评论采集器
 * 使用比特浏览器API创建专门的窗口进行评论采集，避免反爬检测
 */

const axios = require('axios');
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class BitBrowserApiCollector {
    constructor() {
        this.bitBrowserApi = 'http://127.0.0.1:54345';
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        // 反爬策略配置
        this.config = {
            scrollDelay: 3000,        // 滚动间隔3秒
            actionDelay: 2000,        // 操作间隔2秒
            maxScrollAttempts: 15,    // 最大滚动次数
            waitForContent: 4000,     // 等待内容加载4秒
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    // 健康检查
    async healthCheck() {
        try {
            console.log('🔍 检查比特浏览器API连接...');
            const response = await axios.post(`${this.bitBrowserApi}/health`);
            if (response.data.success) {
                console.log('✅ 比特浏览器API连接成功');
                return true;
            }
        } catch (error) {
            console.error('❌ 比特浏览器API连接失败:', error.message);
            return false;
        }
    }

    // 创建专门的浏览器窗口
    async createBrowserWindow() {
        try {
            console.log('🌐 创建专门的浏览器窗口...');
            
            const windowConfig = {
                name: `xiaohongshu-collector-${Date.now()}`,
                remark: '小红书评论采集专用窗口',
                browserFingerPrint: {
                    coreVersion: '112',
                    ostype: 'PC',
                    os: 'Win10',
                    userAgent: this.config.userAgent,
                    // 随机指纹，避免检测
                    screen: '1920,1080',
                    fonts: [],
                    canvas: {},
                    webGL: {},
                    audio: {},
                    timezone: 'Asia/Shanghai',
                    language: 'zh-CN,zh;q=0.9,en;q=0.8',
                    location: 'ask',
                    browserLanguage: 'zh-CN',
                    browserPlatform: 'Win32',
                    browserName: 'chrome',
                    flash: 'block'
                },
                proxyMethod: 2, // 不使用代理
                isHeadless: false
            };

            const response = await axios.post(`${this.bitBrowserApi}/browser/update`, windowConfig);
            
            if (response.data.success) {
                const browserId = response.data.data.id;
                console.log(`✅ 浏览器窗口创建成功，ID: ${browserId}`);
                return browserId;
            } else {
                throw new Error('创建浏览器窗口失败');
            }
        } catch (error) {
            console.error('❌ 创建浏览器窗口失败:', error.message);
            throw error;
        }
    }

    // 启动浏览器窗口
    async startBrowserWindow(browserId) {
        try {
            console.log('🚀 启动浏览器窗口...');
            
            const response = await axios.post(`${this.bitBrowserApi}/browser/start`, {
                id: browserId,
                args: [],
                loadExtensions: false,
                extractIp: false
            });

            if (response.data.success) {
                const debugPort = response.data.data.http;
                console.log(`✅ 浏览器窗口启动成功，调试端口: ${debugPort}`);
                
                // 等待浏览器完全启动
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                return debugPort;
            } else {
                throw new Error('启动浏览器窗口失败');
            }
        } catch (error) {
            console.error('❌ 启动浏览器窗口失败:', error.message);
            throw error;
        }
    }

    // 连接到浏览器
    async connectToBrowser(debugPort) {
        try {
            console.log('🔗 连接到专用浏览器...');
            
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${debugPort}`,
                defaultViewport: null
            });

            const pages = await browser.pages();
            let page = pages[0];
            
            if (!page) {
                page = await browser.newPage();
            }

            console.log('✅ 成功连接到浏览器');
            return { browser, page };
        } catch (error) {
            console.error('❌ 连接浏览器失败:', error.message);
            throw error;
        }
    }

    // 导航到小红书页面
    async navigateToXiaohongshu(page) {
        try {
            console.log('🧭 导航到小红书页面...');
            
            const targetUrl = 'https://www.xiaohongshu.com/explore/67af69ee000000002a003a15?xsec_token=ABQc01ZflzgL1xu3Q8Vdlmlo4YzSNxFhUVVpQNFDi8J5w=&xsec_source=pc_user';
            
            await page.goto(targetUrl, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            console.log('✅ 成功导航到小红书页面');
            
            // 等待页面完全加载
            await new Promise(resolve => setTimeout(resolve, this.config.waitForContent));
            
            return true;
        } catch (error) {
            console.error('❌ 导航失败:', error.message);
            throw error;
        }
    }

    // 智能滚动加载更多评论
    async intelligentScroll(page) {
        console.log('🔄 开始智能滚动加载评论...');
        
        let previousCommentCount = 0;
        let stableCount = 0;
        
        for (let i = 0; i < this.config.maxScrollAttempts; i++) {
            console.log(`   📜 滚动 ${i + 1}/${this.config.maxScrollAttempts}`);
            
            // 检查当前评论数量
            const currentCommentCount = await page.evaluate(() => {
                const commentElements = document.querySelectorAll('[class*="comment"], [class*="reply"], div');
                return commentElements.length;
            });
            
            console.log(`   💬 当前页面元素数: ${currentCommentCount}`);
            
            // 如果评论数量没有增加，增加稳定计数
            if (currentCommentCount === previousCommentCount) {
                stableCount++;
                if (stableCount >= 3) {
                    console.log('   ⏹️ 评论数量稳定，停止滚动');
                    break;
                }
            } else {
                stableCount = 0;
                previousCommentCount = currentCommentCount;
            }
            
            // 模拟人类滚动行为
            await page.evaluate(() => {
                const scrollDistance = 400 + Math.random() * 200; // 400-600px
                window.scrollBy({
                    top: scrollDistance,
                    behavior: 'smooth'
                });
            });
            
            // 随机延迟
            const delay = this.config.scrollDelay + Math.random() * 1000;
            console.log(`   ⏱️ 等待 ${Math.round(delay)}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        console.log('✅ 智能滚动完成');
    }

    // 高级评论提取
    async advancedExtractComments(page) {
        console.log('🧠 开始高级评论提取...');
        
        const result = await page.evaluate(() => {
            const data = {
                noteInfo: {
                    id: '',
                    title: '',
                    author: '',
                    totalCommentCount: 0
                },
                comments: [],
                extractTime: new Date().toISOString(),
                extractMethod: 'bitbrowser-api-v1'
            };
            
            try {
                // 提取笔记基本信息
                const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
                if (urlMatch) {
                    data.noteInfo.id = urlMatch[1];
                }
                
                // 提取标题
                const titleElement = document.querySelector('h1, .title, [class*="title"]');
                if (titleElement) {
                    data.noteInfo.title = titleElement.textContent.trim();
                }
                
                // 提取评论总数
                const pageText = document.body.textContent;
                const countMatch = pageText.match(/共\s*(\d+)\s*条评论/);
                if (countMatch) {
                    data.noteInfo.totalCommentCount = parseInt(countMatch[1]);
                }
                
                // 高级文本解析
                const commentSectionMatch = pageText.match(/共\s*\d+\s*条评论([\s\S]*?)(?:回到顶部|发现|发布|创作中心|window\.|$)/);
                
                if (commentSectionMatch) {
                    const commentText = commentSectionMatch[1];
                    const comments = window.parseAdvancedComments(commentText);
                    data.comments = comments;
                }
                
            } catch (error) {
                console.error('提取过程出错:', error);
                data.error = error.message;
            }
            
            return data;
        });
        
        return result;
    }

    // 注入高级解析函数
    async injectAdvancedParsing(page) {
        await page.evaluateOnNewDocument(() => {
            window.parseAdvancedComments = function(commentText) {
                const comments = [];
                const lines = commentText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
                
                let currentComment = null;
                let commentIndex = 0;
                
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    
                    // 跳过系统信息
                    if (this.shouldSkipLine(line)) continue;
                    
                    // 检查是否是新评论
                    if (this.isNewComment(line)) {
                        if (currentComment && currentComment.content.length > 5) {
                            this.finalizeComment(currentComment);
                            comments.push(currentComment);
                        }
                        
                        currentComment = this.createAdvancedComment(++commentIndex, line);
                    } else if (currentComment && line.length > 3) {
                        currentComment.rawContent += ' ' + line;
                    }
                }
                
                if (currentComment && currentComment.content.length > 5) {
                    this.finalizeComment(currentComment);
                    comments.push(currentComment);
                }
                
                return comments;
            };
            
            window.shouldSkipLine = function(line) {
                const skipPatterns = [
                    '加载中', '你还没有', '温馨提示', '广告屏蔽', '我知道了',
                    '沪ICP备', '营业执照', '公网安备', '增值电信', '医疗器械',
                    '创作服务', '直播管理', '专业号', '商家入驻', 'MCN入驻',
                    'window.', 'function', 'console.', 'document.'
                ];
                
                return skipPatterns.some(pattern => line.includes(pattern)) || 
                       line.length < 3 || 
                       /^[0-9\s\-:\.]+$/.test(line);
            };
            
            window.isNewComment = function(line) {
                return line.includes('作者') || 
                       line.includes('置顶评论') ||
                       line.match(/^\d{2}-\d{2}/) ||
                       line.match(/^[^\d\s]{2,15}\s*\d{2}-\d{2}/) ||
                       line.match(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/) ||
                       (line.includes('求带') && line.length < 50);
            };
            
            window.createAdvancedComment = function(index, line) {
                const comment = {
                    id: index,
                    userId: '',
                    username: '',
                    content: '',
                    time: '',
                    likes: 0,
                    replyCount: 0,
                    isAuthor: line.includes('作者'),
                    isPinned: line.includes('置顶'),
                    rawContent: line
                };
                
                // 提取时间
                const timeMatch = line.match(/(\d{2}-\d{2})/);
                if (timeMatch) {
                    comment.time = timeMatch[1];
                }
                
                // 提取用户名（改进算法）
                const userPatterns = [
                    /^([^\d\s🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]{2,15})\s*\d{2}-\d{2}/,
                    /^([^\d\s]{2,15})\s*作者/,
                    /^([^\d\s]{2,15})\s*置顶/
                ];
                
                for (const pattern of userPatterns) {
                    const match = line.match(pattern);
                    if (match) {
                        comment.username = match[1].trim();
                        break;
                    }
                }
                
                return comment;
            };
            
            window.finalizeComment = function(comment) {
                let content = comment.rawContent;
                
                // 清理用户名、时间等
                if (comment.username) {
                    content = content.replace(comment.username, '');
                }
                content = content.replace(/\d{2}-\d{2}/g, '');
                content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');
                content = content.replace(/作者|置顶评论|回复|赞|仅自己可见/g, '');
                content = content.replace(/\s+/g, ' ').trim();
                
                // 提取数字信息
                const numbers = comment.rawContent.match(/\d+/g) || [];
                numbers.forEach(num => {
                    const n = parseInt(num);
                    if (n > 0 && n < 10000) {
                        if (comment.rawContent.includes(num + '回复')) {
                            comment.replyCount = n;
                        } else if (!comment.likes && n < 1000) {
                            comment.likes = n;
                        }
                    }
                });
                
                comment.content = content;
                delete comment.rawContent;
            };
        });
    }

    // 关闭浏览器窗口
    async closeBrowserWindow(browserId) {
        try {
            console.log('🔒 关闭浏览器窗口...');
            await axios.post(`${this.bitBrowserApi}/browser/close`, { id: browserId });
            console.log('✅ 浏览器窗口已关闭');
        } catch (error) {
            console.error('❌ 关闭浏览器窗口失败:', error.message);
        }
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        let browserId = null;
        
        try {
            console.log('🌐 启动比特浏览器API评论采集器...');
            
            // 健康检查
            const isHealthy = await this.healthCheck();
            if (!isHealthy) {
                throw new Error('比特浏览器API不可用');
            }
            
            // 创建专用浏览器窗口
            browserId = await this.createBrowserWindow();
            
            // 启动浏览器窗口
            const debugPort = await this.startBrowserWindow(browserId);
            
            // 连接到浏览器
            const { browser, page } = await this.connectToBrowser(debugPort);
            
            // 注入高级解析函数
            await this.injectAdvancedParsing(page);
            
            // 导航到小红书页面
            await this.navigateToXiaohongshu(page);
            
            // 智能滚动加载
            await this.intelligentScroll(page);
            
            // 高级评论提取
            const result = await this.advancedExtractComments(page);
            
            // 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `api_comments_${result.noteInfo.id}_${timestamp}.json`;
            this.saveToFile(result, filename);
            
            // 输出结果
            console.log('\n🎉 API采集完成！');
            console.log('📊 采集统计:');
            console.log(`   🆔 笔记ID: ${result.noteInfo.id}`);
            console.log(`   📝 笔记标题: ${result.noteInfo.title}`);
            console.log(`   🎯 目标评论数: ${result.noteInfo.totalCommentCount}`);
            console.log(`   💬 实际采集数: ${result.comments.length}`);
            console.log(`   📁 保存文件: ${filename}`);
            
            if (result.comments.length > 0) {
                console.log('\n👥 评论预览:');
                result.comments.slice(0, 3).forEach((comment, index) => {
                    console.log(`   ${index + 1}. 用户: ${comment.username || '未知'}`);
                    console.log(`      时间: ${comment.time || '未知'}`);
                    console.log(`      内容: ${comment.content.substring(0, 40)}...`);
                    console.log(`      点赞: ${comment.likes} | 回复: ${comment.replyCount}`);
                    console.log('');
                });
            }
            
            await browser.disconnect();
            
        } catch (error) {
            console.error('❌ 采集失败:', error.message);
        } finally {
            // 确保关闭浏览器窗口
            if (browserId) {
                await this.closeBrowserWindow(browserId);
            }
        }
    }
}

// 运行采集器
if (require.main === module) {
    const collector = new BitBrowserApiCollector();
    collector.run();
}

module.exports = BitBrowserApiCollector;
