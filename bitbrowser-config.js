/**
 * 🔧 比特浏览器统一配置文件
 * 所有组件都应该从这里导入配置，确保一致性
 */

// 🌐 比特浏览器API配置
const BITBROWSER_CONFIG = {
    // 🔗 API连接信息
    api_url: "http://127.0.0.1:56906",              // API地址 - 实际运行端口
    api_token: "ca28ee5ca6de4d209182a83aa16a2044",  // API访问令牌
    
    // 🆔 浏览器实例配置
    browser_id: "e3afefd184384c3f90c78b6b19309ca0",  // ace1浏览器实例ID
    browser_name: "95362955272 ace1",               // 浏览器显示名称
    
    // 📋 API端点配置
    endpoints: {
        browser_list: "/browser/list",               // 获取浏览器列表
        browser_open: "/browser/open",               // 启动浏览器实例
        browser_close: "/browser/close",             // 停止浏览器实例
        browser_delete: "/browser/delete",           // 删除浏览器实例
        browser_update: "/browser/update"            // 更新浏览器配置
    },
    
    // 🔧 请求配置
    request_config: {
        timeout: 30000,                              // 请求超时时间(毫秒)
        headers: {
            'Content-Type': 'application/json',      // 请求头 - JSON格式
            'x-api-key': "ca28ee5ca6de4d209182a83aa16a2044"  // API密钥头
        }
    },
    
    // 🎯 调试端口范围
    debug_ports: [
        9222, 9223, 9224, 9225, 9226,              // Chrome默认端口
        63524, 63525, 63526, 63527, 63528,         // 比特浏览器常用端口
        51859, 51860, 51861, 51862, 51863,         // 动态分配端口
        58222, 58223, 58224, 58225, 58226          // 备用端口
    ]
};

// 🌐 本地服务器配置
const LOCAL_SERVER_CONFIG = {
    url: "http://localhost:3000",                    // 桌面应用服务器地址
    api_prefix: "/api/xiaohongshu",                  // API路径前缀
    timeout: 15000                                   // 请求超时时间
};

// 🎯 小红书页面配置
const XIAOHONGSHU_CONFIG = {
    // 🔗 支持的URL模式
    url_patterns: [
        'xiaohongshu.com',
        'creator.xiaohongshu.com',
        'www.xiaohongshu.com'
    ],
    
    // 📝 页面标题关键词
    title_keywords: [
        '小红书',
        'xiaohongshu',
        '创作中心',
        '个人主页'
    ],
    
    // ⏱️ 页面加载等待时间
    wait_times: {
        page_load: 3000,                             // 页面加载等待时间
        element_wait: 1000,                          // 元素等待时间
        click_delay: 2000,                           // 点击间隔时间
        scroll_delay: 1500                           // 滚动间隔时间
    }
};

// 📊 数据采集配置
const COLLECTION_CONFIG = {
    // 🔢 采集限制
    limits: {
        max_notes: 50,                               // 最大笔记数量
        max_comments: 100,                           // 最大评论数量
        max_retries: 3                               // 最大重试次数
    },
    
    // 📁 输出配置
    output: {
        format: 'json',                              // 输出格式
        include_timestamp: true,                     // 包含时间戳
        save_raw_data: true                          // 保存原始数据
    }
};

// 🔧 工具函数
const ConfigUtils = {
    // 获取完整的API URL
    getApiUrl(endpoint) {
        return `${BITBROWSER_CONFIG.api_url}${endpoint}`;
    },
    
    // 获取请求配置
    getRequestConfig(customHeaders = {}) {
        return {
            ...BITBROWSER_CONFIG.request_config,
            headers: {
                ...BITBROWSER_CONFIG.request_config.headers,
                ...customHeaders
            }
        };
    },
    
    // 检查是否为小红书页面
    isXiaohongshuPage(url, title = '') {
        const urlMatch = XIAOHONGSHU_CONFIG.url_patterns.some(pattern => 
            url && url.includes(pattern)
        );
        const titleMatch = XIAOHONGSHU_CONFIG.title_keywords.some(keyword => 
            title && title.includes(keyword)
        );
        return urlMatch || titleMatch;
    },
    
    // 获取桌面应用API URL
    getDesktopApiUrl(endpoint) {
        return `${LOCAL_SERVER_CONFIG.url}${LOCAL_SERVER_CONFIG.api_prefix}${endpoint}`;
    }
};

// 导出配置
module.exports = {
    BITBROWSER_CONFIG,
    LOCAL_SERVER_CONFIG,
    XIAOHONGSHU_CONFIG,
    COLLECTION_CONFIG,
    ConfigUtils
};

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.BitBrowserConfig = {
        BITBROWSER_CONFIG,
        LOCAL_SERVER_CONFIG,
        XIAOHONGSHU_CONFIG,
        COLLECTION_CONFIG,
        ConfigUtils
    };
}
