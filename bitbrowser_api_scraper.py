#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 比特浏览器API调试端口获取和测试脚本
使用比特浏览器API获取调试端口，然后测试连接
"""

import time
import json
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

class BitBrowserApiTester:
    def __init__(self):
        # 从截图中看到的API配置
        self.api_base = "http://127.0.0.1:56906"
        self.api_token = "ca28eeSca6de4d209182a83ae16a2044"  # 从截图中获取
        
        self.headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': self.api_token
        }
        
    def test_api_connection(self):
        """测试API连接"""
        print("🔍 测试比特浏览器API连接...")
        print(f"   API地址: {self.api_base}")
        print(f"   API Token: {self.api_token[:20]}...")
        
        try:
            # 测试基本连接
            response = requests.get(f"{self.api_base}/", timeout=5)
            print(f"   基本连接状态码: {response.status_code}")
            
            return True
        except requests.exceptions.ConnectionError:
            print("   ❌ 无法连接到API服务")
            return False
        except Exception as e:
            print(f"   ❌ API连接异常: {e}")
            return False
    
    def get_debug_ports(self):
        """获取所有调试端口"""
        print("🔍 通过API获取调试端口...")
        
        url = f"{self.api_base}/browser/ports"
        
        try:
            # 尝试不同的请求方式
            methods = [
                # 方法1: POST with headers
                lambda: requests.post(url, json={}, headers=self.headers, timeout=10),
                # 方法2: POST without headers
                lambda: requests.post(url, json={}, timeout=10),
                # 方法3: GET with headers
                lambda: requests.get(url, headers=self.headers, timeout=10),
                # 方法4: GET without headers
                lambda: requests.get(url, timeout=10)
            ]
            
            for i, method in enumerate(methods, 1):
                print(f"   尝试方法 {i}...")
                try:
                    response = method()
                    print(f"   状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                        
                        if data.get('success') and data.get('data'):
                            ports_data = data['data']
                            ports = list(ports_data.values())
                            print(f"   ✅ 获取到 {len(ports)} 个调试端口:")
                            for browser_id, port in ports_data.items():
                                print(f"      🌐 浏览器 {browser_id[:8]}... -> 端口 {port}")
                            return [int(port) for port in ports]
                    else:
                        print(f"   响应内容: {response.text[:200]}")
                        
                except Exception as e:
                    print(f"   方法 {i} 失败: {e}")
                    continue
            
            print("   ❌ 所有方法都失败了")
            return []
            
        except Exception as e:
            print(f"❌ 获取端口异常: {e}")
            return []
    
    def test_debug_port(self, port):
        """测试调试端口连接"""
        print(f"🔍 测试调试端口 {port}...")
        
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            # 尝试连接
            driver = webdriver.Chrome(options=chrome_options)
            
            # 获取基本信息
            current_url = driver.current_url
            title = driver.title
            
            print(f"   ✅ 端口 {port} 连接成功!")
            print(f"   📄 当前页面: {current_url}")
            print(f"   📝 页面标题: {title}")
            
            # 检查是否是小红书页面
            if 'xiaohongshu.com' in current_url:
                print("   🎉 检测到小红书页面!")
                
                # 简单测试页面操作
                try:
                    page_text = driver.find_element("tag name", "body").text
                    comment_count = len([line for line in page_text.split('\n') if '条评论' in line])
                    print(f"   💬 页面中包含评论相关文本: {comment_count} 处")
                except Exception as e:
                    print(f"   ⚠️ 页面操作测试失败: {e}")
            else:
                print("   💡 不是小红书页面，请手动导航到目标页面")
            
            driver.quit()
            return True
            
        except Exception as e:
            print(f"   ❌ 端口 {port} 连接失败: {e}")
            return False
    
    def get_browser_list(self):
        """获取浏览器窗口列表"""
        print("🔍 获取浏览器窗口列表...")
        
        url = f"{self.api_base}/browser/list"
        payload = {
            "page": 0,
            "pageSize": 10
        }
        
        try:
            response = requests.post(url, json=payload, headers=self.headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                return data
            else:
                print(f"   失败响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ 获取窗口列表失败: {e}")
            return None
    
    def run_test(self):
        """运行完整测试"""
        print("🎯 比特浏览器API调试端口测试器")
        print("=" * 50)
        
        # 1. 测试API连接
        if not self.test_api_connection():
            print("❌ API连接失败，请检查:")
            print("   1. 比特浏览器是否正在运行")
            print("   2. API端口是否正确 (当前: 56906)")
            print("   3. API Token是否正确")
            return
        
        # 2. 获取浏览器列表
        browser_list = self.get_browser_list()
        
        # 3. 获取调试端口
        ports = self.get_debug_ports()
        
        if not ports:
            print("❌ 未获取到调试端口，尝试常见端口...")
            # 尝试常见端口
            common_ports = [9222, 9223, 9224, 9225, 64170, 64217, 55276, 56906, 56907]
            ports = common_ports
        
        # 4. 测试每个端口
        successful_ports = []
        for port in ports:
            if self.test_debug_port(port):
                successful_ports.append(port)
        
        # 5. 输出结果
        print("\n" + "=" * 50)
        print("🎉 测试完成!")
        print(f"📊 总共测试端口: {len(ports)}")
        print(f"✅ 成功连接端口: {len(successful_ports)}")
        
        if successful_ports:
            print("🎯 可用的调试端口:")
            for port in successful_ports:
                print(f"   ✅ {port}")
            
            print(f"\n💡 建议使用端口: {successful_ports[0]}")
            print("🚀 现在可以使用这个端口运行爬虫脚本!")
        else:
            print("❌ 没有找到可用的调试端口")
            print("💡 建议:")
            print("   1. 确保比特浏览器窗口已打开")
            print("   2. 确保窗口启用了调试模式")
            print("   3. 尝试重启比特浏览器")

if __name__ == "__main__":
    tester = BitBrowserApiTester()
    tester.run_test()
