#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 比特浏览器19号窗口小红书评论采集器
连接比特浏览器，打开19号窗口，进行评论采集
"""

import json
import time
import random
import re
import requests
from datetime import datetime
from typing import List, Dict, Any


class BitBrowserTab19Collector:
    """比特浏览器19号窗口评论采集器"""
    
    def __init__(self):
        self.api_url = "http://127.0.0.1:56906"
        self.api_token = "your_api_token"  # 需要配置您的API Token
        self.browser_id = None
        self.selenium_port = None
        self.comments_data = {
            'note_url': '',
            'note_title': '',
            'timestamp': '',
            'comments': [],
            'summary': {
                'total_comments': 0,
                'total_replies': 0,
                'total_likes': 0,
                'strategy': 'bitbrowser-tab19'
            }
        }
    
    def check_bitbrowser_status(self):
        """检查比特浏览器状态"""
        print("🔍 检查比特浏览器状态...")
        
        try:
            response = requests.get(f"{self.api_url}/browser/list", 
                                  headers={"token": self.api_token}, 
                                  timeout=5)
            
            if response.status_code == 200:
                browsers = response.json().get('data', [])
                print(f"✅ 比特浏览器连接成功，找到 {len(browsers)} 个浏览器配置")
                return browsers
            else:
                print(f"❌ API请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 连接比特浏览器失败: {str(e)}")
            return []
    
    def find_or_create_browser19(self, browsers):
        """查找或创建19号浏览器"""
        print("🔍 查找19号浏览器配置...")
        
        # 查找名称包含"19"的浏览器
        for browser in browsers:
            if "19" in browser.get('name', '') or browser.get('id') == '19':
                self.browser_id = browser['id']
                print(f"✅ 找到19号浏览器: {browser['name']}")
                return True
        
        # 如果没找到，使用第一个可用的浏览器
        if browsers:
            self.browser_id = browsers[0]['id']
            print(f"✅ 使用浏览器: {browsers[0]['name']} (ID: {self.browser_id})")
            return True
        
        print("❌ 没有找到可用的浏览器配置")
        return False
    
    def start_browser(self):
        """启动浏览器"""
        print(f"🚀 启动浏览器 {self.browser_id}...")
        
        try:
            response = requests.post(f"{self.api_url}/browser/start",
                                   json={"id": self.browser_id},
                                   headers={"token": self.api_token},
                                   timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.selenium_port = result['data']['selenium_port']
                    print(f"✅ 浏览器启动成功，Selenium端口: {self.selenium_port}")
                    return True
                else:
                    print(f"❌ 浏览器启动失败: {result.get('msg', '未知错误')}")
                    return False
            else:
                print(f"❌ 启动请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 启动浏览器出错: {str(e)}")
            return False
    
    def connect_selenium(self):
        """连接Selenium"""
        print(f"🔌 连接Selenium (端口: {self.selenium_port})...")
        
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{self.selenium_port}")
            
            # 创建WebDriver
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            
            print("✅ Selenium连接成功")
            return True
            
        except Exception as e:
            print(f"❌ Selenium连接失败: {str(e)}")
            return False
    
    def navigate_to_xiaohongshu(self, url: str = None):
        """导航到小红书页面"""
        if not url:
            url = "https://www.xiaohongshu.com/explore"
        
        print(f"🌐 导航到小红书: {url[:50]}...")
        
        try:
            self.driver.get(url)
            time.sleep(3)
            
            # 获取页面信息
            self.comments_data['note_url'] = self.driver.current_url
            self.comments_data['note_title'] = self.driver.title.replace(' - 小红书', '').strip()
            self.comments_data['timestamp'] = datetime.now().isoformat()
            
            print(f"📝 页面标题: {self.comments_data['note_title']}")
            print("✅ 页面加载完成")
            return True
            
        except Exception as e:
            print(f"❌ 页面导航失败: {str(e)}")
            return False
    
    def wait_for_comments_load(self):
        """等待评论区域加载"""
        print("⏳ 等待评论区域加载...")
        
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support import expected_conditions as EC
            
            # 等待头像元素出现
            self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'img[src*="avatar"]')))
            print("✅ 评论区域已加载")
            return True
        except:
            print("⚠️ 评论区域加载超时，继续尝试...")
            return True
    
    def count_elements(self):
        """统计页面元素"""
        try:
            from selenium.webdriver.common.by import By
            
            avatars = self.driver.find_elements(By.CSS_SELECTOR, 'img[src*="avatar"]')
            comment_divs = self.driver.find_elements(By.CSS_SELECTOR, '[class*="comment"]')
            
            print(f"📊 当前统计: 👤{len(avatars)}个头像, 💬{len(comment_divs)}个评论div")
            return len(avatars), len(comment_divs)
        except:
            return 0, 0
    
    def scroll_and_expand_comments(self, max_rounds: int = 25):
        """滚动并展开评论"""
        print(f"🔄 开始滚动并展开评论 (最多{max_rounds}轮)...")
        
        total_clicked = 0
        no_change_count = 0
        
        for round_num in range(1, max_rounds + 1):
            print(f"\n🔄 第 {round_num} 轮:")
            
            # 记录当前状态
            before_avatars, before_divs = self.count_elements()
            
            # 滚动到底部
            try:
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(1.5)
            except:
                print("   ⚠️ 滚动失败")
            
            # 点击"更多"按钮
            clicked_in_round = self._click_more_buttons()
            total_clicked += clicked_in_round
            
            # 等待内容加载
            wait_time = 2 + random.uniform(0.5, 1.5)
            time.sleep(wait_time)
            
            # 检查变化
            after_avatars, after_divs = self.count_elements()
            avatars_added = after_avatars - before_avatars
            divs_added = after_divs - before_divs
            
            print(f"   📈 变化: 头像+{avatars_added}, 评论div+{divs_added}, 点击{clicked_in_round}个按钮")
            
            # 判断是否继续
            if avatars_added == 0 and divs_added == 0 and clicked_in_round == 0:
                no_change_count += 1
                print(f"   ⚠️ 无变化轮次: {no_change_count}/3")
                
                if no_change_count >= 3:
                    print("🏁 连续3轮无变化，停止滚动")
                    break
            else:
                no_change_count = 0
        
        print(f"\n🎉 滚动完成! 总共点击了 {total_clicked} 个按钮")
        return total_clicked
    
    def _click_more_buttons(self):
        """点击"更多"按钮"""
        clicked_count = 0
        
        try:
            from selenium.webdriver.common.by import By
            
            # 定义要查找的按钮文本
            button_texts = ['更多', '展开', '查看']
            
            for button_text in button_texts:
                try:
                    # 使用XPath查找包含特定文本的元素
                    xpath = f"//*[contains(text(), '{button_text}')]"
                    elements = self.driver.find_elements(By.XPATH, xpath)
                    
                    # 点击前3个按钮
                    for element in elements[:3]:
                        try:
                            if element.is_displayed() and element.is_enabled():
                                # 滚动到元素可见
                                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                                time.sleep(0.3)
                                
                                # 点击元素
                                element.click()
                                clicked_count += 1
                                
                                print(f"     🖱️ 点击: {element.text[:20]}...")
                                time.sleep(random.uniform(0.5, 1.0))
                                
                        except:
                            continue
                            
                except:
                    continue
            
            # 特别处理"条回复"按钮
            try:
                reply_xpath = "//*[contains(text(), '条回复')]"
                reply_elements = self.driver.find_elements(By.XPATH, reply_xpath)
                
                for element in reply_elements[:2]:
                    try:
                        if element.is_displayed() and element.is_enabled():
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                            time.sleep(0.3)
                            element.click()
                            clicked_count += 1
                            print(f"     🖱️ 点击回复: {element.text[:20]}...")
                            time.sleep(random.uniform(0.5, 1.0))
                    except:
                        continue
            except:
                pass
                
        except Exception as e:
            print(f"     ❌ 点击按钮时出错: {str(e)[:30]}...")
        
        return clicked_count
    
    def extract_comments(self):
        """提取评论"""
        print("🔍 开始提取评论...")
        
        comments = []
        
        try:
            from selenium.webdriver.common.by import By
            
            # 查找所有头像元素
            avatar_elements = self.driver.find_elements(By.CSS_SELECTOR, 'img[src*="avatar"]')
            print(f"   找到 {len(avatar_elements)} 个头像元素")
            
            for index, avatar in enumerate(avatar_elements):
                try:
                    comment_data = self._extract_single_comment(avatar, index + 1)
                    if comment_data and comment_data.get('content'):
                        comments.append(comment_data)
                except:
                    continue
            
            # 去重
            unique_comments = self._remove_duplicates(comments)
            
            print(f"🎉 提取完成! 原始: {len(comments)}条, 去重后: {len(unique_comments)}条")
            return unique_comments
            
        except Exception as e:
            print(f"❌ 提取评论失败: {str(e)}")
            return []
    
    def _extract_single_comment(self, avatar_element, comment_id: int):
        """提取单条评论"""
        try:
            from selenium.webdriver.common.by import By
            
            # 找到评论容器
            comment_container = avatar_element
            for _ in range(5):
                comment_container = comment_container.find_element(By.XPATH, '..')
            
            comment_data = {
                'id': comment_id,
                'username': '',
                'avatar': '',
                'content': '',
                'publish_time': '',
                'likes': 0,
                'is_reply': False
            }
            
            # 提取头像
            comment_data['avatar'] = avatar_element.get_attribute('src') or ''
            
            # 获取容器内的所有文本
            container_text = comment_container.text
            
            # 提取用户名
            text_elements = comment_container.find_elements(By.XPATH, ".//*[string-length(text()) > 1 and string-length(text()) < 30]")
            for elem in text_elements:
                text = elem.text.strip()
                if text and not any(keyword in text for keyword in ['天前', '小时前', '分钟前', '点赞', '回复']):
                    comment_data['username'] = text
                    break
            
            # 提取评论内容
            content_elements = comment_container.find_elements(By.XPATH, ".//*[string-length(text()) > 10 and string-length(text()) < 1000]")
            for elem in content_elements:
                text = elem.text.strip()
                if text and not any(keyword in text for keyword in ['天前', '小时前', '点赞', '回复', '更多', '展开']):
                    if len(text) > len(comment_data['content']):
                        comment_data['content'] = text
            
            # 提取发布时间
            time_pattern = r'\d+天前|\d+小时前|\d+分钟前|昨天|前天|\d{2}-\d{2}'
            time_match = re.search(time_pattern, container_text)
            if time_match:
                comment_data['publish_time'] = time_match.group()
            
            # 提取点赞数
            like_pattern = r'(\d+)(?=\s*赞|\s*❤️)'
            like_match = re.search(like_pattern, container_text)
            if like_match:
                comment_data['likes'] = int(like_match.group(1))
            
            # 判断是否为回复
            if '回复' in container_text:
                comment_data['is_reply'] = True
            
            return comment_data
            
        except:
            return None
    
    def _remove_duplicates(self, comments):
        """去除重复评论"""
        seen_contents = set()
        unique_comments = []
        
        for comment in comments:
            content_key = comment.get('content', '')[:50]
            if content_key and content_key not in seen_contents and len(comment.get('content', '')) > 5:
                seen_contents.add(content_key)
                unique_comments.append(comment)
        
        return unique_comments
    
    def collect_comments(self, xiaohongshu_url: str = None):
        """主要采集方法"""
        print("🎯 启动比特浏览器19号窗口评论采集器...\n")
        
        try:
            # 1. 检查比特浏览器状态
            browsers = self.check_bitbrowser_status()
            if not browsers:
                print("❌ 无法连接到比特浏览器，请确保比特浏览器已启动")
                return None
            
            # 2. 查找或创建19号浏览器
            if not self.find_or_create_browser19(browsers):
                return None
            
            # 3. 启动浏览器
            if not self.start_browser():
                return None
            
            # 4. 连接Selenium
            if not self.connect_selenium():
                return None
            
            # 5. 导航到小红书页面
            if not self.navigate_to_xiaohongshu(xiaohongshu_url):
                return None
            
            # 6. 等待评论加载
            self.wait_for_comments_load()
            
            # 7. 滚动并展开评论
            total_clicked = self.scroll_and_expand_comments(max_rounds=30)
            
            # 8. 提取所有评论
            comments = self.extract_comments()
            self.comments_data['comments'] = comments
            
            # 9. 计算统计信息
            self.comments_data['summary']['total_comments'] = len([c for c in comments if not c.get('is_reply', False)])
            self.comments_data['summary']['total_replies'] = len([c for c in comments if c.get('is_reply', False)])
            self.comments_data['summary']['total_likes'] = sum(c.get('likes', 0) for c in comments)
            
            print(f"\n🎉 采集完成!")
            print(f"📊 总评论: {len(comments)}条")
            print(f"📝 主评论: {self.comments_data['summary']['total_comments']}条")
            print(f"↩️ 回复: {self.comments_data['summary']['total_replies']}条")
            print(f"👍 总点赞: {self.comments_data['summary']['total_likes']}")
            print(f"📈 采集进度: {round(len(comments)/1472*100, 1)}% (目标1472条)")
            
            return self.comments_data
            
        except Exception as e:
            print(f"❌ 采集失败: {str(e)}")
            return None
        finally:
            # 保持浏览器运行，不关闭
            pass
    
    def save_results(self, filename_prefix: str = "bitbrowser_tab19"):
        """保存采集结果"""
        if not self.comments_data.get('comments'):
            print("⚠️ 没有评论数据可保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON文件
        json_filename = f"{filename_prefix}_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.comments_data, f, ensure_ascii=False, indent=2)
        
        # 保存可读报告
        txt_filename = f"{filename_prefix}_{timestamp}.txt"
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(self._generate_report())
        
        print(f"✅ 结果已保存:")
        print(f"   📁 JSON文件: {json_filename}")
        print(f"   📄 文本报告: {txt_filename}")
        
        return json_filename, txt_filename
    
    def _generate_report(self):
        """生成可读报告"""
        lines = []
        
        lines.append("🎯 比特浏览器19号窗口评论采集报告")
        lines.append("=" * 60)
        lines.append(f"📅 采集时间: {self.comments_data['timestamp']}")
        lines.append(f"📝 笔记标题: {self.comments_data['note_title']}")
        lines.append(f"🔗 笔记链接: {self.comments_data['note_url']}")
        lines.append(f"🔧 采集策略: {self.comments_data['summary']['strategy']}")
        lines.append("")
        
        lines.append("📊 采集统计:")
        lines.append(f"💬 总评论数: {len(self.comments_data['comments'])}")
        lines.append(f"📝 主评论数: {self.comments_data['summary']['total_comments']}")
        lines.append(f"↩️ 回复数: {self.comments_data['summary']['total_replies']}")
        lines.append(f"👍 总点赞数: {self.comments_data['summary']['total_likes']}")
        lines.append(f"📈 采集进度: {round(len(self.comments_data['comments'])/1472*100, 1)}% (目标1472条)")
        lines.append("")
        
        lines.append("💬 评论详情:")
        lines.append("-" * 60)
        
        for i, comment in enumerate(self.comments_data['comments'], 1):
            lines.append(f"\n💬 评论 {i}:")
            lines.append(f"👤 用户: {comment.get('username', '匿名')}")
            lines.append(f"📄 内容: {comment.get('content', '无内容')}")
            lines.append(f"👍 点赞: {comment.get('likes', 0)}")
            lines.append(f"📅 时间: {comment.get('publish_time', '未知')}")
            lines.append(f"🔗 头像: {comment.get('avatar', '无')}")
            lines.append(f"↩️ 回复: {'是' if comment.get('is_reply', False) else '否'}")
        
        lines.append("\n" + "=" * 60)
        lines.append("📅 报告生成时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        return "\n".join(lines)


def main():
    """主函数"""
    print("🎯 比特浏览器19号窗口小红书评论采集器")
    print("=" * 60)
    print("📋 功能说明:")
    print("   1. 连接比特浏览器API")
    print("   2. 启动19号浏览器窗口")
    print("   3. 导航到小红书页面")
    print("   4. 智能滚动和展开评论")
    print("   5. 提取完整的评论数据")
    print("   6. 生成详细的采集报告")
    print()
    
    # 可以指定小红书笔记URL
    xiaohongshu_url = input("请输入小红书笔记URL (直接回车使用默认): ").strip()
    if not xiaohongshu_url:
        xiaohongshu_url = None
    
    collector = BitBrowserTab19Collector()
    
    try:
        # 采集评论
        results = collector.collect_comments(xiaohongshu_url)
        
        if results:
            # 保存结果
            collector.save_results()
            print("\n🎉 采集任务完成!")
        else:
            print("\n❌ 采集失败!")
            
    except Exception as e:
        print(f"❌ 程序执行失败: {str(e)}")


if __name__ == "__main__":
    main()
