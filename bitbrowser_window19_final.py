#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 比特浏览器19号窗口最终解决方案
直接使用比特浏览器API打开19号窗口，获取调试端口，用Selenium爬取所有评论
"""

import time
import json
import re
import os
import requests
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

class BitBrowserWindow19Final:
    def __init__(self):
        # 比特浏览器API配置
        self.api_url = "http://127.0.0.1:56906"
        self.api_token = "ca28ee5ca6c4e4d209182a83aa16a2044"
        
        self.output_dir = './scraped_data'
        self.ensure_output_dir()
        
        self.config = {
            'target_comments': 1472,
            'max_scroll_attempts': 1000,
            'scroll_delay': 0.6,
            'click_delay': 0.4
        }
        
        self.driver = None
        self.browser_id = None
        self.debug_port = None
        
    def ensure_output_dir(self):
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def get_api_headers(self):
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_token}',
            'X-API-KEY': self.api_token
        }
    
    def find_window19(self):
        """查找19号窗口"""
        print("🔍 查找19号窗口...")
        
        try:
            # 获取浏览器列表
            response = requests.post(
                f"{self.api_url}/browser/list",
                json={"page": 0, "pageSize": 100},
                headers=self.get_api_headers()
            )
            
            if response.status_code != 200:
                print(f"❌ API请求失败: {response.status_code}")
                return None
            
            data = response.json()
            if not data.get('success'):
                print(f"❌ API返回失败: {data.get('msg', '未知错误')}")
                return None
            
            browsers = data.get('data', [])
            print(f"📄 找到 {len(browsers)} 个浏览器窗口")
            
            # 查找19号窗口
            for browser in browsers:
                seq = browser.get('seq', 0)
                name = browser.get('name', '')
                browser_id = browser.get('id', '')
                
                print(f"   窗口 {seq}: {name} (ID: {browser_id[:8]}...)")
                
                # 查找序号为19的窗口
                if seq == 19:
                    print(f"✅ 找到19号窗口: {name}")
                    return browser
            
            # 如果没找到19号，查找名称包含19的
            for browser in browsers:
                name = browser.get('name', '')
                if '19' in name:
                    print(f"✅ 找到包含19的窗口: {name}")
                    return browser
            
            # 如果都没找到，使用第一个窗口
            if browsers:
                browser = browsers[0]
                print(f"💡 使用第一个窗口: {browser.get('name', '未知')}")
                return browser
            
            print("❌ 未找到任何窗口")
            return None
            
        except Exception as e:
            print(f"❌ 查找窗口失败: {str(e)}")
            return None
    
    def open_window19(self, browser):
        """打开19号窗口"""
        print("🚀 打开19号窗口...")
        
        try:
            browser_id = browser.get('id')
            if not browser_id:
                print("❌ 窗口ID为空")
                return None
            
            # 打开窗口
            response = requests.post(
                f"{self.api_url}/browser/open",
                json={
                    "id": browser_id,
                    "args": ["--remote-debugging-address=0.0.0.0"],
                    "queue": True
                },
                headers=self.get_api_headers()
            )
            
            if response.status_code != 200:
                print(f"❌ 打开窗口失败: {response.status_code}")
                return None
            
            data = response.json()
            if not data.get('success'):
                print(f"❌ 打开窗口失败: {data.get('msg', '未知错误')}")
                return None
            
            window_data = data.get('data', {})
            debug_port = window_data.get('http', '').split(':')[-1]
            
            print(f"✅ 窗口打开成功")
            print(f"   调试端口: {debug_port}")
            print(f"   进程ID: {window_data.get('pid', '未知')}")
            
            self.browser_id = browser_id
            self.debug_port = debug_port
            
            return window_data
            
        except Exception as e:
            print(f"❌ 打开窗口失败: {str(e)}")
            return None
    
    def connect_selenium(self):
        """连接Selenium到调试端口"""
        print(f"🔗 连接Selenium到调试端口 {self.debug_port}...")
        
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{self.debug_port}")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            print("✅ Selenium连接成功")
            
            # 检查当前页面
            current_url = self.driver.current_url
            print(f"📄 当前页面: {current_url}")
            
            if 'xiaohongshu.com' not in current_url:
                print("💡 请手动导航到小红书评论页面...")
                print("完成后按回车继续...")
                input()
            
            return True
            
        except Exception as e:
            print(f"❌ Selenium连接失败: {str(e)}")
            return False
    
    def scrape_all_comments(self):
        """爬取所有评论"""
        print("🚀 开始爬取所有评论...")
        print(f"🎯 目标: {self.config['target_comments']} 条评论")
        
        current_count = 0
        previous_count = 0
        stable_count = 0
        total_scrolls = 0
        total_clicks = 0
        
        # 滚动到评论区域
        self.scroll_to_comments()
        
        for i in range(self.config['max_scroll_attempts']):
            print(f"\n📜 滚动 {i + 1}/{self.config['max_scroll_attempts']}")
            
            # 统计评论数量
            current_count = self.count_comments()
            print(f"   💬 当前评论数: {current_count}")
            
            # 检查是否达到目标
            if current_count >= self.config['target_comments'] * 0.95:
                print(f"🎉 接近目标！{current_count}/{self.config['target_comments']}")
                break
            
            # 检查进度
            if current_count == previous_count:
                stable_count += 1
                print(f"   ⏸️ 稳定 {stable_count} 次")
            else:
                new_comments = current_count - previous_count
                print(f"   📈 新增: {new_comments} 条")
                stable_count = 0
                previous_count = current_count
            
            # 点击加载更多
            if stable_count >= 3:
                print("   🔄 点击加载更多...")
                if self.click_load_more():
                    total_clicks += 1
                    stable_count = 0
                    print(f"   ✅ 点击成功，总计: {total_clicks} 次")
            
            # 执行滚动
            self.perform_scroll(i)
            
            # 如果长时间稳定，停止
            if stable_count >= 20:
                print("   ⏹️ 长时间无新内容，停止")
                break
            
            time.sleep(self.config['scroll_delay'])
            total_scrolls += 1
            
            # 每100次输出进度
            if i % 100 == 0 and i > 0:
                progress = round((current_count / self.config['target_comments']) * 100)
                print(f"\n📊 进度: {progress}% ({current_count}/{self.config['target_comments']})")
                print(f"   📜 滚动: {total_scrolls} 次")
                print(f"   🔄 点击: {total_clicks} 次")
        
        print(f"\n✅ 滚动完成！最终评论数: {current_count}")
        return current_count
    
    def scroll_to_comments(self):
        """滚动到评论区域"""
        try:
            comment_selectors = [
                "//span[contains(text(), '条评论')]",
                "//div[contains(text(), '评论')]",
                "//*[contains(@class, 'comment')]"
            ]
            
            for selector in comment_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    print("✅ 定位到评论区域")
                    time.sleep(2)
                    return
                except:
                    continue
            
            # 如果没找到，滚动到页面中部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.6);")
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ 滚动到评论区域失败: {str(e)}")
    
    def perform_scroll(self, index):
        """执行滚动"""
        strategies = [
            lambda: self.driver.execute_script("window.scrollBy(0, 300);"),
            lambda: self.driver.execute_script("window.scrollBy(0, 500);"),
            lambda: self.driver.execute_script("window.scrollBy(0, 800);"),
            lambda: self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);"),
            lambda: self.driver.execute_script("""
                const comments = document.querySelectorAll('[class*="comment"], [class*="Comment"]');
                if (comments.length > 0) {
                    comments[comments.length - 1].scrollIntoView();
                }
            """)
        ]
        
        strategy_index = index % len(strategies)
        strategies[strategy_index]()
    
    def click_load_more(self):
        """点击加载更多按钮"""
        try:
            load_more_texts = ['加载更多', '展开更多', '查看更多', '更多评论', '展开', '更多']
            
            for text in load_more_texts:
                try:
                    elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{text}')]")
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            time.sleep(self.config['click_delay'])
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            print(f"   ❌ 点击失败: {str(e)}")
            return False
    
    def count_comments(self):
        """统计评论数量"""
        try:
            page_text = self.driver.execute_script("return document.body.textContent;")
            
            # 多种统计方法
            time_count = len(re.findall(r'\d{2}-\d{2}', page_text))
            reply_count = len(re.findall(r'\d+回复', page_text))
            keyword_count = len(re.findall(r'求带|宝子|学姐|兼职|聊天员', page_text)) // 2
            
            try:
                dom_count = len(self.driver.find_elements(By.CSS_SELECTOR, '[class*="comment"], [class*="Comment"]'))
            except:
                dom_count = 0
            
            return max(time_count, reply_count, keyword_count, dom_count)
            
        except Exception as e:
            print(f"❌ 统计评论失败: {str(e)}")
            return 0

    def extract_all_comments(self):
        """提取所有评论"""
        print("🧠 开始提取所有评论...")

        # 获取页面文本
        page_text = self.driver.execute_script("return document.body.textContent;")
        print(f"📄 页面文本长度: {len(page_text)}")

        # 提取基本信息
        note_info = {
            'id': '',
            'title': '',
            'author': '',
            'totalCommentCount': 0
        }

        try:
            # 提取笔记ID
            current_url = self.driver.current_url
            url_match = re.search(r'explore/([a-f0-9]+)', current_url)
            if url_match:
                note_info['id'] = url_match.group(1)

            # 提取标题
            note_info['title'] = self.driver.title.replace(' - 小红书', '')

            # 提取评论总数
            comment_count_match = re.search(r'(\d+)\s*条评论', page_text)
            if comment_count_match:
                note_info['totalCommentCount'] = int(comment_count_match.group(1))

            # 提取作者
            if '漫娴学姐' in page_text:
                note_info['author'] = '漫娴学姐 招暑假工版'

        except Exception as e:
            print(f"❌ 提取基本信息失败: {str(e)}")

        # 解析评论
        comments = self.parse_comments(page_text)

        return {
            'noteInfo': note_info,
            'comments': comments,
            'extractStats': {
                'totalTextLength': len(page_text),
                'successfulExtractions': len(comments),
                'extractionMethods': ['bitbrowser-api-selenium']
            },
            'extractTime': datetime.now().isoformat()
        }

    def parse_comments(self, page_text):
        """解析评论"""
        print("🔍 开始解析评论...")

        all_comments = []

        # 时间模式解析
        time_pattern = r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)'
        time_matches = list(re.finditer(time_pattern, page_text))

        for i, match in enumerate(time_matches):
            time_str = match.group(1)
            time_index = match.start()

            start_index = max(0, time_index - 200)
            end_index = time_matches[i + 1].start() if i + 1 < len(time_matches) else time_index + 1000
            end_index = min(len(page_text), end_index)

            comment_text = page_text[start_index:end_index].strip()

            if 20 < len(comment_text) < 2000:
                comment = self.create_comment(comment_text, len(all_comments) + 1, 'time')
                if comment:
                    all_comments.append(comment)

        # 关键词模式解析
        keywords = ['求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯', 'Carina', '广州']

        for keyword in keywords:
            for match in re.finditer(keyword, page_text):
                start_index = max(0, match.start() - 150)
                end_index = min(len(page_text), match.start() + 800)

                comment_text = page_text[start_index:end_index].strip()

                if 15 < len(comment_text) < 1500:
                    comment = self.create_comment(comment_text, len(all_comments) + 1, 'keyword')
                    if comment:
                        all_comments.append(comment)

        # 去重
        unique_comments = self.deduplicate_comments(all_comments)

        print(f"✅ 解析完成，提取到 {len(unique_comments)} 条评论")
        return unique_comments

    def create_comment(self, text, comment_id, source):
        """创建评论对象"""
        comment = {
            'id': comment_id,
            'userId': '',
            'username': '',
            'content': '',
            'time': '',
            'likes': 0,
            'replyCount': 0,
            'isAuthor': '作者' in text,
            'isPinned': '置顶' in text,
            'extractedInfo': {
                'source': source,
                'originalLength': len(text)
            }
        }

        # 提取时间
        time_match = re.search(r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)', text)
        if time_match:
            comment['time'] = time_match.group(1)

        # 提取用户名
        user_patterns = [
            r'^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}',
            r'([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带',
            r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})'
        ]

        for pattern in user_patterns:
            match = re.search(pattern, text)
            if match:
                comment['username'] = match.group(1).strip()
                break

        # 清理内容
        content = text
        content = re.sub(r'\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前', '', content)
        content = re.sub(r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+', '', content)
        content = re.sub(r'作者|置顶评论|回复|赞|仅自己可见', '', content)
        if comment['username']:
            content = content.replace(comment['username'], '')
        content = re.sub(r'\s+', ' ', content).strip()

        comment['content'] = content

        # 提取数字信息
        like_match = re.search(r'(\d+)\s*赞', text)
        if like_match:
            comment['likes'] = int(like_match.group(1))

        reply_match = re.search(r'(\d+)\s*回复', text)
        if reply_match:
            comment['replyCount'] = int(reply_match.group(1))

        # 提取用户ID
        user_id_match = re.search(r'小红薯([A-F0-9]{8,})', text)
        if user_id_match:
            comment['userId'] = user_id_match.group(1)

        return comment if len(comment['content']) >= 5 else None

    def deduplicate_comments(self, comments):
        """去重评论"""
        unique = []
        seen = set()

        for comment in comments:
            key = comment['content'][:30]
            if key not in seen:
                seen.add(key)
                unique.append(comment)

        # 重新分配ID
        for i, comment in enumerate(unique):
            comment['id'] = i + 1

        return unique

    def save_to_file(self, data, filename):
        """保存数据到文件"""
        filepath = os.path.join(self.output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"💾 数据已保存到: {filepath}")
        return filepath

    def close_window(self):
        """关闭窗口"""
        if self.browser_id:
            try:
                print("🔄 关闭比特浏览器窗口...")
                requests.post(
                    f"{self.api_url}/browser/close",
                    json={"id": self.browser_id},
                    headers=self.get_api_headers()
                )
                print("✅ 窗口已关闭")
            except Exception as e:
                print(f"❌ 关闭窗口失败: {str(e)}")

    def run(self):
        """主运行方法"""
        try:
            print("🎯 启动比特浏览器19号窗口最终解决方案...")
            print(f"🔗 API地址: {self.api_url}")
            print(f"🎯 目标: 获取所有 {self.config['target_comments']} 条评论")
            print("🛡️ 策略: 比特浏览器API + Selenium + 智能爬取")

            # 1. 查找19号窗口
            browser = self.find_window19()
            if not browser:
                print("❌ 未找到19号窗口")
                return

            # 2. 打开19号窗口
            window_data = self.open_window19(browser)
            if not window_data:
                print("❌ 无法打开19号窗口")
                return

            # 等待窗口完全启动
            print("⏳ 等待窗口启动...")
            time.sleep(5)

            # 3. 连接Selenium
            if not self.connect_selenium():
                print("❌ 无法连接Selenium")
                return

            # 4. 爬取所有评论
            loaded_count = self.scrape_all_comments()

            # 5. 提取所有评论
            result = self.extract_all_comments()

            # 6. 保存数据
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"bitbrowser_window19_final_{result['noteInfo']['id']}_{timestamp}.json"
            self.save_to_file(result, filename)

            # 7. 输出结果
            print("\n🎉 比特浏览器19号窗口爬取完成！")
            print("📊 最终统计:")
            print(f"   🔗 API地址: {self.api_url}")
            print(f"   🆔 窗口ID: {self.browser_id[:8]}...")
            print(f"   🔌 调试端口: {self.debug_port}")
            print(f"   📝 笔记ID: {result['noteInfo']['id']}")
            print(f"   📝 笔记标题: {result['noteInfo']['title']}")
            print(f"   👤 笔记作者: {result['noteInfo']['author']}")
            print(f"   🎯 目标评论数: {result['noteInfo']['totalCommentCount']}")
            print(f"   💬 实际提取数: {len(result['comments'])}")

            if result['noteInfo']['totalCommentCount'] > 0:
                completion_rate = round((len(result['comments']) / result['noteInfo']['totalCommentCount']) * 100)
                print(f"   📈 完成度: {completion_rate}%")

            print(f"   📁 保存文件: {filename}")

            if result['comments']:
                print("\n👥 评论预览:")
                for i, comment in enumerate(result['comments'][:10]):
                    print(f"   {i + 1}. {comment['username'] or '匿名'} ({comment['time'] or '未知时间'}): {comment['content'][:80]}...")

                # 兼职相关统计
                job_keywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱']
                job_comments = [c for c in result['comments']
                              if any(keyword in c['content'] for keyword in job_keywords)]

                if job_comments:
                    job_percentage = round((len(job_comments) / len(result['comments'])) * 100)
                    print(f"\n💼 兼职相关评论: {len(job_comments)}/{len(result['comments'])} ({job_percentage}%)")

            print(f"\n✅ 任务完成！数据已保存到: {filename}")

        except Exception as e:
            print(f"❌ 爬取失败: {str(e)}")
        finally:
            if self.driver:
                print("🔄 保持浏览器打开状态...")
                # 不关闭浏览器，保持连接

def main():
    """主函数"""
    scraper = BitBrowserWindow19Final()
    scraper.run()

if __name__ == "__main__":
    main()
