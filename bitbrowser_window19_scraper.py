#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 比特浏览器19号窗口评论爬取器
直接连接比特浏览器19号窗口，爬取所有评论
"""

import time
import json
import re
import os
import requests
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class BitBrowserWindow19Scraper:
    def __init__(self):
        self.api_url = "http://127.0.0.1:56906"
        self.api_token = "ca28ee5ca6c4e4d209182a83aa16a2044"
        self.output_dir = './scraped_data'
        self.ensure_output_dir()
        
        self.config = {
            'target_comments': 1472,
            'max_scroll_attempts': 1000,
            'scroll_delay': 0.5,
            'click_delay': 0.3
        }
        
        self.driver = None
        
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def get_api_headers(self):
        """获取API请求头"""
        return {
            'Content-Type': 'application/json',
            'X-API-KEY': self.api_token
        }
    
    def get_browser_list(self):
        """获取浏览器列表"""
        try:
            print("🔍 获取比特浏览器列表...")
            
            # 尝试不同的API端点
            endpoints = [
                f"{self.api_url}/api/v1/browser/list",
                f"{self.api_url}/browser/list",
                f"{self.api_url}/api/browser/list"
            ]
            
            for endpoint in endpoints:
                try:
                    response = requests.get(endpoint, headers=self.get_api_headers(), timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        if 'data' in data:
                            browsers = data['data']
                            print(f"✅ 找到 {len(browsers)} 个浏览器窗口")
                            return browsers
                except Exception as e:
                    print(f"   ❌ 端点 {endpoint} 失败: {str(e)}")
                    continue
            
            print("❌ 无法获取浏览器列表，尝试直接连接...")
            return []
            
        except Exception as e:
            print(f"❌ 获取浏览器列表失败: {str(e)}")
            return []
    
    def find_window19_or_target(self):
        """查找19号窗口或目标窗口"""
        browsers = self.get_browser_list()
        
        # 查找19号窗口
        for browser in browsers:
            if '19' in str(browser.get('name', '')) or browser.get('id') == '19':
                print(f"✅ 找到19号窗口: {browser}")
                return browser
        
        # 查找包含小红书的窗口
        for browser in browsers:
            if 'xiaohongshu' in str(browser.get('name', '')).lower() or 'xhs' in str(browser.get('name', '')).lower():
                print(f"✅ 找到小红书窗口: {browser}")
                return browser
        
        # 使用第一个活跃窗口
        active_browsers = [b for b in browsers if b.get('status') == 'Active']
        if active_browsers:
            print(f"✅ 使用第一个活跃窗口: {active_browsers[0]}")
            return active_browsers[0]
        
        # 使用第一个窗口
        if browsers:
            print(f"✅ 使用第一个窗口: {browsers[0]}")
            return browsers[0]
        
        return None
    
    def connect_to_bitbrowser(self):
        """连接到比特浏览器"""
        print("🔗 连接到比特浏览器19号窗口...")
        
        # 查找目标窗口
        target_browser = self.find_window19_or_target()
        
        if not target_browser:
            print("❌ 未找到目标窗口，尝试直接连接调试端口...")
            return self.connect_direct()
        
        # 获取调试端口
        debug_port = target_browser.get('debug_port', 9222)
        print(f"🔗 目标窗口调试端口: {debug_port}")
        
        try:
            # 设置Chrome选项连接到现有实例
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{debug_port}")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 创建WebDriver
            self.driver = webdriver.Chrome(options=chrome_options)
            
            print("✅ 成功连接到比特浏览器")
            
            # 检查当前页面
            current_url = self.driver.current_url
            print(f"📄 当前页面: {current_url}")
            
            if 'xiaohongshu.com' not in current_url:
                print("💡 请手动导航到小红书评论页面，然后按回车继续...")
                input()
            
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
            return self.connect_direct()
    
    def connect_direct(self):
        """直接连接调试端口"""
        print("🔄 尝试直接连接调试端口...")
        
        # 常见调试端口
        ports = [9222, 9223, 9224, 9225, 55276, 54345, 56906]
        
        for port in ports:
            try:
                print(f"   🔍 尝试端口 {port}...")
                
                chrome_options = Options()
                chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
                
                self.driver = webdriver.Chrome(options=chrome_options)
                
                print(f"✅ 成功连接到端口 {port}")
                return True
                
            except Exception as e:
                print(f"   ❌ 端口 {port} 失败")
                continue
        
        print("❌ 无法连接到任何调试端口")
        return False
    
    def scroll_and_load_comments(self):
        """滚动并加载所有评论"""
        print("🚀 开始滚动加载所有评论...")
        print(f"🎯 目标: {self.config['target_comments']} 条评论")
        
        current_count = 0
        previous_count = 0
        stable_count = 0
        total_scrolls = 0
        total_clicks = 0
        
        # 先滚动到评论区域
        self.scroll_to_comments()
        
        for i in range(self.config['max_scroll_attempts']):
            print(f"\n📜 滚动 {i + 1}/{self.config['max_scroll_attempts']}")
            
            # 统计评论数量
            current_count = self.count_comments()
            print(f"   💬 当前评论数: {current_count}")
            
            # 检查是否达到目标
            if current_count >= self.config['target_comments'] * 0.95:
                print(f"🎉 接近目标！{current_count}/{self.config['target_comments']}")
                break
            
            # 检查进度
            if current_count == previous_count:
                stable_count += 1
                print(f"   ⏸️ 稳定 {stable_count} 次")
            else:
                new_comments = current_count - previous_count
                print(f"   📈 新增: {new_comments} 条")
                stable_count = 0
                previous_count = current_count
            
            # 点击加载更多
            if stable_count >= 3:
                print("   🔄 点击加载更多...")
                if self.click_load_more():
                    total_clicks += 1
                    stable_count = 0
                    print(f"   ✅ 点击成功，总计: {total_clicks} 次")
            
            # 执行滚动
            self.perform_scroll(i)
            
            # 如果长时间稳定，停止
            if stable_count >= 15:
                print("   ⏹️ 长时间无新内容，停止")
                break
            
            time.sleep(self.config['scroll_delay'])
            total_scrolls += 1
            
            # 每100次输出进度
            if i % 100 == 0 and i > 0:
                progress = round((current_count / self.config['target_comments']) * 100)
                print(f"\n📊 进度: {progress}% ({current_count}/{self.config['target_comments']})")
                print(f"   📜 滚动: {total_scrolls} 次")
                print(f"   🔄 点击: {total_clicks} 次")
        
        print(f"\n✅ 滚动完成！最终评论数: {current_count}")
        return current_count
    
    def scroll_to_comments(self):
        """滚动到评论区域"""
        try:
            # 查找评论区域
            comment_selectors = [
                "//span[contains(text(), '条评论')]",
                "//div[contains(text(), '评论')]",
                "//*[contains(@class, 'comment')]"
            ]
            
            for selector in comment_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    print("✅ 定位到评论区域")
                    time.sleep(2)
                    return
                except:
                    continue
            
            # 如果没找到，滚动到页面中部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.6);")
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ 滚动到评论区域失败: {str(e)}")
    
    def perform_scroll(self, index):
        """执行滚动"""
        strategies = [
            lambda: self.driver.execute_script("window.scrollBy(0, 300);"),
            lambda: self.driver.execute_script("window.scrollBy(0, 500);"),
            lambda: self.driver.execute_script("window.scrollBy(0, 800);"),
            lambda: self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);"),
            lambda: self.driver.execute_script("""
                const comments = document.querySelectorAll('[class*="comment"], [class*="Comment"]');
                if (comments.length > 0) {
                    comments[comments.length - 1].scrollIntoView();
                }
            """)
        ]
        
        strategy_index = index % len(strategies)
        strategies[strategy_index]()
    
    def click_load_more(self):
        """点击加载更多按钮"""
        try:
            load_more_texts = ['加载更多', '展开更多', '查看更多', '更多评论', '展开', '更多']
            
            for text in load_more_texts:
                try:
                    elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{text}')]")
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            time.sleep(self.config['click_delay'])
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            print(f"   ❌ 点击失败: {str(e)}")
            return False
    
    def count_comments(self):
        """统计评论数量"""
        try:
            page_text = self.driver.execute_script("return document.body.textContent;")
            
            # 多种统计方法
            time_count = len(re.findall(r'\d{2}-\d{2}', page_text))
            reply_count = len(re.findall(r'\d+回复', page_text))
            keyword_count = len(re.findall(r'求带|宝子|学姐|兼职|聊天员', page_text)) // 2
            
            try:
                dom_count = len(self.driver.find_elements(By.CSS_SELECTOR, '[class*="comment"], [class*="Comment"]'))
            except:
                dom_count = 0
            
            return max(time_count, reply_count, keyword_count, dom_count)
            
        except Exception as e:
            print(f"❌ 统计评论失败: {str(e)}")
            return 0

    def extract_all_comments(self):
        """提取所有评论"""
        print("🧠 开始提取所有评论...")

        # 获取页面文本
        page_text = self.driver.execute_script("return document.body.textContent;")
        print(f"📄 页面文本长度: {len(page_text)}")

        # 提取基本信息
        note_info = {
            'id': '',
            'title': '',
            'author': '',
            'totalCommentCount': 0
        }

        try:
            # 提取笔记ID
            current_url = self.driver.current_url
            url_match = re.search(r'explore/([a-f0-9]+)', current_url)
            if url_match:
                note_info['id'] = url_match.group(1)

            # 提取标题
            note_info['title'] = self.driver.title.replace(' - 小红书', '')

            # 提取评论总数
            comment_count_match = re.search(r'(\d+)\s*条评论', page_text)
            if comment_count_match:
                note_info['totalCommentCount'] = int(comment_count_match.group(1))

            # 提取作者
            if '漫娴学姐' in page_text:
                note_info['author'] = '漫娴学姐 招暑假工版'

        except Exception as e:
            print(f"❌ 提取基本信息失败: {str(e)}")

        # 解析评论
        comments = self.parse_comments(page_text)

        return {
            'noteInfo': note_info,
            'comments': comments,
            'extractStats': {
                'totalTextLength': len(page_text),
                'successfulExtractions': len(comments),
                'extractionMethods': ['selenium-bitbrowser']
            },
            'extractTime': datetime.now().isoformat()
        }

    def parse_comments(self, page_text):
        """解析评论"""
        print("🔍 开始解析评论...")

        all_comments = []

        # 时间模式解析
        time_pattern = r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)'
        time_matches = list(re.finditer(time_pattern, page_text))

        for i, match in enumerate(time_matches):
            time_str = match.group(1)
            time_index = match.start()

            start_index = max(0, time_index - 200)
            end_index = time_matches[i + 1].start() if i + 1 < len(time_matches) else time_index + 1000
            end_index = min(len(page_text), end_index)

            comment_text = page_text[start_index:end_index].strip()

            if 20 < len(comment_text) < 2000:
                comment = self.create_comment(comment_text, len(all_comments) + 1, 'time')
                if comment:
                    all_comments.append(comment)

        # 关键词模式解析
        keywords = ['求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯', 'Carina', '广州']

        for keyword in keywords:
            for match in re.finditer(keyword, page_text):
                start_index = max(0, match.start() - 150)
                end_index = min(len(page_text), match.start() + 800)

                comment_text = page_text[start_index:end_index].strip()

                if 15 < len(comment_text) < 1500:
                    comment = self.create_comment(comment_text, len(all_comments) + 1, 'keyword')
                    if comment:
                        all_comments.append(comment)

        # 去重
        unique_comments = self.deduplicate_comments(all_comments)

        print(f"✅ 解析完成，提取到 {len(unique_comments)} 条评论")
        return unique_comments

    def create_comment(self, text, comment_id, source):
        """创建评论对象"""
        comment = {
            'id': comment_id,
            'userId': '',
            'username': '',
            'content': '',
            'time': '',
            'likes': 0,
            'replyCount': 0,
            'isAuthor': '作者' in text,
            'isPinned': '置顶' in text,
            'extractedInfo': {
                'source': source,
                'originalLength': len(text)
            }
        }

        # 提取时间
        time_match = re.search(r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)', text)
        if time_match:
            comment['time'] = time_match.group(1)

        # 提取用户名
        user_patterns = [
            r'^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}',
            r'([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带',
            r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})'
        ]

        for pattern in user_patterns:
            match = re.search(pattern, text)
            if match:
                comment['username'] = match.group(1).strip()
                break

        # 清理内容
        content = text
        content = re.sub(r'\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前', '', content)
        content = re.sub(r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+', '', content)
        content = re.sub(r'作者|置顶评论|回复|赞|仅自己可见', '', content)
        if comment['username']:
            content = content.replace(comment['username'], '')
        content = re.sub(r'\s+', ' ', content).strip()

        comment['content'] = content

        # 提取数字信息
        like_match = re.search(r'(\d+)\s*赞', text)
        if like_match:
            comment['likes'] = int(like_match.group(1))

        reply_match = re.search(r'(\d+)\s*回复', text)
        if reply_match:
            comment['replyCount'] = int(reply_match.group(1))

        # 提取用户ID
        user_id_match = re.search(r'小红薯([A-F0-9]{8,})', text)
        if user_id_match:
            comment['userId'] = user_id_match.group(1)

        return comment if len(comment['content']) >= 5 else None

    def deduplicate_comments(self, comments):
        """去重评论"""
        unique = []
        seen = set()

        for comment in comments:
            key = comment['content'][:30]
            if key not in seen:
                seen.add(key)
                unique.append(comment)

        # 重新分配ID
        for i, comment in enumerate(unique):
            comment['id'] = i + 1

        return unique

    def save_to_file(self, data, filename):
        """保存数据到文件"""
        filepath = os.path.join(self.output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"💾 数据已保存到: {filepath}")
        return filepath

    def run(self):
        """主运行方法"""
        try:
            print("🎯 启动比特浏览器19号窗口评论爬取器...")
            print(f"🔗 API地址: {self.api_url}")
            print(f"🎯 目标: 获取所有 {self.config['target_comments']} 条评论")

            # 连接到比特浏览器
            if not self.connect_to_bitbrowser():
                print("❌ 无法连接到比特浏览器")
                return

            # 滚动加载评论
            loaded_count = self.scroll_and_load_comments()

            # 提取所有评论
            result = self.extract_all_comments()

            # 保存数据
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"bitbrowser_window19_comments_{result['noteInfo']['id']}_{timestamp}.json"
            self.save_to_file(result, filename)

            # 输出结果
            print("\n🎉 比特浏览器19号窗口爬取完成！")
            print("📊 最终统计:")
            print(f"   📝 笔记ID: {result['noteInfo']['id']}")
            print(f"   📝 笔记标题: {result['noteInfo']['title']}")
            print(f"   👤 笔记作者: {result['noteInfo']['author']}")
            print(f"   🎯 目标评论数: {result['noteInfo']['totalCommentCount']}")
            print(f"   💬 实际提取数: {len(result['comments'])}")

            if result['noteInfo']['totalCommentCount'] > 0:
                completion_rate = round((len(result['comments']) / result['noteInfo']['totalCommentCount']) * 100)
                print(f"   📈 完成度: {completion_rate}%")

            print(f"   📁 保存文件: {filename}")

            if result['comments']:
                print("\n👥 评论预览:")
                for i, comment in enumerate(result['comments'][:10]):
                    print(f"   {i + 1}. {comment['username'] or '匿名'} ({comment['time'] or '未知时间'}): {comment['content'][:80]}...")

                # 兼职相关统计
                job_keywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱']
                job_comments = [c for c in result['comments']
                              if any(keyword in c['content'] for keyword in job_keywords)]

                if job_comments:
                    job_percentage = round((len(job_comments) / len(result['comments'])) * 100)
                    print(f"\n💼 兼职相关评论: {len(job_comments)}/{len(result['comments'])} ({job_percentage}%)")

            print(f"\n✅ 任务完成！数据已保存到: {filename}")

        except Exception as e:
            print(f"❌ 爬取失败: {str(e)}")
        finally:
            if self.driver:
                print("🔄 保持浏览器打开状态...")
                # 不关闭浏览器，保持连接

def main():
    """主函数"""
    scraper = BitBrowserWindow19Scraper()
    scraper.run()

if __name__ == "__main__":
    main()
