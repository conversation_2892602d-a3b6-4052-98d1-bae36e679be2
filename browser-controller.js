#!/usr/bin/env node

/**
 * 🎮 比特浏览器控制器 - 增强版
 * 通过Chrome DevTools Protocol实现完整的浏览器自动化控制
 */

const axios = require('axios');

class BitBrowserController {
    constructor() {
        this.debugPort = null;
        this.currentTab = null;
        this.config = {
            api_url: "http://127.0.0.1:54345",
            api_token: "ca28ee5ca6de4d209182a83aa16a2044",
            browser_id: "95362955272" // 使用您的ace1浏览器
        };
    }

    // 🔍 查找并连接到浏览器
    async connect() {
        console.log('🔍 正在连接比特浏览器...');
        
        // 查找可用的调试端口
        const debugInfo = await this.findDebugPort();
        if (!debugInfo) {
            throw new Error('未找到可用的浏览器调试端口');
        }
        
        this.debugPort = debugInfo.port;
        this.currentTab = debugInfo.xiaohongshuTab;
        
        console.log(`✅ 已连接到浏览器 (端口: ${this.debugPort})`);
        return true;
    }

    // 🔍 查找调试端口
    async findDebugPort() {
        const ports = [9222, 9223, 9224, 9225, 9226, 9227, 9228, 9229, 9230, 9231, 9232, 9233, 9234, 9235, 9236, 9237, 9238, 9239, 9240];
        
        for (const port of ports) {
            try {
                const response = await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                const tabs = response.data;
                
                // 查找小红书标签页
                const xiaohongshuTab = tabs.find(tab =>
                    tab.url && (
                        tab.url.includes('xiaohongshu.com') ||
                        tab.url.includes('creator.xiaohongshu.com') ||
                        tab.title.includes('小红书')
                    )
                );
                
                if (xiaohongshuTab) {
                    return { port, xiaohongshuTab };
                }
            } catch (error) {
                // 端口不可用，继续尝试下一个
            }
        }
        return null;
    }

    // 🌐 导航到指定URL
    async navigateTo(url) {
        console.log(`🌐 导航到: ${url}`);
        
        const script = `window.location.href = '${url}';`;
        await this.executeScript(script);
        
        // 等待页面加载
        await this.waitForPageLoad();
        console.log('✅ 页面导航完成');
    }

    // ⏱️ 等待页面加载完成
    async waitForPageLoad(timeout = 10000) {
        const script = `
            new Promise((resolve) => {
                if (document.readyState === 'complete') {
                    resolve(true);
                } else {
                    window.addEventListener('load', () => resolve(true));
                    setTimeout(() => resolve(false), ${timeout});
                }
            });
        `;
        
        await this.executeScript(script);
    }

    // 🖱️ 点击元素
    async clickElement(selector) {
        console.log(`🖱️ 点击元素: ${selector}`);
        
        const script = `
            (function() {
                const element = document.querySelector('${selector}');
                if (element) {
                    element.click();
                    return true;
                } else {
                    return false;
                }
            })();
        `;
        
        const result = await this.executeScript(script);
        if (result) {
            console.log('✅ 元素点击成功');
        } else {
            console.log('❌ 未找到指定元素');
        }
        return result;
    }

    // ⌨️ 输入文本
    async typeText(selector, text) {
        console.log(`⌨️ 在 ${selector} 中输入文本`);
        
        const script = `
            (function() {
                const element = document.querySelector('${selector}');
                if (element) {
                    element.focus();
                    element.value = '${text}';
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    return true;
                } else {
                    return false;
                }
            })();
        `;
        
        const result = await this.executeScript(script);
        if (result) {
            console.log('✅ 文本输入成功');
        } else {
            console.log('❌ 未找到输入框');
        }
        return result;
    }

    // 📜 滚动页面
    async scrollPage(direction = 'down', distance = 500) {
        console.log(`📜 滚动页面: ${direction} ${distance}px`);
        
        const script = `
            window.scrollBy(0, ${direction === 'down' ? distance : -distance});
        `;
        
        await this.executeScript(script);
        console.log('✅ 页面滚动完成');
    }

    // 📊 获取页面数据
    async getPageData() {
        console.log('📊 获取页面数据...');
        
        const script = `
            (function() {
                const data = {
                    url: window.location.href,
                    title: document.title,
                    timestamp: new Date().toISOString()
                };
                
                // 提取小红书用户信息
                if (window.location.href.includes('xiaohongshu.com')) {
                    // 尝试提取用户名
                    const nameSelectors = ['.user-name', '.nickname', 'h1', '.title'];
                    for (const selector of nameSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            data.username = element.textContent.trim();
                            break;
                        }
                    }
                    
                    // 尝试提取粉丝数
                    const statsElements = document.querySelectorAll('[class*="count"], [class*="num"]');
                    statsElements.forEach(el => {
                        const text = el.textContent;
                        if (text.includes('粉丝') || text.includes('关注者')) {
                            data.followers = text;
                        } else if (text.includes('关注')) {
                            data.following = text;
                        } else if (text.includes('获赞')) {
                            data.likes = text;
                        }
                    });
                    
                    // 提取头像
                    const avatar = document.querySelector('img[alt*="头像"], .avatar img, .user-avatar img');
                    if (avatar && avatar.src) {
                        data.avatar = avatar.src;
                    }
                }
                
                return data;
            })();
        `;
        
        const result = await this.executeScript(script);
        console.log('✅ 页面数据获取完成');
        return result;
    }

    // 📸 截取页面截图
    async takeScreenshot() {
        console.log('📸 截取页面截图...');
        
        try {
            const response = await axios.post(`http://127.0.0.1:${this.debugPort}/json/runtime/evaluate`, {
                expression: `
                    new Promise((resolve) => {
                        html2canvas(document.body).then(canvas => {
                            resolve(canvas.toDataURL());
                        }).catch(() => resolve(null));
                    });
                `
            }, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 10000
            });
            
            if (response.data?.result?.value) {
                console.log('✅ 截图完成');
                return response.data.result.value;
            }
        } catch (error) {
            console.log('❌ 截图失败:', error.message);
        }
        return null;
    }

    // 🔧 执行自定义JavaScript脚本
    async executeScript(script) {
        if (!this.debugPort) {
            throw new Error('未连接到浏览器，请先调用 connect()');
        }
        
        try {
            const response = await axios.post(`http://127.0.0.1:${this.debugPort}/json/runtime/evaluate`, {
                expression: script,
                awaitPromise: true
            }, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 15000
            });
            
            if (response.data?.result) {
                return response.data.result.value;
            }
            
            return null;
        } catch (error) {
            console.error('❌ 脚本执行失败:', error.message);
            throw error;
        }
    }

    // 🚀 启动浏览器实例
    async startBrowser() {
        console.log('🚀 启动浏览器实例...');
        
        try {
            const response = await axios.post(`${this.config.api_url}/browser/open`, {
                id: this.config.browser_id
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.config.api_token}`
                },
                timeout: 20000
            });
            
            if (response.data?.success) {
                console.log('✅ 浏览器启动成功');
                return true;
            } else {
                console.log('❌ 浏览器启动失败:', response.data?.msg);
                return false;
            }
        } catch (error) {
            console.error('❌ 启动浏览器出错:', error.message);
            return false;
        }
    }

    // 🛑 停止浏览器实例
    async stopBrowser() {
        console.log('🛑 停止浏览器实例...');
        
        try {
            const response = await axios.post(`${this.config.api_url}/browser/close`, {
                id: this.config.browser_id
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.config.api_token}`
                },
                timeout: 10000
            });
            
            if (response.data?.success) {
                console.log('✅ 浏览器停止成功');
                return true;
            } else {
                console.log('❌ 浏览器停止失败:', response.data?.msg);
                return false;
            }
        } catch (error) {
            console.error('❌ 停止浏览器出错:', error.message);
            return false;
        }
    }
}

// 🧪 测试示例
async function testBrowserControl() {
    const controller = new BitBrowserController();
    
    try {
        // 1. 连接浏览器
        await controller.connect();
        
        // 2. 获取当前页面数据
        const pageData = await controller.getPageData();
        console.log('📊 页面数据:', pageData);
        
        // 3. 滚动页面
        await controller.scrollPage('down', 300);
        
        // 4. 等待一秒
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 5. 再次获取数据
        const newData = await controller.getPageData();
        console.log('📊 滚动后数据:', newData);
        
        console.log('🎉 浏览器控制测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    testBrowserControl();
}

module.exports = BitBrowserController;
