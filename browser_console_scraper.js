// 🎯 浏览器控制台评论爬取器
// 直接在比特浏览器的开发者工具控制台中运行

console.log('🎯 启动小红书评论爬取器...');

// 配置
const config = {
    targetComments: 1472,
    maxScrollAttempts: 1000,
    scrollDelay: 800,
    clickDelay: 500
};

// 全局变量
let allComments = [];
let currentCount = 0;
let scrollCount = 0;
let clickCount = 0;

// 滚动到评论区域
function scrollToComments() {
    console.log('🎯 定位评论区域...');
    
    // 查找评论区域
    const commentIndicators = ['条评论', '评论'];
    
    for (const indicator of commentIndicators) {
        const elements = Array.from(document.querySelectorAll('*')).filter(el => 
            el.textContent.includes(indicator) && el.offsetHeight > 0
        );
        
        if (elements.length > 0) {
            elements[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            console.log('✅ 找到评论区域');
            return true;
        }
    }
    
    // 如果没找到，滚动到页面中部
    window.scrollTo(0, document.body.scrollHeight * 0.6);
    console.log('💡 滚动到页面中部');
    return true;
}

// 统计评论数量
function countComments() {
    const pageText = document.body.textContent;
    
    // 多种统计方法
    const timeCount = (pageText.match(/\d{2}-\d{2}/g) || []).length;
    const replyCount = (pageText.match(/\d+回复/g) || []).length;
    const keywordCount = (pageText.match(/求带|宝子|学姐|兼职|聊天员/g) || []).length;
    
    return Math.max(timeCount, replyCount, keywordCount);
}

// 点击加载更多
function clickLoadMore() {
    const loadMoreTexts = ['加载更多', '展开更多', '查看更多', '更多评论', '展开', '更多'];
    
    for (const text of loadMoreTexts) {
        const elements = Array.from(document.querySelectorAll('*')).filter(el => 
            el.textContent.includes(text) && 
            el.offsetHeight > 0 && 
            el.offsetWidth > 0
        );
        
        for (const el of elements) {
            try {
                el.click();
                console.log(`✅ 点击了: ${text}`);
                return true;
            } catch (e) {
                // 忽略点击失败
            }
        }
    }
    
    return false;
}

// 执行滚动
function performScroll(index) {
    const strategies = [
        () => window.scrollBy(0, 300 + Math.random() * 100),
        () => window.scrollBy(0, 500 + Math.random() * 200),
        () => window.scrollBy(0, 800 + Math.random() * 300),
        () => window.scrollTo(0, document.body.scrollHeight),
        () => {
            const comments = document.querySelectorAll('[class*="comment"], [class*="Comment"]');
            if (comments.length > 0) {
                comments[comments.length - 1].scrollIntoView({ behavior: 'smooth' });
            }
        }
    ];
    
    const strategyIndex = index % strategies.length;
    strategies[strategyIndex]();
}

// 解析评论
function parseComments() {
    console.log('🧠 开始解析评论...');
    
    const pageText = document.body.textContent;
    const comments = [];
    
    // 时间模式解析
    const timePattern = /(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/g;
    const timeMatches = [];
    let match;
    
    while ((match = timePattern.exec(pageText)) !== null) {
        timeMatches.push({
            time: match[1],
            index: match.index
        });
    }
    
    for (let i = 0; i < timeMatches.length; i++) {
        const currentTime = timeMatches[i];
        const nextTime = timeMatches[i + 1];
        
        const startIndex = Math.max(0, currentTime.index - 200);
        const endIndex = nextTime ? nextTime.index : currentTime.index + 800;
        
        const commentText = pageText.substring(startIndex, endIndex).trim();
        
        if (commentText.length > 20 && commentText.length < 1500) {
            // 提取用户名
            let username = '';
            const userMatch = commentText.match(/^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/);
            if (userMatch) {
                username = userMatch[1];
            }
            
            // 清理内容
            let content = commentText;
            content = content.replace(/\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前/g, '');
            content = content.replace(/作者|置顶|回复|赞/g, '');
            if (username) {
                content = content.replace(username, '');
            }
            content = content.replace(/\s+/g, ' ').trim();
            
            if (content.length >= 5) {
                const comment = {
                    id: comments.length + 1,
                    username: username,
                    content: content,
                    time: currentTime.time,
                    likes: 0,
                    replyCount: 0
                };
                
                // 提取点赞数
                const likeMatch = commentText.match(/(\d+)\s*赞/);
                if (likeMatch) {
                    comment.likes = parseInt(likeMatch[1]);
                }
                
                comments.push(comment);
            }
        }
    }
    
    // 去重
    const unique = [];
    const seen = new Set();
    
    comments.forEach(comment => {
        const key = comment.content.substring(0, 30);
        if (!seen.has(key)) {
            seen.add(key);
            unique.push(comment);
        }
    });
    
    // 重新分配ID
    unique.forEach((comment, index) => {
        comment.id = index + 1;
    });
    
    console.log(`✅ 解析完成，提取到 ${unique.length} 条评论`);
    return unique;
}

// 保存数据
function saveData(comments) {
    const noteId = window.location.href.match(/explore\/([a-f0-9]+)/)?.[1] || 'unknown';
    const title = document.title.replace(' - 小红书', '');
    
    const result = {
        noteInfo: {
            id: noteId,
            title: title,
            totalCommentCount: comments.length,
            url: window.location.href
        },
        comments: comments,
        extractTime: new Date().toISOString(),
        extractStats: {
            totalScrolls: scrollCount,
            totalClicks: clickCount,
            extractionMethod: 'browser-console'
        }
    };
    
    // 创建下载链接
    const dataStr = JSON.stringify(result, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `xiaohongshu_comments_${noteId}_${new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    
    console.log('💾 数据已下载');
    return result;
}

// 主要爬取函数
async function scrapeComments() {
    console.log(`🚀 开始爬取评论，目标: ${config.targetComments} 条`);
    
    // 滚动到评论区域
    scrollToComments();
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    let previousCount = 0;
    let stableCount = 0;
    
    for (let i = 0; i < config.maxScrollAttempts; i++) {
        console.log(`📜 滚动 ${i + 1}/${config.maxScrollAttempts}`);
        
        // 统计评论数量
        currentCount = countComments();
        console.log(`   💬 当前评论数: ${currentCount}`);
        
        // 检查是否达到目标
        if (currentCount >= config.targetComments * 0.95) {
            console.log(`🎉 接近目标！${currentCount}/${config.targetComments}`);
            break;
        }
        
        // 检查进度
        if (currentCount === previousCount) {
            stableCount++;
            console.log(`   ⏸️ 稳定 ${stableCount} 次`);
        } else {
            const newComments = currentCount - previousCount;
            console.log(`   📈 新增: ${newComments} 条`);
            stableCount = 0;
            previousCount = currentCount;
        }
        
        // 点击加载更多
        if (stableCount >= 3) {
            console.log('   🔄 尝试点击加载更多...');
            if (clickLoadMore()) {
                clickCount++;
                stableCount = 0;
            }
        }
        
        // 执行滚动
        performScroll(i);
        scrollCount++;
        
        // 如果长时间稳定，停止
        if (stableCount >= 20) {
            console.log('   ⏹️ 长时间无新内容，停止');
            break;
        }
        
        // 等待
        await new Promise(resolve => setTimeout(resolve, config.scrollDelay));
        
        // 每50次输出进度
        if (i % 50 === 0 && i > 0) {
            const progress = Math.round((currentCount / config.targetComments) * 100);
            console.log(`📊 进度: ${progress}% (${currentCount}/${config.targetComments})`);
        }
    }
    
    console.log(`✅ 滚动完成！最终评论数: ${currentCount}`);
    
    // 解析评论
    const comments = parseComments();
    
    // 保存数据
    const result = saveData(comments);
    
    // 输出结果
    console.log('\n🎉 爬取完成！');
    console.log(`📝 笔记ID: ${result.noteInfo.id}`);
    console.log(`📝 笔记标题: ${result.noteInfo.title}`);
    console.log(`💬 提取评论数: ${comments.length}`);
    console.log(`📜 总滚动次数: ${scrollCount}`);
    console.log(`🔄 总点击次数: ${clickCount}`);
    
    if (comments.length > 0) {
        console.log('\n👥 评论预览:');
        comments.slice(0, 10).forEach((comment, index) => {
            console.log(`${index + 1}. ${comment.username || '匿名'} (${comment.time}): ${comment.content.substring(0, 60)}...`);
        });
        
        // 兼职相关统计
        const jobKeywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱'];
        const jobComments = comments.filter(c => 
            jobKeywords.some(keyword => c.content.includes(keyword))
        );
        
        if (jobComments.length > 0) {
            const jobPercentage = Math.round((jobComments.length / comments.length) * 100);
            console.log(`\n💼 兼职相关评论: ${jobComments.length}/${comments.length} (${jobPercentage}%)`);
        }
    }
    
    console.log('\n✅ 任务完成！数据已自动下载');
    return result;
}

// 启动爬取
console.log('🎯 准备开始爬取...');
console.log('💡 请确保你在小红书评论页面');
console.log('🚀 3秒后开始爬取...');

setTimeout(() => {
    scrapeComments().catch(error => {
        console.error('❌ 爬取失败:', error);
    });
}, 3000);
