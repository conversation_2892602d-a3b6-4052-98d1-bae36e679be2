#!/usr/bin/env node

/**
 * 🔧 小红书验证码操作助手
 * 分析验证码并提供操作方案
 */

const axios = require('axios');

async function analyzeCaptcha() {
    const DEBUG_PORT = 55276;
    
    console.log('🔍 分析小红书验证码页面...');
    console.log('');
    
    try {
        // 1. 获取标签页信息
        const tabsResponse = await axios.get(`http://127.0.0.1:${DEBUG_PORT}/json`, { timeout: 5000 });
        const tabs = tabsResponse.data;
        
        const xiaohongshuTab = tabs.find(tab => 
            tab.url && tab.url.includes('xiaohongshu.com') && tab.type === 'page'
        );
        
        if (!xiaohongshuTab) {
            console.log('❌ 未找到小红书标签页');
            return;
        }
        
        console.log('✅ 找到小红书验证码页面:');
        console.log(`   标题: ${xiaohongshuTab.title}`);
        console.log(`   URL: ${xiaohongshuTab.url}`);
        console.log('');
        
        // 2. 分析验证码类型
        console.log('🔧 验证码操作方案:');
        console.log('');
        
        console.log('📋 这是小红书的滑动验证码');
        console.log('');
        console.log('💡 手动操作步骤:');
        console.log('   1. 观察验证码图片，找到缺失的拼图块位置');
        console.log('   2. 点击并按住右侧的圆形滑动按钮');
        console.log('   3. 向右拖动滑块，使拼图块对齐');
        console.log('   4. 释放鼠标完成验证');
        console.log('');
        
        console.log('🤖 自动化操作方案:');
        console.log('');
        console.log('方案1: Puppeteer (推荐)');
        console.log('```javascript');
        console.log('const puppeteer = require("puppeteer");');
        console.log('');
        console.log('async function solveCaptcha() {');
        console.log(`  const browser = await puppeteer.connect({`);
        console.log(`    browserURL: "http://127.0.0.1:${DEBUG_PORT}"`);
        console.log('  });');
        console.log('  ');
        console.log('  const pages = await browser.pages();');
        console.log('  const page = pages.find(p => p.url().includes("xiaohongshu"));');
        console.log('  ');
        console.log('  // 等待验证码加载');
        console.log('  await page.waitForSelector(".slider-track", {timeout: 5000});');
        console.log('  ');
        console.log('  // 获取滑块元素');
        console.log('  const slider = await page.$(".slider-button");');
        console.log('  const sliderBox = await slider.boundingBox();');
        console.log('  ');
        console.log('  // 计算拖动距离（通常需要分析图片）');
        console.log('  const dragDistance = 200; // 需要根据实际情况调整');
        console.log('  ');
        console.log('  // 执行拖动');
        console.log('  await page.mouse.move(sliderBox.x + sliderBox.width/2, sliderBox.y + sliderBox.height/2);');
        console.log('  await page.mouse.down();');
        console.log('  await page.mouse.move(sliderBox.x + dragDistance, sliderBox.y + sliderBox.height/2, {steps: 10});');
        console.log('  await page.mouse.up();');
        console.log('}');
        console.log('```');
        console.log('');
        
        console.log('方案2: Selenium WebDriver');
        console.log('```python');
        console.log('from selenium import webdriver');
        console.log('from selenium.webdriver.common.action_chains import ActionChains');
        console.log('from selenium.webdriver.chrome.options import Options');
        console.log('');
        console.log('# 连接到已有的浏览器实例');
        console.log('options = Options()');
        console.log(`options.add_experimental_option("debuggerAddress", "127.0.0.1:${DEBUG_PORT}")`);
        console.log('driver = webdriver.Chrome(options=options)');
        console.log('');
        console.log('# 切换到小红书标签页');
        console.log('for handle in driver.window_handles:');
        console.log('    driver.switch_to.window(handle)');
        console.log('    if "xiaohongshu" in driver.current_url:');
        console.log('        break');
        console.log('');
        console.log('# 找到滑块并拖动');
        console.log('slider = driver.find_element("css selector", ".slider-button")');
        console.log('ActionChains(driver).drag_and_drop_by_offset(slider, 200, 0).perform()');
        console.log('```');
        console.log('');
        
        console.log('🔧 技术细节:');
        console.log(`   调试端口: ${DEBUG_PORT}`);
        console.log(`   WebSocket: ${xiaohongshuTab.webSocketDebuggerUrl}`);
        console.log(`   标签页ID: ${xiaohongshuTab.id}`);
        console.log('');
        
        console.log('⚠️ 注意事项:');
        console.log('   1. 滑动速度不要太快，模拟人类操作');
        console.log('   2. 拖动距离需要根据图片缺口位置计算');
        console.log('   3. 可能需要多次尝试才能成功');
        console.log('   4. 小红书有反爬虫机制，避免频繁操作');
        console.log('');
        
        console.log('💡 建议操作顺序:');
        console.log('   1. 🖱️ 先手动尝试操作验证码');
        console.log('   2. 🤖 如果需要自动化，使用上述代码');
        console.log('   3. 🔄 失败时可以刷新验证码重试');
        console.log('');
        
        console.log('🎯 现在你可以:');
        console.log('   - 直接在19号窗口中手动拖动滑块');
        console.log('   - 使用提供的代码进行自动化操作');
        console.log('   - 如果验证失败，刷新页面重新获取验证码');
        
    } catch (error) {
        console.error('❌ 分析失败:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 无法连接到调试端口，请确保19号窗口正在运行');
        }
    }
}

if (require.main === module) {
    analyzeCaptcha();
}

module.exports = { analyzeCaptcha };
