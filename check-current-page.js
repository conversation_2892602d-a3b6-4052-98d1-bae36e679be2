#!/usr/bin/env node

/**
 * 🔍 检查当前页面类型
 * 确认是否在正确的笔记页面
 */

const puppeteer = require('puppeteer');

class CurrentPageChecker {
    constructor() {
        this.debugPort = 55276;
    }

    async checkCurrentPage() {
        console.log('🔍 检查当前页面类型...');
        console.log('');

        let browser = null;

        try {
            console.log('🔗 连接到比特浏览器...');
            browser = await puppeteer.connect({
                browserURL: `http://127.0.0.1:${this.debugPort}`,
                defaultViewport: null
            });

            const pages = await browser.pages();
            const activePage = pages[pages.length - 1]; // 获取最后一个页面

            console.log('✅ 连接成功');
            console.log('📄 当前URL:', activePage.url());
            console.log('');

            // 检查页面类型
            const pageInfo = await activePage.evaluate(() => {
                return {
                    url: window.location.href,
                    title: document.title,
                    isExplorePage: window.location.href.includes('/explore/'),
                    isUserPage: window.location.href.includes('/user/profile/'),
                    hasComments: !!document.querySelector('.comment, [class*="comment"]'),
                    commentCount: document.querySelectorAll('.comment, [class*="comment"]').length,
                    pageType: window.location.href.includes('/explore/') ? '笔记页面' : 
                             window.location.href.includes('/user/profile/') ? '用户主页' : '其他页面'
                };
            });

            console.log('📊 页面信息:');
            console.log(`   页面类型: ${pageInfo.pageType}`);
            console.log(`   页面标题: ${pageInfo.title}`);
            console.log(`   是否为笔记页面: ${pageInfo.isExplorePage ? '✅ 是' : '❌ 否'}`);
            console.log(`   是否为用户主页: ${pageInfo.isUserPage ? '✅ 是' : '❌ 否'}`);
            console.log(`   是否有评论区域: ${pageInfo.hasComments ? '✅ 有' : '❌ 无'}`);
            console.log(`   评论元素数量: ${pageInfo.commentCount}`);
            console.log('');

            if (pageInfo.isExplorePage) {
                console.log('🎉 太好了！你现在在正确的笔记页面');
                console.log('💡 可以运行评论爬取脚本了');
                
                if (pageInfo.hasComments) {
                    console.log('✅ 页面有评论区域，可以开始爬取');
                } else {
                    console.log('⚠️ 页面暂时没有加载评论，可能需要滚动一下');
                }
            } else if (pageInfo.isUserPage) {
                console.log('❌ 当前在用户主页，不是笔记页面');
                console.log('💡 请点击浏览器后退按钮，回到具体的笔记页面');
                console.log('🎯 正确的URL应该包含 /explore/ 而不是 /user/profile/');
            } else {
                console.log('❓ 当前页面类型未知');
                console.log('💡 请确保访问的是小红书的具体笔记页面');
            }

            return pageInfo;

        } catch (error) {
            console.error('❌ 检查失败:', error.message);
            return null;
        } finally {
            if (browser) {
                await browser.disconnect();
            }
        }
    }
}

if (require.main === module) {
    const checker = new CurrentPageChecker();
    checker.checkCurrentPage().catch(console.error);
}

module.exports = CurrentPageChecker;
