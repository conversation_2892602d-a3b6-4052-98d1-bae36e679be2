#!/usr/bin/env node

/**
 * 🔍 检查页面状态
 * 查看当前页面是否有二级回复可以展开
 */

const puppeteer = require('puppeteer');

class PageStatusChecker {
    constructor() {
        this.debugPort = 55276;
    }

    async checkPageStatus() {
        console.log('🔍 检查页面状态...');
        console.log('');

        let browser = null;

        try {
            console.log('🔗 连接到比特浏览器...');
            browser = await puppeteer.connect({
                browserURL: `http://127.0.0.1:${this.debugPort}`,
                defaultViewport: null
            });

            const pages = await browser.pages();
            const xiaohongshuPage = pages.find(page => 
                page.url().includes('xiaohongshu.com/explore/')
            );

            if (!xiaohongshuPage) {
                throw new Error('未找到小红书笔记页面');
            }

            console.log('✅ 找到小红书笔记页面');
            console.log('📄 当前URL:', xiaohongshuPage.url());
            console.log('');

            // 检查页面状态
            const pageStatus = await xiaohongshuPage.evaluate(() => {
                const status = {
                    pageTitle: document.title,
                    currentUrl: window.location.href,
                    totalElements: document.querySelectorAll('*').length,
                    
                    // 检查评论相关元素
                    commentElements: {
                        '.comment-item': document.querySelectorAll('.comment-item').length,
                        '.parent-comment': document.querySelectorAll('.parent-comment').length,
                        '.comment': document.querySelectorAll('.comment').length,
                        '[class*="comment"]': document.querySelectorAll('[class*="comment"]').length
                    },
                    
                    // 检查回复相关元素
                    replyElements: {
                        '.reply-container': document.querySelectorAll('.reply-container').length,
                        '.sub-comment': document.querySelectorAll('.sub-comment').length,
                        '[class*="reply"]': document.querySelectorAll('[class*="reply"]').length,
                        '.show-more': document.querySelectorAll('.show-more').length
                    },
                    
                    // 检查展开按钮
                    expandButtons: [],
                    
                    // 检查包含"展开"文字的元素
                    expandTexts: [],
                    
                    // 检查头像元素
                    avatarElements: document.querySelectorAll('img[src*="avatar"]').length,
                    
                    // 检查用户名元素
                    userElements: document.querySelectorAll('[class*="user"], [class*="name"]').length
                };
                
                // 查找展开按钮
                const allElements = document.querySelectorAll('*');
                
                allElements.forEach((el, index) => {
                    const text = el.textContent?.trim() || '';
                    
                    // 查找包含"展开"的文字
                    if (text.includes('展开') && text.includes('回复')) {
                        status.expandTexts.push({
                            index: index,
                            tagName: el.tagName,
                            className: el.className,
                            text: text,
                            isClickable: el.tagName === 'BUTTON' || 
                                         el.tagName === 'A' ||
                                         !!el.onclick ||
                                         el.getAttribute('role') === 'button' ||
                                         window.getComputedStyle(el).cursor === 'pointer'
                        });
                    }
                    
                    // 查找可能的展开按钮
                    if ((el.tagName === 'BUTTON' || el.tagName === 'A' || el.onclick) && 
                        text && (text.includes('展开') || text.includes('更多') || text.includes('查看'))) {
                        status.expandButtons.push({
                            index: index,
                            tagName: el.tagName,
                            className: el.className,
                            text: text
                        });
                    }
                });
                
                return status;
            });

            // 输出检查结果
            console.log('📊 页面状态检查结果:');
            console.log('');
            
            console.log('📄 基本信息:');
            console.log(`   标题: ${pageStatus.pageTitle}`);
            console.log(`   总元素数: ${pageStatus.totalElements}`);
            console.log(`   头像元素: ${pageStatus.avatarElements} 个`);
            console.log(`   用户元素: ${pageStatus.userElements} 个`);
            console.log('');
            
            console.log('💬 评论元素统计:');
            Object.entries(pageStatus.commentElements).forEach(([selector, count]) => {
                console.log(`   ${selector}: ${count} 个`);
            });
            console.log('');
            
            console.log('🔄 回复元素统计:');
            Object.entries(pageStatus.replyElements).forEach(([selector, count]) => {
                console.log(`   ${selector}: ${count} 个`);
            });
            console.log('');
            
            console.log('🔽 展开相关元素:');
            console.log(`   包含"展开"文字的元素: ${pageStatus.expandTexts.length} 个`);
            pageStatus.expandTexts.forEach((item, i) => {
                console.log(`   ${i + 1}. ${item.tagName}.${item.className}`);
                console.log(`      文字: "${item.text}"`);
                console.log(`      可点击: ${item.isClickable}`);
                console.log('');
            });
            
            console.log(`   可能的展开按钮: ${pageStatus.expandButtons.length} 个`);
            pageStatus.expandButtons.forEach((item, i) => {
                console.log(`   ${i + 1}. ${item.tagName}.${item.className}`);
                console.log(`      文字: "${item.text}"`);
                console.log('');
            });
            
            // 给出建议
            console.log('💡 建议:');
            
            if (pageStatus.expandTexts.length > 0) {
                console.log('   ✅ 发现展开文字，建议尝试点击展开');
                const clickableExpand = pageStatus.expandTexts.filter(item => item.isClickable);
                if (clickableExpand.length > 0) {
                    console.log(`   ✅ 其中 ${clickableExpand.length} 个可直接点击`);
                } else {
                    console.log('   ⚠️ 但没有直接可点击的元素，需要查找父元素');
                }
            } else {
                console.log('   ⚠️ 未发现展开文字，可能所有回复已展开');
            }
            
            if (pageStatus.replyElements['.reply-container'] > 0 || 
                pageStatus.replyElements['[class*="reply"]'] > 0) {
                console.log('   ✅ 发现回复容器，建议直接爬取回复内容');
            } else {
                console.log('   ⚠️ 未发现明显的回复容器');
            }
            
            const totalCommentElements = Math.max(...Object.values(pageStatus.commentElements));
            if (totalCommentElements > 0) {
                console.log(`   ✅ 发现 ${totalCommentElements} 个评论元素，可以爬取一级评论`);
            } else {
                console.log('   ⚠️ 未发现明显的评论元素');
            }

            return pageStatus;

        } catch (error) {
            console.error('❌ 检查失败:', error.message);
            return null;
        } finally {
            if (browser) {
                await browser.disconnect();
            }
        }
    }
}

if (require.main === module) {
    const checker = new PageStatusChecker();
    checker.checkPageStatus().catch(console.error);
}

module.exports = PageStatusChecker;
