// ===== 比特浏览器连接检查工具 =====

const axios = require('axios');

// 比特浏览器配置
const BITBROWSER_CONFIG = {
    api_url: "http://127.0.0.1:56906",
    api_token: "ca28ee5ca6de4d209182a83aa16a2044",
    browser_19_id: "e3afefd184384c3f90c78b6b19309ca0"
};

async function checkBitBrowser() {
    console.log('🔍 检查比特浏览器连接状态...\n');
    
    // 1. 测试API连接
    console.log('1️⃣ 测试API连接...');
    try {
        const response = await axios.post(`${BITBROWSER_CONFIG.api_url}/browser/list`, {
            page: 1,
            pageSize: 10
        }, {
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': BITBROWSER_CONFIG.api_token
            },
            timeout: 5000
        });
        
        console.log('✅ API连接成功');
        console.log('📊 API响应:', JSON.stringify(response.data, null, 2));

        if (response.data && response.data.success) {
            const browsers = response.data.data?.list || response.data.data || [];
            console.log(`📋 找到 ${browsers.length} 个浏览器实例`);
            
            // 2. 查找目标浏览器
            console.log('\n2️⃣ 查找目标浏览器...');
            const targetBrowser = browsers.find(b => b.id === BITBROWSER_CONFIG.browser_19_id);
            
            if (targetBrowser) {
                console.log('✅ 找到目标浏览器');
                console.log(`   ID: ${targetBrowser.id}`);
                console.log(`   名称: ${targetBrowser.name}`);
                console.log(`   状态: ${targetBrowser.status}`);
                console.log(`   调试端口: ${targetBrowser.debug_port || '未设置'}`);
                
                if (targetBrowser.status === 'running') {
                    console.log('✅ 浏览器正在运行');
                    
                    // 3. 测试DevTools连接
                    await testDevToolsConnection(targetBrowser.debug_port || 9222);
                } else {
                    console.log('⚠️ 浏览器未运行');
                    console.log('💡 请在比特浏览器中启动此浏览器实例');
                }
            } else {
                console.log('❌ 未找到目标浏览器');
                console.log('💡 可用的浏览器实例：');
                browsers.forEach(browser => {
                    console.log(`   - ${browser.name} (ID: ${browser.id}, 状态: ${browser.status})`);
                });
            }
        } else {
            console.log('❌ API响应格式错误');
        }
        
    } catch (error) {
        console.log('❌ API连接失败');
        console.log(`   错误: ${error.message}`);
        console.log('\n💡 可能的原因：');
        console.log('   - 比特浏览器未启动');
        console.log('   - API端口不是54345');
        console.log('   - API Token不正确');
        console.log('   - 防火墙阻止连接');
    }
}

async function testDevToolsConnection(debugPort) {
    console.log('\n3️⃣ 测试DevTools连接...');
    
    try {
        const response = await axios.get(`http://127.0.0.1:${debugPort}/json`, {
            timeout: 5000
        });
        
        const tabs = response.data;
        console.log('✅ DevTools连接成功');
        console.log(`📋 找到 ${tabs.length} 个标签页`);
        
        // 查找小红书标签页
        const xiaohongshuTabs = tabs.filter(tab => 
            tab.url && (
                tab.url.includes('xiaohongshu.com') || 
                tab.title.includes('小红书')
            )
        );
        
        if (xiaohongshuTabs.length > 0) {
            console.log(`🎯 找到 ${xiaohongshuTabs.length} 个小红书标签页：`);
            xiaohongshuTabs.forEach((tab, index) => {
                console.log(`   ${index + 1}. ${tab.title}`);
                console.log(`      URL: ${tab.url}`);
            });
            
            console.log('\n🎉 系统已准备就绪，可以采集小红书数据！');
        } else {
            console.log('⚠️ 未找到小红书标签页');
            console.log('💡 请在浏览器中打开小红书网站：');
            console.log('   - https://creator.xiaohongshu.com/new/home (创作中心)');
            console.log('   - https://www.xiaohongshu.com (主站)');
            
            console.log('\n📋 当前打开的标签页：');
            tabs.slice(0, 5).forEach((tab, index) => {
                console.log(`   ${index + 1}. ${tab.title || '无标题'}`);
                console.log(`      URL: ${tab.url || '无URL'}`);
            });
            if (tabs.length > 5) {
                console.log(`   ... 还有 ${tabs.length - 5} 个标签页`);
            }
        }
        
    } catch (error) {
        console.log('❌ DevTools连接失败');
        console.log(`   错误: ${error.message}`);
        console.log('💡 可能的原因：');
        console.log('   - 调试端口未开启');
        console.log('   - 端口号不正确');
        console.log('   - 浏览器安全设置阻止连接');
    }
}

// 运行检查
if (require.main === module) {
    checkBitBrowser().then(() => {
        console.log('\n🔍 检查完成');
    }).catch(error => {
        console.error('\n❌ 检查过程出错:', error.message);
    });
}

module.exports = checkBitBrowser;
