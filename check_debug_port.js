/**
 * 🔍 调试端口检查器
 * 检查比特浏览器调试端口提供的服务
 */

const axios = require('axios');

async function checkDebugPort(port) {
    console.log(`🔍 检查调试端口 ${port}...`);
    
    // 尝试访问不同的端点
    const endpoints = [
        `http://localhost:${port}`,
        `http://localhost:${port}/json`,
        `http://localhost:${port}/json/list`,
        `http://localhost:${port}/json/version`,
        `http://localhost:${port}/devtools/inspector.html`,
        `http://127.0.0.1:${port}`,
        `http://127.0.0.1:${port}/json`,
        `http://127.0.0.1:${port}/json/list`,
        `http://127.0.0.1:${port}/json/version`
    ];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`\n📡 尝试访问: ${endpoint}`);
            const response = await axios.get(endpoint, { timeout: 5000 });
            
            console.log(`   ✅ 状态码: ${response.status}`);
            console.log(`   📄 内容类型: ${response.headers['content-type']}`);
            
            if (response.data) {
                if (typeof response.data === 'string') {
                    console.log(`   📝 响应内容 (前200字符): ${response.data.substring(0, 200)}...`);
                } else {
                    console.log(`   📝 响应内容:`, JSON.stringify(response.data, null, 2));
                }
            }
            
            // 如果是JSON列表，查找WebSocket URL
            if (endpoint.includes('/json') && Array.isArray(response.data)) {
                console.log(`   🎯 找到 ${response.data.length} 个页面/标签:`);
                response.data.forEach((page, index) => {
                    console.log(`      ${index + 1}. ${page.title || 'Untitled'}`);
                    console.log(`         URL: ${page.url}`);
                    if (page.webSocketDebuggerUrl) {
                        console.log(`         WebSocket: ${page.webSocketDebuggerUrl}`);
                    }
                });
            }
            
        } catch (error) {
            if (error.code === 'ECONNREFUSED') {
                console.log(`   ❌ 连接被拒绝`);
            } else if (error.response) {
                console.log(`   ❌ HTTP错误: ${error.response.status} ${error.response.statusText}`);
            } else {
                console.log(`   ❌ 请求失败: ${error.message}`);
            }
        }
    }
}

async function getBitBrowserPorts() {
    console.log('🔌 获取比特浏览器调试端口...');
    
    try {
        const response = await axios.post('http://127.0.0.1:56906/browser/ports');
        
        if (response.data.success) {
            const ports = response.data.data;
            console.log(`✅ 获取到 ${Object.keys(ports).length} 个调试端口:`);
            
            for (const [browserId, port] of Object.entries(ports)) {
                console.log(`   浏览器 ${browserId.substring(0, 8)}... -> 端口 ${port}`);
            }
            
            return Object.values(ports);
        } else {
            throw new Error(`获取端口失败: ${response.data.msg}`);
        }
    } catch (error) {
        console.error('❌ 获取调试端口失败:', error.message);
        return [];
    }
}

async function main() {
    console.log('🎯 比特浏览器调试端口检查器');
    console.log('='.repeat(50));
    
    // 1. 获取所有调试端口
    const ports = await getBitBrowserPorts();
    
    if (ports.length === 0) {
        console.log('❌ 未获取到任何调试端口');
        return;
    }
    
    // 2. 检查每个端口
    for (const port of ports) {
        console.log('\n' + '='.repeat(30));
        await checkDebugPort(port);
    }
    
    console.log('\n' + '='.repeat(50));
    console.log('🎯 检查完成');
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { checkDebugPort, getBitBrowserPorts };
