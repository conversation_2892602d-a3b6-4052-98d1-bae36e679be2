#!/usr/bin/env node

/**
 * 🖱️ 自动点击笔记采集器
 * 自动点击每篇笔记，进入详情页面采集完整数据
 */

const axios = require('axios');
const WebSocket = require('ws');

class ClickNotesCollector {
    constructor() {
        this.debugPort = null;
        this.collectedNotes = [];
        this.currentNoteIndex = 0;
    }

    // 🖱️ 主要采集方法
    async collectByClicking() {
        console.log('🖱️ 开始点击笔记采集详细数据...\n');

        try {
            // 1. 获取调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 目标页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 获取笔记列表
            console.log('📋 获取笔记列表...');
            const notesList = await this.getNotesList(tab);
            console.log(`✅ 找到 ${notesList.length} 篇笔记\n`);

            // 4. 逐个点击采集
            console.log('🖱️ 开始逐个点击采集...');
            for (let i = 0; i < Math.min(notesList.length, 5); i++) { // 限制前5篇，避免过长
                console.log(`📝 采集第 ${i + 1} 篇笔记...`);
                const noteData = await this.clickAndCollectNote(tab, i);
                if (noteData) {
                    this.collectedNotes.push(noteData);
                    console.log(`✅ 第 ${i + 1} 篇笔记采集完成`);
                } else {
                    console.log(`⚠️ 第 ${i + 1} 篇笔记采集失败`);
                }
                
                // 等待一下避免操作过快
                await this.sleep(2000);
            }

            // 5. 返回结果
            const result = {
                timestamp: new Date().toISOString(),
                url: tab.url,
                title: tab.title,
                notes: this.collectedNotes,
                summary: {
                    totalNotes: this.collectedNotes.length,
                    totalLikes: this.collectedNotes.reduce((sum, note) => sum + (note.likes || 0), 0),
                    totalCollects: this.collectedNotes.reduce((sum, note) => sum + (note.collects || 0), 0),
                    totalComments: this.collectedNotes.reduce((sum, note) => sum + (note.comments || 0), 0),
                    totalViews: this.collectedNotes.reduce((sum, note) => sum + (note.views || 0), 0)
                },
                dataSource: 'click_collection',
                extractionMethod: 'auto_click_and_extract'
            };

            return result;

        } catch (error) {
            console.error('❌ 点击采集失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取调试端口
    async getCurrentDebugPort() {
        try {
            // 直接使用比特浏览器API启动浏览器
            const response = await axios.post('http://127.0.0.1:56906/browser/open', {
                id: "e3afefd184384c3f90c78b6b19309ca0"
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': 'ca28ee5ca6de4d209182a83aa16a2044'
                },
                timeout: 30000
            });

            if (response.data.success && response.data.data) {
                const debugPort = response.data.data.debug_port || response.data.data.selenium_port;
                if (debugPort) {
                    this.debugPort = debugPort;
                    console.log(`✅ 调试端口: ${this.debugPort}`);
                    return;
                }
            }

            // 备用端口扫描
            const commonPorts = [63524, 51859, 58222, 9222];
            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到端口: ${port}`);
                    return;
                } catch (error) {
                    // 继续尝试
                }
            }

            throw new Error('未找到可用端口');
        } catch (error) {
            throw new Error(`获取端口失败: ${error.message}`);
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
            timeout: 5000
        });

        return response.data.find(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.url.includes('creator.xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );
    }

    // 📋 获取笔记列表
    async getNotesList(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const getNotesScript = `
                        (function() {
                            const notesList = [];
                            
                            // 查找所有笔记链接
                            const noteLinks = document.querySelectorAll('a[href*="/explore/"]');
                            
                            noteLinks.forEach((link, index) => {
                                const noteContainer = link.closest('div');
                                if (noteContainer) {
                                    notesList.push({
                                        index: index,
                                        href: link.href,
                                        title: link.textContent.trim() || '无标题',
                                        element: {
                                            className: noteContainer.className,
                                            id: noteContainer.id || 'note_' + index
                                        }
                                    });
                                }
                            });
                            
                            return notesList;
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: getNotesScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const notesList = message.result.result.value;
                        ws.close();
                        resolve(notesList);
                    }
                } catch (error) {
                    reject(error);
                }
            });

            ws.on('error', reject);
            setTimeout(() => {
                ws.close();
                reject(new Error('获取笔记列表超时'));
            }, 15000);
        });
    }

    // 🖱️ 点击并采集单篇笔记
    async clickAndCollectNote(tab, noteIndex) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;
            let step = 'click';

            ws.on('open', () => {
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                // 启用页面域以便监听导航
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Page.enable'
                }));

                setTimeout(() => {
                    this.clickNote(ws, requestId++, noteIndex);
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.method === 'Page.frameNavigated') {
                        // 页面导航完成，开始采集数据
                        console.log('   📄 页面加载完成，开始采集数据...');
                        setTimeout(() => {
                            this.collectNoteDetails(ws, requestId++);
                            step = 'collect';
                        }, 3000); // 等待页面完全加载
                    }
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (step === 'click' && result.clicked) {
                            console.log('   🖱️ 点击成功，等待页面加载...');
                        } else if (step === 'collect' && result.noteData) {
                            console.log('   📊 数据采集成功');
                            
                            // 返回上一页
                            setTimeout(() => {
                                this.goBack(ws, requestId++);
                            }, 1000);
                            
                            setTimeout(() => {
                                ws.close();
                                resolve(result.noteData);
                            }, 3000);
                        }
                    }
                } catch (error) {
                    console.error('   ❌ 处理消息失败:', error.message);
                }
            });

            ws.on('error', (error) => {
                console.error('   ❌ WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('点击采集超时'));
            }, 30000);
        });
    }

    // 🖱️ 点击笔记
    clickNote(ws, requestId, noteIndex) {
        const clickScript = `
            (function() {
                try {
                    const noteLinks = document.querySelectorAll('a[href*="/explore/"]');
                    if (noteLinks[${noteIndex}]) {
                        noteLinks[${noteIndex}].click();
                        return { clicked: true, href: noteLinks[${noteIndex}].href };
                    } else {
                        return { clicked: false, error: '未找到指定笔记' };
                    }
                } catch (error) {
                    return { clicked: false, error: error.message };
                }
            })();
        `;

        ws.send(JSON.stringify({
            id: requestId,
            method: 'Runtime.evaluate',
            params: {
                expression: clickScript,
                returnByValue: true
            }
        }));
    }

    // 📊 采集笔记详情
    collectNoteDetails(ws, requestId) {
        const collectScript = `
            (function() {
                try {
                    const noteData = {
                        url: window.location.href,
                        title: '',
                        content: '',
                        author: '',
                        publishTime: '',
                        likes: 0,
                        collects: 0,
                        comments: 0,
                        views: 0,
                        tags: [],
                        images: []
                    };

                    // 提取标题
                    const titleSelectors = [
                        'h1', '.note-title', '[class*="title"]', '.content-title'
                    ];
                    for (const selector of titleSelectors) {
                        const titleEl = document.querySelector(selector);
                        if (titleEl && titleEl.textContent.trim()) {
                            noteData.title = titleEl.textContent.trim();
                            break;
                        }
                    }

                    // 提取内容
                    const contentSelectors = [
                        '.note-content', '.content', '[class*="content"]', '.desc'
                    ];
                    for (const selector of contentSelectors) {
                        const contentEl = document.querySelector(selector);
                        if (contentEl && contentEl.textContent.trim()) {
                            noteData.content = contentEl.textContent.trim();
                            break;
                        }
                    }

                    // 提取作者
                    const authorSelectors = [
                        '.author', '.username', '[class*="author"]', '[class*="user"]'
                    ];
                    for (const selector of authorSelectors) {
                        const authorEl = document.querySelector(selector);
                        if (authorEl && authorEl.textContent.trim()) {
                            noteData.author = authorEl.textContent.trim();
                            break;
                        }
                    }

                    // 提取互动数据 - 更精确的方法
                    const allElements = document.querySelectorAll('*');
                    allElements.forEach(el => {
                        const text = el.textContent.trim();
                        const number = parseInt(text.replace(/[^0-9]/g, ''));
                        
                        if (!isNaN(number) && number > 0) {
                            const context = el.parentElement ? el.parentElement.textContent : '';
                            const className = el.className.toLowerCase();
                            
                            if (text.includes('赞') || text.includes('❤️') || text.includes('👍') || 
                                className.includes('like') || className.includes('heart')) {
                                noteData.likes = Math.max(noteData.likes, number);
                            } else if (text.includes('收藏') || text.includes('💖') || 
                                      className.includes('collect') || className.includes('favorite')) {
                                noteData.collects = Math.max(noteData.collects, number);
                            } else if (text.includes('评论') || text.includes('💬') || 
                                      className.includes('comment')) {
                                noteData.comments = Math.max(noteData.comments, number);
                            } else if (text.includes('浏览') || text.includes('👀') || 
                                      className.includes('view')) {
                                noteData.views = Math.max(noteData.views, number);
                            }
                        }
                    });

                    // 提取图片
                    const images = document.querySelectorAll('img');
                    images.forEach(img => {
                        if (img.src && img.src.includes('xhscdn.com') && 
                            !img.src.includes('avatar') && !img.src.includes('icon')) {
                            noteData.images.push({
                                src: img.src,
                                alt: img.alt || '',
                                width: img.naturalWidth || img.width || 0,
                                height: img.naturalHeight || img.height || 0
                            });
                        }
                    });

                    // 提取标签
                    const tagElements = document.querySelectorAll('[class*="tag"], .hashtag, [class*="topic"]');
                    tagElements.forEach(tagEl => {
                        const tagText = tagEl.textContent.trim();
                        if (tagText && tagText.length > 0 && tagText.length < 50) {
                            noteData.tags.push(tagText);
                        }
                    });

                    // 提取发布时间
                    const timeElements = document.querySelectorAll('*');
                    timeElements.forEach(el => {
                        const text = el.textContent.trim();
                        if (text.match(/\\d{4}-\\d{2}-\\d{2}|\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天/)) {
                            noteData.publishTime = text;
                        }
                    });

                    return { noteData: noteData };
                    
                } catch (error) {
                    return { 
                        noteData: null, 
                        error: error.message,
                        url: window.location.href 
                    };
                }
            })();
        `;

        ws.send(JSON.stringify({
            id: requestId,
            method: 'Runtime.evaluate',
            params: {
                expression: collectScript,
                returnByValue: true
            }
        }));
    }

    // ⬅️ 返回上一页
    goBack(ws, requestId) {
        const goBackScript = `
            window.history.back();
            return { goBack: true };
        `;

        ws.send(JSON.stringify({
            id: requestId,
            method: 'Runtime.evaluate',
            params: {
                expression: goBackScript,
                returnByValue: true
            }
        }));
    }

    // 💤 等待函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 🧪 测试点击采集
async function testClickCollection() {
    const collector = new ClickNotesCollector();
    
    try {
        const data = await collector.collectByClicking();
        
        console.log('\n🖱️ 点击采集结果:');
        console.log('=' * 60);
        console.log(`📝 总笔记数: ${data.summary.totalNotes}`);
        console.log(`👍 总点赞数: ${data.summary.totalLikes}`);
        console.log(`💖 总收藏数: ${data.summary.totalCollects}`);
        console.log(`💬 总评论数: ${data.summary.totalComments}`);
        console.log(`👀 总浏览数: ${data.summary.totalViews}\n`);
        
        // 显示每篇笔记的详细信息
        data.notes.forEach((note, index) => {
            console.log(`📝 笔记 ${index + 1}:`);
            console.log(`   标题: ${note.title || '无标题'}`);
            console.log(`   内容: ${note.content ? note.content.substring(0, 100) + '...' : '无内容'}`);
            console.log(`   作者: ${note.author || '未知'}`);
            console.log(`   👍 点赞: ${note.likes}`);
            console.log(`   💖 收藏: ${note.collects}`);
            console.log(`   💬 评论: ${note.comments}`);
            console.log(`   👀 浏览: ${note.views}`);
            console.log(`   🖼️  图片数量: ${note.images.length}`);
            console.log(`   🏷️  标签数量: ${note.tags.length}`);
            if (note.publishTime) {
                console.log(`   📅 发布时间: ${note.publishTime}`);
            }
            console.log(`   🔗 链接: ${note.url}`);
            console.log('');
        });
        
        return data;
        
    } catch (error) {
        console.error('❌ 点击采集失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    testClickCollection().catch(console.error);
}

module.exports = { ClickNotesCollector, testClickCollection };
