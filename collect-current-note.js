#!/usr/bin/env node

/**
 * 📊 采集当前笔记页面数据
 * 直接从当前打开的笔记详情页面采集完整数据
 */

const axios = require('axios');
const WebSocket = require('ws');

class CurrentNoteCollector {
    constructor() {
        this.debugPort = null;
    }

    // 📊 采集当前页面数据
    async collectCurrentNote() {
        console.log('📊 开始采集当前笔记页面数据...\n');

        try {
            // 1. 获取调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 当前页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 采集当前页面数据
            const noteData = await this.extractCurrentPageData(tab);
            
            return noteData;

        } catch (error) {
            console.error('❌ 采集失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取调试端口
    async getCurrentDebugPort() {
        try {
            const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 10000
            });

            if (response.data.success && response.data.data && response.data.data.result) {
                const httpInfo = response.data.data.result.http;
                if (httpInfo) {
                    this.debugPort = httpInfo.split(':')[1];
                    console.log(`✅ 调试端口: ${this.debugPort}`);
                    return;
                }
            }

            const commonPorts = [63524, 51859, 58222, 9222];
            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到端口: ${port}`);
                    return;
                } catch (error) {
                    // 继续尝试
                }
            }

            throw new Error('未找到可用端口');
        } catch (error) {
            throw new Error(`获取端口失败: ${error.message}`);
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
            timeout: 5000
        });

        return response.data.find(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.url.includes('creator.xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );
    }

    // 📊 提取当前页面数据
    async extractCurrentPageData(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const extractScript = `
                        (function() {
                            try {
                                const noteData = {
                                    url: window.location.href,
                                    title: '',
                                    content: '',
                                    author: '',
                                    publishTime: '',
                                    likes: 0,
                                    collects: 0,
                                    comments: 0,
                                    views: 0,
                                    shares: 0,
                                    tags: [],
                                    images: [],
                                    timestamp: new Date().toISOString(),
                                    pageType: '',
                                    debug: {
                                        pageTitle: document.title,
                                        totalElements: document.querySelectorAll('*').length,
                                        totalImages: document.querySelectorAll('img').length,
                                        totalSpans: document.querySelectorAll('span').length
                                    }
                                };

                                // 判断页面类型
                                if (window.location.href.includes('/explore/')) {
                                    noteData.pageType = 'note_detail';
                                } else if (window.location.href.includes('/user/profile/')) {
                                    noteData.pageType = 'user_profile';
                                } else {
                                    noteData.pageType = 'other';
                                }

                                // 提取标题 - 多种策略
                                const titleStrategies = [
                                    () => document.querySelector('h1')?.textContent?.trim(),
                                    () => document.querySelector('h2')?.textContent?.trim(),
                                    () => document.querySelector('.note-title')?.textContent?.trim(),
                                    () => document.querySelector('[class*="title"]')?.textContent?.trim(),
                                    () => document.querySelector('[data-testid="note-title"]')?.textContent?.trim(),
                                    () => {
                                        const pageTitle = document.title.replace(' - 小红书', '').trim();
                                        return pageTitle.length > 0 ? pageTitle : null;
                                    }
                                ];
                                
                                for (const strategy of titleStrategies) {
                                    const title = strategy();
                                    if (title && title.length > 3) {
                                        noteData.title = title;
                                        break;
                                    }
                                }

                                // 提取内容 - 多种策略
                                const contentStrategies = [
                                    () => document.querySelector('.note-content')?.textContent?.trim(),
                                    () => document.querySelector('.content')?.textContent?.trim(),
                                    () => document.querySelector('[class*="content"]')?.textContent?.trim(),
                                    () => document.querySelector('.desc')?.textContent?.trim(),
                                    () => document.querySelector('[data-testid="note-content"]')?.textContent?.trim(),
                                    () => document.querySelector('.note-text')?.textContent?.trim(),
                                    () => {
                                        // 查找包含较多文字的div
                                        const divs = document.querySelectorAll('div');
                                        for (const div of divs) {
                                            const text = div.textContent.trim();
                                            if (text.length > 50 && text.length < 2000 && 
                                                div.children.length < 5) {
                                                return text;
                                            }
                                        }
                                        return null;
                                    }
                                ];
                                
                                for (const strategy of contentStrategies) {
                                    const content = strategy();
                                    if (content && content.length > 10) {
                                        noteData.content = content;
                                        break;
                                    }
                                }

                                // 提取作者信息
                                const authorStrategies = [
                                    () => document.querySelector('.author')?.textContent?.trim(),
                                    () => document.querySelector('.username')?.textContent?.trim(),
                                    () => document.querySelector('[class*="author"]')?.textContent?.trim(),
                                    () => document.querySelector('[class*="user"]')?.textContent?.trim(),
                                    () => document.querySelector('.user-name')?.textContent?.trim(),
                                    () => document.querySelector('.nickname')?.textContent?.trim()
                                ];
                                
                                for (const strategy of authorStrategies) {
                                    const author = strategy();
                                    if (author && author.length > 0 && author.length < 50) {
                                        noteData.author = author;
                                        break;
                                    }
                                }

                                // 精确提取互动数据
                                const allElements = document.querySelectorAll('*');
                                const interactionData = {
                                    likes: [],
                                    collects: [],
                                    comments: [],
                                    views: [],
                                    shares: []
                                };

                                allElements.forEach(el => {
                                    const text = el.textContent.trim();
                                    
                                    // 匹配数字（包括带单位的）
                                    const numberMatches = text.match(/^(\\d+(?:\\.\\d+)?)(万|k|K|w|W)?$/);
                                    if (numberMatches) {
                                        let number = parseFloat(numberMatches[1]);
                                        const unit = numberMatches[2];
                                        
                                        if (unit === '万' || unit === 'w' || unit === 'W') {
                                            number *= 10000;
                                        } else if (unit === 'k' || unit === 'K') {
                                            number *= 1000;
                                        }
                                        
                                        const parent = el.parentElement;
                                        const grandParent = parent ? parent.parentElement : null;
                                        const context = (parent?.textContent || '') + ' ' + (grandParent?.textContent || '');
                                        const className = (el.className + ' ' + (parent?.className || '')).toLowerCase();
                                        
                                        // 分类存储
                                        if (context.includes('赞') || context.includes('❤️') || context.includes('👍') || 
                                            className.includes('like') || className.includes('heart')) {
                                            interactionData.likes.push(Math.round(number));
                                        } else if (context.includes('收藏') || context.includes('💖') || 
                                                  className.includes('collect') || className.includes('favorite')) {
                                            interactionData.collects.push(Math.round(number));
                                        } else if (context.includes('评论') || context.includes('💬') || 
                                                  className.includes('comment')) {
                                            interactionData.comments.push(Math.round(number));
                                        } else if (context.includes('浏览') || context.includes('👀') || 
                                                  className.includes('view')) {
                                            interactionData.views.push(Math.round(number));
                                        } else if (context.includes('分享') || context.includes('转发') || 
                                                  className.includes('share')) {
                                            interactionData.shares.push(Math.round(number));
                                        }
                                    }
                                });

                                // 取最大值作为最终结果
                                noteData.likes = Math.max(0, ...interactionData.likes);
                                noteData.collects = Math.max(0, ...interactionData.collects);
                                noteData.comments = Math.max(0, ...interactionData.comments);
                                noteData.views = Math.max(0, ...interactionData.views);
                                noteData.shares = Math.max(0, ...interactionData.shares);

                                // 提取图片
                                const images = document.querySelectorAll('img');
                                images.forEach(img => {
                                    if (img.src && img.src.includes('xhscdn.com') && 
                                        !img.src.includes('avatar') && !img.src.includes('icon') &&
                                        img.naturalWidth > 50) {
                                        noteData.images.push({
                                            src: img.src,
                                            alt: img.alt || '',
                                            width: img.naturalWidth || img.width || 0,
                                            height: img.naturalHeight || img.height || 0
                                        });
                                    }
                                });

                                // 提取标签
                                const tagSelectors = [
                                    '[class*="tag"]', '.hashtag', '[class*="topic"]', '.tag',
                                    '[data-testid="tag"]', '.note-tag', '[class*="label"]'
                                ];
                                
                                tagSelectors.forEach(selector => {
                                    const tagElements = document.querySelectorAll(selector);
                                    tagElements.forEach(tagEl => {
                                        const tagText = tagEl.textContent.trim();
                                        if (tagText && tagText.length > 0 && tagText.length < 50 && 
                                            !noteData.tags.includes(tagText) &&
                                            !tagText.match(/^\\d+$/)) { // 排除纯数字
                                            noteData.tags.push(tagText);
                                        }
                                    });
                                });

                                // 提取发布时间
                                const timeElements = document.querySelectorAll('*');
                                timeElements.forEach(el => {
                                    const text = el.textContent.trim();
                                    const timePattern = /\\d{4}-\\d{2}-\\d{2}|\\d{4}\\/\\d{2}\\/\\d{2}|\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}:\\d{2}/;
                                    if (timePattern.test(text)) {
                                        if (!noteData.publishTime || text.length > noteData.publishTime.length) {
                                            noteData.publishTime = text;
                                        }
                                    }
                                });

                                // 添加调试信息
                                noteData.debug.interactionData = interactionData;
                                noteData.debug.extractedTitle = noteData.title;
                                noteData.debug.extractedContent = noteData.content ? noteData.content.substring(0, 100) + '...' : 'none';

                                return { success: true, noteData: noteData };
                                
                            } catch (error) {
                                return { 
                                    success: false,
                                    error: error.message,
                                    stack: error.stack,
                                    url: window.location.href 
                                };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: extractScript,
                            returnByValue: true
                        }
                    }));
                }, 2000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            console.log('✅ 数据采集成功');
                            ws.close();
                            resolve(result.noteData);
                        } else {
                            console.log('❌ 数据采集失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理消息失败:', error.message);
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('数据采集超时'));
            }, 30000);
        });
    }
}

// 🧪 测试当前页面采集
async function testCurrentNoteCollection() {
    const collector = new CurrentNoteCollector();
    
    try {
        const noteData = await collector.collectCurrentNote();
        
        console.log('\n📊 当前笔记数据采集结果:');
        console.log('=' * 60);
        console.log(`📄 页面类型: ${noteData.pageType}`);
        console.log(`📝 标题: ${noteData.title || '无标题'}`);
        console.log(`👤 作者: ${noteData.author || '未知'}`);
        console.log(`📄 内容: ${noteData.content ? noteData.content.substring(0, 200) + '...' : '无内容'}`);
        console.log(`👍 点赞: ${noteData.likes}`);
        console.log(`💖 收藏: ${noteData.collects}`);
        console.log(`💬 评论: ${noteData.comments}`);
        console.log(`👀 浏览: ${noteData.views}`);
        console.log(`📤 分享: ${noteData.shares}`);
        console.log(`🖼️  图片数量: ${noteData.images.length}`);
        console.log(`🏷️  标签数量: ${noteData.tags.length}`);
        if (noteData.publishTime) {
            console.log(`📅 发布时间: ${noteData.publishTime}`);
        }
        console.log(`🔗 笔记链接: ${noteData.url}`);
        
        if (noteData.tags.length > 0) {
            console.log(`🏷️  标签: ${noteData.tags.slice(0, 5).join(', ')}${noteData.tags.length > 5 ? '...' : ''}`);
        }
        
        console.log('\n🔧 调试信息:');
        console.log(`   页面标题: ${noteData.debug.pageTitle}`);
        console.log(`   总元素数: ${noteData.debug.totalElements}`);
        console.log(`   图片数: ${noteData.debug.totalImages}`);
        console.log(`   提取的标题: ${noteData.debug.extractedTitle || '无'}`);
        console.log(`   提取的内容: ${noteData.debug.extractedContent || '无'}`);
        
        return noteData;
        
    } catch (error) {
        console.error('❌ 当前页面采集失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    testCurrentNoteCollection().catch(console.error);
}

module.exports = { CurrentNoteCollector, testCurrentNoteCollection };
