#!/usr/bin/env node

/**
 * 💬 笔记评论采集器
 * 自动采集小红书笔记的所有评论数据
 */

const axios = require('axios');
const WebSocket = require('ws');

class CommentsCollector {
    constructor() {
        this.debugPort = null;
    }

    // 💬 主要采集方法
    async collectComments() {
        console.log('💬 开始采集笔记评论数据...\n');

        try {
            // 1. 获取调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 当前页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 检查是否在笔记详情页
            if (!tab.url.includes('/explore/')) {
                console.log('⚠️ 当前不在笔记详情页，尝试导航到笔记页面...');
                // 这里可以添加导航逻辑
            }

            // 4. 滚动加载评论
            console.log('📜 滚动加载所有评论...');
            await this.scrollToLoadComments(tab);

            // 5. 采集评论数据
            console.log('💬 开始采集评论数据...');
            const commentsData = await this.extractCommentsData(tab);
            
            return commentsData;

        } catch (error) {
            console.error('❌ 评论采集失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取调试端口
    async getCurrentDebugPort() {
        try {
            const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 10000
            });

            if (response.data.success && response.data.data && response.data.data.result) {
                const httpInfo = response.data.data.result.http;
                if (httpInfo) {
                    this.debugPort = httpInfo.split(':')[1];
                    console.log(`✅ 调试端口: ${this.debugPort}`);
                    return;
                }
            }

            const commonPorts = [63524, 51859, 58222, 9222];
            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到端口: ${port}`);
                    return;
                } catch (error) {
                    // 继续尝试
                }
            }

            throw new Error('未找到可用端口');
        } catch (error) {
            throw new Error(`获取端口失败: ${error.message}`);
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
            timeout: 5000
        });

        return response.data.find(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.url.includes('creator.xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );
    }

    // 📜 滚动加载评论
    async scrollToLoadComments(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;
            let scrollCount = 0;
            const maxScrolls = 100; // 增加到100次滚动
            let lastCommentCount = 0;
            let noChangeCount = 0;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                // 开始滚动加载评论
                setTimeout(() => {
                    this.performCommentScroll(ws, requestId++, scrollCount, maxScrolls, lastCommentCount, noChangeCount, resolve, reject);
                }, 2000);
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('评论滚动超时'));
            }, 300000); // 增加到5分钟超时
        });
    }

    // 🔄 执行评论区滚动
    performCommentScroll(ws, requestId, scrollCount, maxScrolls, lastCommentCount, noChangeCount, resolve, reject) {
        // 如果连续5次滚动评论数量没有变化，或者达到最大滚动次数，则停止
        if (scrollCount >= maxScrolls || noChangeCount >= 5) {
            console.log(`✅ 完成 ${scrollCount} 次评论区滚动 (无变化次数: ${noChangeCount})`);
            ws.close();
            resolve();
            return;
        }

        scrollCount++;
        console.log(`📜 第 ${scrollCount} 次滚动评论区...`);

        const scrollScript = `
            (function() {
                try {
                    // 🎯 精确评论区域滚动策略

                    // 1. 首先查找评论容器
                    let commentContainer = null;
                    const containerSelectors = [
                        '.comments-container', '.comment-list', '.interaction-container',
                        '[class*="comment-section"]', '[class*="comment-wrapper"]'
                    ];

                    for (const selector of containerSelectors) {
                        const containers = document.querySelectorAll(selector);
                        for (const container of containers) {
                            if (container.scrollHeight > container.clientHeight) {
                                commentContainer = container;
                                console.log('找到可滚动评论容器:', selector);
                                break;
                            }
                        }
                        if (commentContainer) break;
                    }

                    // 如果没找到专门容器，查找评论密集区域
                    if (!commentContainer) {
                        const allDivs = document.querySelectorAll('div');
                        let maxComments = 0;
                        allDivs.forEach(div => {
                            const avatars = div.querySelectorAll('img[src*="avatar"]');
                            if (avatars.length > maxComments) {
                                maxComments = avatars.length;
                                commentContainer = div;
                            }
                        });
                        if (commentContainer) {
                            console.log('找到评论密集区域，包含', maxComments, '个头像');
                        }
                    }

                    // 2. 滚动评论容器或页面
                    if (commentContainer && commentContainer.scrollHeight > commentContainer.clientHeight) {
                        commentContainer.scrollTop = commentContainer.scrollHeight;
                        console.log('滚动评论容器到底部');
                    } else {
                        window.scrollTo(0, document.body.scrollHeight);
                        console.log('滚动页面到底部');
                    }

                    // 3. 在评论区域内查找并点击"更多"按钮
                    const searchArea = commentContainer || document.body;
                    const moreButtonTexts = [
                        '查看更多评论', '展开更多', '加载更多', '更多评论', '显示更多',
                        'more', '更多', '展开', '查看全部', '全部评论', '查看更多回复',
                        '展开回复', '查看回复', '显示回复', '条回复'
                    ];

                    let clickedButtons = 0;
                    for (const buttonText of moreButtonTexts) {
                        // 只在评论区域内查找按钮
                        const elements = Array.from(searchArea.querySelectorAll('*')).filter(el => {
                            const text = el.textContent.trim();
                            const isVisible = el.offsetParent !== null;
                            const isClickable = el.tagName === 'BUTTON' ||
                                              el.tagName === 'A' ||
                                              el.onclick ||
                                              el.className.includes('btn') ||
                                              el.className.includes('button') ||
                                              el.className.includes('more') ||
                                              el.className.includes('expand') ||
                                              getComputedStyle(el).cursor === 'pointer';

                            return isVisible && isClickable && (
                                text.includes(buttonText) ||
                                (buttonText === '条回复' && /\\d+条回复/.test(text))
                            );
                        });

                        elements.forEach(el => {
                            try {
                                // 滚动到按钮可见
                                el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                el.click();
                                clickedButtons++;
                                console.log('点击按钮:', el.textContent.trim().substring(0, 20));
                            } catch (e) {
                                // 忽略点击错误
                            }
                        });
                    }

                    // 3. 查找评论区并滚动
                    const commentSelectors = [
                        '.comments-container', '.comment-list', '.comment-section',
                        '[class*="comment"]', '[class*="reply"]', '.interaction-container',
                        '[data-testid*="comment"]', '.comment-wrapper'
                    ];

                    let commentContainer = null;
                    for (const selector of commentSelectors) {
                        const containers = document.querySelectorAll(selector);
                        for (const container of containers) {
                            if (container.scrollHeight > container.clientHeight) {
                                commentContainer = container;
                                break;
                            }
                        }
                        if (commentContainer) break;
                    }

                    if (commentContainer) {
                        commentContainer.scrollTop = commentContainer.scrollHeight;
                    }

                    // 4. 统计当前评论数量 - 专注于评论区域
                    let commentCount = 0;

                    // 方法1: 在评论区域内查找头像
                    const commentsWithAvatar = searchArea.querySelectorAll('img[src*="avatar"]');
                    commentCount = Math.max(commentCount, commentsWithAvatar.length);

                    // 方法2: 在评论区域内查找时间信息
                    const timeElements = Array.from(searchArea.querySelectorAll('*')).filter(el => {
                        const text = el.textContent.trim();
                        return /\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}-\\d{2}/.test(text);
                    });
                    commentCount = Math.max(commentCount, timeElements.length);

                    // 方法3: 在评论区域内查找评论相关的class
                    const commentElements = searchArea.querySelectorAll(
                        '[class*="comment"], [class*="reply"], [data-testid*="comment"]'
                    );
                    commentCount = Math.max(commentCount, commentElements.length);

                    console.log('评论区域统计 - 头像:', commentsWithAvatar.length, '时间:', timeElements.length, 'class:', commentElements.length);

                    // 检查是否还有更多按钮
                    const hasMoreButton = moreButtonTexts.some(text =>
                        Array.from(document.querySelectorAll('*')).some(el =>
                            el.textContent.includes(text) && el.offsetParent !== null
                        )
                    );

                    return {
                        scrolled: true,
                        commentCount: commentCount,
                        hasMoreButton: hasMoreButton,
                        clickedButtons: clickedButtons
                    };
                } catch (error) {
                    return {
                        scrolled: false,
                        error: error.message
                    };
                }
            })();
        `;

        ws.send(JSON.stringify({
            id: requestId,
            method: 'Runtime.evaluate',
            params: {
                expression: scrollScript,
                returnByValue: true
            }
        }));

        // 监听滚动结果
        const messageHandler = (data) => {
            try {
                const message = JSON.parse(data);
                if (message.id === requestId && message.result && message.result.result && message.result.result.value) {
                    const result = message.result.result.value;

                    // 移除监听器避免重复处理
                    ws.removeListener('message', messageHandler);

                    if (result.scrolled) {
                        console.log(`   📊 当前评论数: ${result.commentCount}`);
                        if (result.hasMoreButton) {
                            console.log('   🔘 发现"更多"按钮');
                        }
                        if (result.clickedButtons > 0) {
                            console.log(`   🖱️ 点击了 ${result.clickedButtons} 个按钮`);
                        }

                        // 检查评论数量是否有变化
                        let newNoChangeCount = noChangeCount;
                        if (result.commentCount === lastCommentCount) {
                            newNoChangeCount++;
                            console.log(`   ⚠️ 评论数量无变化 (${newNoChangeCount}/5)`);
                        } else {
                            newNoChangeCount = 0;
                            console.log(`   ✅ 评论数量增加: ${lastCommentCount} → ${result.commentCount}`);
                        }

                        // 等待后继续下一次滚动
                        setTimeout(() => {
                            this.performCommentScroll(ws, requestId + 1, scrollCount, maxScrolls,
                                result.commentCount, newNoChangeCount, resolve, reject);
                        }, 2000); // 减少等待时间到2秒
                    } else {
                        console.log('   ❌ 滚动失败:', result.error);
                        setTimeout(() => {
                            this.performCommentScroll(ws, requestId + 1, scrollCount, maxScrolls,
                                lastCommentCount, noChangeCount, resolve, reject);
                        }, 2000);
                    }
                }
            } catch (error) {
                console.error('   ❌ 处理滚动结果失败:', error.message);
                // 即使出错也要继续
                setTimeout(() => {
                    this.performCommentScroll(ws, requestId + 1, scrollCount, maxScrolls,
                        lastCommentCount, noChangeCount, resolve, reject);
                }, 2000);
            }
        };

        ws.on('message', messageHandler);

        // 添加超时保护，防止单次滚动卡住
        setTimeout(() => {
            ws.removeListener('message', messageHandler);
            console.log('   ⏰ 单次滚动超时，继续下一次');
            this.performCommentScroll(ws, requestId + 1, scrollCount, maxScrolls,
                lastCommentCount, noChangeCount, resolve, reject);
        }, 10000); // 10秒超时
    }

    // 💬 提取评论数据
    async extractCommentsData(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ 评论数据采集WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const extractScript = this.buildCommentsExtractionScript();
                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: extractScript,
                            returnByValue: true
                        }
                    }));
                }, 2000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            console.log('✅ 评论数据采集成功');
                            ws.close();
                            resolve(result.data);
                        } else {
                            console.log('❌ 评论数据采集失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理评论数据失败:', error.message);
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ 评论采集WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('评论数据采集超时'));
            }, 30000);
        });
    }

    // 📝 构建评论提取脚本
    buildCommentsExtractionScript() {
        return `
            (function() {
                try {
                    const commentsData = {
                        noteUrl: window.location.href,
                        noteTitle: document.title.replace(' - 小红书', '').trim(),
                        timestamp: new Date().toISOString(),
                        comments: [],
                        summary: {
                            totalComments: 0,
                            totalReplies: 0,
                            totalLikes: 0
                        }
                    };

                    // 查找评论容器
                    const commentSelectors = [
                        '[class*="comment"]',
                        '[class*="reply"]',
                        '[data-testid*="comment"]',
                        '.comment-item',
                        '.comment-list > *'
                    ];

                    let commentElements = [];
                    for (const selector of commentSelectors) {
                        commentElements = document.querySelectorAll(selector);
                        if (commentElements.length > 0) {
                            console.log('使用选择器:', selector, '找到', commentElements.length, '个评论元素');
                            break;
                        }
                    }

                    // 如果没找到，使用更通用的方法
                    if (commentElements.length === 0) {
                        const allDivs = document.querySelectorAll('div');
                        commentElements = Array.from(allDivs).filter(div => {
                            const text = div.textContent.trim();
                            const hasAvatar = div.querySelector('img[src*="avatar"]') || 
                                            div.querySelector('img[class*="avatar"]');
                            const hasUserName = text.length > 10 && text.length < 1000;
                            const hasTimeInfo = text.includes('天前') || text.includes('小时前') || 
                                              text.includes('分钟前') || text.includes('昨天');
                            
                            return hasAvatar && hasUserName && hasTimeInfo;
                        });
                        console.log('通用方法找到', commentElements.length, '个评论元素');
                    }

                    // 提取每个评论的详细信息
                    commentElements.forEach((element, index) => {
                        try {
                            const commentData = {
                                id: index + 1,
                                username: '',
                                avatar: '',
                                content: '',
                                publishTime: '',
                                likes: 0,
                                replies: [],
                                isReply: false,
                                replyTo: '',
                                level: 0
                            };

                            // 提取用户名
                            const usernameSelectors = [
                                '.username', '.user-name', '.nickname', '[class*="user"]', '[class*="name"]'
                            ];
                            for (const selector of usernameSelectors) {
                                const usernameEl = element.querySelector(selector);
                                if (usernameEl && usernameEl.textContent.trim()) {
                                    const username = usernameEl.textContent.trim();
                                    if (username.length > 0 && username.length < 50) {
                                        commentData.username = username;
                                        break;
                                    }
                                }
                            }

                            // 如果没找到用户名，从文本中提取
                            if (!commentData.username) {
                                const textNodes = Array.from(element.querySelectorAll('*')).filter(el => 
                                    el.children.length === 0 && 
                                    el.textContent.trim().length > 1 && 
                                    el.textContent.trim().length < 30
                                );
                                if (textNodes.length > 0) {
                                    commentData.username = textNodes[0].textContent.trim();
                                }
                            }

                            // 提取头像
                            const avatarImg = element.querySelector('img[src*="avatar"]') || 
                                            element.querySelector('img[class*="avatar"]') ||
                                            element.querySelector('img');
                            if (avatarImg && avatarImg.src) {
                                commentData.avatar = avatarImg.src;
                            }

                            // 提取评论内容
                            const contentSelectors = [
                                '.comment-content', '.content', '.text', '[class*="content"]', '[class*="text"]'
                            ];
                            
                            for (const selector of contentSelectors) {
                                const contentEl = element.querySelector(selector);
                                if (contentEl && contentEl.textContent.trim()) {
                                    const content = contentEl.textContent.trim();
                                    if (content.length > 5 && content.length < 2000) {
                                        commentData.content = content;
                                        break;
                                    }
                                }
                            }

                            // 如果没找到内容，从整个元素中提取最长的文本
                            if (!commentData.content) {
                                const allTexts = Array.from(element.querySelectorAll('*')).map(el => 
                                    el.textContent.trim()
                                ).filter(text => 
                                    text.length > 10 && text.length < 1000 &&
                                    !text.includes('天前') && !text.includes('小时前') &&
                                    !text.includes('点赞') && !text.includes('回复')
                                );
                                
                                if (allTexts.length > 0) {
                                    commentData.content = allTexts.reduce((a, b) => a.length > b.length ? a : b);
                                }
                            }

                            // 提取发布时间
                            const timePatterns = [
                                /\\d+天前/, /\\d+小时前/, /\\d+分钟前/, /昨天/, /前天/,
                                /\\d{2}-\\d{2}/, /\\d{4}-\\d{2}-\\d{2}/
                            ];
                            
                            const allText = element.textContent;
                            for (const pattern of timePatterns) {
                                const match = allText.match(pattern);
                                if (match) {
                                    commentData.publishTime = match[0];
                                    break;
                                }
                            }

                            // 提取点赞数
                            const likeElements = Array.from(element.querySelectorAll('*')).filter(el => {
                                const text = el.textContent.trim();
                                const context = el.parentElement ? el.parentElement.textContent : '';
                                return /^\\d+$/.test(text) && (
                                    context.includes('赞') || context.includes('❤️') || 
                                    el.className.includes('like')
                                );
                            });
                            
                            if (likeElements.length > 0) {
                                commentData.likes = parseInt(likeElements[0].textContent.trim()) || 0;
                            }

                            // 判断是否为回复
                            const replyIndicators = ['回复', '@', 'Reply to', '回复给'];
                            for (const indicator of replyIndicators) {
                                if (element.textContent.includes(indicator)) {
                                    commentData.isReply = true;
                                    // 尝试提取回复对象
                                    const replyMatch = element.textContent.match(new RegExp(indicator + '\\s*([^\\s:：]+)'));
                                    if (replyMatch) {
                                        commentData.replyTo = replyMatch[1];
                                    }
                                    break;
                                }
                            }

                            // 判断评论层级（通过缩进或class判断）
                            const style = window.getComputedStyle(element);
                            const marginLeft = parseInt(style.marginLeft) || 0;
                            const paddingLeft = parseInt(style.paddingLeft) || 0;
                            const indent = marginLeft + paddingLeft;
                            
                            if (indent > 50) {
                                commentData.level = Math.floor(indent / 30);
                            }

                            // 只添加有效的评论
                            if (commentData.username || commentData.content) {
                                commentsData.comments.push(commentData);
                            }

                        } catch (error) {
                            console.error('处理评论', index, '时出错:', error.message);
                        }
                    });

                    // 计算统计信息
                    commentsData.summary.totalComments = commentsData.comments.length;
                    commentsData.summary.totalReplies = commentsData.comments.filter(c => c.isReply).length;
                    commentsData.summary.totalLikes = commentsData.comments.reduce((sum, c) => sum + c.likes, 0);

                    // 按层级组织评论结构
                    const organizedComments = [];
                    const commentMap = new Map();

                    commentsData.comments.forEach(comment => {
                        if (comment.level === 0 && !comment.isReply) {
                            // 主评论
                            comment.replies = [];
                            organizedComments.push(comment);
                            commentMap.set(comment.username, comment);
                        } else {
                            // 回复评论
                            const parentComment = commentMap.get(comment.replyTo) || 
                                                organizedComments[organizedComments.length - 1];
                            if (parentComment && parentComment.replies) {
                                parentComment.replies.push(comment);
                            } else {
                                organizedComments.push(comment);
                            }
                        }
                    });

                    commentsData.organizedComments = organizedComments;

                    return { success: true, data: commentsData };
                    
                } catch (error) {
                    return { 
                        success: false, 
                        error: error.message,
                        stack: error.stack 
                    };
                }
            })();
        `;
    }
}

// 🧪 测试评论采集
async function testCommentsCollection() {
    const collector = new CommentsCollector();
    
    try {
        const commentsData = await collector.collectComments();
        
        console.log('\n💬 评论采集结果:');
        console.log('=' * 60);
        console.log(`📝 笔记标题: ${commentsData.noteTitle}`);
        console.log(`🔗 笔记链接: ${commentsData.noteUrl}`);
        console.log(`💬 总评论数: ${commentsData.summary.totalComments}`);
        console.log(`↩️  回复数: ${commentsData.summary.totalReplies}`);
        console.log(`👍 总点赞数: ${commentsData.summary.totalLikes}\n`);
        
        // 显示前10条评论
        const displayComments = commentsData.comments.slice(0, 10);
        displayComments.forEach((comment, index) => {
            console.log(`💬 评论 ${index + 1}:`);
            console.log(`   👤 用户: ${comment.username || '匿名'}`);
            console.log(`   📄 内容: ${comment.content || '无内容'}`);
            console.log(`   👍 点赞: ${comment.likes}`);
            console.log(`   📅 时间: ${comment.publishTime || '未知'}`);
            if (comment.isReply) {
                console.log(`   ↩️  回复给: ${comment.replyTo}`);
            }
            if (comment.replies && comment.replies.length > 0) {
                console.log(`   💭 回复数: ${comment.replies.length}`);
            }
            console.log('');
        });
        
        if (commentsData.comments.length > 10) {
            console.log(`... 还有 ${commentsData.comments.length - 10} 条评论`);
        }
        
        return commentsData;
        
    } catch (error) {
        console.error('❌ 评论采集测试失败:', error.message);
    }
}

// 💾 保存评论数据
async function saveCommentsData() {
    const fs = require('fs');
    const collector = new CommentsCollector();

    try {
        console.log('💾 开始采集并保存评论数据...\n');

        const commentsData = await collector.collectComments();

        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const jsonFileName = `comments-${timestamp}.json`;
        const txtFileName = `comments-${timestamp}.txt`;

        // 保存JSON数据
        fs.writeFileSync(jsonFileName, JSON.stringify(commentsData, null, 2), 'utf8');
        console.log(`✅ JSON数据已保存: ${jsonFileName}`);

        // 生成可读报告
        const report = generateCommentsReport(commentsData);
        fs.writeFileSync(txtFileName, report, 'utf8');
        console.log(`✅ 可读报告已保存: ${txtFileName}`);

        return { jsonFile: jsonFileName, txtFile: txtFileName, data: commentsData };

    } catch (error) {
        console.error('❌ 保存评论数据失败:', error.message);
        throw error;
    }
}

// 📝 生成评论报告
function generateCommentsReport(data) {
    const lines = [];

    lines.push('💬 小红书笔记评论数据报告');
    lines.push('=' * 60);
    lines.push(`📅 采集时间: ${data.timestamp}`);
    lines.push(`📝 笔记标题: ${data.noteTitle}`);
    lines.push(`🔗 笔记链接: ${data.noteUrl}`);
    lines.push('');

    lines.push('📊 评论统计:');
    lines.push(`💬 总评论数: ${data.summary.totalComments}`);
    lines.push(`↩️  回复数: ${data.summary.totalReplies}`);
    lines.push(`👍 总点赞数: ${data.summary.totalLikes}`);
    lines.push('');

    lines.push('💬 评论详情:');
    lines.push('-' * 60);

    data.comments.forEach((comment, index) => {
        lines.push(`\n💬 评论 ${index + 1}:`);
        lines.push(`👤 用户: ${comment.username || '匿名'}`);
        lines.push(`📄 内容: ${comment.content || '无内容'}`);
        lines.push(`👍 点赞: ${comment.likes}`);
        lines.push(`📅 时间: ${comment.publishTime || '未知'}`);
        lines.push(`🔗 头像: ${comment.avatar || '无'}`);

        if (comment.isReply) {
            lines.push(`↩️  回复给: ${comment.replyTo}`);
            lines.push(`📊 层级: ${comment.level}`);
        }

        if (comment.replies && comment.replies.length > 0) {
            lines.push(`💭 回复数: ${comment.replies.length}`);
            comment.replies.forEach((reply, replyIndex) => {
                lines.push(`   💭 回复 ${replyIndex + 1}: ${reply.username} - ${reply.content}`);
            });
        }
    });

    lines.push('\n' + '=' * 60);
    lines.push('📅 报告生成时间: ' + new Date().toLocaleString('zh-CN'));

    return lines.join('\n');
}

// 运行测试
if (require.main === module) {
    const args = process.argv.slice(2);
    if (args.includes('--save')) {
        saveCommentsData().then(result => {
            console.log('\n🎉 评论数据保存完成!');
            console.log(`📁 JSON文件: ${result.jsonFile}`);
            console.log(`📄 文本报告: ${result.txtFile}`);
        }).catch(console.error);
    } else {
        testCommentsCollection().catch(console.error);
    }
}

module.exports = { CommentsCollector, testCommentsCollection, saveCommentsData };
