/**
 * 🎯 小红书评论控制台提取器
 * 在浏览器开发者工具控制台中运行此代码
 */

console.log('🎯 小红书评论提取器启动...');

// 配置
const config = {
    targetComments: 1472,
    maxScrolls: 1000,
    scrollDelay: 600
};

let scrollCount = 0;
let clickCount = 0;

// 主函数
async function extractComments() {
    console.log(`🎯 目标: ${config.targetComments} 条评论`);
    
    // 滚动到评论区
    scrollToComments();
    
    // 开始滚动加载
    await scrollAndLoad();
    
    // 提取评论
    const comments = parseAllComments();
    
    // 保存结果
    const result = saveData(comments);
    
    // 输出结果
    console.log('\n🎉 爬取完成！');
    console.log(`📝 笔记ID: ${result.noteInfo.id}`);
    console.log(`📝 笔记标题: ${result.noteInfo.title}`);
    console.log(`💬 提取评论数: ${comments.length}`);
    console.log(`📜 总滚动次数: ${scrollCount}`);
    console.log(`🔄 总点击次数: ${clickCount}`);
    
    return result;
}

// 滚动到评论区
function scrollToComments() {
    console.log('🎯 定位评论区...');
    
    // 查找评论区域
    const elements = Array.from(document.querySelectorAll('*')).filter(el => 
        el.textContent.includes('条评论') && el.offsetHeight > 0
    );
    
    if (elements.length > 0) {
        elements[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
        console.log('✅ 找到评论区');
    } else {
        window.scrollTo(0, document.body.scrollHeight * 0.6);
        console.log('💡 滚动到页面中部');
    }
}

// 滚动并加载
async function scrollAndLoad() {
    console.log('🚀 开始滚动加载...');
    
    let currentCount = 0;
    let stableCount = 0;
    
    for (let i = 0; i < config.maxScrolls; i++) {
        // 统计评论
        const newCount = countComments();
        
        if (newCount === currentCount) {
            stableCount++;
        } else {
            console.log(`📈 评论数: ${newCount} (+${newCount - currentCount})`);
            currentCount = newCount;
            stableCount = 0;
        }
        
        // 检查是否达到目标
        if (currentCount >= config.targetComments * 0.95) {
            console.log(`🎉 接近目标！${currentCount}/${config.targetComments}`);
            break;
        }
        
        // 点击加载更多
        if (stableCount >= 3) {
            if (clickLoadMore()) {
                clickCount++;
                stableCount = 0;
            }
        }
        
        // 执行滚动
        performScroll(i);
        scrollCount++;
        
        // 停止条件
        if (stableCount >= 20) {
            console.log('⏹️ 长时间无新内容，停止');
            break;
        }
        
        // 进度报告
        if (i % 100 === 0 && i > 0) {
            const progress = Math.round((currentCount / config.targetComments) * 100);
            console.log(`📊 进度: ${progress}% (${currentCount}/${config.targetComments})`);
        }
        
        // 等待
        await sleep(config.scrollDelay);
    }
    
    console.log(`✅ 滚动完成！最终评论数: ${currentCount}`);
}

// 执行滚动
function performScroll(index) {
    const strategies = [
        () => window.scrollBy(0, 300),
        () => window.scrollBy(0, 500),
        () => window.scrollBy(0, 800),
        () => window.scrollTo(0, document.body.scrollHeight)
    ];
    
    strategies[index % strategies.length]();
}

// 点击加载更多
function clickLoadMore() {
    const texts = ['加载更多', '展开更多', '查看更多', '更多评论', '展开', '更多'];
    
    for (const text of texts) {
        const elements = Array.from(document.querySelectorAll('*')).filter(el => 
            el.textContent.includes(text) && el.offsetHeight > 0
        );
        
        for (const el of elements) {
            try {
                el.click();
                return true;
            } catch (e) {
                continue;
            }
        }
    }
    return false;
}

// 统计评论数量
function countComments() {
    const text = document.body.textContent;
    const timeCount = (text.match(/\d{2}-\d{2}/g) || []).length;
    const replyCount = (text.match(/\d+回复/g) || []).length;
    const keywordCount = (text.match(/求带|宝子|学姐|兼职|聊天员/g) || []).length / 2;
    
    return Math.max(timeCount, replyCount, keywordCount);
}

// 解析所有评论
function parseAllComments() {
    console.log('🧠 开始解析评论...');
    
    const pageText = document.body.textContent;
    const comments = [];
    
    // 时间模式解析
    const timePattern = /(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/g;
    let match;
    const timeMatches = [];
    
    while ((match = timePattern.exec(pageText)) !== null) {
        timeMatches.push({
            time: match[1],
            index: match.index
        });
    }
    
    // 提取评论
    for (let i = 0; i < timeMatches.length; i++) {
        const current = timeMatches[i];
        const next = timeMatches[i + 1];
        
        const start = Math.max(0, current.index - 200);
        const end = next ? next.index : current.index + 1000;
        
        const commentText = pageText.substring(start, Math.min(pageText.length, end)).trim();
        
        if (commentText.length > 20 && commentText.length < 2000) {
            const comment = createComment(commentText, comments.length + 1);
            if (comment) {
                comments.push(comment);
            }
        }
    }
    
    // 关键词解析
    const keywords = ['求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯'];
    
    for (const keyword of keywords) {
        const regex = new RegExp(keyword, 'g');
        let match;
        
        while ((match = regex.exec(pageText)) !== null) {
            const start = Math.max(0, match.index - 150);
            const end = Math.min(pageText.length, match.index + 800);
            
            const commentText = pageText.substring(start, end).trim();
            
            if (commentText.length > 15 && commentText.length < 1500) {
                const comment = createComment(commentText, comments.length + 1);
                if (comment) {
                    comments.push(comment);
                }
            }
        }
    }
    
    // 去重
    const unique = [];
    const seen = new Set();
    
    for (const comment of comments) {
        const key = comment.content.substring(0, 30);
        if (!seen.has(key)) {
            seen.add(key);
            unique.push(comment);
        }
    }
    
    // 重新编号
    unique.forEach((comment, index) => {
        comment.id = index + 1;
    });
    
    console.log(`✅ 解析完成，提取到 ${unique.length} 条评论`);
    return unique;
}

// 创建评论对象
function createComment(text, id) {
    const comment = {
        id: id,
        userId: '',
        username: '',
        content: '',
        time: '',
        likes: 0,
        replyCount: 0,
        isAuthor: text.includes('作者'),
        isPinned: text.includes('置顶')
    };
    
    // 提取时间
    const timeMatch = text.match(/(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/);
    if (timeMatch) {
        comment.time = timeMatch[1];
    }
    
    // 提取用户名
    const userMatch = text.match(/^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/);
    if (userMatch) {
        comment.username = userMatch[1];
    }
    
    // 清理内容
    let content = text;
    content = content.replace(/\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前/g, '');
    content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');
    content = content.replace(/作者|置顶评论|回复|赞/g, '');
    if (comment.username) {
        content = content.replace(comment.username, '');
    }
    content = content.replace(/\s+/g, ' ').trim();
    
    comment.content = content;
    
    // 提取点赞数
    const likeMatch = text.match(/(\d+)\s*赞/);
    if (likeMatch) {
        comment.likes = parseInt(likeMatch[1]);
    }
    
    // 提取回复数
    const replyMatch = text.match(/(\d+)\s*回复/);
    if (replyMatch) {
        comment.replyCount = parseInt(replyMatch[1]);
    }
    
    // 提取用户ID
    const userIdMatch = text.match(/小红薯([A-F0-9]{8,})/);
    if (userIdMatch) {
        comment.userId = userIdMatch[1];
    }
    
    return comment.content.length >= 5 ? comment : null;
}

// 保存数据
function saveData(comments) {
    const noteInfo = {
        id: '',
        title: document.title.replace(' - 小红书', ''),
        author: '',
        totalCommentCount: 0
    };
    
    // 提取笔记ID
    const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
    if (urlMatch) {
        noteInfo.id = urlMatch[1];
    }
    
    // 提取评论总数
    const pageText = document.body.textContent;
    const commentCountMatch = pageText.match(/(\d+)\s*条评论/);
    if (commentCountMatch) {
        noteInfo.totalCommentCount = parseInt(commentCountMatch[1]);
    }
    
    // 提取作者
    if (pageText.includes('漫娴学姐')) {
        noteInfo.author = '漫娴学姐 招暑假工版';
    }
    
    const result = {
        noteInfo,
        comments,
        extractStats: {
            totalTextLength: pageText.length,
            successfulExtractions: comments.length,
            extractionMethods: ['browser-console']
        },
        extractTime: new Date().toISOString()
    };
    
    // 下载文件
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `console_comments_${noteInfo.id}_${timestamp}.json`;
    
    const dataStr = JSON.stringify(result, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
    
    console.log(`💾 数据已下载: ${filename}`);
    
    return result;
}

// 工具函数
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 自动启动
console.log('💡 3秒后自动开始提取...');
setTimeout(() => {
    extractComments();
}, 3000);
