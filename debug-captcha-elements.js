#!/usr/bin/env node

/**
 * 🔍 调试小红书验证码页面元素
 * 分析页面结构，找到正确的选择器
 */

const puppeteer = require('puppeteer');

async function debugCaptchaElements() {
    console.log('🔍 调试小红书验证码页面元素...');
    console.log('');

    let browser = null;

    try {
        // 连接到比特浏览器
        console.log('🔗 连接到比特浏览器...');
        browser = await puppeteer.connect({
            browserURL: 'http://127.0.0.1:55276',
            defaultViewport: null
        });

        // 获取小红书页面
        const pages = await browser.pages();
        const xiaohongshuPage = pages.find(page => 
            page.url().includes('xiaohongshu.com')
        );

        if (!xiaohongshuPage) {
            throw new Error('未找到小红书页面');
        }

        console.log('✅ 找到小红书页面');
        console.log('📄 页面URL:', xiaohongshuPage.url());
        console.log('');

        // 获取页面标题
        const title = await xiaohongshuPage.title();
        console.log('📋 页面标题:', title);
        console.log('');

        // 分析页面上的所有元素
        console.log('🔍 分析页面元素结构...');
        
        const elementInfo = await xiaohongshuPage.evaluate(() => {
            const result = {
                allElements: [],
                possibleSliders: [],
                possibleButtons: [],
                images: [],
                divs: []
            };

            // 获取所有元素
            const allElements = document.querySelectorAll('*');
            
            allElements.forEach((el, index) => {
                const tagName = el.tagName.toLowerCase();
                const className = (el.className || '').toString();
                const id = el.id || '';
                const textContent = el.textContent ? el.textContent.trim().substring(0, 50) : '';

                // 基本元素信息
                const elementData = {
                    index,
                    tag: tagName,
                    className: className,
                    id,
                    text: textContent
                };

                // 查找可能的滑块相关元素
                if (className.includes('slider') || className.includes('slide') ||
                    className.includes('captcha') || className.includes('verify') ||
                    id.includes('slider') || id.includes('slide') ||
                    id.includes('captcha') || id.includes('verify')) {
                    result.possibleSliders.push(elementData);
                }

                // 查找可能的按钮元素
                if (tagName === 'button' || className.includes('button') ||
                    className.includes('btn') || id.includes('button') ||
                    id.includes('btn')) {
                    result.possibleButtons.push(elementData);
                }

                // 查找图片元素
                if (tagName === 'img') {
                    result.images.push({
                        ...elementData,
                        src: el.src,
                        alt: el.alt
                    });
                }

                // 查找div元素（可能包含滑块）
                if (tagName === 'div' && (className || id)) {
                    result.divs.push(elementData);
                }
            });

            // 特别查找验证码相关的元素
            const captchaContainer = document.querySelector('[class*="captcha"], [class*="verify"], [id*="captcha"], [id*="verify"]');
            if (captchaContainer) {
                result.captchaContainer = {
                    tag: captchaContainer.tagName,
                    className: (captchaContainer.className || '').toString(),
                    id: captchaContainer.id,
                    innerHTML: captchaContainer.innerHTML.substring(0, 200)
                };
            }

            return result;
        });

        // 输出分析结果
        console.log('📊 页面元素分析结果:');
        console.log('');

        if (elementInfo.possibleSliders.length > 0) {
            console.log('🎯 可能的滑块相关元素:');
            elementInfo.possibleSliders.forEach((el, i) => {
                console.log(`   ${i + 1}. <${el.tag}> class="${el.className}" id="${el.id}"`);
                if (el.text) console.log(`      文本: "${el.text}"`);
            });
            console.log('');
        }

        if (elementInfo.possibleButtons.length > 0) {
            console.log('🔘 可能的按钮元素:');
            elementInfo.possibleButtons.forEach((el, i) => {
                console.log(`   ${i + 1}. <${el.tag}> class="${el.className}" id="${el.id}"`);
                if (el.text) console.log(`      文本: "${el.text}"`);
            });
            console.log('');
        }

        if (elementInfo.images.length > 0) {
            console.log('🖼️ 图片元素:');
            elementInfo.images.forEach((el, i) => {
                console.log(`   ${i + 1}. <img> class="${el.className}" src="${el.src}"`);
                if (el.alt) console.log(`      alt: "${el.alt}"`);
            });
            console.log('');
        }

        if (elementInfo.captchaContainer) {
            console.log('📦 验证码容器:');
            console.log(`   标签: <${elementInfo.captchaContainer.tag}>`);
            console.log(`   类名: "${elementInfo.captchaContainer.className}"`);
            console.log(`   ID: "${elementInfo.captchaContainer.id}"`);
            console.log(`   内容预览: ${elementInfo.captchaContainer.innerHTML}`);
            console.log('');
        }

        // 尝试更多的选择器
        console.log('🔍 测试常见的验证码选择器:');
        
        const testSelectors = [
            // 滑块轨道
            '.slider-track', '.slide-track', '.captcha-slider', '.verify-slider',
            '[class*="slider"]', '[class*="slide"]', '[class*="track"]',
            
            // 滑块按钮
            '.slider-button', '.slide-button', '.captcha-button', '.verify-button',
            '[class*="button"]', '[class*="btn"]', '[class*="handle"]',
            
            // 通用验证码元素
            '.captcha', '.verify', '[class*="captcha"]', '[class*="verify"]',
            
            // 可拖拽元素
            '[draggable="true"]', '.draggable', '[class*="drag"]'
        ];

        for (const selector of testSelectors) {
            try {
                const elements = await xiaohongshuPage.$$(selector);
                if (elements.length > 0) {
                    console.log(`   ✅ ${selector} - 找到 ${elements.length} 个元素`);
                    
                    // 获取第一个元素的详细信息
                    const firstElement = elements[0];
                    const elementDetails = await firstElement.evaluate(el => ({
                        tag: el.tagName,
                        className: el.className,
                        id: el.id,
                        textContent: el.textContent ? el.textContent.trim().substring(0, 50) : '',
                        style: el.style.cssText,
                        offsetWidth: el.offsetWidth,
                        offsetHeight: el.offsetHeight
                    }));
                    
                    console.log(`      详情: <${elementDetails.tag}> "${elementDetails.className}"`);
                    console.log(`      尺寸: ${elementDetails.offsetWidth}x${elementDetails.offsetHeight}`);
                    if (elementDetails.textContent) {
                        console.log(`      文本: "${elementDetails.textContent}"`);
                    }
                } else {
                    console.log(`   ❌ ${selector} - 未找到`);
                }
            } catch (error) {
                console.log(`   ⚠️ ${selector} - 选择器错误`);
            }
        }

        console.log('');
        console.log('💡 建议的操作方案:');
        
        // 基于分析结果给出建议
        if (elementInfo.possibleSliders.length > 0) {
            console.log('   1. 找到了可能的滑块元素，可以尝试自动化操作');
            console.log('   2. 推荐使用找到的选择器进行操作');
        } else {
            console.log('   1. 未找到明显的滑块元素');
            console.log('   2. 建议手动操作或等待页面完全加载');
        }
        
        console.log('   3. 可以尝试点击页面刷新验证码');
        console.log('   4. 检查是否有iframe嵌套的验证码');

        // 检查是否有iframe
        const iframes = await xiaohongshuPage.$$('iframe');
        if (iframes.length > 0) {
            console.log('');
            console.log(`🖼️ 发现 ${iframes.length} 个iframe，验证码可能在iframe中`);
            
            for (let i = 0; i < iframes.length; i++) {
                try {
                    const frame = await iframes[i].contentFrame();
                    if (frame) {
                        const frameUrl = await frame.url();
                        console.log(`   iframe ${i + 1}: ${frameUrl}`);
                    }
                } catch (error) {
                    console.log(`   iframe ${i + 1}: 无法访问`);
                }
            }
        }

    } catch (error) {
        console.error('❌ 调试失败:', error.message);
    } finally {
        if (browser) {
            await browser.disconnect();
        }
    }
}

// 运行调试
if (require.main === module) {
    debugCaptchaElements().catch(console.error);
}

module.exports = { debugCaptchaElements };
