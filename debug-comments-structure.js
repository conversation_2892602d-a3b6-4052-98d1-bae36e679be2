#!/usr/bin/env node

/**
 * 🔍 小红书评论结构调试器
 * 分析页面上的评论结构，找出二级回复的正确选择器
 */

const puppeteer = require('puppeteer');

class CommentsStructureDebugger {
    constructor() {
        this.debugPort = 55276;
    }

    async debugCommentsStructure() {
        console.log('🔍 启动评论结构调试器...');
        console.log('');

        let browser = null;

        try {
            // 连接到比特浏览器
            console.log('🔗 连接到比特浏览器...');
            browser = await puppeteer.connect({
                browserURL: `http://127.0.0.1:${this.debugPort}`,
                defaultViewport: null
            });

            // 获取小红书页面
            const pages = await browser.pages();
            const xiaohongshuPage = pages.find(page => 
                page.url().includes('xiaohongshu.com/explore/')
            );

            if (!xiaohongshuPage) {
                throw new Error('未找到小红书笔记页面');
            }

            console.log('✅ 找到小红书笔记页面');
            console.log('📄 当前URL:', xiaohongshuPage.url());
            console.log('');

            // 等待页面加载完成
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 分析页面结构
            const structureInfo = await xiaohongshuPage.evaluate(() => {
                const result = {
                    allElements: [],
                    possibleComments: [],
                    possibleReplies: [],
                    clickableElements: [],
                    textElements: []
                };

                // 1. 查找所有可能的评论元素
                const allDivs = document.querySelectorAll('div, section, article');
                
                allDivs.forEach((el, index) => {
                    const text = el.textContent?.trim() || '';
                    const hasAvatar = el.querySelector('img[src*="avatar"]');
                    const hasUserName = el.querySelector('[class*="user"], [class*="name"]');
                    
                    // 记录可能的评论元素
                    if (hasAvatar && hasUserName && text.length > 10) {
                        result.possibleComments.push({
                            index,
                            tagName: el.tagName,
                            className: el.className,
                            textLength: text.length,
                            textPreview: text.substring(0, 50) + '...',
                            hasAvatar: !!hasAvatar,
                            hasUserName: !!hasUserName,
                            childrenCount: el.children.length
                        });
                    }
                });

                // 2. 查找包含"回复"文字的元素
                const allElements = document.querySelectorAll('*');
                
                allElements.forEach((el, index) => {
                    const text = el.textContent?.trim() || '';
                    
                    if (text.includes('回复') || text.includes('查看') || text.includes('展开')) {
                        result.textElements.push({
                            index,
                            tagName: el.tagName,
                            className: el.className,
                            text: text,
                            isClickable: el.tagName === 'BUTTON' || 
                                         el.tagName === 'A' || 
                                         !!el.onclick || 
                                         el.style.cursor === 'pointer' ||
                                         el.getAttribute('role') === 'button'
                        });
                    }
                });

                // 3. 查找所有可点击元素
                const clickableSelectors = ['button', 'a', '[onclick]', '[role="button"]', '[style*="cursor: pointer"]'];
                
                clickableSelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach((el, index) => {
                        const text = el.textContent?.trim() || '';
                        if (text) {
                            result.clickableElements.push({
                                selector,
                                index,
                                tagName: el.tagName,
                                className: el.className,
                                text: text.substring(0, 30),
                                hasReplyText: text.includes('回复') || text.includes('查看') || text.includes('展开')
                            });
                        }
                    });
                });

                // 4. 查找嵌套结构（可能的二级回复）
                result.possibleComments.forEach(comment => {
                    const commentEl = allDivs[comment.index];
                    const nestedDivs = commentEl.querySelectorAll('div');
                    
                    nestedDivs.forEach(nestedDiv => {
                        const nestedText = nestedDiv.textContent?.trim() || '';
                        const nestedHasAvatar = nestedDiv.querySelector('img[src*="avatar"]');
                        const nestedHasUser = nestedDiv.querySelector('[class*="user"], [class*="name"]');
                        
                        if (nestedHasAvatar && nestedHasUser && nestedText.length > 5) {
                            result.possibleReplies.push({
                                parentIndex: comment.index,
                                tagName: nestedDiv.tagName,
                                className: nestedDiv.className,
                                textPreview: nestedText.substring(0, 30) + '...',
                                hasAvatar: !!nestedHasAvatar,
                                hasUserName: !!nestedHasUser
                            });
                        }
                    });
                });

                return result;
            });

            // 输出分析结果
            console.log('📊 页面结构分析结果:');
            console.log('');
            
            console.log('🔍 可能的评论元素:', structureInfo.possibleComments.length, '个');
            structureInfo.possibleComments.slice(0, 5).forEach((comment, i) => {
                console.log(`   ${i + 1}. ${comment.tagName}.${comment.className}`);
                console.log(`      文本预览: ${comment.textPreview}`);
                console.log(`      子元素: ${comment.childrenCount} 个`);
                console.log('');
            });

            console.log('🔍 可能的二级回复:', structureInfo.possibleReplies.length, '个');
            structureInfo.possibleReplies.slice(0, 5).forEach((reply, i) => {
                console.log(`   ${i + 1}. ${reply.tagName}.${reply.className}`);
                console.log(`      文本预览: ${reply.textPreview}`);
                console.log(`      父评论索引: ${reply.parentIndex}`);
                console.log('');
            });

            console.log('🔍 包含"回复"文字的元素:', structureInfo.textElements.length, '个');
            structureInfo.textElements.forEach((el, i) => {
                console.log(`   ${i + 1}. ${el.tagName}.${el.className}`);
                console.log(`      文本: "${el.text}"`);
                console.log(`      可点击: ${el.isClickable}`);
                console.log('');
            });

            console.log('🔍 可点击元素中包含回复相关文字的:', 
                structureInfo.clickableElements.filter(el => el.hasReplyText).length, '个');
            
            structureInfo.clickableElements
                .filter(el => el.hasReplyText)
                .forEach((el, i) => {
                    console.log(`   ${i + 1}. ${el.tagName}.${el.className}`);
                    console.log(`      文本: "${el.text}"`);
                    console.log(`      选择器: ${el.selector}`);
                    console.log('');
                });

            console.log('🎯 建议的选择器策略:');
            
            if (structureInfo.possibleReplies.length > 0) {
                console.log('   ✅ 发现可能的二级回复结构');
                console.log('   💡 建议直接爬取嵌套的评论元素');
            } else {
                console.log('   ⚠️ 未发现明显的二级回复结构');
                console.log('   💡 可能需要先点击展开按钮');
            }

            const replyButtons = structureInfo.clickableElements.filter(el => el.hasReplyText);
            if (replyButtons.length > 0) {
                console.log('   ✅ 发现可能的回复展开按钮');
                console.log('   💡 建议点击这些按钮后再爬取');
            } else {
                console.log('   ⚠️ 未发现回复展开按钮');
                console.log('   💡 可能所有回复已经展开，或者使用了不同的交互方式');
            }

            return structureInfo;

        } catch (error) {
            console.error('❌ 调试失败:', error.message);
            return null;
        } finally {
            if (browser) {
                await browser.disconnect();
            }
        }
    }
}

// 如果直接运行此文件
if (require.main === module) {
    const structureDebugger = new CommentsStructureDebugger();
    structureDebugger.debugCommentsStructure().catch(console.error);
}

module.exports = CommentsStructureDebugger;
