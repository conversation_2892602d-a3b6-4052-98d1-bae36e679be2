// ===== 🔍 浏览器调试连接检测工具 【诊断工具】 =====
// 📝 功能说明：检测Chrome/比特浏览器的调试端口连接状态
// 🎯 主要用途：诊断浏览器调试接口、查找小红书标签页、验证连接可用性
// 🔧 技术原理：Chrome DevTools Protocol端口扫描 + 标签页内容检测

const axios = require('axios'); // 🌐 HTTP客户端 - 调试接口通信

async function debugBrowserConnection() {
    console.log('🔍 开始检测浏览器调试连接...\n');

    // 🔍 扩大端口搜索范围 - 覆盖常见的Chrome调试端口
    const ports = [];
    for (let i = 9222; i <= 9250; i++) {
        ports.push(i);
    }
    
    console.log(`检测端口范围: ${ports[0]} - ${ports[ports.length-1]}`);
    
    const foundPorts = [];
    
    for (const port of ports) {
        try {
            const response = await axios.get(`http://localhost:${port}/json`, {
                timeout: 1000
            });
            
            if (response.data && Array.isArray(response.data)) {
                foundPorts.push({
                    port: port,
                    tabs: response.data.length,
                    data: response.data
                });
                
                console.log(`\n✅ 端口 ${port} 可用:`);
                console.log(`   标签页数量: ${response.data.length}`);
                
                // 检查是否有小红书标签页
                const xiaohongshuTabs = response.data.filter(tab => 
                    tab.url && (
                        tab.url.includes('xiaohongshu.com') || 
                        tab.url.includes('xhscdn.com') ||
                        tab.title.includes('小红书')
                    )
                );
                
                if (xiaohongshuTabs.length > 0) {
                    console.log(`   🎯 找到小红书标签页: ${xiaohongshuTabs.length} 个`);
                    xiaohongshuTabs.forEach((tab, index) => {
                        console.log(`      ${index + 1}. ${tab.title}`);
                        console.log(`         URL: ${tab.url}`);
                        console.log(`         WebSocket: ${tab.webSocketDebuggerUrl}`);
                    });
                } else {
                    console.log(`   ⚠️  未找到小红书标签页`);
                    console.log(`   当前标签页:`);
                    response.data.slice(0, 3).forEach((tab, index) => {
                        console.log(`      ${index + 1}. ${tab.title || '无标题'}`);
                        console.log(`         URL: ${tab.url || '无URL'}`);
                    });
                }
            }
        } catch (error) {
            // 端口不可用，静默跳过
        }
    }
    
    console.log(`\n📊 检测结果:`);
    console.log(`   可用调试端口: ${foundPorts.length} 个`);
    
    if (foundPorts.length === 0) {
        console.log('\n❌ 未找到任何可用的浏览器调试端口');
        console.log('\n💡 解决建议:');
        console.log('   1. 确保比特浏览器已启动');
        console.log('   2. 在比特浏览器中启动一个浏览器实例');
        console.log('   3. 确保浏览器实例开启了调试模式');
        console.log('   4. 在浏览器中打开小红书网站 (xiaohongshu.com)');
        console.log('\n🔧 如果问题持续，请尝试:');
        console.log('   - 重启比特浏览器');
        console.log('   - 创建新的浏览器实例');
        console.log('   - 检查防火墙设置');
    } else {
        console.log('\n✅ 找到可用的调试端口，请确保在浏览器中打开小红书网站');
    }
}

// 运行检测
debugBrowserConnection().catch(console.error);
