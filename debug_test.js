#!/usr/bin/env node

/**
 * 🐛 调试测试脚本
 */

const axios = require('axios');
const WebSocket = require('ws');

async function debugTest() {
    console.log('🐛 开始调试测试...');

    try {
        console.log('1. 测试端口连接...');
        const debugPort = 63524;
        const response = await axios.get(`http://127.0.0.1:${debugPort}/json`, {
            timeout: 5000
        });
        console.log(`✅ 端口 ${debugPort} 连接成功，找到 ${response.data.length} 个标签页`);

        const tab = response.data.find(tab =>
            tab.url && tab.url.includes('xiaohongshu.com')
        );

        if (!tab) {
            console.log('❌ 未找到小红书标签页');
            console.log('可用标签页:');
            response.data.forEach((t, i) => {
                console.log(`  ${i + 1}. ${t.title} - ${t.url}`);
            });
            return;
        }

        console.log(`✅ 找到小红书标签页: ${tab.title}`);

        console.log('2. 测试WebSocket连接...');
        const ws = new WebSocket(tab.webSocketDebuggerUrl);

        ws.on('open', () => {
            console.log('✅ WebSocket连接成功');
            
            ws.send(JSON.stringify({
                id: 1,
                method: 'Runtime.enable'
            }));

            setTimeout(() => {
                console.log('3. 执行简单脚本...');
                const simpleScript = `
                    (function() {
                        console.log('脚本开始执行...');
                        const result = {
                            url: window.location.href,
                            title: document.title,
                            avatars: document.querySelectorAll('img[src*="avatar"]').length,
                            height: document.body.scrollHeight
                        };
                        console.log('脚本执行完成:', result);
                        return result;
                    })();
                `;

                ws.send(JSON.stringify({
                    id: 2,
                    method: 'Runtime.evaluate',
                    params: {
                        expression: simpleScript,
                        returnByValue: true
                    }
                }));
            }, 1000);
        });

        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                console.log('收到消息:', message.id);
                
                if (message.id === 2 && message.result) {
                    if (message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        console.log('✅ 脚本执行成功:');
                        console.log(`   URL: ${result.url}`);
                        console.log(`   标题: ${result.title}`);
                        console.log(`   头像数: ${result.avatars}`);
                        console.log(`   页面高度: ${result.height}px`);
                    } else if (message.result.exceptionDetails) {
                        console.log('❌ 脚本执行错误:', message.result.exceptionDetails);
                    }
                    ws.close();
                }
            } catch (error) {
                console.error('❌ 处理消息失败:', error.message);
                ws.close();
            }
        });

        ws.on('error', (error) => {
            console.error('❌ WebSocket错误:', error.message);
        });

        ws.on('close', () => {
            console.log('WebSocket连接已关闭');
        });

    } catch (error) {
        console.error('❌ 调试测试失败:', error.message);
    }
}

if (require.main === module) {
    debugTest();
}

module.exports = debugTest;
