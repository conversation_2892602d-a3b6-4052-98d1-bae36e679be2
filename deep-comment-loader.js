#!/usr/bin/env node

/**
 * 🚀 深度评论加载器
 * 专门针对评论区域进行深度滚动和点击加载
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class DeepCommentLoader {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        // 深度加载配置
        this.config = {
            maxScrollAttempts: 100,     // 增加到100次滚动
            scrollDelay: 2000,          // 2秒间隔
            clickDelay: 1500,           // 点击间隔1.5秒
            waitForContent: 3000,       // 等待内容3秒
            targetComments: 1472,       // 目标评论数
            minCommentLength: 5,        // 最小评论长度
            maxCommentLength: 1000      // 最大评论长度
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    async connectToBrowser() {
        try {
            console.log('🔗 连接到比特浏览器...');
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${this.debugPort}`,
                defaultViewport: null
            });
            
            const pages = await browser.pages();
            const page = pages.find(p => p.url().includes('xiaohongshu.com/explore/'));
            
            if (!page) {
                throw new Error('未找到小红书笔记页面');
            }
            
            console.log('✅ 找到小红书笔记页面');
            console.log(`📄 当前URL: ${page.url()}`);
            
            return { browser, page };
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    // 深度评论区域滚动
    async deepCommentScroll(page) {
        console.log('🚀 开始深度评论区域滚动...');
        console.log(`🎯 目标: 加载接近 ${this.config.targetComments} 条评论`);
        
        let previousCommentCount = 0;
        let stableCount = 0;
        let totalScrolls = 0;
        
        // 首先滚动到评论区域
        console.log('📍 定位到评论区域...');
        await page.evaluate(() => {
            // 查找评论区域并滚动到那里
            const commentSelectors = [
                '[class*="comment"]',
                '[class*="Comment"]', 
                '[data-testid*="comment"]',
                'div:contains("条评论")',
                'div:contains("评论")'
            ];
            
            for (const selector of commentSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    console.log('找到评论区域:', selector);
                    break;
                }
            }
            
            // 如果没找到，滚动到页面中部
            window.scrollTo(0, document.body.scrollHeight * 0.6);
        });
        
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        for (let i = 0; i < this.config.maxScrollAttempts; i++) {
            console.log(`\n📜 深度滚动 ${i + 1}/${this.config.maxScrollAttempts}`);
            
            // 检查当前评论数量
            const currentCommentCount = await this.countComments(page);
            console.log(`   💬 当前评论数: ${currentCommentCount}`);
            
            if (currentCommentCount === previousCommentCount) {
                stableCount++;
                console.log(`   ⏸️ 评论数稳定 ${stableCount} 次`);
            } else {
                console.log(`   📈 新增评论: ${currentCommentCount - previousCommentCount}`);
                stableCount = 0;
                previousCommentCount = currentCommentCount;
            }
            
            // 如果连续5次没有新评论，尝试点击加载更多
            if (stableCount >= 5) {
                console.log('   🔄 尝试点击加载更多按钮...');
                const clickSuccess = await this.clickLoadMoreButtons(page);
                if (clickSuccess) {
                    stableCount = 0;
                    await new Promise(resolve => setTimeout(resolve, this.config.clickDelay));
                }
            }
            
            // 智能滚动策略
            await this.smartCommentScroll(page);
            
            // 检查是否达到目标
            if (currentCommentCount >= this.config.targetComments * 0.9) {
                console.log(`🎉 接近目标评论数！当前: ${currentCommentCount}`);
                break;
            }
            
            // 如果长时间没有新内容，停止
            if (stableCount >= 15) {
                console.log('   ⏹️ 长时间无新内容，停止滚动');
                break;
            }
            
            // 随机延迟
            const delay = this.config.scrollDelay + Math.random() * 1000;
            console.log(`   ⏱️ 等待 ${Math.round(delay)}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            
            totalScrolls++;
        }
        
        console.log(`✅ 深度滚动完成，总计滚动 ${totalScrolls} 次`);
        
        // 最终统计
        const finalCommentCount = await this.countComments(page);
        console.log(`📊 最终评论数: ${finalCommentCount}`);
        console.log(`📈 完成度: ${Math.round(finalCommentCount / this.config.targetComments * 100)}%`);
    }

    // 智能评论区域滚动
    async smartCommentScroll(page) {
        await page.evaluate(() => {
            // 多种滚动策略
            const scrollStrategies = [
                // 策略1: 小幅度滚动
                () => window.scrollBy(0, 300 + Math.random() * 200),
                
                // 策略2: 滚动到评论区域底部
                () => {
                    const comments = document.querySelectorAll('[class*="comment"], [class*="Comment"]');
                    if (comments.length > 0) {
                        const lastComment = comments[comments.length - 1];
                        lastComment.scrollIntoView({ behavior: 'smooth' });
                    }
                },
                
                // 策略3: 滚动到页面底部
                () => window.scrollTo(0, document.body.scrollHeight),
                
                // 策略4: 随机位置滚动
                () => {
                    const randomPosition = document.body.scrollHeight * (0.5 + Math.random() * 0.5);
                    window.scrollTo(0, randomPosition);
                }
            ];
            
            // 随机选择策略
            const strategy = scrollStrategies[Math.floor(Math.random() * scrollStrategies.length)];
            strategy();
        });
    }

    // 点击加载更多按钮
    async clickLoadMoreButtons(page) {
        try {
            const clickResults = await page.evaluate(() => {
                const results = [];
                
                // 查找各种"加载更多"相关的按钮和文字
                const loadMoreTexts = [
                    '加载更多', '展开更多', '查看更多', '显示更多', '更多评论',
                    '展开', '更多', '加载', '查看全部', '展开全部',
                    'Load more', 'Show more', 'View more'
                ];
                
                // 查找包含这些文字的可点击元素
                loadMoreTexts.forEach(text => {
                    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                        const elementText = el.textContent.trim();
                        return elementText.includes(text) && 
                               el.offsetHeight > 0 && 
                               el.offsetWidth > 0 &&
                               elementText.length < 50; // 避免点击长文本
                    });
                    
                    elements.forEach(el => {
                        try {
                            // 检查是否可点击
                            const style = window.getComputedStyle(el);
                            if (style.cursor === 'pointer' || 
                                el.tagName === 'BUTTON' || 
                                el.onclick || 
                                el.getAttribute('role') === 'button') {
                                
                                el.click();
                                results.push(`点击了: ${text} (${el.tagName})`);
                            }
                        } catch (e) {
                            // 忽略点击失败
                        }
                    });
                });
                
                // 查找按钮元素
                const buttons = document.querySelectorAll('button, [role="button"], .btn, [class*="button"]');
                buttons.forEach(btn => {
                    const btnText = btn.textContent.trim().toLowerCase();
                    if (btnText.includes('more') || btnText.includes('展开') || btnText.includes('加载')) {
                        try {
                            btn.click();
                            results.push(`点击按钮: ${btnText}`);
                        } catch (e) {
                            // 忽略点击失败
                        }
                    }
                });
                
                return results;
            });
            
            if (clickResults.length > 0) {
                console.log(`   ✅ 点击结果: ${clickResults.join(', ')}`);
                return true;
            } else {
                console.log(`   ⚠️ 未找到可点击的加载更多按钮`);
                return false;
            }
        } catch (error) {
            console.log(`   ❌ 点击按钮时出错: ${error.message}`);
            return false;
        }
    }

    // 统计当前评论数量
    async countComments(page) {
        return await page.evaluate(() => {
            const pageText = document.body.textContent;
            
            // 多种方式统计评论
            const methods = [
                // 方法1: 统计时间格式
                () => (pageText.match(/\d{2}-\d{2}/g) || []).length,
                
                // 方法2: 统计"回复"关键词
                () => (pageText.match(/\d+回复/g) || []).length,
                
                // 方法3: 统计评论相关元素
                () => document.querySelectorAll('[class*="comment"], [class*="Comment"]').length,
                
                // 方法4: 统计用户相关关键词
                () => (pageText.match(/求带|宝子|学姐|兼职/g) || []).length / 2
            ];
            
            // 取最大值作为估算
            const counts = methods.map(method => method());
            return Math.max(...counts);
        });
    }

    // 深度评论提取
    async deepExtractComments(page) {
        console.log('🧠 开始深度评论提取...');

        const result = await page.evaluate(() => {
            const data = {
                noteInfo: {
                    id: '',
                    title: '',
                    author: '',
                    totalCommentCount: 0
                },
                comments: [],
                extractStats: {
                    totalTextLength: 0,
                    commentSectionLength: 0,
                    processedLines: 0
                },
                extractTime: new Date().toISOString()
            };

            try {
                // 获取页面文本
                const pageText = document.body.textContent;
                data.extractStats.totalTextLength = pageText.length;

                // 提取笔记ID
                const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
                if (urlMatch) {
                    data.noteInfo.id = urlMatch[1];
                }

                // 提取标题
                const titleElement = document.querySelector('title');
                if (titleElement) {
                    data.noteInfo.title = titleElement.textContent.replace(' - 小红书', '');
                }

                // 提取评论总数
                const commentCountPatterns = [
                    /(\d+)\s*条评论/,
                    /共\s*(\d+)\s*条/,
                    /💬\s*(\d+)/
                ];

                for (const pattern of commentCountPatterns) {
                    const match = pageText.match(pattern);
                    if (match) {
                        data.noteInfo.totalCommentCount = parseInt(match[1]);
                        break;
                    }
                }

                // 提取作者
                const authorPatterns = [
                    /漫娴学姐[\s\S]*?招暑假工版/,
                    /作者[\s\S]*?漫娴学姐/
                ];

                for (const pattern of authorPatterns) {
                    const match = pageText.match(pattern);
                    if (match) {
                        data.noteInfo.author = '漫娴学姐 招暑假工版';
                        break;
                    }
                }

                return data;
            } catch (error) {
                console.error('提取基本信息时出错:', error);
                data.error = error.message;
                return data;
            }
        });

        // 在Node.js中进行深度解析
        const pageText = await page.evaluate(() => document.body.textContent);
        const comments = this.parseDeepComments(pageText);
        result.comments = comments;

        return result;
    }

    // 深度解析评论
    parseDeepComments(pageText) {
        console.log('🔍 开始深度解析评论...');

        const comments = [];

        // 更智能的评论区域识别
        let commentText = '';

        // 尝试多种方式提取评论区域
        const extractionMethods = [
            // 方法1: 从"条评论"开始
            () => {
                const match = pageText.match(/\d+\s*条评论([\s\S]*?)(?:相关推荐|猜你喜欢|更多笔记|$)/);
                return match ? match[1] : '';
            },

            // 方法2: 从评论关键词开始
            () => {
                const match = pageText.match(/(求带[\s\S]*?)(?:相关推荐|猜你喜欢|$)/);
                return match ? match[1] : '';
            },

            // 方法3: 查找包含大量时间格式的区域
            () => {
                const lines = pageText.split('\n');
                let startIndex = -1;
                let maxTimeCount = 0;

                for (let i = 0; i < lines.length - 20; i++) {
                    const chunk = lines.slice(i, i + 20).join('\n');
                    const timeCount = (chunk.match(/\d{2}-\d{2}/g) || []).length;

                    if (timeCount > maxTimeCount && timeCount >= 3) {
                        maxTimeCount = timeCount;
                        startIndex = i;
                    }
                }

                if (startIndex >= 0) {
                    return lines.slice(startIndex).join('\n');
                }
                return '';
            }
        ];

        for (const method of extractionMethods) {
            const extracted = method();
            if (extracted && extracted.length > commentText.length) {
                commentText = extracted;
            }
        }

        if (!commentText) {
            console.log('❌ 未找到评论区域，使用整个页面');
            commentText = pageText;
        } else {
            console.log(`📝 评论区域长度: ${commentText.length}`);
        }

        // 按行分割并过滤
        const lines = commentText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0 && !this.shouldSkipLine(line));

        console.log(`📄 有效行数: ${lines.length}`);

        let currentComment = null;
        let commentIndex = 0;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            // 检查是否是新评论的开始
            if (this.isCommentStart(line)) {
                // 保存前一个评论
                if (currentComment && this.isValidComment(currentComment)) {
                    this.finalizeComment(currentComment);
                    comments.push(currentComment);
                }

                // 创建新评论
                currentComment = this.createComment(++commentIndex, line);

                if (commentIndex <= 20) { // 显示前20个
                    console.log(`📝 发现评论 ${commentIndex}: ${line.substring(0, 50)}...`);
                }
            } else if (currentComment && line.length > 2) {
                // 添加到当前评论
                currentComment.rawContent += ' ' + line;
                currentComment.allLines.push(line);
            }
        }

        // 保存最后一个评论
        if (currentComment && this.isValidComment(currentComment)) {
            this.finalizeComment(currentComment);
            comments.push(currentComment);
        }

        console.log(`✅ 深度解析完成，提取到 ${comments.length} 条评论`);
        return comments;
    }

    // 判断是否应该跳过这一行
    shouldSkipLine(line) {
        const skipPatterns = [
            '加载中', '你还没有', '温馨提示', '广告屏蔽', '我知道了',
            '沪ICP备', '营业执照', '公网安备', '增值电信', '医疗器械',
            '创作服务', '直播管理', '专业号', '商家入驻', 'MCN入驻',
            'window.', 'function', 'console.', 'document.', '更多',
            '行吟信息', '黄浦区', '举报电话', '举报中心', '网络文化',
            '相关推荐', '猜你喜欢', '更多笔记', '发现', '首页'
        ];

        return skipPatterns.some(pattern => line.includes(pattern)) ||
               line.length < 3 ||
               /^[0-9\s\-:\.]+$/.test(line) ||
               /^[^\w\u4e00-\u9fff]+$/.test(line);
    }

    // 判断是否是评论开始
    isCommentStart(line) {
        // 作者标识
        if (line.includes('作者') && line.length < 100) return true;

        // 置顶评论
        if (line.includes('置顶评论') || line.includes('置顶')) return true;

        // 时间格式 (更宽松的匹配)
        if (line.match(/\d{2}-\d{2}/)) return true;
        if (line.match(/\d+小时前|\d+分钟前|\d+天前/)) return true;

        // 表情符号开头
        if (line.match(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/)) return true;

        // 用户名模式
        const userPatterns = [
            /^[A-Za-z0-9_\u4e00-\u9fff]{2,20}\s*\d{2}-\d{2}/,
            /^.*学姐.*\d{2}-\d{2}/,
            /^.*宝子.*\d{2}-\d{2}/,
            /^.*求带.*\d{2}-\d{2}/
        ];

        for (const pattern of userPatterns) {
            if (line.match(pattern)) return true;
        }

        // 关键词开头
        const keywords = ['求带', '宝子', '广州', '学姐', '兼职', '工作', '聊天员', '陪玩', '小红薯'];
        if (keywords.some(keyword => line.startsWith(keyword)) && line.length < 100) {
            return true;
        }

        return false;
    }

    // 创建评论对象
    createComment(index, firstLine) {
        const comment = {
            id: index,
            userId: '',
            username: '',
            content: '',
            time: '',
            likes: 0,
            replyCount: 0,
            isAuthor: firstLine.includes('作者'),
            isPinned: firstLine.includes('置顶'),
            rawContent: firstLine,
            allLines: [firstLine],
            extractedInfo: {}
        };

        // 提取时间
        const timePatterns = [
            /(\d{2}-\d{2})/,
            /(\d+小时前)/,
            /(\d+分钟前)/,
            /(\d+天前)/
        ];

        for (const pattern of timePatterns) {
            const match = firstLine.match(pattern);
            if (match) {
                comment.time = match[1];
                break;
            }
        }

        return comment;
    }

    // 完善评论
    finalizeComment(comment) {
        // 清理和提取内容
        let content = comment.rawContent;

        // 移除表情符号
        content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');

        // 移除时间
        content = content.replace(/\d{2}-\d{2}/g, '');
        content = content.replace(/\d+小时前|\d+分钟前|\d+天前/g, '');

        // 移除标识
        content = content.replace(/作者|置顶评论|回复|赞|仅自己可见/g, '');

        // 清理空格
        content = content.replace(/\s+/g, ' ').trim();

        // 从所有行中提取信息
        const allText = comment.allLines.join(' ');

        // 提取用户名
        const userPatterns = [
            /^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/,
            /^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/
        ];

        for (const pattern of userPatterns) {
            const match = comment.rawContent.match(pattern);
            if (match && match[1]) {
                comment.username = match[1].trim();
                content = content.replace(comment.username, '').trim();
                break;
            }
        }

        // 提取点赞数
        const likePatterns = [
            /(\d+)\s*赞/,
            /赞\s*(\d+)/,
            /❤️\s*(\d+)/
        ];

        for (const pattern of likePatterns) {
            const match = allText.match(pattern);
            if (match) {
                const num = parseInt(match[1]);
                if (num > 0 && num < 10000) {
                    comment.likes = num;
                    break;
                }
            }
        }

        // 提取回复数
        const replyPatterns = [
            /(\d+)\s*回复/,
            /回复\s*(\d+)/,
            /展开\s*(\d+)\s*条/
        ];

        for (const pattern of replyPatterns) {
            const match = allText.match(pattern);
            if (match) {
                comment.replyCount = parseInt(match[1]);
                break;
            }
        }

        // 提取用户ID
        const userIdPatterns = [
            /小红薯([A-F0-9]{8,})/,
            /user\/([a-f0-9]{20,})/,
            /uid[=:]([a-f0-9]{20,})/
        ];

        for (const pattern of userIdPatterns) {
            const match = allText.match(pattern);
            if (match) {
                comment.userId = match[1];
                break;
            }
        }

        comment.content = content;
        delete comment.rawContent;
        delete comment.allLines;
    }

    // 验证评论
    isValidComment(comment) {
        return comment &&
               comment.rawContent &&
               comment.rawContent.length >= this.config.minCommentLength &&
               comment.rawContent.length <= this.config.maxCommentLength;
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        try {
            console.log('🚀 启动深度评论加载器...');
            console.log(`🎯 目标: 深度加载接近 ${this.config.targetComments} 条评论`);
            console.log('🛡️ 策略: 评论区域定位 + 深度滚动 + 智能点击');

            const { browser, page } = await this.connectToBrowser();

            // 深度评论区域滚动
            await this.deepCommentScroll(page);

            // 深度评论提取
            const result = await this.deepExtractComments(page);

            // 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `deep_comments_${result.noteInfo.id}_${timestamp}.json`;
            this.saveToFile(result, filename);

            // 输出结果
            console.log('\n🎉 深度加载完成！');
            console.log('📊 最终统计:');
            console.log(`   🆔 笔记ID: ${result.noteInfo.id}`);
            console.log(`   📝 笔记标题: ${result.noteInfo.title || '未知'}`);
            console.log(`   👤 笔记作者: ${result.noteInfo.author || '未知'}`);
            console.log(`   🎯 目标评论数: ${result.noteInfo.totalCommentCount}`);
            console.log(`   💬 实际提取数: ${result.comments.length}`);
            console.log(`   📈 完成度: ${Math.round(result.comments.length / result.noteInfo.totalCommentCount * 100)}%`);
            console.log(`   📄 页面文本长度: ${result.extractStats.totalTextLength}`);
            console.log(`   📁 保存文件: ${filename}`);

            if (result.comments.length > 0) {
                console.log('\n👥 评论样本预览:');
                result.comments.slice(0, 5).forEach((comment, index) => {
                    console.log(`   ${index + 1}. 用户: ${comment.username || '未知'}`);
                    console.log(`      用户ID: ${comment.userId || '未提取到'}`);
                    console.log(`      时间: ${comment.time || '未知'}`);
                    console.log(`      内容: ${comment.content.substring(0, 60)}...`);
                    console.log(`      点赞: ${comment.likes} | 回复: ${comment.replyCount}`);
                    console.log(`      标识: ${comment.isAuthor ? '作者' : ''}${comment.isPinned ? '置顶' : ''}`);
                    console.log('');
                });

                // 质量统计
                const withUsername = result.comments.filter(c => c.username).length;
                const withUserId = result.comments.filter(c => c.userId).length;
                const withTime = result.comments.filter(c => c.time).length;

                console.log('📈 数据质量统计:');
                console.log(`   👤 有用户名: ${withUsername}/${result.comments.length} (${Math.round(withUsername/result.comments.length*100)}%)`);
                console.log(`   🆔 有用户ID: ${withUserId}/${result.comments.length} (${Math.round(withUserId/result.comments.length*100)}%)`);
                console.log(`   ⏰ 有时间: ${withTime}/${result.comments.length} (${Math.round(withTime/result.comments.length*100)}%)`);

                // 成功度评估
                const completionRate = result.comments.length / result.noteInfo.totalCommentCount;
                if (completionRate >= 0.8) {
                    console.log('\n🎉 优秀！已提取到大部分评论数据！');
                } else if (completionRate >= 0.5) {
                    console.log('\n👍 良好！已提取到一半以上的评论数据！');
                } else if (completionRate >= 0.2) {
                    console.log('\n📈 进步！提取到了更多评论数据！');
                } else {
                    console.log('\n💡 提示：可能需要手动滚动或等待更长时间加载');
                }
            }

            await browser.disconnect();

        } catch (error) {
            console.error('❌ 深度加载失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行深度加载器
if (require.main === module) {
    const loader = new DeepCommentLoader();
    loader.run();
}

module.exports = DeepCommentLoader;
