#!/usr/bin/env node

/**
 * 🎯 直接Puppeteer评论提取器
 * 绕过API，直接连接到调试端口
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class DirectPuppeteerExtractor {
    constructor() {
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        // 从比特浏览器界面看到的端口
        this.debugPort = 56906;
        
        this.config = {
            targetComments: 1472,
            maxScrollAttempts: 800,
            scrollDelay: 600,
            clickDelay: 400,
            waitForContent: 1200,
            aggressiveMode: true
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    // 直接连接到调试端口
    async connectDirectly() {
        console.log('🎯 直接连接到比特浏览器...');
        console.log(`🔗 调试端口: ${this.debugPort}`);
        
        try {
            // 尝试连接到指定端口
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${this.debugPort}`,
                defaultViewport: null
            });
            
            console.log('✅ 成功连接到浏览器');
            
            const pages = await browser.pages();
            console.log(`📄 找到 ${pages.length} 个页面`);
            
            // 列出所有页面
            for (let i = 0; i < pages.length; i++) {
                const page = pages[i];
                const url = page.url();
                const title = await page.title().catch(() => '无标题');
                console.log(`   页面 ${i + 1}: ${title} - ${url.substring(0, 80)}...`);
            }
            
            // 查找小红书页面
            let targetPage = null;
            for (let i = 0; i < pages.length; i++) {
                const page = pages[i];
                const url = page.url();
                
                if (url.includes('xiaohongshu.com/explore/') || 
                    url.includes('xhs') || 
                    url.includes('redbook')) {
                    targetPage = page;
                    console.log(`✅ 找到小红书页面: 页面 ${i + 1}`);
                    break;
                }
            }
            
            // 如果没找到小红书页面，使用第一个页面
            if (!targetPage && pages.length > 0) {
                targetPage = pages[0];
                console.log(`💡 使用第一个页面作为目标`);
            }
            
            if (!targetPage) {
                throw new Error('没有找到可用的页面');
            }
            
            console.log(`🎯 目标页面: ${await targetPage.title()}`);
            console.log(`🔗 页面URL: ${targetPage.url()}`);
            
            return { browser, page: targetPage };
            
        } catch (error) {
            console.error('❌ 直接连接失败:', error.message);
            
            // 如果指定端口失败，尝试其他常见端口
            const fallbackPorts = [9222, 9223, 9224, 9225, 55276, 54345];
            
            for (const port of fallbackPorts) {
                try {
                    console.log(`🔄 尝试备用端口: ${port}`);
                    const browser = await puppeteer.connect({
                        browserURL: `http://localhost:${port}`,
                        defaultViewport: null
                    });
                    
                    const pages = await browser.pages();
                    if (pages.length > 0) {
                        console.log(`✅ 备用端口 ${port} 连接成功`);
                        this.debugPort = port;
                        return { browser, page: pages[0] };
                    }
                } catch (e) {
                    console.log(`❌ 备用端口 ${port} 失败`);
                    continue;
                }
            }
            
            throw new Error('无法连接到任何调试端口，请确保比特浏览器正在运行');
        }
    }

    // 超级激进滚动
    async superAggressiveScroll(page) {
        console.log('🚀 开始超级激进滚动...');
        console.log(`🎯 目标: ${this.config.targetComments} 条评论`);
        
        let currentCommentCount = 0;
        let previousCommentCount = 0;
        let stableCount = 0;
        let totalScrolls = 0;
        let totalClicks = 0;
        let maxCommentsSeen = 0;
        
        // 首先快速定位到评论区域
        await this.quickNavigateToComments(page);
        
        for (let i = 0; i < this.config.maxScrollAttempts; i++) {
            console.log(`\n🔥 超级滚动 ${i + 1}/${this.config.maxScrollAttempts}`);
            
            // 统计当前评论数量
            currentCommentCount = await this.countCommentsAdvanced(page);
            console.log(`   💬 当前评论数: ${currentCommentCount}`);
            
            // 更新最大值
            if (currentCommentCount > maxCommentsSeen) {
                maxCommentsSeen = currentCommentCount;
                console.log(`   🎯 新记录: ${maxCommentsSeen} 条评论！`);
            }
            
            // 检查是否达到目标
            if (currentCommentCount >= this.config.targetComments * 0.95) {
                console.log(`🎉 接近目标！当前: ${currentCommentCount}/${this.config.targetComments}`);
                break;
            }
            
            // 检查进度
            if (currentCommentCount === previousCommentCount) {
                stableCount++;
                console.log(`   ⏸️ 评论数稳定 ${stableCount} 次`);
            } else {
                const newComments = currentCommentCount - previousCommentCount;
                console.log(`   📈 新增评论: ${newComments} 条`);
                stableCount = 0;
                previousCommentCount = currentCommentCount;
            }
            
            // 超级激进点击策略
            if (stableCount >= 2) {
                console.log('   💥 超级激进点击...');
                const clickSuccess = await this.superClick(page);
                if (clickSuccess) {
                    totalClicks++;
                    stableCount = 0;
                    console.log(`   ✅ 点击成功！总计: ${totalClicks} 次`);
                }
            }
            
            // 执行多样化滚动
            await this.diversifiedScroll(page, i);
            
            // 如果长时间无新内容，尝试刷新策略
            if (stableCount >= 20) {
                console.log('   🔄 尝试刷新策略...');
                await this.refreshStrategy(page);
                stableCount = 0;
            }
            
            // 动态等待时间
            let waitTime = this.config.scrollDelay;
            if (currentCommentCount > previousCommentCount) {
                waitTime = Math.max(300, waitTime * 0.7); // 有新内容时加速
            } else if (stableCount >= 5) {
                waitTime = Math.min(1500, waitTime * 1.2); // 无新内容时稍微减速
            }
            
            await new Promise(resolve => setTimeout(resolve, waitTime));
            totalScrolls++;
            
            // 每100次滚动输出进度
            if (i % 100 === 0 && i > 0) {
                const progress = Math.round((currentCommentCount / this.config.targetComments) * 100);
                console.log(`\n📊 进度报告:`);
                console.log(`   📈 完成度: ${progress}% (${currentCommentCount}/${this.config.targetComments})`);
                console.log(`   📜 总滚动次数: ${totalScrolls}`);
                console.log(`   💥 总点击次数: ${totalClicks}`);
                console.log(`   🏆 历史最高: ${maxCommentsSeen}`);
            }
        }
        
        console.log(`\n✅ 超级滚动完成！`);
        console.log(`📊 最终统计:`);
        console.log(`   💬 最终评论数: ${currentCommentCount}`);
        console.log(`   📈 完成度: ${Math.round((currentCommentCount / this.config.targetComments) * 100)}%`);
        console.log(`   📜 总滚动次数: ${totalScrolls}`);
        console.log(`   💥 总点击次数: ${totalClicks}`);
        console.log(`   🏆 历史最高: ${maxCommentsSeen}`);
        
        return currentCommentCount;
    }

    // 快速导航到评论区域
    async quickNavigateToComments(page) {
        console.log('🎯 快速导航到评论区域...');
        
        await page.evaluate(() => {
            // 快速滚动到页面中部
            window.scrollTo(0, document.body.scrollHeight * 0.5);
            
            // 查找评论区域
            const commentIndicators = ['条评论', '评论', 'comment', '💬'];
            
            for (const indicator of commentIndicators) {
                const elements = Array.from(document.querySelectorAll('*')).filter(el => 
                    el.textContent.includes(indicator) && el.offsetHeight > 0
                );
                
                if (elements.length > 0) {
                    elements[0].scrollIntoView({ behavior: 'auto', block: 'center' });
                    console.log('找到评论区域:', indicator);
                    return;
                }
            }
            
            // 如果没找到，滚动到页面70%位置
            window.scrollTo(0, document.body.scrollHeight * 0.7);
        });
        
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 多样化滚动策略
    async diversifiedScroll(page, scrollIndex) {
        const strategies = [
            // 策略1: 小步滚动
            () => page.evaluate(() => window.scrollBy(0, 180 + Math.random() * 80)),
            
            // 策略2: 中步滚动
            () => page.evaluate(() => window.scrollBy(0, 350 + Math.random() * 150)),
            
            // 策略3: 大步滚动
            () => page.evaluate(() => window.scrollBy(0, 550 + Math.random() * 250)),
            
            // 策略4: 滚动到底部
            () => page.evaluate(() => window.scrollTo(0, document.body.scrollHeight)),
            
            // 策略5: 滚动到最后评论
            () => page.evaluate(() => {
                const comments = document.querySelectorAll('[class*="comment"], [class*="Comment"], [data-testid*="comment"]');
                if (comments.length > 0) {
                    const lastComment = comments[comments.length - 1];
                    lastComment.scrollIntoView({ behavior: 'auto' });
                }
            }),
            
            // 策略6: 随机位置滚动
            () => page.evaluate(() => {
                const randomY = document.body.scrollHeight * (0.6 + Math.random() * 0.4);
                window.scrollTo(0, randomY);
            }),
            
            // 策略7: 向上再向下
            () => page.evaluate(() => {
                window.scrollBy(0, -250);
                setTimeout(() => window.scrollBy(0, 500), 150);
            }),
            
            // 策略8: 连续滚动
            () => page.evaluate(() => {
                for (let i = 0; i < 4; i++) {
                    setTimeout(() => window.scrollBy(0, 120), i * 80);
                }
            })
        ];
        
        // 根据滚动次数选择策略
        let strategyIndex;
        if (scrollIndex < 100) {
            strategyIndex = scrollIndex % 4; // 前100次使用温和策略
        } else if (scrollIndex < 300) {
            strategyIndex = scrollIndex % 6; // 中期使用多样化策略
        } else {
            strategyIndex = scrollIndex % strategies.length; // 后期使用所有策略
        }
        
        await strategies[strategyIndex]();
    }

    // 超级点击
    async superClick(page) {
        try {
            const clickResults = await page.evaluate(() => {
                const results = [];

                // 超级激进文字点击
                const targetTexts = [
                    '加载更多', '展开更多', '查看更多', '显示更多', '更多评论', '更多内容',
                    '展开', '更多', '加载', '查看全部', '展开全部', '点击加载', '点击展开',
                    '显示', '查看', '展开回复', '更多回复', '全部回复', '查看回复',
                    'Load more', 'Show more', 'View more', 'Expand', 'More'
                ];

                targetTexts.forEach(text => {
                    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                        const elementText = el.textContent.trim();
                        return elementText.includes(text) &&
                               el.offsetHeight > 0 &&
                               el.offsetWidth > 0 &&
                               elementText.length < 300;
                    });

                    elements.forEach(el => {
                        try {
                            el.click();
                            results.push(`文字点击: ${text}`);
                        } catch (e) {
                            try {
                                el.dispatchEvent(new MouseEvent('click', {
                                    view: window,
                                    bubbles: true,
                                    cancelable: true
                                }));
                                results.push(`事件触发: ${text}`);
                            } catch (e2) {
                                // 忽略失败
                            }
                        }
                    });
                });

                // 超级激进按钮点击
                const buttonSelectors = [
                    'button', '[role="button"]', '.btn', '[class*="button"]',
                    '[class*="load"]', '[class*="more"]', '[class*="expand"]',
                    '[class*="show"]', '[class*="view"]', 'a[href*="more"]'
                ];

                buttonSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        Array.from(elements).forEach(el => {
                            if (el.offsetHeight > 0 && el.offsetWidth > 0) {
                                const text = el.textContent.trim().toLowerCase();
                                const hasTargetText = [
                                    'more', '更多', '展开', '加载', 'load', 'expand',
                                    'show', 'view', '显示', '查看', '全部', '评论'
                                ].some(keyword => text.includes(keyword));

                                if (hasTargetText) {
                                    try {
                                        el.click();
                                        results.push(`按钮点击: ${text.substring(0, 20)}`);
                                    } catch (e) {
                                        try {
                                            el.dispatchEvent(new MouseEvent('click', {
                                                view: window,
                                                bubbles: true,
                                                cancelable: true
                                            }));
                                            results.push(`按钮事件: ${text.substring(0, 20)}`);
                                        } catch (e2) {
                                            // 忽略失败
                                        }
                                    }
                                }
                            }
                        });
                    } catch (e) {
                        // 忽略选择器错误
                    }
                });

                return results;
            });

            if (clickResults.length > 0) {
                console.log(`   💥 超级点击成功: ${clickResults.slice(0, 3).join(', ')}`);
                return true;
            }
            return false;
        } catch (error) {
            console.log(`   ❌ 超级点击出错: ${error.message}`);
            return false;
        }
    }

    // 刷新策略
    async refreshStrategy(page) {
        await page.evaluate(() => {
            // 刷新策略1: 疯狂滚动
            for (let i = 0; i < 15; i++) {
                window.scrollBy(0, 100);
                window.scrollBy(0, -50);
            }

            // 刷新策略2: 触发各种事件
            ['scroll', 'resize', 'focus', 'blur'].forEach(eventType => {
                try {
                    window.dispatchEvent(new Event(eventType));
                } catch (e) {
                    // 忽略失败
                }
            });

            // 刷新策略3: 滚动到各个位置
            const positions = [0, 0.3, 0.6, 0.9, 1.0];
            positions.forEach((pos, index) => {
                setTimeout(() => {
                    window.scrollTo(0, document.body.scrollHeight * pos);
                }, index * 100);
            });
        });

        await new Promise(resolve => setTimeout(resolve, 1500));
    }

    // 高级评论统计
    async countCommentsAdvanced(page) {
        return await page.evaluate(() => {
            const pageText = document.body.textContent;

            // 高级统计方法
            const methods = [
                // 方法1: 时间格式统计
                () => {
                    const timePatterns = [
                        /\d{2}-\d{2}/g,
                        /\d+小时前/g,
                        /\d+分钟前/g,
                        /\d+天前/g
                    ];

                    let totalMatches = 0;
                    timePatterns.forEach(pattern => {
                        const matches = pageText.match(pattern) || [];
                        totalMatches += matches.length;
                    });

                    return totalMatches;
                },

                // 方法2: 回复数统计
                () => {
                    const replyMatches = pageText.match(/\d+回复/g) || [];
                    return replyMatches.length;
                },

                // 方法3: DOM元素统计
                () => {
                    const selectors = [
                        '[class*="comment"]',
                        '[class*="Comment"]',
                        '[data-testid*="comment"]',
                        '[class*="reply"]',
                        '[class*="user"]'
                    ];

                    let maxCount = 0;
                    selectors.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            maxCount = Math.max(maxCount, elements.length);
                        } catch (e) {
                            // 忽略选择器错误
                        }
                    });

                    return maxCount;
                },

                // 方法4: 关键词统计
                () => {
                    const keywords = ['求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯'];
                    let totalMatches = 0;

                    keywords.forEach(keyword => {
                        const regex = new RegExp(keyword, 'g');
                        const matches = pageText.match(regex) || [];
                        totalMatches += matches.length;
                    });

                    return Math.floor(totalMatches / 3);
                },

                // 方法5: 用户ID统计
                () => {
                    const userIdMatches = pageText.match(/小红薯[A-F0-9]{8}/g) || [];
                    return userIdMatches.length;
                },

                // 方法6: 点赞数统计
                () => {
                    const likeMatches = pageText.match(/\d+赞/g) || [];
                    return likeMatches.length;
                }
            ];

            // 计算所有方法的结果
            const counts = methods.map((method, index) => {
                try {
                    const result = method();
                    return result;
                } catch (e) {
                    return 0;
                }
            });

            // 取最大值
            return Math.max(...counts);
        });
    }

    // 提取所有评论
    async extractAllComments(page) {
        console.log('🧠 开始提取所有评论...');

        // 获取页面文本
        const pageText = await page.evaluate(() => document.body.textContent);
        console.log(`📄 页面文本长度: ${pageText.length}`);

        // 提取基本信息
        const noteInfo = await page.evaluate(() => {
            const info = {
                id: '',
                title: '',
                author: '',
                totalCommentCount: 0
            };

            // 提取笔记ID
            const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
            if (urlMatch) {
                info.id = urlMatch[1];
            }

            // 提取标题
            const titleElement = document.querySelector('title');
            if (titleElement) {
                info.title = titleElement.textContent.replace(' - 小红书', '');
            }

            // 提取评论总数
            const pageText = document.body.textContent;
            const commentCountMatch = pageText.match(/(\d+)\s*条评论/);
            if (commentCountMatch) {
                info.totalCommentCount = parseInt(commentCountMatch[1]);
            }

            // 提取作者
            if (pageText.includes('漫娴学姐')) {
                info.author = '漫娴学姐 招暑假工版';
            }

            return info;
        });

        // 解析评论
        const comments = this.parseComments(pageText);

        return {
            noteInfo,
            comments,
            extractStats: {
                totalTextLength: pageText.length,
                successfulExtractions: comments.length,
                extractionMethods: ['direct-puppeteer']
            },
            extractTime: new Date().toISOString()
        };
    }

    // 解析评论（简化版）
    parseComments(pageText) {
        console.log('🔍 开始解析评论...');

        const allComments = [];

        // 时间模式解析
        const timePattern = /(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/g;
        const timeMatches = [];
        let match;

        while ((match = timePattern.exec(pageText)) !== null) {
            timeMatches.push({
                time: match[1],
                index: match.index
            });
        }

        for (let i = 0; i < timeMatches.length; i++) {
            const currentTime = timeMatches[i];
            const nextTime = timeMatches[i + 1];

            const startIndex = Math.max(0, currentTime.index - 200);
            const endIndex = nextTime ? nextTime.index : currentTime.index + 1000;

            const commentText = pageText.substring(startIndex, endIndex).trim();

            if (commentText.length > 20 && commentText.length < 2000) {
                const comment = this.createComment(commentText, allComments.length + 1, 'time');
                if (comment) {
                    allComments.push(comment);
                }
            }
        }

        // 关键词模式解析
        const keywords = ['求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯'];

        keywords.forEach(keyword => {
            const regex = new RegExp(keyword, 'g');
            let match;

            while ((match = regex.exec(pageText)) !== null) {
                const startIndex = Math.max(0, match.index - 150);
                const endIndex = Math.min(pageText.length, match.index + 800);

                const commentText = pageText.substring(startIndex, endIndex).trim();

                if (commentText.length > 15 && commentText.length < 1500) {
                    const comment = this.createComment(commentText, allComments.length + 1, 'keyword');
                    if (comment) {
                        allComments.push(comment);
                    }
                }
            }
        });

        // 去重
        const uniqueComments = this.deduplicateComments(allComments);

        console.log(`✅ 解析完成，提取到 ${uniqueComments.length} 条评论`);
        return uniqueComments;
    }

    // 创建评论对象
    createComment(text, id, source) {
        const comment = {
            id: id,
            userId: '',
            username: '',
            content: '',
            time: '',
            likes: 0,
            replyCount: 0,
            isAuthor: text.includes('作者'),
            isPinned: text.includes('置顶'),
            extractedInfo: {
                source: source,
                originalLength: text.length
            }
        };

        // 提取时间
        const timeMatch = text.match(/(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/);
        if (timeMatch) {
            comment.time = timeMatch[1];
        }

        // 提取用户名
        const userPatterns = [
            /^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带/,
            /^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})/
        ];

        for (const pattern of userPatterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                comment.username = match[1].trim();
                break;
            }
        }

        // 清理内容
        let content = text;
        content = content.replace(/\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前/g, '');
        content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');
        content = content.replace(/作者|置顶评论|回复|赞|仅自己可见/g, '');
        if (comment.username) {
            content = content.replace(comment.username, '');
        }
        content = content.replace(/\s+/g, ' ').trim();

        comment.content = content;

        // 提取数字信息
        const likeMatch = text.match(/(\d+)\s*赞/);
        if (likeMatch) {
            comment.likes = parseInt(likeMatch[1]);
        }

        const replyMatch = text.match(/(\d+)\s*回复/);
        if (replyMatch) {
            comment.replyCount = parseInt(replyMatch[1]);
        }

        // 提取用户ID
        const userIdMatch = text.match(/小红薯([A-F0-9]{8,})/);
        if (userIdMatch) {
            comment.userId = userIdMatch[1];
        }

        return comment.content.length >= 5 ? comment : null;
    }

    // 去重
    deduplicateComments(comments) {
        const unique = [];
        const seen = new Set();

        comments.forEach(comment => {
            const key = comment.content.substring(0, 30);
            if (!seen.has(key)) {
                seen.add(key);
                unique.push(comment);
            }
        });

        // 重新分配ID
        unique.forEach((comment, index) => {
            comment.id = index + 1;
        });

        return unique;
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        try {
            console.log('🎯 启动直接Puppeteer评论提取器...');
            console.log(`🎯 目标: 获取所有 ${this.config.targetComments} 条评论`);
            console.log('🛡️ 策略: 直接连接 + 超级滚动 + 智能解析');

            const { browser, page } = await this.connectDirectly();

            // 超级激进滚动
            const loadedCommentCount = await this.superAggressiveScroll(page);

            // 提取所有评论
            const result = await this.extractAllComments(page);

            // 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `direct_comments_${result.noteInfo.id}_${timestamp}.json`;
            this.saveToFile(result, filename);

            // 输出结果
            console.log('\n🎉 直接提取完成！');
            console.log('📊 最终统计:');
            console.log(`   🔗 调试端口: ${this.debugPort}`);
            console.log(`   📝 笔记ID: ${result.noteInfo.id}`);
            console.log(`   📝 笔记标题: ${result.noteInfo.title || '未知'}`);
            console.log(`   👤 笔记作者: ${result.noteInfo.author || '未知'}`);
            console.log(`   🎯 目标评论数: ${result.noteInfo.totalCommentCount}`);
            console.log(`   💬 实际提取数: ${result.comments.length}`);
            console.log(`   📈 完成度: ${Math.round(result.comments.length / result.noteInfo.totalCommentCount * 100)}%`);
            console.log(`   📄 页面文本长度: ${result.extractStats.totalTextLength}`);
            console.log(`   📁 保存文件: ${filename}`);

            if (result.comments.length > 0) {
                console.log('\n👥 评论详细预览:');
                result.comments.slice(0, 20).forEach((comment, index) => {
                    console.log(`\n   ${index + 1}. 评论ID: ${comment.id}`);
                    console.log(`      👤 用户: ${comment.username || '未知'}`);
                    console.log(`      🆔 用户ID: ${comment.userId || '未提取到'}`);
                    console.log(`      ⏰ 时间: ${comment.time || '未知'}`);
                    console.log(`      💬 内容: ${comment.content.substring(0, 120)}${comment.content.length > 120 ? '...' : ''}`);
                    console.log(`      👍 点赞: ${comment.likes} | 💬 回复: ${comment.replyCount}`);
                    console.log(`      📊 来源: ${comment.extractedInfo?.source || '未知'}`);
                });

                // 质量统计
                const withUsername = result.comments.filter(c => c.username).length;
                const withUserId = result.comments.filter(c => c.userId).length;
                const withTime = result.comments.filter(c => c.time).length;

                console.log('\n📈 数据质量统计:');
                console.log(`   👤 有用户名: ${withUsername}/${result.comments.length} (${Math.round(withUsername/result.comments.length*100)}%)`);
                console.log(`   🆔 有用户ID: ${withUserId}/${result.comments.length} (${Math.round(withUserId/result.comments.length*100)}%)`);
                console.log(`   ⏰ 有时间: ${withTime}/${result.comments.length} (${Math.round(withTime/result.comments.length*100)}%)`);

                // 兼职信息分析
                const jobKeywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱', '月入'];
                const jobComments = result.comments.filter(c =>
                    jobKeywords.some(keyword => c.content.includes(keyword))
                );

                if (jobComments.length > 0) {
                    console.log(`\n💼 兼职相关评论: ${jobComments.length}/${result.comments.length} (${Math.round(jobComments.length/result.comments.length*100)}%)`);

                    console.log('\n💼 兼职评论示例:');
                    jobComments.slice(0, 10).forEach((comment, index) => {
                        console.log(`   ${index + 1}. ${comment.username || '匿名'} (${comment.time || '未知时间'}): ${comment.content.substring(0, 100)}...`);
                    });
                }

                // 成功度评估
                const completionRate = result.comments.length / result.noteInfo.totalCommentCount;
                if (completionRate >= 0.8) {
                    console.log('\n🎉 优秀！已提取到大部分评论数据！');
                } else if (completionRate >= 0.5) {
                    console.log('\n👍 良好！已提取到一半以上的评论数据！');
                } else if (completionRate >= 0.3) {
                    console.log('\n📈 进步！直接提取效果显著！');
                } else {
                    console.log('\n💡 提示：可能需要更长时间的滚动或手动干预');
                }
            }

            await browser.disconnect();

            console.log('\n✅ 直接提取任务完成！');
            console.log(`📁 数据文件: ${filename}`);
            console.log(`🔗 调试端口: ${this.debugPort}`);

        } catch (error) {
            console.error('❌ 直接提取失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行直接提取器
if (require.main === module) {
    const extractor = new DirectPuppeteerExtractor();
    extractor.run();
}

module.exports = DirectPuppeteerExtractor;
