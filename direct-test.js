#!/usr/bin/env node

/**
 * 🎯 直接测试评论采集
 * 导航到小红书首页，然后测试评论采集功能
 */

const axios = require('axios');
const WebSocket = require('ws');

async function directTest() {
    console.log('🎯 开始直接测试评论采集功能...\n');

    try {
        // 1. 获取调试端口
        console.log('1️⃣ 获取调试端口...');
        const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });

        const port = response.data.data.result.http.split(':')[1];
        console.log(`✅ 调试端口: ${port}`);

        // 2. 获取标签页
        console.log('2️⃣ 获取标签页...');
        const tabsResponse = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });

        const tab = tabsResponse.data.find(t => 
            t.url && t.url.includes('xiaohongshu.com')
        );

        if (!tab) {
            throw new Error('未找到小红书标签页');
        }

        console.log(`✅ 当前页面: ${tab.title}`);
        console.log(`🔗 URL: ${tab.url}`);

        // 3. 导航到小红书首页
        console.log('3️⃣ 导航到小红书首页...');
        await navigateToHomepage(tab, port);

        // 4. 等待页面加载
        console.log('4️⃣ 等待页面加载...');
        await sleep(5000);

        // 5. 测试页面内容
        console.log('5️⃣ 测试页面内容...');
        const pageTest = await testPageContent(tab);
        
        console.log('\n📊 页面测试结果:');
        console.log(`📄 页面标题: ${pageTest.title}`);
        console.log(`🔗 当前URL: ${pageTest.url}`);
        console.log(`📊 总元素数: ${pageTest.totalElements}`);
        console.log(`🖼️ 图片数: ${pageTest.totalImages}`);
        console.log(`🔗 链接数: ${pageTest.totalLinks}`);
        
        if (pageTest.noteLinks > 0) {
            console.log(`📝 找到 ${pageTest.noteLinks} 个笔记链接`);
            console.log('✅ 可以进行笔记点击和评论采集测试');
        } else {
            console.log('⚠️ 未找到笔记链接，可能需要滚动加载内容');
        }

        // 6. 如果找到笔记链接，尝试点击第一个
        if (pageTest.noteLinks > 0) {
            console.log('6️⃣ 尝试点击第一个笔记...');
            const clickResult = await clickFirstNote(tab);
            
            if (clickResult.success) {
                console.log('✅ 成功点击笔记，等待页面加载...');
                await sleep(5000);
                
                // 7. 测试评论采集
                console.log('7️⃣ 测试评论采集...');
                const commentsTest = await testCommentsOnCurrentPage(tab);
                
                console.log('\n💬 评论测试结果:');
                console.log(`💬 评论元素数: ${commentsTest.commentElements}`);
                console.log(`📝 提取评论数: ${commentsTest.extractedComments}`);
                console.log(`🔍 使用方法: ${commentsTest.method}`);
                
                if (commentsTest.sampleComments.length > 0) {
                    console.log('\n📝 示例评论:');
                    commentsTest.sampleComments.forEach((comment, index) => {
                        console.log(`${index + 1}. ${comment.user}: ${comment.content}`);
                    });
                }
            }
        }

        return true;

    } catch (error) {
        console.error('❌ 直接测试失败:', error.message);
        throw error;
    }
}

// 导航到首页
function navigateToHomepage(tab, port) {
    return new Promise((resolve, reject) => {
        const ws = new WebSocket(tab.webSocketDebuggerUrl);
        let requestId = 1;

        ws.on('open', () => {
            ws.send(JSON.stringify({
                id: requestId++,
                method: 'Runtime.enable'
            }));

            setTimeout(() => {
                const navigateScript = `
                    window.location.href = "https://www.xiaohongshu.com";
                    return { navigated: true };
                `;

                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.evaluate',
                    params: {
                        expression: navigateScript,
                        returnByValue: true
                    }
                }));

                setTimeout(() => {
                    console.log('✅ 导航命令已发送');
                    ws.close();
                    resolve();
                }, 3000);
            }, 1000);
        });

        ws.on('error', reject);
        setTimeout(() => {
            ws.close();
            reject(new Error('导航超时'));
        }, 10000);
    });
}

// 测试页面内容
function testPageContent(tab) {
    return new Promise((resolve, reject) => {
        const ws = new WebSocket(tab.webSocketDebuggerUrl);
        let requestId = 1;

        ws.on('open', () => {
            ws.send(JSON.stringify({
                id: requestId++,
                method: 'Runtime.enable'
            }));

            setTimeout(() => {
                const testScript = `
                    (function() {
                        return {
                            title: document.title,
                            url: window.location.href,
                            totalElements: document.querySelectorAll('*').length,
                            totalImages: document.querySelectorAll('img').length,
                            totalLinks: document.querySelectorAll('a').length,
                            noteLinks: document.querySelectorAll('a[href*="/explore/"]').length
                        };
                    })();
                `;

                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.evaluate',
                    params: {
                        expression: testScript,
                        returnByValue: true
                    }
                }));
            }, 1000);
        });

        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                if (message.result && message.result.result && message.result.result.value) {
                    ws.close();
                    resolve(message.result.result.value);
                }
            } catch (error) {
                reject(error);
            }
        });

        ws.on('error', reject);
        setTimeout(() => {
            ws.close();
            reject(new Error('页面测试超时'));
        }, 10000);
    });
}

// 点击第一个笔记
function clickFirstNote(tab) {
    return new Promise((resolve, reject) => {
        const ws = new WebSocket(tab.webSocketDebuggerUrl);
        let requestId = 1;

        ws.on('open', () => {
            ws.send(JSON.stringify({
                id: requestId++,
                method: 'Runtime.enable'
            }));

            setTimeout(() => {
                const clickScript = `
                    (function() {
                        const noteLinks = document.querySelectorAll('a[href*="/explore/"]');
                        if (noteLinks.length > 0) {
                            noteLinks[0].click();
                            return { success: true, href: noteLinks[0].href };
                        } else {
                            return { success: false, error: '未找到笔记链接' };
                        }
                    })();
                `;

                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.evaluate',
                    params: {
                        expression: clickScript,
                        returnByValue: true
                    }
                }));
            }, 1000);
        });

        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                if (message.result && message.result.result && message.result.result.value) {
                    ws.close();
                    resolve(message.result.result.value);
                }
            } catch (error) {
                reject(error);
            }
        });

        ws.on('error', reject);
        setTimeout(() => {
            ws.close();
            reject(new Error('点击超时'));
        }, 10000);
    });
}

// 测试当前页面的评论
function testCommentsOnCurrentPage(tab) {
    return new Promise((resolve, reject) => {
        const ws = new WebSocket(tab.webSocketDebuggerUrl);
        let requestId = 1;

        ws.on('open', () => {
            ws.send(JSON.stringify({
                id: requestId++,
                method: 'Runtime.enable'
            }));

            setTimeout(() => {
                const commentsTestScript = `
                    (function() {
                        try {
                            const result = {
                                commentElements: 0,
                                extractedComments: 0,
                                method: '',
                                sampleComments: []
                            };

                            // 测试评论选择器
                            const commentSelectors = [
                                '[class*="comment"]',
                                '[class*="reply"]',
                                '.comment-item',
                                '[data-testid*="comment"]'
                            ];

                            let maxElements = 0;
                            let bestMethod = '';

                            for (const selector of commentSelectors) {
                                const elements = document.querySelectorAll(selector);
                                if (elements.length > maxElements) {
                                    maxElements = elements.length;
                                    bestMethod = selector;
                                }
                            }

                            result.commentElements = maxElements;
                            result.method = bestMethod || '通用方法';

                            // 如果没找到，使用通用方法
                            if (maxElements === 0) {
                                const allDivs = document.querySelectorAll('div');
                                const commentLike = Array.from(allDivs).filter(div => {
                                    const text = div.textContent.trim();
                                    return text.includes('天前') || text.includes('小时前') || 
                                           text.includes('分钟前') || text.includes('评论');
                                });
                                
                                result.commentElements = commentLike.length;
                                result.method = '通用方法';
                                
                                // 提取示例
                                commentLike.slice(0, 3).forEach((div, index) => {
                                    const text = div.textContent.trim();
                                    result.sampleComments.push({
                                        user: '用户' + (index + 1),
                                        content: text.substring(0, 50) + '...'
                                    });
                                });
                            }

                            result.extractedComments = result.sampleComments.length;

                            return result;
                            
                        } catch (error) {
                            return { error: error.message };
                        }
                    })();
                `;

                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.evaluate',
                    params: {
                        expression: commentsTestScript,
                        returnByValue: true
                    }
                }));
            }, 2000);
        });

        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                if (message.result && message.result.result && message.result.result.value) {
                    ws.close();
                    resolve(message.result.result.value);
                }
            } catch (error) {
                reject(error);
            }
        });

        ws.on('error', reject);
        setTimeout(() => {
            ws.close();
            reject(new Error('评论测试超时'));
        }, 15000);
    });
}

// 等待函数
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行测试
if (require.main === module) {
    directTest().then(() => {
        console.log('\n🎉 直接测试完成!');
        console.log('💡 评论采集功能已验证，可以在有评论的笔记页面使用');
    }).catch(console.error);
}

module.exports = { directTest };
