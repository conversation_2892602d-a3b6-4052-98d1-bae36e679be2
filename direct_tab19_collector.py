#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 直接连接19号窗口小红书评论采集器
使用Chrome DevTools API直接连接到19号标签页
"""

import json
import time
import requests
import websocket
from datetime import datetime
from typing import Dict, Any, List


class DirectTab19Collector:
    """直接连接19号窗口评论采集器"""
    
    def __init__(self):
        self.debug_ports = [63524, 9222, 51859, 58222]
        self.active_port = None
        self.ws = None
        self.request_id = 1
        self.comments_data = {
            'note_url': '',
            'note_title': '',
            'timestamp': '',
            'comments': [],
            'summary': {
                'total_comments': 0,
                'total_replies': 0,
                'total_likes': 0,
                'strategy': 'direct-tab19'
            }
        }
    
    def find_active_debug_port(self):
        """查找活跃的调试端口"""
        print("🔍 查找Chrome调试端口...")
        
        for port in self.debug_ports:
            try:
                print(f"   🔌 尝试端口 {port}...")
                response = requests.get(f'http://127.0.0.1:{port}/json', timeout=3)
                if response.status_code == 200:
                    self.active_port = port
                    print(f"✅ 找到活跃端口: {port}")
                    return response.json()
            except:
                continue
        
        print("❌ 没有找到活跃的Chrome调试端口")
        print("💡 请确保Chrome浏览器以调试模式启动:")
        print("   chrome.exe --remote-debugging-port=9222")
        return []
    
    def find_tab19_or_xiaohongshu(self, tabs):
        """查找19号标签页或小红书标签页"""
        print("🔍 查找目标标签页...")
        
        # 方法1: 查找第19个标签页
        if len(tabs) >= 19:
            tab19 = tabs[18]  # 索引从0开始
            print(f"✅ 找到19号标签页: {tab19['title'][:50]}...")
            return tab19
        
        # 方法2: 查找小红书标签页
        for i, tab in enumerate(tabs):
            if 'xiaohongshu.com' in tab.get('url', ''):
                print(f"✅ 找到小红书标签页(第{i+1}号): {tab['title'][:50]}...")
                return tab
        
        # 方法3: 使用第一个可用标签页
        if tabs:
            print(f"⚠️ 使用第一个标签页: {tabs[0]['title'][:50]}...")
            return tabs[0]
        
        print("❌ 没有找到可用的标签页")
        return None
    
    def connect_websocket(self, tab):
        """连接WebSocket"""
        print("🔌 连接WebSocket...")
        
        try:
            ws_url = tab['webSocketDebuggerUrl']
            self.ws = websocket.create_connection(ws_url, timeout=10)
            
            # 启用Runtime
            self.send_command('Runtime.enable')
            time.sleep(1)
            
            print("✅ WebSocket连接成功")
            return True
        except Exception as e:
            print(f"❌ WebSocket连接失败: {str(e)}")
            return False
    
    def send_command(self, method, params=None):
        """发送命令"""
        command = {
            'id': self.request_id,
            'method': method
        }
        
        if params:
            command['params'] = params
        
        self.ws.send(json.dumps(command))
        self.request_id += 1
        
        return self.request_id - 1
    
    def execute_js_and_wait(self, script, timeout=60):
        """执行JavaScript并等待结果"""
        command_id = self.send_command('Runtime.evaluate', {
            'expression': script,
            'returnByValue': True
        })
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = self.ws.recv()
                message = json.loads(response)
                
                if message.get('id') == command_id:
                    if 'result' in message and 'result' in message['result']:
                        return message['result']['result'].get('value')
                    elif 'error' in message:
                        print(f"❌ JavaScript执行错误: {message['error']}")
                        return None
                
            except Exception as e:
                continue
        
        print("❌ JavaScript执行超时")
        return None
    
    def collect_comments_from_tab19(self):
        """从19号标签页采集评论"""
        print("🎯 启动19号标签页评论采集...\n")
        
        try:
            # 1. 查找活跃的调试端口
            tabs = self.find_active_debug_port()
            if not tabs:
                return None
            
            # 2. 查找19号标签页
            target_tab = self.find_tab19_or_xiaohongshu(tabs)
            if not target_tab:
                return None
            
            # 3. 连接WebSocket
            if not self.connect_websocket(target_tab):
                return None
            
            # 4. 执行评论采集脚本
            print("🔄 执行评论采集脚本...")
            
            collection_script = '''
                (function() {
                    try {
                        console.log('🎯 开始19号标签页评论采集...');
                        
                        const results = {
                            note_url: window.location.href,
                            note_title: document.title.replace(' - 小红书', '').trim(),
                            timestamp: new Date().toISOString(),
                            comments: [],
                            summary: {
                                total_comments: 0,
                                total_replies: 0,
                                total_likes: 0,
                                strategy: 'direct-tab19'
                            }
                        };

                        // 第一步：统计初始状态
                        const initialAvatars = document.querySelectorAll('img[src*="avatar"]').length;
                        console.log(`初始头像数量: ${initialAvatars}`);

                        // 第二步：点击所有"更多"按钮
                        console.log('🔘 点击"更多"按钮...');
                        const buttonTexts = ['更多', '展开', '查看'];
                        let totalClicked = 0;
                        
                        for (const buttonText of buttonTexts) {
                            const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                                const text = el.textContent.trim();
                                const isVisible = el.offsetParent !== null;
                                const isClickable = el.tagName === 'BUTTON' || 
                                                  el.tagName === 'A' ||
                                                  el.onclick || 
                                                  (el.className && el.className.includes && el.className.includes('btn')) ||
                                                  getComputedStyle(el).cursor === 'pointer';
                                
                                return isVisible && isClickable && text.length > 0 && text.length < 50 && 
                                       text.includes(buttonText);
                            });
                            
                            elements.forEach(el => {
                                try {
                                    el.click();
                                    totalClicked++;
                                    console.log('点击:', el.textContent.trim().substring(0, 20));
                                } catch (e) {
                                    // 忽略点击错误
                                }
                            });
                        }
                        
                        // 处理"条回复"按钮
                        const replyElements = Array.from(document.querySelectorAll('*')).filter(el => {
                            const text = el.textContent.trim();
                            return /\\d+条回复/.test(text) && el.offsetParent !== null;
                        });
                        
                        replyElements.forEach(el => {
                            try {
                                el.click();
                                totalClicked++;
                                console.log('点击回复:', el.textContent.trim().substring(0, 20));
                            } catch (e) {
                                // 忽略点击错误
                            }
                        });
                        
                        console.log(`总共点击了 ${totalClicked} 个按钮`);

                        // 第三步：滚动确保内容加载
                        console.log('📜 滚动确保内容加载...');
                        window.scrollTo(0, document.body.scrollHeight);

                        // 第四步：提取所有评论
                        console.log('🔍 提取所有评论...');
                        
                        // 查找所有头像元素
                        const avatars = document.querySelectorAll('img[src*="avatar"]');
                        console.log(`找到 ${avatars.length} 个头像`);
                        
                        const commentElements = new Set();
                        
                        // 基于头像查找评论容器
                        avatars.forEach(img => {
                            const commentDiv = img.closest('div');
                            if (commentDiv && commentDiv.textContent.trim().length > 20) {
                                commentElements.add(commentDiv);
                            }
                        });
                        
                        // 基于时间信息查找
                        const timeElements = Array.from(document.querySelectorAll('*')).filter(el => {
                            const text = el.textContent.trim();
                            return /\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}-\\d{2}/.test(text);
                        });
                        
                        timeElements.forEach(timeEl => {
                            const commentDiv = timeEl.closest('div');
                            if (commentDiv && commentDiv.textContent.trim().length > 20) {
                                commentElements.add(commentDiv);
                            }
                        });

                        console.log(`找到 ${commentElements.size} 个独特的评论元素`);

                        // 提取评论详细信息
                        Array.from(commentElements).forEach((element, index) => {
                            try {
                                const commentData = {
                                    id: index + 1,
                                    username: '',
                                    avatar: '',
                                    content: '',
                                    publish_time: '',
                                    likes: 0,
                                    is_reply: false
                                };

                                // 提取头像
                                const avatarImg = element.querySelector('img[src*="avatar"]');
                                if (avatarImg) {
                                    commentData.avatar = avatarImg.src;
                                }

                                // 提取用户名
                                const textNodes = Array.from(element.querySelectorAll('*')).filter(el => 
                                    el.children.length === 0 && 
                                    el.textContent.trim().length > 1 && 
                                    el.textContent.trim().length < 30 &&
                                    !el.textContent.includes('天前') &&
                                    !el.textContent.includes('小时前')
                                );
                                if (textNodes.length > 0) {
                                    commentData.username = textNodes[0].textContent.trim();
                                }

                                // 提取评论内容
                                const allTexts = Array.from(element.querySelectorAll('*')).map(el => 
                                    el.textContent.trim()
                                ).filter(text => 
                                    text.length > 10 && text.length < 1000 &&
                                    !text.includes('天前') && !text.includes('小时前') &&
                                    !text.includes('点赞') && !text.includes('回复') &&
                                    !text.includes('更多') && !text.includes('展开')
                                );
                                
                                if (allTexts.length > 0) {
                                    commentData.content = allTexts.reduce((a, b) => a.length > b.length ? a : b);
                                }

                                // 提取发布时间
                                const timeMatch = element.textContent.match(/\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}-\\d{2}/);
                                if (timeMatch) {
                                    commentData.publish_time = timeMatch[0];
                                }

                                // 提取点赞数
                                const likeMatch = element.textContent.match(/\\d+(?=\\s*赞|\\s*❤️)/);
                                if (likeMatch) {
                                    commentData.likes = parseInt(likeMatch[0]) || 0;
                                }

                                // 判断是否为回复
                                if (element.textContent.includes('回复')) {
                                    commentData.is_reply = true;
                                }

                                // 只添加有内容的评论
                                if (commentData.content && commentData.content.length > 5) {
                                    results.comments.push(commentData);
                                }

                            } catch (e) {
                                // 忽略提取错误
                            }
                        });

                        // 去重
                        const uniqueComments = [];
                        const seenContents = new Set();
                        
                        results.comments.forEach(comment => {
                            const key = comment.content.substring(0, 50);
                            if (!seenContents.has(key)) {
                                seenContents.add(key);
                                uniqueComments.push(comment);
                            }
                        });
                        
                        results.comments = uniqueComments;

                        // 计算统计信息
                        results.summary.total_comments = results.comments.filter(c => !c.is_reply).length;
                        results.summary.total_replies = results.comments.filter(c => c.is_reply).length;
                        results.summary.total_likes = results.comments.reduce((sum, c) => sum + c.likes, 0);

                        console.log(`🎉 采集完成! 采集到 ${results.comments.length} 条评论`);
                        
                        return results;
                        
                    } catch (error) {
                        console.error('采集过程出错:', error);
                        return { 
                            error: error.message || '未知错误',
                            success: false
                        };
                    }
                })();
            '''
            
            result = self.execute_js_and_wait(collection_script, timeout=90)
            
            if result and not result.get('error'):
                print("✅ 采集成功!")
                print(f"📊 采集到 {len(result['comments'])} 条评论")
                
                # 更新数据
                self.comments_data = result
                return result
            else:
                print(f"❌ 采集失败: {result.get('error', '未知错误') if result else '无返回结果'}")
                return None
                
        except Exception as e:
            print(f"❌ 采集过程出错: {str(e)}")
            return None
        finally:
            if self.ws:
                self.ws.close()
    
    def save_results(self, filename_prefix="direct_tab19"):
        """保存结果"""
        if not self.comments_data.get('comments'):
            print("⚠️ 没有评论数据可保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON文件
        json_filename = f"{filename_prefix}_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.comments_data, f, ensure_ascii=False, indent=2)
        
        # 生成报告
        report = self.generate_report()
        txt_filename = f"{filename_prefix}_{timestamp}.txt"
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ 结果已保存:")
        print(f"   📁 JSON文件: {json_filename}")
        print(f"   📄 文本报告: {txt_filename}")
        
        return json_filename, txt_filename
    
    def generate_report(self):
        """生成报告"""
        lines = []
        
        lines.append("🎯 19号标签页小红书评论采集报告")
        lines.append("=" * 60)
        lines.append(f"📅 采集时间: {self.comments_data['timestamp']}")
        lines.append(f"📝 笔记标题: {self.comments_data['note_title']}")
        lines.append(f"🔗 笔记链接: {self.comments_data['note_url']}")
        lines.append(f"🔧 采集策略: {self.comments_data['summary']['strategy']}")
        lines.append("")
        
        lines.append("📊 采集统计:")
        lines.append(f"💬 总评论数: {len(self.comments_data['comments'])}")
        lines.append(f"📝 主评论数: {self.comments_data['summary']['total_comments']}")
        lines.append(f"↩️ 回复数: {self.comments_data['summary']['total_replies']}")
        lines.append(f"👍 总点赞数: {self.comments_data['summary']['total_likes']}")
        lines.append(f"📈 采集进度: {round(len(self.comments_data['comments'])/1472*100, 1)}% (目标1472条)")
        lines.append("")
        
        lines.append("💬 评论详情:")
        lines.append("-" * 60)
        
        for i, comment in enumerate(self.comments_data['comments'], 1):
            lines.append(f"\n💬 评论 {i}:")
            lines.append(f"👤 用户: {comment.get('username', '匿名')}")
            lines.append(f"📄 内容: {comment.get('content', '无内容')}")
            lines.append(f"👍 点赞: {comment.get('likes', 0)}")
            lines.append(f"📅 时间: {comment.get('publish_time', '未知')}")
            lines.append(f"🔗 头像: {comment.get('avatar', '无')}")
            lines.append(f"↩️ 回复: {'是' if comment.get('is_reply', False) else '否'}")
        
        lines.append("\n" + "=" * 60)
        lines.append("📅 报告生成时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        return "\n".join(lines)


def main():
    """主函数"""
    print("🎯 19号标签页小红书评论采集器")
    print("=" * 60)
    print("📋 这个工具会:")
    print("   1. 查找Chrome调试端口")
    print("   2. 连接到19号标签页或小红书页面")
    print("   3. 执行JavaScript评论采集脚本")
    print("   4. 保存完整的采集结果")
    print()
    
    collector = DirectTab19Collector()
    
    try:
        # 采集评论
        results = collector.collect_comments_from_tab19()
        
        if results:
            # 保存结果
            collector.save_results()
            
            print(f"\n🎉 采集完成!")
            print(f"📊 总评论: {len(results['comments'])}条")
            print(f"📝 主评论: {results['summary']['total_comments']}条")
            print(f"↩️ 回复: {results['summary']['total_replies']}条")
            print(f"👍 总点赞: {results['summary']['total_likes']}")
            print(f"📈 采集进度: {round(len(results['comments'])/1472*100, 1)}%")
        else:
            print("\n❌ 采集失败!")
            
    except Exception as e:
        print(f"❌ 程序执行失败: {str(e)}")


if __name__ == "__main__":
    main()
