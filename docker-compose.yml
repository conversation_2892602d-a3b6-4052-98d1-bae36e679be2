version: '3.8'

services:
  # 主应用服务
  btx-platform:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: btx-tech-platform
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - REDIS_URL=redis://redis:6379
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=btx_platform
      - DB_USER=btxuser
      - DB_PASSWORD=btx2024secure
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - btx-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.btx-platform.rule=Host(`btx.local`)"
      - "traefik.http.services.btx-platform.loadbalancer.server.port=3000"

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: btx-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass btx2024redis
    restart: unless-stopped
    networks:
      - btx-network

  # PostgreSQL数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: btx-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=btx_platform
      - POSTGRES_USER=btxuser
      - POSTGRES_PASSWORD=btx2024secure
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - btx-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: btx-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./static:/usr/share/nginx/html/static
    depends_on:
      - btx-platform
    restart: unless-stopped
    networks:
      - btx-network

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: btx-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    networks:
      - btx-network

  # Grafana仪表板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: btx-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=btx2024admin
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped
    networks:
      - btx-network

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:

networks:
  btx-network:
    driver: bridge
