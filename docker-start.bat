@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🐳 BTX科技平台 - Docker部署
echo ========================================
echo.

echo 🔍 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Docker，请先安装Docker Desktop
    echo 📥 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker环境检查通过

echo 🔍 检查Docker Compose...
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Docker Compose
    echo 💡 请确保Docker Desktop已正确安装
    pause
    exit /b 1
)

echo ✅ Docker Compose检查通过

echo.
echo 🛑 停止现有容器...
docker-compose down

echo.
echo 🏗️ 构建Docker镜像...
docker-compose build --no-cache

if errorlevel 1 (
    echo ❌ Docker镜像构建失败
    echo 💡 请检查Dockerfile和网络连接
    pause
    exit /b 1
)

echo ✅ Docker镜像构建完成

echo.
echo 🚀 启动BTX科技平台...
echo 📡 Web界面: http://localhost:3000
echo 📊 监控面板: http://localhost:3001 (admin/btx2024admin)
echo 🔍 Prometheus: http://localhost:9090
echo.

docker-compose up -d

if errorlevel 1 (
    echo ❌ 容器启动失败
    echo 💡 请检查端口占用和配置文件
    pause
    exit /b 1
)

echo.
echo ✅ 平台启动成功！
echo.
echo 📋 服务状态:
docker-compose ps

echo.
echo 🔧 常用命令:
echo   查看日志: docker-compose logs -f
echo   停止服务: docker-compose down
echo   重启服务: docker-compose restart
echo   进入容器: docker-compose exec btx-platform sh
echo.

echo 🌐 请在浏览器中访问: http://localhost:3000
echo.
pause
