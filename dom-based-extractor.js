#!/usr/bin/env node

/**
 * 🎯 基于DOM结构的精确评论提取器
 * 使用开发者工具发现的具体DOM选择器
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class DOMBasedExtractor {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        // DOM选择器配置
        this.selectors = {
            // 评论容器
            commentContainer: [
                '[class*="comment"]',
                '[class*="Comment"]',
                '[data-testid*="comment"]',
                '.note-item',
                '.comment-item'
            ],
            
            // 用户信息
            username: [
                '[class*="username"]',
                '[class*="nickname"]',
                '[class*="user-name"]',
                '.author-name',
                '.user-info .name'
            ],
            
            // 用户ID
            userId: [
                '[class*="user-id"]',
                '[data-user-id]',
                '[class*="userid"]',
                '.user-info [class*="id"]'
            ],
            
            // 时间
            time: [
                '[class*="time"]',
                '[class*="date"]',
                '.publish-time',
                '.comment-time',
                'time'
            ],
            
            // 评论内容
            content: [
                '[class*="content"]',
                '[class*="text"]',
                '.comment-content',
                '.note-text'
            ],
            
            // 点赞数
            likes: [
                '[class*="like"]',
                '[class*="praise"]',
                '.like-count',
                '.praise-count'
            ],
            
            // 回复数
            replies: [
                '[class*="reply"]',
                '[class*="comment-count"]',
                '.reply-count'
            ]
        };
        
        this.config = {
            maxScrollAttempts: 200,
            scrollDelay: 1000,
            waitForContent: 2000,
            targetComments: 1472
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    async connectToBrowser() {
        try {
            console.log('🔗 连接到比特浏览器...');
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${this.debugPort}`,
                defaultViewport: null
            });
            
            const pages = await browser.pages();
            const page = pages.find(p => p.url().includes('xiaohongshu.com/explore/'));
            
            if (!page) {
                throw new Error('未找到小红书笔记页面');
            }
            
            console.log('✅ 找到小红书笔记页面');
            console.log(`📄 当前URL: ${page.url()}`);
            
            return { browser, page };
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    // 智能DOM滚动加载
    async smartDOMScrollLoad(page) {
        console.log('🚀 开始智能DOM滚动加载...');
        
        let currentCommentCount = 0;
        let previousCommentCount = 0;
        let stableCount = 0;
        
        for (let i = 0; i < this.config.maxScrollAttempts; i++) {
            console.log(`\n📜 滚动 ${i + 1}/${this.config.maxScrollAttempts}`);
            
            // 使用DOM选择器统计评论数量
            currentCommentCount = await this.countCommentsByDOM(page);
            console.log(`   💬 当前评论数: ${currentCommentCount}`);
            
            if (currentCommentCount >= this.config.targetComments * 0.95) {
                console.log(`🎉 接近目标评论数！当前: ${currentCommentCount}`);
                break;
            }
            
            if (currentCommentCount === previousCommentCount) {
                stableCount++;
                console.log(`   ⏸️ 评论数稳定 ${stableCount} 次`);
            } else {
                console.log(`   📈 新增评论: ${currentCommentCount - previousCommentCount}`);
                stableCount = 0;
                previousCommentCount = currentCommentCount;
            }
            
            // 如果稳定太久，尝试点击加载更多
            if (stableCount >= 5) {
                console.log('   🔄 尝试点击加载更多...');
                await this.clickLoadMore(page);
                stableCount = 0;
            }
            
            // 智能滚动
            await this.performSmartScroll(page);
            
            if (stableCount >= 15) {
                console.log('   ⏹️ 长时间无新内容，停止滚动');
                break;
            }
            
            await new Promise(resolve => setTimeout(resolve, this.config.scrollDelay));
        }
        
        console.log(`✅ 滚动完成，最终评论数: ${currentCommentCount}`);
        return currentCommentCount;
    }

    // 使用DOM选择器统计评论数量
    async countCommentsByDOM(page) {
        return await page.evaluate((selectors) => {
            let maxCount = 0;
            
            // 尝试所有评论容器选择器
            selectors.commentContainer.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    maxCount = Math.max(maxCount, elements.length);
                } catch (e) {
                    // 忽略无效选择器
                }
            });
            
            return maxCount;
        }, this.selectors);
    }

    // 执行智能滚动
    async performSmartScroll(page) {
        await page.evaluate(() => {
            // 滚动到页面底部
            window.scrollTo(0, document.body.scrollHeight);
            
            // 等待一下再滚动到评论区域
            setTimeout(() => {
                const commentElements = document.querySelectorAll('[class*="comment"], [class*="Comment"]');
                if (commentElements.length > 0) {
                    const lastComment = commentElements[commentElements.length - 1];
                    lastComment.scrollIntoView({ behavior: 'smooth' });
                }
            }, 500);
        });
    }

    // 点击加载更多
    async clickLoadMore(page) {
        await page.evaluate(() => {
            const loadMoreTexts = ['加载更多', '展开更多', '查看更多', '更多评论', '展开'];
            
            loadMoreTexts.forEach(text => {
                const elements = Array.from(document.querySelectorAll('*')).filter(el => 
                    el.textContent.includes(text) && el.offsetHeight > 0
                );
                
                elements.forEach(el => {
                    try {
                        el.click();
                    } catch (e) {
                        // 忽略点击失败
                    }
                });
            });
        });
    }

    // 基于DOM的精确评论提取
    async extractCommentsByDOM(page) {
        console.log('🎯 开始基于DOM的精确评论提取...');
        
        const result = await page.evaluate((selectors) => {
            const data = {
                noteInfo: {
                    id: '',
                    title: '',
                    author: '',
                    totalCommentCount: 0
                },
                comments: [],
                extractStats: {
                    totalElements: 0,
                    successfulExtractions: 0,
                    failedExtractions: 0
                },
                extractTime: new Date().toISOString()
            };
            
            try {
                // 提取基本信息
                const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
                if (urlMatch) {
                    data.noteInfo.id = urlMatch[1];
                }
                
                const titleElement = document.querySelector('title');
                if (titleElement) {
                    data.noteInfo.title = titleElement.textContent.replace(' - 小红书', '');
                }
                
                const pageText = document.body.textContent;
                const commentCountMatch = pageText.match(/(\d+)\s*条评论/);
                if (commentCountMatch) {
                    data.noteInfo.totalCommentCount = parseInt(commentCountMatch[1]);
                }
                
                if (pageText.includes('漫娴学姐')) {
                    data.noteInfo.author = '漫娴学姐 招暑假工版';
                }
                
                // 查找评论容器
                let commentElements = [];
                
                selectors.commentContainer.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > commentElements.length) {
                            commentElements = Array.from(elements);
                        }
                    } catch (e) {
                        // 忽略无效选择器
                    }
                });
                
                data.extractStats.totalElements = commentElements.length;
                console.log(`找到 ${commentElements.length} 个评论元素`);
                
                // 提取每个评论的详细信息
                commentElements.forEach((element, index) => {
                    try {
                        const comment = {
                            id: index + 1,
                            userId: '',
                            username: '',
                            content: '',
                            time: '',
                            likes: 0,
                            replyCount: 0,
                            isAuthor: false,
                            isPinned: false,
                            extractedInfo: {
                                elementIndex: index,
                                extractionMethod: 'DOM'
                            }
                        };
                        
                        // 提取用户名
                        selectors.username.forEach(selector => {
                            if (!comment.username) {
                                try {
                                    const usernameEl = element.querySelector(selector);
                                    if (usernameEl && usernameEl.textContent.trim()) {
                                        comment.username = usernameEl.textContent.trim();
                                    }
                                } catch (e) {
                                    // 忽略选择器错误
                                }
                            }
                        });
                        
                        // 提取用户ID
                        selectors.userId.forEach(selector => {
                            if (!comment.userId) {
                                try {
                                    const userIdEl = element.querySelector(selector);
                                    if (userIdEl) {
                                        comment.userId = userIdEl.textContent.trim() || userIdEl.getAttribute('data-user-id') || '';
                                    }
                                } catch (e) {
                                    // 忽略选择器错误
                                }
                            }
                        });
                        
                        // 提取时间
                        selectors.time.forEach(selector => {
                            if (!comment.time) {
                                try {
                                    const timeEl = element.querySelector(selector);
                                    if (timeEl && timeEl.textContent.trim()) {
                                        comment.time = timeEl.textContent.trim();
                                    }
                                } catch (e) {
                                    // 忽略选择器错误
                                }
                            }
                        });
                        
                        // 提取评论内容
                        selectors.content.forEach(selector => {
                            if (!comment.content) {
                                try {
                                    const contentEl = element.querySelector(selector);
                                    if (contentEl && contentEl.textContent.trim()) {
                                        comment.content = contentEl.textContent.trim();
                                    }
                                } catch (e) {
                                    // 忽略选择器错误
                                }
                            }
                        });
                        
                        // 如果没有找到内容，使用元素的文本内容
                        if (!comment.content) {
                            comment.content = element.textContent.trim();
                        }
                        
                        // 提取点赞数
                        selectors.likes.forEach(selector => {
                            if (comment.likes === 0) {
                                try {
                                    const likeEl = element.querySelector(selector);
                                    if (likeEl) {
                                        const likeText = likeEl.textContent.trim();
                                        const likeMatch = likeText.match(/(\d+)/);
                                        if (likeMatch) {
                                            comment.likes = parseInt(likeMatch[1]);
                                        }
                                    }
                                } catch (e) {
                                    // 忽略选择器错误
                                }
                            }
                        });
                        
                        // 提取回复数
                        selectors.replies.forEach(selector => {
                            if (comment.replyCount === 0) {
                                try {
                                    const replyEl = element.querySelector(selector);
                                    if (replyEl) {
                                        const replyText = replyEl.textContent.trim();
                                        const replyMatch = replyText.match(/(\d+)/);
                                        if (replyMatch) {
                                            comment.replyCount = parseInt(replyMatch[1]);
                                        }
                                    }
                                } catch (e) {
                                    // 忽略选择器错误
                                }
                            }
                        });
                        
                        // 检查是否是作者或置顶
                        const elementText = element.textContent;
                        comment.isAuthor = elementText.includes('作者');
                        comment.isPinned = elementText.includes('置顶');
                        
                        // 清理和验证评论
                        comment.content = comment.content.replace(/\s+/g, ' ').trim();
                        
                        if (comment.content.length >= 5) {
                            data.comments.push(comment);
                            data.extractStats.successfulExtractions++;
                        } else {
                            data.extractStats.failedExtractions++;
                        }
                        
                    } catch (error) {
                        console.error('提取评论时出错:', error);
                        data.extractStats.failedExtractions++;
                    }
                });
                
                return data;
                
            } catch (error) {
                console.error('DOM提取过程出错:', error);
                data.error = error.message;
                return data;
            }
        }, this.selectors);
        
        return result;
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        try {
            console.log('🎯 启动基于DOM的精确评论提取器...');
            console.log(`🎯 目标: 获取所有 ${this.config.targetComments} 条评论`);
            console.log('🛡️ 策略: DOM选择器 + 智能滚动 + 精确提取');

            const { browser, page } = await this.connectToBrowser();

            // 智能DOM滚动加载
            const loadedCommentCount = await this.smartDOMScrollLoad(page);

            // 基于DOM的精确评论提取
            const result = await this.extractCommentsByDOM(page);

            // 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `dom_comments_${result.noteInfo.id}_${timestamp}.json`;
            this.saveToFile(result, filename);

            // 输出结果
            console.log('\n🎉 DOM提取完成！');
            console.log('📊 最终统计:');
            console.log(`   🆔 笔记ID: ${result.noteInfo.id}`);
            console.log(`   📝 笔记标题: ${result.noteInfo.title || '未知'}`);
            console.log(`   👤 笔记作者: ${result.noteInfo.author || '未知'}`);
            console.log(`   🎯 目标评论数: ${result.noteInfo.totalCommentCount}`);
            console.log(`   💬 实际提取数: ${result.comments.length}`);
            console.log(`   📈 完成度: ${Math.round(result.comments.length / result.noteInfo.totalCommentCount * 100)}%`);
            console.log(`   📁 保存文件: ${filename}`);

            console.log('\n📊 提取统计:');
            console.log(`   🔍 总DOM元素: ${result.extractStats.totalElements}`);
            console.log(`   ✅ 成功提取: ${result.extractStats.successfulExtractions}`);
            console.log(`   ❌ 提取失败: ${result.extractStats.failedExtractions}`);
            console.log(`   📈 成功率: ${Math.round(result.extractStats.successfulExtractions / result.extractStats.totalElements * 100)}%`);

            if (result.comments.length > 0) {
                console.log('\n👥 评论详细预览:');
                result.comments.slice(0, 10).forEach((comment, index) => {
                    console.log(`\n   ${index + 1}. 评论ID: ${comment.id}`);
                    console.log(`      👤 用户: ${comment.username || '未知'}`);
                    console.log(`      🆔 用户ID: ${comment.userId || '未提取到'}`);
                    console.log(`      ⏰ 时间: ${comment.time || '未知'}`);
                    console.log(`      💬 内容: ${comment.content.substring(0, 100)}${comment.content.length > 100 ? '...' : ''}`);
                    console.log(`      👍 点赞: ${comment.likes} | 💬 回复: ${comment.replyCount}`);
                    console.log(`      🏷️ 标识: ${comment.isAuthor ? '作者' : ''}${comment.isPinned ? '置顶' : ''}${!comment.isAuthor && !comment.isPinned ? '普通' : ''}`);
                });

                // 质量统计
                const withUsername = result.comments.filter(c => c.username).length;
                const withUserId = result.comments.filter(c => c.userId).length;
                const withTime = result.comments.filter(c => c.time).length;
                const withLikes = result.comments.filter(c => c.likes > 0).length;
                const withReplies = result.comments.filter(c => c.replyCount > 0).length;

                console.log('\n📈 数据质量统计:');
                console.log(`   👤 有用户名: ${withUsername}/${result.comments.length} (${Math.round(withUsername/result.comments.length*100)}%)`);
                console.log(`   🆔 有用户ID: ${withUserId}/${result.comments.length} (${Math.round(withUserId/result.comments.length*100)}%)`);
                console.log(`   ⏰ 有时间: ${withTime}/${result.comments.length} (${Math.round(withTime/result.comments.length*100)}%)`);
                console.log(`   👍 有点赞数: ${withLikes}/${result.comments.length} (${Math.round(withLikes/result.comments.length*100)}%)`);
                console.log(`   💬 有回复数: ${withReplies}/${result.comments.length} (${Math.round(withReplies/result.comments.length*100)}%)`);

                // 成功度评估
                const completionRate = result.comments.length / result.noteInfo.totalCommentCount;
                if (completionRate >= 0.8) {
                    console.log('\n🎉 优秀！已提取到大部分评论数据！');
                } else if (completionRate >= 0.5) {
                    console.log('\n👍 良好！已提取到一半以上的评论数据！');
                } else if (completionRate >= 0.2) {
                    console.log('\n📈 进步！DOM提取效果显著！');
                } else {
                    console.log('\n💡 提示：可能需要调整DOM选择器或增加滚动时间');
                }

                // 兼职信息分析
                const jobKeywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱', '月入'];
                const jobComments = result.comments.filter(c =>
                    jobKeywords.some(keyword => c.content.includes(keyword))
                );

                if (jobComments.length > 0) {
                    console.log(`\n💼 兼职相关评论: ${jobComments.length}/${result.comments.length} (${Math.round(jobComments.length/result.comments.length*100)}%)`);

                    // 显示一些兼职评论示例
                    console.log('\n💼 兼职评论示例:');
                    jobComments.slice(0, 5).forEach((comment, index) => {
                        console.log(`   ${index + 1}. ${comment.username || '匿名'}: ${comment.content.substring(0, 80)}...`);
                    });
                }
            }

            await browser.disconnect();

            console.log('\n✅ DOM提取任务完成！');
            console.log(`📁 数据文件: ${filename}`);

        } catch (error) {
            console.error('❌ DOM提取失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行DOM提取器
if (require.main === module) {
    const extractor = new DOMBasedExtractor();
    extractor.run();
}

module.exports = DOMBasedExtractor;
