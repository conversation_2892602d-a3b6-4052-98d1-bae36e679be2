#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 ChromeDriver 134版本下载器
专门下载匹配比特浏览器Chrome 134版本的ChromeDriver
"""

import requests
import zipfile
import os
import shutil
import json
from pathlib import Path

def download_chromedriver_134():
    """下载ChromeDriver 134版本"""
    print("🔍 下载ChromeDriver 134版本...")
    
    # 尝试多个可能的下载源
    download_sources = [
        # 官方源 - 尝试不同的版本号
        "https://chromedriver.storage.googleapis.com/134.0.6998.70/chromedriver_win32.zip",
        "https://chromedriver.storage.googleapis.com/134.0.6998.69/chromedriver_win32.zip", 
        "https://chromedriver.storage.googleapis.com/134.0.6998.68/chromedriver_win32.zip",
        "https://chromedriver.storage.googleapis.com/134.0.6998.67/chromedriver_win32.zip",
        "https://chromedriver.storage.googleapis.com/134.0.6998.66/chromedriver_win32.zip",
        "https://chromedriver.storage.googleapis.com/134.0.6998.65/chromedriver_win32.zip",
        "https://chromedriver.storage.googleapis.com/134.0.6998.64/chromedriver_win32.zip",
        "https://chromedriver.storage.googleapis.com/134.0.6998.63/chromedriver_win32.zip",
        "https://chromedriver.storage.googleapis.com/134.0.6998.62/chromedriver_win32.zip",
        "https://chromedriver.storage.googleapis.com/134.0.6998.61/chromedriver_win32.zip",
        "https://chromedriver.storage.googleapis.com/134.0.6998.60/chromedriver_win32.zip",
        
        # 尝试通用版本
        "https://chromedriver.storage.googleapis.com/134.0.6998.0/chromedriver_win32.zip",
        
        # 备用镜像源
        "https://npm.taobao.org/mirrors/chromedriver/134.0.6998.70/chromedriver_win32.zip",
        "https://npm.taobao.org/mirrors/chromedriver/134.0.6998.69/chromedriver_win32.zip",
        
        # GitHub releases (如果有的话)
        "https://github.com/electron/electron/releases/download/v27.0.0/chromedriver-v27.0.0-win32-x64.zip",
    ]
    
    for i, url in enumerate(download_sources, 1):
        print(f"   尝试源 {i}: {url.split('/')[-2] if '/' in url else 'unknown'}")
        
        try:
            # 先检查是否存在
            response = requests.head(url, timeout=10)
            if response.status_code == 200:
                print(f"   ✅ 找到可用源，开始下载...")
                
                # 下载文件
                response = requests.get(url, timeout=60)
                if response.status_code == 200:
                    zip_path = "chromedriver_134.zip"
                    with open(zip_path, 'wb') as f:
                        f.write(response.content)
                    
                    print(f"   ✅ 下载成功: {zip_path} ({len(response.content)} bytes)")
                    return zip_path
                else:
                    print(f"   ❌ 下载失败: {response.status_code}")
            else:
                print(f"   ❌ 源不可用: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 连接失败: {str(e)[:50]}...")
            continue
    
    print("❌ 所有下载源都失败了")
    return None

def install_chromedriver(zip_path):
    """安装ChromeDriver"""
    print("🔧 安装ChromeDriver...")
    
    try:
        # 解压zip文件
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall('.')
        
        # 检查解压结果
        if os.path.exists('chromedriver.exe'):
            print("   ✅ ChromeDriver解压成功")
            
            # 备份现有版本
            if os.path.exists('chromedriver_backup.exe'):
                os.remove('chromedriver_backup.exe')
            
            # 如果系统中有ChromeDriver，备份它
            system_driver = shutil.which('chromedriver')
            if system_driver and os.path.exists(system_driver):
                try:
                    shutil.copy(system_driver, 'chromedriver_backup.exe')
                    print("   📦 已备份系统ChromeDriver")
                except:
                    pass
            
            # 设置可执行权限（Windows上通常不需要，但以防万一）
            os.chmod('chromedriver.exe', 0o755)
            
            print(f"   ✅ ChromeDriver安装完成")
            print(f"   📍 位置: {os.path.abspath('chromedriver.exe')}")
            
            # 清理zip文件
            os.remove(zip_path)
            
            return True
        else:
            print("   ❌ 解压后未找到chromedriver.exe")
            return False
            
    except Exception as e:
        print(f"❌ 安装失败: {e}")
        return False

def test_chromedriver():
    """测试ChromeDriver版本"""
    print("🔍 测试ChromeDriver版本...")
    
    try:
        import subprocess
        result = subprocess.run(['./chromedriver.exe', '--version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version_info = result.stdout.strip()
            print(f"   ✅ ChromeDriver版本: {version_info}")
            return True
        else:
            print(f"   ❌ 版本检查失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ 版本检查异常: {e}")
        return False

def test_connection_with_port():
    """测试与调试端口的连接"""
    print("🔍 测试与调试端口60811的连接...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # 使用本地ChromeDriver
        service = Service('./chromedriver.exe')
        
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", "localhost:60811")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        
        print("   🔗 尝试连接...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 获取页面信息
        current_url = driver.current_url
        title = driver.title
        
        print(f"   ✅ 连接成功!")
        print(f"   📄 当前页面: {current_url}")
        print(f"   📝 页面标题: {title}")
        
        # 检查是否是小红书页面
        if 'xiaohongshu.com' in current_url:
            print("   🎉 检测到小红书页面!")
        
        driver.quit()
        return True
        
    except Exception as e:
        error_msg = str(e)
        print(f"   ❌ 连接失败: {error_msg[:100]}...")
        
        # 分析错误类型
        if "version" in error_msg.lower():
            print("   💡 仍然是版本不匹配问题")
        elif "connect" in error_msg.lower():
            print("   💡 可能是端口连接问题")
        elif "session" in error_msg.lower():
            print("   💡 可能是会话创建问题")
        
        return False

def main():
    print("🎯 ChromeDriver 134版本下载器")
    print("=" * 50)
    
    # 1. 下载ChromeDriver
    zip_path = download_chromedriver_134()
    
    if not zip_path:
        print("\n❌ 无法下载ChromeDriver 134")
        print("💡 可能的解决方案:")
        print("   1. 手动下载ChromeDriver 134版本")
        print("   2. 升级比特浏览器到最新版本")
        print("   3. 使用浏览器控制台方案")
        return
    
    # 2. 安装ChromeDriver
    if not install_chromedriver(zip_path):
        print("❌ ChromeDriver安装失败")
        return
    
    # 3. 测试版本
    if not test_chromedriver():
        print("❌ ChromeDriver版本测试失败")
        return
    
    # 4. 测试连接
    print("\n🔍 测试与比特浏览器的连接...")
    if test_connection_with_port():
        print("\n🎉 ChromeDriver 134安装成功!")
        print("🚀 现在可以运行爬虫脚本:")
        print("   python test_debug_port.py 60811")
        print("   或者运行完整的爬虫脚本")
    else:
        print("\n⚠️ ChromeDriver安装成功但连接测试失败")
        print("💡 可能的原因:")
        print("   1. 比特浏览器19号窗口未打开")
        print("   2. 调试端口未启用")
        print("   3. 仍然存在版本不匹配")

if __name__ == "__main__":
    main()
