#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 DrissionPage小红书评论采集器
使用DrissionPage模块进行高效的评论采集
"""

import json
import time
import random
from datetime import datetime
from typing import List, Dict, Any
from DrissionPage import ChromiumPage, ChromiumOptions
import re


class DrissionCommentsCollector:
    """DrissionPage评论采集器"""
    
    def __init__(self):
        self.page = None
        self.comments_data = {
            'note_url': '',
            'note_title': '',
            'timestamp': '',
            'comments': [],
            'summary': {
                'total_comments': 0,
                'total_replies': 0,
                'total_likes': 0,
                'strategy': 'drissionpage-optimized'
            }
        }
    
    def setup_browser(self):
        """设置浏览器"""
        print("🔧 设置DrissionPage浏览器...")
        
        # 配置浏览器选项
        options = ChromiumOptions()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option('excludeSwitches', ['enable-automation'])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 创建页面对象
        self.page = ChromiumPage(addr_or_opts=options)
        
        # 执行反检测脚本
        self.page.run_js('''
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });
        ''')
        
        print("✅ 浏览器设置完成")
    
    def navigate_to_page(self, url: str):
        """导航到指定页面"""
        print(f"🌐 导航到页面: {url[:80]}...")
        
        self.page.get(url)
        
        # 等待页面加载
        time.sleep(3)
        
        # 获取页面信息
        self.comments_data['note_url'] = self.page.url
        self.comments_data['note_title'] = self.page.title.replace(' - 小红书', '').strip()
        self.comments_data['timestamp'] = datetime.now().isoformat()
        
        print(f"📝 页面标题: {self.comments_data['note_title']}")
        print("✅ 页面加载完成")
    
    def wait_for_comments_load(self):
        """等待评论区域加载"""
        print("⏳ 等待评论区域加载...")
        
        # 等待头像元素出现
        try:
            self.page.wait.ele_loaded('img[src*="avatar"]', timeout=10)
            print("✅ 评论区域已加载")
            return True
        except:
            print("⚠️ 评论区域加载超时")
            return False
    
    def scroll_and_expand_comments(self, max_rounds: int = 20):
        """滚动并展开评论"""
        print(f"🔄 开始滚动并展开评论 (最多{max_rounds}轮)...")
        
        total_clicked = 0
        no_change_count = 0
        
        for round_num in range(1, max_rounds + 1):
            print(f"\n🔄 第 {round_num} 轮滚动和展开...")
            
            # 记录当前评论数量
            before_avatars = len(self.page.eles('img[src*="avatar"]'))
            
            # 滚动到页面底部
            self.page.scroll.to_bottom()
            time.sleep(1)
            
            # 查找并点击"更多"按钮
            clicked_in_round = self._click_more_buttons()
            total_clicked += clicked_in_round
            
            # 等待内容加载
            time.sleep(2 + random.uniform(0.5, 1.5))
            
            # 检查是否有新内容
            after_avatars = len(self.page.eles('img[src*="avatar"]'))
            avatars_added = after_avatars - before_avatars
            
            print(f"   👤 头像: {before_avatars} → {after_avatars} (+{avatars_added})")
            print(f"   🖱️ 点击按钮: {clicked_in_round}个")
            
            # 如果没有新内容且没有按钮可点击
            if avatars_added == 0 and clicked_in_round == 0:
                no_change_count += 1
                print(f"   ⚠️ 无变化轮次: {no_change_count}/3")
                
                if no_change_count >= 3:
                    print("🏁 连续3轮无变化，停止滚动")
                    break
            else:
                no_change_count = 0
        
        print(f"\n🎉 滚动完成! 总共点击了 {total_clicked} 个按钮")
        return total_clicked
    
    def _click_more_buttons(self) -> int:
        """点击"更多"按钮"""
        clicked_count = 0
        
        # 定义要查找的按钮文本
        button_texts = ['更多', '展开', '查看', '条回复']
        
        for button_text in button_texts:
            try:
                if button_text == '条回复':
                    # 使用正则表达式查找回复按钮
                    elements = self.page.eles('xpath://*/text()[contains(., "条回复")]/parent::*')
                else:
                    # 查找包含特定文本的元素
                    elements = self.page.eles(f'xpath://*/text()[contains(., "{button_text}")]/parent::*')
                
                # 点击前几个按钮
                for i, element in enumerate(elements[:3]):
                    try:
                        # 检查元素是否可见和可点击
                        if element.states.is_displayed and element.states.is_enabled:
                            # 滚动到元素可见
                            element.scroll.to_center()
                            time.sleep(0.3)
                            
                            # 点击元素
                            element.click()
                            clicked_count += 1
                            
                            print(f"     🖱️ 点击: {element.text[:20]}...")
                            
                            # 随机等待
                            time.sleep(random.uniform(0.5, 1.0))
                            
                    except Exception as e:
                        print(f"     ❌ 点击失败: {str(e)[:30]}...")
                        continue
                        
            except Exception as e:
                continue
        
        return clicked_count
    
    def extract_all_comments(self) -> List[Dict[str, Any]]:
        """提取所有评论"""
        print("🔍 开始提取所有评论...")
        
        comments = []
        
        # 查找所有头像元素
        avatar_elements = self.page.eles('img[src*="avatar"]')
        print(f"   找到 {len(avatar_elements)} 个头像元素")
        
        for index, avatar in enumerate(avatar_elements):
            try:
                comment_data = self._extract_single_comment(avatar, index + 1)
                if comment_data and comment_data['content']:
                    comments.append(comment_data)
            except Exception as e:
                print(f"   ❌ 提取评论 {index + 1} 失败: {str(e)[:30]}...")
                continue
        
        # 去重
        unique_comments = self._remove_duplicates(comments)
        
        print(f"🎉 提取完成! 原始: {len(comments)}条, 去重后: {len(unique_comments)}条")
        return unique_comments
    
    def _extract_single_comment(self, avatar_element, comment_id: int) -> Dict[str, Any]:
        """提取单条评论"""
        try:
            # 找到评论容器
            comment_container = avatar_element.parent(5)  # 向上查找5层
            
            comment_data = {
                'id': comment_id,
                'username': '',
                'avatar': '',
                'content': '',
                'publish_time': '',
                'likes': 0,
                'is_reply': False
            }
            
            # 提取头像
            comment_data['avatar'] = avatar_element.attr('src') or ''
            
            # 提取用户名
            username_elements = comment_container.eles('xpath:.//text()[string-length(.) > 1 and string-length(.) < 30]')
            for elem in username_elements:
                text = elem.text.strip()
                if text and not any(keyword in text for keyword in ['天前', '小时前', '分钟前', '点赞', '回复']):
                    comment_data['username'] = text
                    break
            
            # 提取评论内容
            content_elements = comment_container.eles('xpath:.//text()[string-length(.) > 10 and string-length(.) < 1000]')
            for elem in content_elements:
                text = elem.text.strip()
                if text and not any(keyword in text for keyword in ['天前', '小时前', '点赞', '回复', '更多', '展开']):
                    if len(text) > len(comment_data['content']):
                        comment_data['content'] = text
            
            # 提取发布时间
            time_pattern = r'\d+天前|\d+小时前|\d+分钟前|昨天|前天|\d{2}-\d{2}'
            container_text = comment_container.text
            time_match = re.search(time_pattern, container_text)
            if time_match:
                comment_data['publish_time'] = time_match.group()
            
            # 提取点赞数
            like_pattern = r'(\d+)(?=\s*赞|\s*❤️)'
            like_match = re.search(like_pattern, container_text)
            if like_match:
                comment_data['likes'] = int(like_match.group(1))
            
            # 判断是否为回复
            if '回复' in container_text or 'reply' in comment_container.attr('class', '').lower():
                comment_data['is_reply'] = True
            
            return comment_data
            
        except Exception as e:
            return None
    
    def _remove_duplicates(self, comments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去除重复评论"""
        seen_contents = set()
        unique_comments = []
        
        for comment in comments:
            content_key = comment['content'][:50]  # 使用前50个字符作为去重键
            if content_key not in seen_contents and len(comment['content']) > 5:
                seen_contents.add(content_key)
                unique_comments.append(comment)
        
        return unique_comments
    
    def collect_comments(self, url: str = None, max_rounds: int = 20) -> Dict[str, Any]:
        """主要采集方法"""
        try:
            print("🎯 启动DrissionPage评论采集器...\n")
            
            # 设置浏览器
            self.setup_browser()
            
            # 如果提供了URL，导航到该页面
            if url:
                self.navigate_to_page(url)
            else:
                # 使用当前页面
                self.comments_data['note_url'] = self.page.url
                self.comments_data['note_title'] = self.page.title.replace(' - 小红书', '').strip()
                self.comments_data['timestamp'] = datetime.now().isoformat()
                print(f"📝 使用当前页面: {self.comments_data['note_title']}")
            
            # 等待评论加载
            if not self.wait_for_comments_load():
                raise Exception("评论区域加载失败")
            
            # 滚动并展开评论
            total_clicked = self.scroll_and_expand_comments(max_rounds)
            
            # 提取所有评论
            comments = self.extract_all_comments()
            self.comments_data['comments'] = comments
            
            # 计算统计信息
            self.comments_data['summary']['total_comments'] = len([c for c in comments if not c['is_reply']])
            self.comments_data['summary']['total_replies'] = len([c for c in comments if c['is_reply']])
            self.comments_data['summary']['total_likes'] = sum(c['likes'] for c in comments)
            
            print(f"\n🎉 采集完成!")
            print(f"📊 总评论: {len(comments)}条")
            print(f"📝 主评论: {self.comments_data['summary']['total_comments']}条")
            print(f"↩️ 回复: {self.comments_data['summary']['total_replies']}条")
            print(f"👍 总点赞: {self.comments_data['summary']['total_likes']}")
            print(f"📈 采集进度: {round(len(comments)/1472*100, 1)}% (目标1472条)")
            
            return self.comments_data
            
        except Exception as e:
            print(f"❌ 采集失败: {str(e)}")
            raise e
        finally:
            if self.page:
                self.page.quit()
    
    def save_results(self, filename_prefix: str = "drission_comments"):
        """保存采集结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON文件
        json_filename = f"{filename_prefix}_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.comments_data, f, ensure_ascii=False, indent=2)
        
        # 保存可读报告
        txt_filename = f"{filename_prefix}_{timestamp}.txt"
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(self._generate_report())
        
        print(f"✅ 结果已保存:")
        print(f"   📁 JSON文件: {json_filename}")
        print(f"   📄 文本报告: {txt_filename}")
        
        return json_filename, txt_filename
    
    def _generate_report(self) -> str:
        """生成可读报告"""
        lines = []
        
        lines.append("🎯 DrissionPage小红书评论采集报告")
        lines.append("=" * 60)
        lines.append(f"📅 采集时间: {self.comments_data['timestamp']}")
        lines.append(f"📝 笔记标题: {self.comments_data['note_title']}")
        lines.append(f"🔗 笔记链接: {self.comments_data['note_url']}")
        lines.append(f"🔧 采集策略: {self.comments_data['summary']['strategy']}")
        lines.append("")
        
        lines.append("📊 采集统计:")
        lines.append(f"💬 总评论数: {len(self.comments_data['comments'])}")
        lines.append(f"📝 主评论数: {self.comments_data['summary']['total_comments']}")
        lines.append(f"↩️ 回复数: {self.comments_data['summary']['total_replies']}")
        lines.append(f"👍 总点赞数: {self.comments_data['summary']['total_likes']}")
        lines.append(f"📈 采集进度: {round(len(self.comments_data['comments'])/1472*100, 1)}% (目标1472条)")
        lines.append("")
        
        lines.append("💬 评论详情:")
        lines.append("-" * 60)
        
        for i, comment in enumerate(self.comments_data['comments'], 1):
            lines.append(f"\n💬 评论 {i}:")
            lines.append(f"👤 用户: {comment['username'] or '匿名'}")
            lines.append(f"📄 内容: {comment['content'] or '无内容'}")
            lines.append(f"👍 点赞: {comment['likes']}")
            lines.append(f"📅 时间: {comment['publish_time'] or '未知'}")
            lines.append(f"🔗 头像: {comment['avatar'] or '无'}")
            lines.append(f"↩️ 回复: {'是' if comment['is_reply'] else '否'}")
        
        lines.append("\n" + "=" * 60)
        lines.append("📅 报告生成时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        return "\n".join(lines)


def main():
    """主函数"""
    collector = DrissionCommentsCollector()
    
    try:
        # 采集评论 (使用当前打开的页面)
        results = collector.collect_comments(max_rounds=30)
        
        # 保存结果
        collector.save_results()
        
    except Exception as e:
        print(f"❌ 程序执行失败: {str(e)}")


if __name__ == "__main__":
    main()
