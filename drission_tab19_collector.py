#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 DrissionPage 19号窗口小红书评论采集器
专门针对19号窗口进行评论采集
"""

import json
import time
import random
import re
from datetime import datetime
from typing import List, Dict, Any

try:
    from DrissionPage import ChromiumPage
    DRISSION_AVAILABLE = True
except ImportError:
    DRISSION_AVAILABLE = False
    print("⚠️ DrissionPage未安装，将使用备用方案")


class DrissionTab19Collector:
    """DrissionPage 19号窗口评论采集器"""
    
    def __init__(self):
        self.page = None
        self.comments_data = {
            'note_url': '',
            'note_title': '',
            'timestamp': '',
            'comments': [],
            'summary': {
                'total_comments': 0,
                'total_replies': 0,
                'total_likes': 0,
                'strategy': 'drissionpage-tab19'
            }
        }
    
    def connect_to_tab19(self):
        """连接到19号标签页"""
        print("🔌 连接到19号标签页...")
        
        if not DRISSION_AVAILABLE:
            print("❌ DrissionPage不可用，请先安装: pip install DrissionPage")
            return False
        
        try:
            # 创建页面对象，连接到现有浏览器
            self.page = ChromiumPage()
            
            # 获取所有标签页
            tabs = self.page.get_tabs()
            print(f"📋 找到 {len(tabs)} 个标签页")
            
            # 查找19号标签页或小红书标签页
            target_tab = None
            
            # 方法1: 直接查找第19个标签页
            if len(tabs) >= 19:
                target_tab = tabs[18]  # 索引从0开始，所以19号是索引18
                print(f"✅ 找到19号标签页: {target_tab.title[:50]}...")
            else:
                # 方法2: 查找小红书标签页
                for i, tab in enumerate(tabs):
                    if 'xiaohongshu.com' in tab.url:
                        target_tab = tab
                        print(f"✅ 找到小红书标签页(第{i+1}号): {tab.title[:50]}...")
                        break
            
            if not target_tab:
                print("❌ 未找到19号标签页或小红书标签页")
                return False
            
            # 切换到目标标签页
            self.page.set.tab(target_tab)
            time.sleep(2)
            
            print("✅ 成功连接到目标标签页")
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
            return False
    
    def get_page_info(self):
        """获取页面信息"""
        print("📋 获取页面信息...")
        
        try:
            self.comments_data['note_url'] = self.page.url
            self.comments_data['note_title'] = self.page.title.replace(' - 小红书', '').strip()
            self.comments_data['timestamp'] = datetime.now().isoformat()
            
            print(f"📝 页面标题: {self.comments_data['note_title']}")
            print(f"🔗 页面URL: {self.comments_data['note_url'][:80]}...")
            
            return True
        except Exception as e:
            print(f"❌ 获取页面信息失败: {str(e)}")
            return False
    
    def wait_for_comments(self):
        """等待评论区域加载"""
        print("⏳ 等待评论区域加载...")
        
        try:
            # 等待头像元素出现
            self.page.wait.ele_loaded('img[src*="avatar"]', timeout=10)
            print("✅ 评论区域已加载")
            return True
        except:
            print("⚠️ 评论区域加载超时，继续尝试...")
            return True  # 即使超时也继续，可能页面已经有内容
    
    def count_elements(self):
        """统计页面元素"""
        try:
            avatars = self.page.eles('img[src*="avatar"]')
            comment_divs = self.page.eles('[class*="comment"]')
            
            print(f"📊 当前统计: 👤{len(avatars)}个头像, 💬{len(comment_divs)}个评论div")
            return len(avatars), len(comment_divs)
        except:
            return 0, 0
    
    def scroll_and_expand(self, max_rounds: int = 25):
        """滚动并展开评论"""
        print(f"🔄 开始滚动并展开评论 (最多{max_rounds}轮)...")
        
        total_clicked = 0
        no_change_count = 0
        
        for round_num in range(1, max_rounds + 1):
            print(f"\n🔄 第 {round_num} 轮:")
            
            # 记录当前状态
            before_avatars, before_divs = self.count_elements()
            
            # 滚动到底部
            try:
                self.page.scroll.to_bottom()
                time.sleep(1.5)
            except:
                print("   ⚠️ 滚动失败")
            
            # 点击"更多"按钮
            clicked_in_round = self._click_more_buttons()
            total_clicked += clicked_in_round
            
            # 等待内容加载
            wait_time = 2 + random.uniform(0.5, 1.5)
            time.sleep(wait_time)
            
            # 检查变化
            after_avatars, after_divs = self.count_elements()
            avatars_added = after_avatars - before_avatars
            divs_added = after_divs - before_divs
            
            print(f"   📈 变化: 头像+{avatars_added}, 评论div+{divs_added}, 点击{clicked_in_round}个按钮")
            
            # 判断是否继续
            if avatars_added == 0 and divs_added == 0 and clicked_in_round == 0:
                no_change_count += 1
                print(f"   ⚠️ 无变化轮次: {no_change_count}/3")
                
                if no_change_count >= 3:
                    print("🏁 连续3轮无变化，停止滚动")
                    break
            else:
                no_change_count = 0
        
        print(f"\n🎉 滚动完成! 总共点击了 {total_clicked} 个按钮")
        return total_clicked
    
    def _click_more_buttons(self):
        """点击"更多"按钮"""
        clicked_count = 0
        
        try:
            # 查找"更多"按钮
            button_selectors = [
                'text:更多',
                'text:展开', 
                'text:查看',
                'text:条回复'
            ]
            
            for selector in button_selectors:
                try:
                    elements = self.page.eles(selector)
                    
                    # 点击前3个按钮
                    for i, element in enumerate(elements[:3]):
                        try:
                            if element.states.is_displayed:
                                element.scroll.to_center()
                                time.sleep(0.3)
                                element.click()
                                clicked_count += 1
                                print(f"     🖱️ 点击: {element.text[:20]}...")
                                time.sleep(random.uniform(0.5, 1.0))
                        except:
                            continue
                            
                except:
                    continue
                    
        except Exception as e:
            print(f"     ❌ 点击按钮时出错: {str(e)[:30]}...")
        
        return clicked_count
    
    def extract_comments(self):
        """提取评论"""
        print("🔍 开始提取评论...")
        
        comments = []
        
        try:
            # 查找所有头像元素
            avatars = self.page.eles('img[src*="avatar"]')
            print(f"   找到 {len(avatars)} 个头像元素")
            
            for index, avatar in enumerate(avatars):
                try:
                    comment_data = self._extract_single_comment(avatar, index + 1)
                    if comment_data and comment_data.get('content'):
                        comments.append(comment_data)
                except Exception as e:
                    continue
            
            # 去重
            unique_comments = self._remove_duplicates(comments)
            
            print(f"🎉 提取完成! 原始: {len(comments)}条, 去重后: {len(unique_comments)}条")
            return unique_comments
            
        except Exception as e:
            print(f"❌ 提取评论失败: {str(e)}")
            return []
    
    def _extract_single_comment(self, avatar_element, comment_id: int):
        """提取单条评论"""
        try:
            # 获取评论容器
            container = avatar_element.parent(5)  # 向上查找5层
            
            comment_data = {
                'id': comment_id,
                'username': '',
                'avatar': '',
                'content': '',
                'publish_time': '',
                'likes': 0,
                'is_reply': False
            }
            
            # 提取头像
            comment_data['avatar'] = avatar_element.attr('src') or ''
            
            # 获取容器文本
            container_text = container.text
            
            # 提取用户名 - 查找短文本元素
            try:
                text_elements = container.eles('tag:*')
                for elem in text_elements:
                    text = elem.text.strip()
                    if (text and len(text) > 1 and len(text) < 30 and 
                        not any(keyword in text for keyword in ['天前', '小时前', '分钟前', '点赞', '回复'])):
                        comment_data['username'] = text
                        break
            except:
                pass
            
            # 提取评论内容 - 查找较长文本
            try:
                content_elements = container.eles('tag:*')
                for elem in content_elements:
                    text = elem.text.strip()
                    if (text and len(text) > 10 and len(text) < 1000 and
                        not any(keyword in text for keyword in ['天前', '小时前', '点赞', '回复', '更多', '展开'])):
                        if len(text) > len(comment_data['content']):
                            comment_data['content'] = text
            except:
                pass
            
            # 提取发布时间
            time_pattern = r'\d+天前|\d+小时前|\d+分钟前|昨天|前天|\d{2}-\d{2}'
            time_match = re.search(time_pattern, container_text)
            if time_match:
                comment_data['publish_time'] = time_match.group()
            
            # 提取点赞数
            like_pattern = r'(\d+)(?=\s*赞|\s*❤️)'
            like_match = re.search(like_pattern, container_text)
            if like_match:
                comment_data['likes'] = int(like_match.group(1))
            
            # 判断是否为回复
            if '回复' in container_text:
                comment_data['is_reply'] = True
            
            return comment_data
            
        except Exception as e:
            return None
    
    def _remove_duplicates(self, comments):
        """去除重复评论"""
        seen_contents = set()
        unique_comments = []
        
        for comment in comments:
            content_key = comment.get('content', '')[:50]
            if content_key and content_key not in seen_contents and len(comment.get('content', '')) > 5:
                seen_contents.add(content_key)
                unique_comments.append(comment)
        
        return unique_comments
    
    def collect_comments(self):
        """主要采集方法"""
        print("🎯 启动DrissionPage 19号窗口评论采集器...\n")
        
        try:
            # 1. 连接到19号标签页
            if not self.connect_to_tab19():
                return None
            
            # 2. 获取页面信息
            if not self.get_page_info():
                return None
            
            # 3. 等待评论加载
            self.wait_for_comments()
            
            # 4. 滚动并展开评论
            total_clicked = self.scroll_and_expand(max_rounds=30)
            
            # 5. 提取所有评论
            comments = self.extract_comments()
            self.comments_data['comments'] = comments
            
            # 6. 计算统计信息
            self.comments_data['summary']['total_comments'] = len([c for c in comments if not c.get('is_reply', False)])
            self.comments_data['summary']['total_replies'] = len([c for c in comments if c.get('is_reply', False)])
            self.comments_data['summary']['total_likes'] = sum(c.get('likes', 0) for c in comments)
            
            print(f"\n🎉 采集完成!")
            print(f"📊 总评论: {len(comments)}条")
            print(f"📝 主评论: {self.comments_data['summary']['total_comments']}条")
            print(f"↩️ 回复: {self.comments_data['summary']['total_replies']}条")
            print(f"👍 总点赞: {self.comments_data['summary']['total_likes']}")
            print(f"📈 采集进度: {round(len(comments)/1472*100, 1)}% (目标1472条)")
            
            return self.comments_data
            
        except Exception as e:
            print(f"❌ 采集失败: {str(e)}")
            return None
        finally:
            # 不关闭页面，保持连接
            pass
    
    def save_results(self, filename_prefix: str = "drission_tab19"):
        """保存采集结果"""
        if not self.comments_data['comments']:
            print("⚠️ 没有评论数据可保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON文件
        json_filename = f"{filename_prefix}_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.comments_data, f, ensure_ascii=False, indent=2)
        
        # 保存可读报告
        txt_filename = f"{filename_prefix}_{timestamp}.txt"
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(self._generate_report())
        
        print(f"✅ 结果已保存:")
        print(f"   📁 JSON文件: {json_filename}")
        print(f"   📄 文本报告: {txt_filename}")
        
        return json_filename, txt_filename
    
    def _generate_report(self):
        """生成可读报告"""
        lines = []
        
        lines.append("🎯 DrissionPage 19号窗口评论采集报告")
        lines.append("=" * 60)
        lines.append(f"📅 采集时间: {self.comments_data['timestamp']}")
        lines.append(f"📝 笔记标题: {self.comments_data['note_title']}")
        lines.append(f"🔗 笔记链接: {self.comments_data['note_url']}")
        lines.append(f"🔧 采集策略: {self.comments_data['summary']['strategy']}")
        lines.append("")
        
        lines.append("📊 采集统计:")
        lines.append(f"💬 总评论数: {len(self.comments_data['comments'])}")
        lines.append(f"📝 主评论数: {self.comments_data['summary']['total_comments']}")
        lines.append(f"↩️ 回复数: {self.comments_data['summary']['total_replies']}")
        lines.append(f"👍 总点赞数: {self.comments_data['summary']['total_likes']}")
        lines.append(f"📈 采集进度: {round(len(self.comments_data['comments'])/1472*100, 1)}% (目标1472条)")
        lines.append("")
        
        lines.append("💬 评论详情:")
        lines.append("-" * 60)
        
        for i, comment in enumerate(self.comments_data['comments'], 1):
            lines.append(f"\n💬 评论 {i}:")
            lines.append(f"👤 用户: {comment.get('username', '匿名')}")
            lines.append(f"📄 内容: {comment.get('content', '无内容')}")
            lines.append(f"👍 点赞: {comment.get('likes', 0)}")
            lines.append(f"📅 时间: {comment.get('publish_time', '未知')}")
            lines.append(f"🔗 头像: {comment.get('avatar', '无')}")
            lines.append(f"↩️ 回复: {'是' if comment.get('is_reply', False) else '否'}")
        
        lines.append("\n" + "=" * 60)
        lines.append("📅 报告生成时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        return "\n".join(lines)


def main():
    """主函数"""
    print("🎯 DrissionPage 19号窗口小红书评论采集器")
    print("=" * 60)
    print("📋 功能说明:")
    print("   1. 自动连接到19号浏览器标签页")
    print("   2. 智能滚动和展开评论")
    print("   3. 提取完整的评论数据")
    print("   4. 生成详细的采集报告")
    print("   5. 保存JSON和文本格式结果")
    print()
    
    collector = DrissionTab19Collector()
    
    try:
        # 采集评论
        results = collector.collect_comments()
        
        if results:
            # 保存结果
            collector.save_results()
            print("\n🎉 采集任务完成!")
        else:
            print("\n❌ 采集失败!")
            
    except Exception as e:
        print(f"❌ 程序执行失败: {str(e)}")


if __name__ == "__main__":
    main()
