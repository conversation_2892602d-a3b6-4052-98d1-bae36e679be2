// ===== Electron 桌面应用主进程文件 =====
// 这个文件是桌面应用的入口，负责创建窗口、启动内置服务器等

// 导入 Electron 相关模块
const { app, BrowserWindow, Menu, shell, dialog, ipcMain } = require('electron');
const path = require('path'); // Node.js 路径处理模块
const { spawn } = require('child_process'); // 子进程模块，用于启动内置 Node.js 服务器
const http = require('http'); // HTTP 模块，用于检测服务器状态

// ===== 全局变量定义 =====
let mainWindow; // 主窗口对象，保持全局引用防止被垃圾回收
let serverProcess; // 内置服务器进程对象
const isDev = process.env.NODE_ENV === 'development'; // 是否为开发模式
const serverPort = 3000; // 内置服务器端口号

// ===== 启动内置 Node.js 服务器 =====
// 这个函数负责启动应用的后端服务器，提供 API 和静态文件服务
function startServer() {
    return new Promise((resolve, reject) => {
        // 先检查服务器是否已经在运行（避免重复启动）
        checkServer().then(isRunning => {
            if (isRunning) {
                console.log('✅ 检测到服务器已在运行，直接连接...');
                resolve(); // 服务器已运行，直接返回成功
                return;
            }

            console.log('🚀 启动内置服务器...');

            // 启动服务器进程
            serverProcess = spawn('node', ['server.js'], {
                stdio: 'pipe',
                cwd: __dirname
            });

            serverProcess.stdout.on('data', (data) => {
                console.log(`服务器输出: ${data}`);
                if (data.toString().includes('黑默科技平台启动成功')) {
                    resolve();
                }
            });

            serverProcess.stderr.on('data', (data) => {
                console.error(`服务器错误: ${data}`);
                // 如果是端口被占用错误，尝试连接现有服务器
                if (data.toString().includes('EADDRINUSE')) {
                    console.log('⚠️ 端口被占用，尝试连接现有服务器...');
                    resolve();
                }
            });

            serverProcess.on('close', (code) => {
                console.log(`服务器进程退出，代码: ${code}`);
                if (code !== 0) {
                    // 如果服务器启动失败，尝试连接现有服务器
                    resolve();
                }
            });

            // 5秒后如果还没启动成功，也继续
            setTimeout(resolve, 5000);
        });
    });
}

// 检查服务器是否可用
function checkServer() {
    return new Promise((resolve) => {
        const req = http.get(`http://localhost:${serverPort}`, (res) => {
            resolve(true);
        });
        
        req.on('error', () => {
            resolve(false);
        });
        
        req.setTimeout(2000, () => {
            req.destroy();
            resolve(false);
        });
    });
}

// 创建主窗口
async function createWindow() {
    console.log('📱 创建主窗口...');
    
    // 创建浏览器窗口
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1000,
        minHeight: 700,
        icon: path.join(__dirname, 'assets', 'icon.png'),
        title: '黑默科技 - 营销管理平台',
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js'),
            webSecurity: false, // 开发时禁用web安全
            allowRunningInsecureContent: true
        },
        titleBarStyle: 'default',
        show: false, // 先不显示，等加载完成后再显示
        backgroundColor: '#f5f6fa'
    });
    
    // 窗口准备好后显示
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // 开发模式下打开开发者工具
        if (isDev) {
            mainWindow.webContents.openDevTools();
        }
    });
    
    // 启动服务器
    try {
        await startServer();
        
        // 等待服务器启动
        let serverReady = false;
        for (let i = 0; i < 10; i++) {
            serverReady = await checkServer();
            if (serverReady) break;
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        if (serverReady) {
            console.log('✅ 服务器已就绪，加载Premium UI...');

            // 清除缓存并加载页面
            await mainWindow.webContents.session.clearCache();
            console.log('🧹 缓存已清除');

            // 加载原有的Premium UI页面
            const timestamp = Date.now();
            mainWindow.loadURL(`http://localhost:${serverPort}/premium?t=${timestamp}`);
        } else {
            console.log('❌ 服务器启动失败，显示错误信息...');
            mainWindow.loadURL('data:text/html,<h1>服务器启动失败</h1><p>请重启应用程序</p>');
        }

    } catch (error) {
        console.error('启动服务器失败:', error);
        mainWindow.loadURL('data:text/html,<h1>启动失败</h1><p>服务器无法启动，请检查端口占用情况</p>');
    }
    
    // 处理窗口关闭
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
    
    // 处理外部链接
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
    
    // 阻止导航到外部网站
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        if (parsedUrl.origin !== `http://localhost:${serverPort}`) {
            event.preventDefault();
            shell.openExternal(navigationUrl);
        }
    });
}

// 创建应用菜单
function createMenu() {
    const template = [
        {
            label: '文件',
            submenu: [
                {
                    label: '刷新',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.reload();
                        }
                    }
                },
                {
                    label: '强制刷新',
                    accelerator: 'CmdOrCtrl+Shift+R',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.reloadIgnoringCache();
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: '退出',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: '编辑',
            submenu: [
                { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                { type: 'separator' },
                { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' }
            ]
        },
        {
            label: '视图',
            submenu: [
                { label: '重新加载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                { label: '强制重新加载', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
                { label: '切换开发者工具', accelerator: 'F12', role: 'toggleDevTools' },
                { type: 'separator' },
                { label: '实际大小', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
                { label: '放大', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
                { label: '缩小', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
                { type: 'separator' },
                { label: '切换全屏', accelerator: 'F11', role: 'togglefullscreen' }
            ]
        },
        {
            label: '窗口',
            submenu: [
                { label: '最小化', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
                { label: '关闭', accelerator: 'CmdOrCtrl+W', role: 'close' }
            ]
        },
        {
            label: '帮助',
            submenu: [
                {
                    label: '关于黑默科技平台',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: '关于',
                            message: '黑默科技 - 综合管理平台',
                            detail: `版本: ${app.getVersion()}\n基于 Electron ${process.versions.electron}\n\n© 2024 黑默科技团队`
                        });
                    }
                }
            ]
        }
    ];
    
    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// 应用事件处理
app.whenReady().then(async () => {
    console.log('🎯 Electron应用已准备就绪');
    
    createMenu();
    await createWindow();
    
    app.on('activate', async () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            await createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    // 关闭服务器进程
    if (serverProcess) {
        console.log('🛑 关闭服务器进程...');
        serverProcess.kill();
    }
    
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('before-quit', () => {
    // 关闭服务器进程
    if (serverProcess) {
        console.log('🛑 应用退出前关闭服务器...');
        serverProcess.kill();
    }
});

// 安全设置
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
    });
});

console.log('🚀 黑默科技桌面应用启动中...');
