#!/usr/bin/env node

/**
 * 🔍 增强版小红书数据采集器
 * 可以获取截图中显示的所有详细信息：粉丝数、关注数、获赞数、个人标签等
 */

const axios = require('axios');
const WebSocket = require('ws');

class EnhancedXiaohongshuCollector {
    constructor() {
        this.debugPort = 51859;
        this.currentTab = null;
    }

    // 🔍 主要采集方法
    async collectDetailedData() {
        console.log('🔍 开始增强版小红书数据采集...\n');

        try {
            // 1. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 目标页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 2. 通过WebSocket连接获取详细数据
            const detailedData = await this.extractDetailedDataViaWebSocket(tab);
            
            console.log('✅ 详细数据采集完成!');
            return detailedData;

        } catch (error) {
            console.error('❌ 采集失败:', error.message);
            throw error;
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        try {
            const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
                timeout: 5000
            });

            const tabs = response.data;
            return tabs.find(tab =>
                tab.url && (
                    tab.url.includes('xiaohongshu.com') ||
                    tab.url.includes('creator.xiaohongshu.com') ||
                    tab.title.includes('小红书')
                )
            );
        } catch (error) {
            console.error('❌ 无法获取标签页列表:', error.message);
            return null;
        }
    }

    // 🌐 通过WebSocket获取详细数据
    async extractDetailedDataViaWebSocket(tab) {
        return new Promise((resolve, reject) => {
            const wsUrl = tab.webSocketDebuggerUrl;
            console.log(`🔌 连接WebSocket: ${wsUrl}`);

            const ws = new WebSocket(wsUrl);
            let requestId = 1;
            const results = {};

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');

                // 启用Runtime域
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                // 执行数据提取脚本
                setTimeout(() => {
                    const extractScript = this.buildDetailedExtractionScript();
                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: extractScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const extractedData = message.result.result.value;
                        console.log('📊 成功提取页面数据');
                        
                        // 处理提取的数据
                        const processedData = this.processDetailedData(extractedData, tab);
                        ws.close();
                        resolve(processedData);
                    }
                } catch (error) {
                    console.error('❌ 解析WebSocket消息失败:', error.message);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket连接错误:', error.message);
                reject(error);
            });

            ws.on('close', () => {
                console.log('🔌 WebSocket连接已关闭');
            });

            // 超时处理
            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                    reject(new Error('WebSocket连接超时'));
                }
            }, 15000);
        });
    }

    // 📝 构建详细数据提取脚本
    buildDetailedExtractionScript() {
        return `
            (function() {
                try {
                    const data = {
                        timestamp: new Date().toISOString(),
                        url: window.location.href,
                        title: document.title
                    };

                    // 提取用户昵称
                    const nicknameSelectors = [
                        '.user-name',
                        '.username',
                        '[class*="user-name"]',
                        '[class*="username"]',
                        'h1',
                        '.title'
                    ];
                    
                    for (const selector of nicknameSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            data.nickname = element.textContent.trim();
                            break;
                        }
                    }

                    // 提取小红书号
                    const xiaohongshuIdElements = document.querySelectorAll('*');
                    for (const element of xiaohongshuIdElements) {
                        const text = element.textContent;
                        if (text && text.includes('小红书号:')) {
                            const match = text.match(/小红书号[：:]\\s*([0-9]+)/);
                            if (match) {
                                data.xiaohongshuId = match[1];
                                break;
                            }
                        }
                    }

                    // 提取统计数据 (关注、粉丝、获赞)
                    const statsElements = document.querySelectorAll('[class*="count"], [class*="num"], [class*="stat"]');
                    const statsText = Array.from(statsElements).map(el => el.textContent.trim()).join(' ');
                    
                    // 查找包含数字的元素
                    const numberElements = document.querySelectorAll('*');
                    const stats = [];
                    
                    for (const element of numberElements) {
                        const text = element.textContent.trim();
                        if (/^\\d+$/.test(text) && parseInt(text) > 0) {
                            const parent = element.parentElement;
                            if (parent) {
                                const context = parent.textContent.toLowerCase();
                                if (context.includes('关注') || context.includes('follow')) {
                                    data.followCount = parseInt(text);
                                } else if (context.includes('粉丝') || context.includes('fans') || context.includes('follower')) {
                                    data.fansCount = parseInt(text);
                                } else if (context.includes('获赞') || context.includes('点赞') || context.includes('like')) {
                                    data.likesAndCollects = parseInt(text);
                                }
                            }
                        }
                    }

                    // 提取头像
                    const avatarSelectors = [
                        'img[alt*="头像"]',
                        '.avatar img',
                        '.user-avatar img',
                        '[class*="avatar"] img'
                    ];
                    
                    for (const selector of avatarSelectors) {
                        const img = document.querySelector(selector);
                        if (img && img.src && !img.src.includes('data:')) {
                            data.avatar = img.src;
                            break;
                        }
                    }

                    // 提取个人简介/标签
                    const bioSelectors = [
                        '.user-desc',
                        '.bio',
                        '.description',
                        '[class*="desc"]',
                        '[class*="bio"]',
                        '.intro'
                    ];
                    
                    for (const selector of bioSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            data.bio = element.textContent.trim();
                            break;
                        }
                    }

                    // 提取年龄和地区信息
                    const infoElements = document.querySelectorAll('*');
                    for (const element of infoElements) {
                        const text = element.textContent;
                        
                        // 年龄匹配
                        const ageMatch = text.match(/(\\d+)岁/);
                        if (ageMatch) {
                            data.age = ageMatch[1] + '岁';
                        }
                        
                        // 地区匹配
                        const locationMatch = text.match(/([\\u4e00-\\u9fa5]+[\\u4e00-\\u9fa5]+)$/);
                        if (locationMatch && locationMatch[1].length >= 2) {
                            data.location = locationMatch[1];
                        }
                    }

                    // 设置数据来源
                    data.dataSource = 'browser_websocket';
                    data.extractionMethod = 'enhanced_dom_analysis';
                    
                    return data;
                    
                } catch (error) {
                    return {
                        error: error.message,
                        timestamp: new Date().toISOString()
                    };
                }
            })();
        `;
    }

    // 📊 处理提取的详细数据
    processDetailedData(rawData, tab) {
        const processedData = {
            ...rawData,
            tabId: tab.id,
            webSocketUrl: tab.webSocketDebuggerUrl
        };

        // 从标题中提取昵称（备用方法）
        if (!processedData.nickname && tab.title && tab.title.includes(' - 小红书')) {
            const titleParts = tab.title.split(' - 小红书');
            if (titleParts.length > 0) {
                processedData.nickname = titleParts[0].trim();
            }
        }

        // 从URL中提取用户ID（备用方法）
        if (!processedData.xiaohongshuId && tab.url) {
            const urlMatch = tab.url.match(/user\/profile\/([a-zA-Z0-9]+)/);
            if (urlMatch) {
                processedData.xiaohongshuId = urlMatch[1];
            }
        }

        return processedData;
    }
}

// 🧪 测试增强版数据采集
async function testEnhancedCollection() {
    const collector = new EnhancedXiaohongshuCollector();
    
    try {
        const data = await collector.collectDetailedData();
        
        console.log('\n📊 采集到的详细数据:');
        console.log('=' * 50);
        console.log(`🏷️  昵称: ${data.nickname || '未获取'}`);
        console.log(`🆔 小红书号: ${data.xiaohongshuId || '未获取'}`);
        console.log(`👥 关注数: ${data.followCount || '未获取'}`);
        console.log(`👥 粉丝数: ${data.fansCount || '未获取'}`);
        console.log(`❤️  获赞数: ${data.likesAndCollects || '未获取'}`);
        console.log(`🎂 年龄: ${data.age || '未获取'}`);
        console.log(`📍 地区: ${data.location || '未获取'}`);
        console.log(`🖼️  头像: ${data.avatar ? '已获取' : '未获取'}`);
        console.log(`📝 简介: ${data.bio || '未获取'}`);
        console.log(`📊 数据来源: ${data.dataSource}`);
        
        return data;
        
    } catch (error) {
        console.error('❌ 增强版采集失败:', error.message);
        console.log('💡 请确保19号浏览器已启动并打开了小红书用户页面');
    }
}

// 运行测试
if (require.main === module) {
    testEnhancedCollection().catch(console.error);
}

module.exports = { EnhancedXiaohongshuCollector, testEnhancedCollection };
