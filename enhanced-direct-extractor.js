#!/usr/bin/env node

/**
 * 🚀 增强版直接提取器
 * 优化用户名、用户ID提取，增加滚动加载，改进内容清理
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class EnhancedDirectExtractor {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        // 优化配置
        this.config = {
            scrollDelay: 2500,        // 滚动间隔2.5秒
            maxScrollAttempts: 12,    // 最大滚动次数
            waitForContent: 3000,     // 等待内容加载3秒
            minCommentLength: 5,      // 最小评论长度
            maxCommentLength: 1000    // 最大评论长度
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    async connectToBrowser() {
        try {
            console.log('🔗 连接到比特浏览器...');
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${this.debugPort}`,
                defaultViewport: null
            });
            
            const pages = await browser.pages();
            const page = pages.find(p => p.url().includes('xiaohongshu.com/explore/'));
            
            if (!page) {
                throw new Error('未找到小红书笔记页面');
            }
            
            console.log('✅ 找到小红书笔记页面');
            console.log(`📄 当前URL: ${page.url()}`);
            
            return { browser, page };
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    // 增强版滚动加载
    async enhancedScrollLoad(page) {
        console.log('🔄 开始增强版滚动加载...');
        
        let previousTextLength = 0;
        let stableCount = 0;
        
        for (let i = 0; i < this.config.maxScrollAttempts; i++) {
            console.log(`   📜 滚动 ${i + 1}/${this.config.maxScrollAttempts}`);
            
            // 检查页面文本长度变化
            const currentTextLength = await page.evaluate(() => document.body.textContent.length);
            console.log(`   📝 页面文本长度: ${currentTextLength}`);
            
            if (currentTextLength === previousTextLength) {
                stableCount++;
                if (stableCount >= 3) {
                    console.log('   ⏹️ 内容稳定，停止滚动');
                    break;
                }
            } else {
                stableCount = 0;
                previousTextLength = currentTextLength;
            }
            
            // 模拟人类滚动
            await page.evaluate(() => {
                const scrollDistance = 350 + Math.random() * 150; // 350-500px
                window.scrollBy({
                    top: scrollDistance,
                    behavior: 'smooth'
                });
            });
            
            // 随机延迟
            const delay = this.config.scrollDelay + Math.random() * 500;
            console.log(`   ⏱️ 等待 ${Math.round(delay)}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        console.log('✅ 增强版滚动完成');
        
        // 等待最终内容稳定
        await new Promise(resolve => setTimeout(resolve, this.config.waitForContent));
    }

    // 增强版评论提取
    async enhancedExtractComments(page) {
        console.log('🧠 开始增强版评论提取...');
        
        const result = await page.evaluate(() => {
            const data = {
                noteInfo: {
                    id: '',
                    title: '',
                    author: '',
                    totalCommentCount: 0
                },
                comments: [],
                extractStats: {
                    totalTextLength: 0,
                    commentSectionLength: 0,
                    processedLines: 0
                },
                extractTime: new Date().toISOString()
            };
            
            try {
                // 获取页面文本
                const pageText = document.body.textContent;
                data.extractStats.totalTextLength = pageText.length;
                
                // 提取笔记基本信息
                const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
                if (urlMatch) {
                    data.noteInfo.id = urlMatch[1];
                }
                
                // 提取标题（多种方式）
                const titleSelectors = ['h1', '.title', '[class*="title"]', '.note-title'];
                for (const selector of titleSelectors) {
                    const titleEl = document.querySelector(selector);
                    if (titleEl && titleEl.textContent.trim()) {
                        data.noteInfo.title = titleEl.textContent.trim();
                        break;
                    }
                }
                
                // 如果没找到标题，从页面文本中提取
                if (!data.noteInfo.title) {
                    const titleMatch = pageText.match(/有什么软件可以手机上兼织专米？\s*求推荐！/);
                    if (titleMatch) {
                        data.noteInfo.title = titleMatch[0];
                    }
                }
                
                // 提取评论总数
                const countMatch = pageText.match(/共\s*(\d+)\s*条评论/);
                if (countMatch) {
                    data.noteInfo.totalCommentCount = parseInt(countMatch[1]);
                }
                
                // 提取作者信息
                const authorMatch = pageText.match(/漫娴学姐\s*招暑假工版/);
                if (authorMatch) {
                    data.noteInfo.author = '漫娴学姐 招暑假工版';
                }
                
                return data;
            } catch (error) {
                console.error('提取基本信息时出错:', error);
                data.error = error.message;
                return data;
            }
        });
        
        // 在Node.js中进行增强解析
        const pageText = await page.evaluate(() => document.body.textContent);
        const comments = this.enhancedParseComments(pageText);
        result.comments = comments;
        result.extractStats.processedLines = comments.length;
        
        return result;
    }

    // 增强版评论解析
    enhancedParseComments(pageText) {
        console.log('🔍 开始增强版评论解析...');
        
        const comments = [];
        
        // 查找评论区域（更精确的匹配）
        const commentPatterns = [
            /共\s*\d+\s*条评论([\s\S]*?)(?:回到顶部|发现|发布|创作中心|window\.|$)/,
            /\d+\s*条评论([\s\S]*?)(?:回到顶部|发现|发布|$)/,
            /评论([\s\S]*?)(?:回到顶部|发现|发布|$)/
        ];
        
        let commentText = '';
        for (const pattern of commentPatterns) {
            const match = pageText.match(pattern);
            if (match && match[1].length > 100) {
                commentText = match[1];
                break;
            }
        }
        
        if (!commentText) {
            console.log('❌ 未找到评论区域');
            return comments;
        }
        
        console.log(`📝 评论区域文本长度: ${commentText.length}`);
        
        // 按行分割并预处理
        const lines = commentText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);
        
        console.log(`📄 总行数: ${lines.length}`);
        
        let currentComment = null;
        let commentIndex = 0;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            // 跳过无用行
            if (this.shouldSkipLine(line)) {
                continue;
            }
            
            // 检查是否是新评论的开始
            if (this.isEnhancedCommentStart(line)) {
                // 保存前一个评论
                if (currentComment && this.isValidComment(currentComment)) {
                    this.enhancedFinalizeComment(currentComment);
                    comments.push(currentComment);
                }
                
                // 创建新评论
                currentComment = this.createEnhancedComment(++commentIndex, line);
                console.log(`📝 发现评论 ${commentIndex}: ${this.truncateText(line, 40)}`);
            } else if (currentComment) {
                // 继续当前评论
                if (line.length > 3 && !this.isMetaInfo(line)) {
                    currentComment.rawContent += ' ' + line;
                }
                currentComment.allLines.push(line);
            }
        }
        
        // 保存最后一个评论
        if (currentComment && this.isValidComment(currentComment)) {
            this.enhancedFinalizeComment(currentComment);
            comments.push(currentComment);
        }
        
        console.log(`✅ 增强解析完成，提取到 ${comments.length} 条评论`);
        return comments;
    }

    // 增强版评论开始判断
    isEnhancedCommentStart(line) {
        // 1. 作者标识
        if (line.includes('作者') && line.length < 100) return true;
        
        // 2. 置顶评论
        if (line.includes('置顶评论')) return true;
        
        // 3. 时间格式开头
        if (line.match(/^\d{2}-\d{2}/)) return true;
        
        // 4. 用户名+时间格式（更精确）
        if (line.match(/^[^\d\s]{2,20}\s*\d{2}-\d{2}/)) return true;
        
        // 5. 表情符号开头
        if (line.match(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/)) return true;
        
        // 6. 常见用户名模式
        const userPatterns = [
            /^[A-Za-z0-9_]{3,15}\s*\d{2}-\d{2}/,  // 英文用户名
            /^[\u4e00-\u9fff]{2,8}\s*\d{2}-\d{2}/, // 中文用户名
            /^.*学姐.*\d{2}-\d{2}/,                // 学姐类用户名
            /^.*宝子.*\d{2}-\d{2}/                 // 宝子类用户名
        ];
        
        for (const pattern of userPatterns) {
            if (line.match(pattern)) return true;
        }
        
        // 7. 包含特定关键词的短行
        const keywords = ['求带', '宝子', '广州', '学姐', '兼职', '工作'];
        if (keywords.some(keyword => line.includes(keyword)) && line.length < 60) {
            return true;
        }
        
        return false;
    }

    // 创建增强版评论对象
    createEnhancedComment(index, firstLine) {
        const comment = {
            id: index,
            userId: '',
            username: '',
            content: '',
            time: '',
            likes: 0,
            replyCount: 0,
            isAuthor: firstLine.includes('作者'),
            isPinned: firstLine.includes('置顶'),
            rawContent: firstLine,
            allLines: [firstLine],
            extractedInfo: {}
        };
        
        // 增强版时间提取
        const timePatterns = [
            /(\d{2}-\d{2})/,
            /(\d+小时前)/,
            /(\d+分钟前)/,
            /(\d+天前)/
        ];
        
        for (const pattern of timePatterns) {
            const match = firstLine.match(pattern);
            if (match) {
                comment.time = match[1];
                break;
            }
        }
        
        // 增强版用户名提取
        const userPatterns = [
            // 中文用户名 + 时间
            /^([\u4e00-\u9fff]{2,8})\s*\d{2}-\d{2}/,
            // 英文用户名 + 时间
            /^([A-Za-z0-9_]{3,15})\s*\d{2}-\d{2}/,
            // 特殊格式：用户名 + 作者
            /^([^\d\s]{2,20})\s*作者/,
            // 特殊格式：用户名 + 置顶
            /^([^\d\s]{2,20})\s*置顶/,
            // 表情符号后的用户名
            /^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([^\d\s]{2,15})\s*\d{2}-\d{2}/
        ];
        
        for (const pattern of userPatterns) {
            const match = firstLine.match(pattern);
            if (match && match[1]) {
                let username = match[1].trim();
                // 清理用户名中的表情符号
                username = username.replace(/[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/g, '').trim();
                if (username.length >= 2 && username.length <= 20) {
                    comment.username = username;
                    break;
                }
            }
        }
        
        return comment;
    }

    // 增强版评论完善
    enhancedFinalizeComment(comment) {
        // 清理内容
        let content = comment.rawContent;

        // 移除用户名
        if (comment.username) {
            content = content.replace(new RegExp(comment.username, 'g'), '');
        }

        // 移除时间
        content = content.replace(/\d{2}-\d{2}/g, '');
        content = content.replace(/\d+小时前|\d+分钟前|\d+天前/g, '');

        // 移除表情符号开头
        content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');

        // 移除常见标识
        content = content.replace(/作者|置顶评论|回复|赞|仅自己可见/g, '');

        // 清理空格和特殊字符
        content = content.replace(/\s+/g, ' ').trim();
        content = content.replace(/^[^\w\u4e00-\u9fff]+/, '').trim();

        // 从所有行中提取数字信息
        const allText = comment.allLines.join(' ');

        // 提取点赞数
        const likePatterns = [
            /(\d+)\s*赞/,
            /赞\s*(\d+)/,
            /^(\d+)$/
        ];

        for (const pattern of likePatterns) {
            const match = allText.match(pattern);
            if (match) {
                const num = parseInt(match[1]);
                if (num > 0 && num < 10000 && !allText.includes(num + '回复')) {
                    comment.likes = num;
                    break;
                }
            }
        }

        // 提取回复数
        const replyPatterns = [
            /(\d+)\s*回复/,
            /回复\s*(\d+)/,
            /展开\s*(\d+)\s*条/
        ];

        for (const pattern of replyPatterns) {
            const match = allText.match(pattern);
            if (match) {
                comment.replyCount = parseInt(match[1]);
                break;
            }
        }

        // 尝试提取用户ID（从可能的链接或特殊格式中）
        const userIdPatterns = [
            /user\/([a-f0-9]{20,})/,
            /uid[=:]([a-f0-9]{20,})/,
            /小红薯([A-F0-9]{8,})/
        ];

        for (const pattern of userIdPatterns) {
            const match = allText.match(pattern);
            if (match) {
                comment.userId = match[1];
                break;
            }
        }

        comment.content = content;
        delete comment.rawContent;
        delete comment.allLines;
    }

    // 判断是否应该跳过这一行
    shouldSkipLine(line) {
        const skipPatterns = [
            '加载中', '你还没有', '温馨提示', '广告屏蔽', '我知道了',
            '沪ICP备', '营业执照', '公网安备', '增值电信', '医疗器械',
            '创作服务', '直播管理', '专业号', '商家入驻', 'MCN入驻',
            'window.', 'function', 'console.', 'document.', '更多',
            '行吟信息', '黄浦区', '举报电话', '举报中心', '网络文化'
        ];

        return skipPatterns.some(pattern => line.includes(pattern)) ||
               line.length < 3 ||
               /^[0-9\s\-:\.]+$/.test(line) ||
               /^[^\w\u4e00-\u9fff]+$/.test(line);
    }

    // 判断是否是元信息
    isMetaInfo(line) {
        return line.match(/^\d+$/) ||
               line.match(/^\d+回复$/) ||
               line.match(/^回复$/) ||
               line.match(/^赞$/) ||
               line.includes('展开') ||
               line.includes('条回复') ||
               line.includes('仅自己可见') ||
               line.includes('加载中');
    }

    // 验证评论是否有效
    isValidComment(comment) {
        return comment &&
               comment.rawContent &&
               comment.rawContent.length >= this.config.minCommentLength &&
               comment.rawContent.length <= this.config.maxCommentLength;
    }

    // 截断文本
    truncateText(text, maxLength) {
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        try {
            console.log('🚀 启动增强版直接提取器...');
            console.log('🛡️ 优化策略: 增强滚动 + 智能解析 + 用户信息提取');

            const { browser, page } = await this.connectToBrowser();

            // 增强版滚动加载
            await this.enhancedScrollLoad(page);

            // 增强版评论提取
            const result = await this.enhancedExtractComments(page);

            // 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `enhanced_comments_${result.noteInfo.id}_${timestamp}.json`;
            this.saveToFile(result, filename);

            // 输出结果
            console.log('\n🎉 增强版提取完成！');
            console.log('📊 提取统计:');
            console.log(`   🆔 笔记ID: ${result.noteInfo.id}`);
            console.log(`   📝 笔记标题: ${result.noteInfo.title || '未知'}`);
            console.log(`   👤 笔记作者: ${result.noteInfo.author || '未知'}`);
            console.log(`   🎯 目标评论数: ${result.noteInfo.totalCommentCount}`);
            console.log(`   💬 实际提取数: ${result.comments.length}`);
            console.log(`   📄 页面文本长度: ${result.extractStats.totalTextLength}`);
            console.log(`   📁 保存文件: ${filename}`);

            if (result.comments.length > 0) {
                console.log('\n👥 用户信息预览:');
                result.comments.slice(0, 5).forEach((comment, index) => {
                    console.log(`   ${index + 1}. 用户: ${comment.username || '未知'}`);
                    console.log(`      用户ID: ${comment.userId || '未提取到'}`);
                    console.log(`      时间: ${comment.time || '未知'}`);
                    console.log(`      内容: ${this.truncateText(comment.content, 50)}`);
                    console.log(`      点赞: ${comment.likes} | 回复: ${comment.replyCount}`);
                    console.log(`      标识: ${comment.isAuthor ? '作者' : ''}${comment.isPinned ? '置顶' : ''}`);
                    console.log('');
                });

                // 统计信息
                const withUsername = result.comments.filter(c => c.username).length;
                const withUserId = result.comments.filter(c => c.userId).length;
                const withTime = result.comments.filter(c => c.time).length;

                console.log('📈 提取质量统计:');
                console.log(`   👤 有用户名: ${withUsername}/${result.comments.length} (${Math.round(withUsername/result.comments.length*100)}%)`);
                console.log(`   🆔 有用户ID: ${withUserId}/${result.comments.length} (${Math.round(withUserId/result.comments.length*100)}%)`);
                console.log(`   ⏰ 有时间: ${withTime}/${result.comments.length} (${Math.round(withTime/result.comments.length*100)}%)`);
            }

            await browser.disconnect();

        } catch (error) {
            console.error('❌ 提取失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行提取器
if (require.main === module) {
    const extractor = new EnhancedDirectExtractor();
    extractor.run();
}

module.exports = EnhancedDirectExtractor;
