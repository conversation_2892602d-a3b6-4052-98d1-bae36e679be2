/**
 * 🚀 快速滑动爬虫
 * 加快滑动速度，快速触发懒加载机制获取所有1472条评论
 */

const puppeteer = require('puppeteer-core');
const axios = require('axios');
const fs = require('fs');

class FastScrollScraper {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.targetWindowSeq = 19;
        this.browser = null;
        this.page = null;
        this.comments = [];
        this.targetCommentCount = 1472;
    }

    /**
     * 连接到比特浏览器
     */
    async connectToBrowser() {
        console.log('🔗 连接到比特浏览器...');
        
        try {
            // 获取窗口信息
            const windowResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            const browsers = windowResponse.data.data.list;
            const window19 = browsers.find(browser => browser.seq === this.targetWindowSeq);
            
            if (!window19) {
                throw new Error('未找到19号窗口');
            }

            // 获取调试端口
            const portsResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            const ports = portsResponse.data.data;
            const debugPort = ports[window19.id];
            
            if (!debugPort) {
                throw new Error('19号窗口未获取到调试端口');
            }

            // 获取浏览器WebSocket端点
            const versionResponse = await axios.get(`http://localhost:${debugPort}/json/version`);
            const browserWSEndpoint = versionResponse.data.webSocketDebuggerUrl;
            
            this.browser = await puppeteer.connect({
                browserWSEndpoint,
                defaultViewport: null
            });
            
            // 获取小红书页面
            const browserPages = await this.browser.pages();
            this.page = browserPages.find(page => page.url().includes('xiaohongshu.com/explore/'));
            
            if (!this.page) {
                this.page = browserPages.find(page => page.url().includes('xiaohongshu.com'));
            }
            
            if (!this.page) {
                throw new Error('未找到小红书页面');
            }
            
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            console.log('✅ 连接成功!');
            console.log(`📄 页面: ${title}`);
            
            return true;
            
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    /**
     * 快速滑动 - 减少延迟，增加滑动频率
     */
    async fastScroll(distance = 500) {
        // 快速滚动，减少延迟
        await this.page.mouse.wheel({ deltaY: distance });
        
        // 大幅减少等待时间
        await new Promise(resolve => setTimeout(resolve, 200)); // 从2000ms减少到200ms
    }

    /**
     * 快速点击展开按钮
     */
    async fastClickExpand() {
        try {
            const expandSelectors = [
                'button:contains("展开")',
                'button:contains("更多")',
                'span:contains("展开")',
                '[class*="expand"]',
                '[class*="more"]'
            ];

            let clickCount = 0;
            
            for (const selector of expandSelectors) {
                try {
                    const elements = await this.page.$$(selector);
                    
                    for (const element of elements) {
                        const isVisible = await element.isIntersectingViewport();
                        if (isVisible) {
                            await element.click();
                            clickCount++;
                            
                            // 快速等待
                            await new Promise(resolve => setTimeout(resolve, 100));
                        }
                    }
                } catch (e) {
                    continue;
                }
            }
            
            return clickCount;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 超快速滚动策略
     */
    async superFastScroll() {
        console.log('🚀 开始超快速滑动，快速加载所有评论...');
        
        let scrollCount = 0;
        let lastCommentCount = 0;
        let noProgressCount = 0;
        let totalClickCount = 0;
        
        // 大幅增加滚动次数，减少等待时间
        while (scrollCount < 500 && noProgressCount < 20) {
            // 检查当前评论数量
            const currentCommentCount = await this.page.evaluate(() => {
                const commentElements = document.querySelectorAll(
                    '[class*="comment"], [class*="Comment"], [class*="reply"], [class*="Reply"]'
                );
                return commentElements.length;
            });
            
            // 每50次显示进度
            if (scrollCount % 50 === 0) {
                console.log(`🔄 快速滚动 ${scrollCount} 次，评论元素: ${currentCommentCount}，目标: ${this.targetCommentCount}`);
            }
            
            // 快速点击展开按钮
            if (scrollCount % 5 === 0) {
                const clickCount = await this.fastClickExpand();
                totalClickCount += clickCount;
            }
            
            // 快速滚动
            const scrollDistance = 600 + Math.random() * 400; // 600-1000px，增加滚动距离
            await this.fastScroll(scrollDistance);
            
            // 检查进度
            if (currentCommentCount > lastCommentCount) {
                noProgressCount = 0;
                if (scrollCount % 50 === 0) {
                    console.log(`   ✅ 新增 ${currentCommentCount - lastCommentCount} 个评论元素`);
                }
            } else {
                noProgressCount++;
            }
            
            lastCommentCount = currentCommentCount;
            scrollCount++;
            
            // 如果接近目标，继续加速
            if (currentCommentCount > this.targetCommentCount * 0.5) {
                // 超过50%时，进一步加速
                await this.fastScroll(800);
                await this.fastClickExpand();
            }
            
            // 如果接近目标数量，增加操作频率
            if (currentCommentCount > this.targetCommentCount * 0.8) {
                console.log(`   🎯 接近目标 (${currentCommentCount}/${this.targetCommentCount})，全速冲刺！`);
                await this.fastScroll(1000);
                await this.fastClickExpand();
                await this.fastScroll(1000);
            }
        }
        
        console.log(`\n🏁 超快速滚动完成！`);
        console.log(`   总滚动次数: ${scrollCount}`);
        console.log(`   总点击次数: ${totalClickCount}`);
        console.log(`   最终评论元素: ${lastCommentCount}`);
        console.log(`   完成度: ${Math.round((lastCommentCount / this.targetCommentCount) * 100)}%`);
        
        // 快速滚动回顶部
        await this.page.evaluate(() => window.scrollTo(0, 0));
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    /**
     * 增强版评论提取
     */
    async enhancedExtractComments() {
        console.log('🔍 开始增强版评论提取...');
        
        try {
            const comments = await this.page.evaluate(() => {
                const extractedComments = [];
                
                // 更全面的选择器策略
                const allSelectors = [
                    '[class*="comment-item"]',
                    '[class*="comment-container"]',
                    '[class*="user-comment"]',
                    '[class*="comment-content"]',
                    '[class*="comment-text"]',
                    '[class*="comment"]',
                    '[class*="Comment"]',
                    '[class*="reply"]',
                    '[class*="Reply"]',
                    '[class*="interaction"]',
                    '[data-testid*="comment"]',
                    '[role="comment"]'
                ];
                
                // 执行每种选择器
                allSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        
                        elements.forEach((element, index) => {
                            const text = element.textContent?.trim();
                            if (text && text.length > 10) {
                                // 智能解析评论结构
                                const lines = text.split('\n').filter(line => line.trim());
                                
                                let username = '';
                                let content = '';
                                let time = '';
                                let likes = '';
                                
                                // 解析逻辑
                                for (let i = 0; i < lines.length; i++) {
                                    const line = lines[i].trim();
                                    
                                    // 跳过无用行
                                    if (line.length < 2 || 
                                        /^(赞|回复|展开|收起|点赞|分享|举报)$/.test(line)) {
                                        continue;
                                    }
                                    
                                    // 时间识别
                                    if (/\d{2}-\d{2}|\d+[分小天月年]前/.test(line)) {
                                        time = line;
                                        continue;
                                    }
                                    
                                    // 点赞数识别
                                    if (/^\d+$/.test(line) && parseInt(line) > 0 && parseInt(line) < 10000) {
                                        likes = line;
                                        continue;
                                    }
                                    
                                    // 用户名识别
                                    if (!username && line.length < 50 && 
                                        !line.includes('条评论') && 
                                        !line.includes('回复') &&
                                        !line.includes('展开') &&
                                        !line.includes('emoji')) {
                                        username = line;
                                    } else if (line.length > 5 && line.length < 2000) {
                                        // 评论内容
                                        if (content) {
                                            content += ' ' + line;
                                        } else {
                                            content = line;
                                        }
                                    }
                                }
                                
                                // 清理内容
                                if (content) {
                                    content = content
                                        .replace(/展开\s*\d+\s*条回复/g, '')
                                        .replace(/\d+赞回复/g, '')
                                        .replace(/赞回复$/g, '')
                                        .replace(/回复$/g, '')
                                        .replace(/[🥝😃🦁🥦🍟🐭🥪😁🥭🐡🥒😛🦊🥬🥞🍍🧁🍙😷🍉🐟🍊😏🍑]/g, '') // 移除emoji
                                        .trim();
                                    
                                    if (content.length > 5 && content.length < 2000) {
                                        extractedComments.push({
                                            username: username || '未知用户',
                                            content: content,
                                            time: time || '',
                                            likes: likes || '',
                                            method: `fast_${selector}`,
                                            element_index: index,
                                            timestamp: new Date().toISOString()
                                        });
                                    }
                                }
                            }
                        });
                    } catch (e) {
                        console.log(`选择器 ${selector} 处理失败:`, e.message);
                    }
                });
                
                // 文本分析策略 - 更激进的提取
                const bodyText = document.body.textContent || '';
                const allLines = bodyText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
                
                for (let i = 0; i < allLines.length; i++) {
                    const line = allLines[i];
                    
                    // 查找时间模式
                    if (/\d{2}-\d{2}|\d+[分小天月年]前/.test(line) && line.length < 50) {
                        // 向前查找评论内容
                        for (let j = 1; j <= 3; j++) {
                            if (i - j >= 0) {
                                const contentLine = allLines[i - j];
                                if (contentLine.length > 10 && contentLine.length < 500 &&
                                    !/(赞|回复|展开|收起|点赞|分享|举报|条评论)$/.test(contentLine)) {
                                    
                                    // 查找用户名
                                    let username = '未知用户';
                                    if (i - j - 1 >= 0) {
                                        const userLine = allLines[i - j - 1];
                                        if (userLine.length > 1 && userLine.length < 30) {
                                            username = userLine;
                                        }
                                    }
                                    
                                    // 检查是否已存在
                                    const exists = extractedComments.some(comment => 
                                        comment.content.includes(contentLine.substring(0, 30))
                                    );
                                    
                                    if (!exists) {
                                        extractedComments.push({
                                            username: username,
                                            content: contentLine,
                                            time: line,
                                            likes: '',
                                            method: 'fast_text_analysis',
                                            timestamp: new Date().toISOString()
                                        });
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
                
                // 去重处理
                const uniqueComments = [];
                const seenContents = new Set();
                
                for (const comment of extractedComments) {
                    const contentKey = comment.content.substring(0, 50);
                    if (!seenContents.has(contentKey) && 
                        comment.content.length > 5 && 
                        comment.content.length < 2000) {
                        seenContents.add(contentKey);
                        uniqueComments.push(comment);
                    }
                }
                
                return uniqueComments;
            });
            
            this.comments = comments;
            console.log(`✅ 增强版提取完成，共获得 ${this.comments.length} 条评论`);
            
            // 显示提取统计
            const methodStats = {};
            this.comments.forEach(comment => {
                const method = comment.method.split('_')[1] || comment.method;
                methodStats[method] = (methodStats[method] || 0) + 1;
            });
            
            console.log('📊 提取方式统计:');
            Object.entries(methodStats).forEach(([method, count]) => {
                console.log(`   ${method}: ${count} 条`);
            });
            
            return this.comments.length > 0;
            
        } catch (error) {
            console.error('❌ 增强版提取失败:', error.message);
            return false;
        }
    }

    /**
     * 保存为快速版TXT格式
     */
    async saveFastTXT() {
        if (this.comments.length === 0) {
            console.log('⚠️ 没有评论数据可保存');
            return;
        }
        
        try {
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            // 生成快速版TXT内容
            let txtContent = '';
            txtContent += '='.repeat(80) + '\n';
            txtContent += '小红书评论完整数据 (快速滑动版)\n';
            txtContent += '='.repeat(80) + '\n';
            txtContent += `页面标题: ${title}\n`;
            txtContent += `页面链接: ${currentUrl}\n`;
            txtContent += `爬取时间: ${new Date().toLocaleString('zh-CN')}\n`;
            txtContent += `评论总数: ${this.comments.length} 条\n`;
            txtContent += `目标数量: ${this.targetCommentCount} 条\n`;
            txtContent += `完成度: ${Math.round((this.comments.length / this.targetCommentCount) * 100)}%\n`;
            txtContent += `爬取方式: 超快速滑动 + 增强提取\n`;
            txtContent += '='.repeat(80) + '\n\n';
            
            // 按时间排序
            const sortedComments = this.comments.sort((a, b) => {
                if (a.time && b.time) {
                    return a.time.localeCompare(b.time);
                }
                return 0;
            });
            
            // 添加评论内容
            sortedComments.forEach((comment, index) => {
                txtContent += `【评论 ${index + 1}】\n`;
                txtContent += `用户名: ${comment.username}\n`;
                if (comment.time) {
                    txtContent += `发布时间: ${comment.time}\n`;
                }
                if (comment.likes) {
                    txtContent += `点赞数: ${comment.likes}\n`;
                }
                txtContent += `评论内容: ${comment.content}\n`;
                txtContent += `提取方式: ${comment.method}\n`;
                txtContent += '-'.repeat(60) + '\n\n';
            });
            
            // 添加详细统计
            txtContent += '='.repeat(80) + '\n';
            txtContent += '详细统计信息\n';
            txtContent += '='.repeat(80) + '\n';
            
            // 按提取方式统计
            const methodStats = {};
            this.comments.forEach(comment => {
                methodStats[comment.method] = (methodStats[comment.method] || 0) + 1;
            });
            
            txtContent += '按提取方式统计:\n';
            Object.entries(methodStats).sort((a, b) => b[1] - a[1]).forEach(([method, count]) => {
                const percentage = Math.round((count / this.comments.length) * 100);
                txtContent += `  ${method}: ${count} 条 (${percentage}%)\n`;
            });
            
            // 按时间统计
            const timeStats = {};
            this.comments.forEach(comment => {
                if (comment.time) {
                    const timeKey = comment.time.includes('-') ? comment.time.substring(0, 5) : 
                                   comment.time.includes('前') ? comment.time : '其他';
                    timeStats[timeKey] = (timeStats[timeKey] || 0) + 1;
                }
            });
            
            if (Object.keys(timeStats).length > 0) {
                txtContent += '\n按时间统计:\n';
                Object.entries(timeStats).sort().forEach(([time, count]) => {
                    txtContent += `  ${time}: ${count} 条\n`;
                });
            }
            
            txtContent += '\n' + '='.repeat(80) + '\n';
            txtContent += '快速滑动技术说明:\n';
            txtContent += '- 大幅减少滑动间隔时间 (200ms vs 2000ms)\n';
            txtContent += '- 增加滚动距离和频率\n';
            txtContent += '- 智能加速策略，接近目标时全速冲刺\n';
            txtContent += '- 增强版评论提取，更全面的选择器\n';
            txtContent += '- 自动清理emoji和无用信息\n';
            txtContent += '='.repeat(80) + '\n';
            
            // 保存文件
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const txtFilename = `xiaohongshu_fast_scroll_comments_${timestamp}.txt`;
            
            fs.writeFileSync(txtFilename, txtContent, 'utf8');
            
            console.log(`💾 快速滑动版TXT文件已保存: ${txtFilename}`);
            console.log(`📊 总计 ${this.comments.length} 条评论 (目标: ${this.targetCommentCount} 条)`);
            
            return txtFilename;
            
        } catch (error) {
            console.error('❌ 保存文件失败:', error.message);
        }
    }

    /**
     * 运行快速滑动爬虫
     */
    async run() {
        console.log('🚀 快速滑动爬虫 - 目标1472条评论');
        console.log('='.repeat(80));
        
        try {
            // 1. 连接浏览器
            await this.connectToBrowser();
            
            // 2. 快速等待页面稳定
            console.log('⏳ 快速等待页面稳定...');
            await new Promise(resolve => setTimeout(resolve, 2000)); // 减少等待时间
            
            // 3. 超快速滚动加载所有评论
            await this.superFastScroll();
            
            // 4. 增强版提取所有评论
            const success = await this.enhancedExtractComments();
            
            if (!success) {
                console.log('❌ 未能提取到评论数据');
                return false;
            }
            
            // 5. 保存为快速版TXT格式
            const txtFile = await this.saveFastTXT();
            
            console.log('\n🎉 快速滑动爬取完成!');
            console.log(`📁 请查看生成的TXT文件: ${txtFile}`);
            console.log(`📊 成功获取 ${this.comments.length} / ${this.targetCommentCount} 条评论`);
            
            const completionRate = Math.round((this.comments.length / this.targetCommentCount) * 100);
            console.log(`🎯 完成度: ${completionRate}%`);
            
            if (completionRate >= 80) {
                console.log('🏆 优秀！获取了大部分评论数据！');
            } else if (completionRate >= 50) {
                console.log('👍 良好！获取了一半以上的评论数据！');
            } else if (completionRate >= 20) {
                console.log('📈 进步！获取了相当数量的评论数据！');
            } else {
                console.log('💡 建议：可能需要更长时间运行或调整策略');
            }
            
            return true;
            
        } catch (error) {
            console.error('❌ 快速滑动爬虫运行失败:', error.message);
            return false;
        } finally {
            if (this.browser) {
                console.log('🔒 断开浏览器连接...');
                await this.browser.disconnect();
            }
        }
    }
}

// 主函数
async function main() {
    console.log('🚀 快速滑动爬虫启动器');
    console.log('='.repeat(80));
    
    console.log('⚡ 优化特点:');
    console.log('   🚀 大幅减少滑动间隔 (200ms vs 2000ms)');
    console.log('   📏 增加滚动距离 (600-1000px)');
    console.log('   🎯 智能加速策略');
    console.log('   🔍 增强版评论提取');
    console.log('   🧹 自动清理无用信息');
    
    const scraper = new FastScrollScraper();
    await scraper.run();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = FastScrollScraper;
