#!/usr/bin/env node

/**
 * 🎯 终极评论提取器
 * 不依赖调试端口，直接从页面内容提取所有评论
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class FinalCommentExtractor {
    constructor() {
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        this.config = {
            targetComments: 1472
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    // 获取页面内容的多种方式
    async getPageContent() {
        console.log('📄 获取页面内容...');
        console.log('请选择获取页面内容的方式：');
        console.log('1. 从文件读取（推荐）');
        console.log('2. 直接粘贴内容');
        console.log('3. 使用剪贴板内容');
        
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        return new Promise((resolve) => {
            rl.question('请选择方式 (1/2/3): ', async (choice) => {
                rl.close();
                
                switch (choice.trim()) {
                    case '1':
                        resolve(await this.readFromFile());
                        break;
                    case '2':
                        resolve(await this.readFromInput());
                        break;
                    case '3':
                        resolve(await this.readFromClipboard());
                        break;
                    default:
                        console.log('默认使用方式1：从文件读取');
                        resolve(await this.readFromFile());
                        break;
                }
            });
        });
    }

    // 从文件读取
    async readFromFile() {
        console.log('\n📁 从文件读取页面内容...');
        
        // 检查可能的文件
        const possibleFiles = [
            'page_content.txt',
            'xiaohongshu_content.txt',
            'comments_raw.txt',
            'page_source.txt',
            'content.txt'
        ];
        
        for (const filename of possibleFiles) {
            const filepath = path.join(this.outputDir, filename);
            if (fs.existsSync(filepath)) {
                console.log(`✅ 找到文件: ${filename}`);
                const content = fs.readFileSync(filepath, 'utf8');
                console.log(`📄 文件大小: ${content.length} 字符`);
                return content;
            }
        }
        
        console.log('❌ 未找到页面内容文件');
        console.log('💡 请将小红书页面内容保存到以下任一文件：');
        possibleFiles.forEach(file => {
            console.log(`   - ${this.outputDir}/${file}`);
        });
        console.log('\n📋 获取页面内容的步骤：');
        console.log('1. 在小红书页面按 Ctrl+A 全选');
        console.log('2. 按 Ctrl+C 复制');
        console.log('3. 将内容粘贴到文本文件中');
        console.log('4. 保存为上述文件名之一');
        
        return await this.readFromInput();
    }

    // 从输入读取
    async readFromInput() {
        console.log('\n📝 直接输入页面内容...');
        console.log('请粘贴小红书页面的完整内容（输入 END 结束）：');
        
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        let content = '';
        let lineCount = 0;
        
        return new Promise((resolve) => {
            rl.on('line', (line) => {
                if (line.trim() === 'END') {
                    rl.close();
                    console.log(`✅ 输入完成，共 ${lineCount} 行，${content.length} 字符`);
                    resolve(content);
                } else {
                    content += line + '\n';
                    lineCount++;
                    
                    if (lineCount % 100 === 0) {
                        console.log(`已输入 ${lineCount} 行...`);
                    }
                }
            });
        });
    }

    // 从剪贴板读取（需要额外的包，这里提供指导）
    async readFromClipboard() {
        console.log('\n📋 从剪贴板读取...');
        console.log('💡 请先复制小红书页面内容到剪贴板');
        console.log('然后按回车继续...');
        
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        return new Promise((resolve) => {
            rl.question('', () => {
                rl.close();
                console.log('❌ 剪贴板功能需要额外配置');
                console.log('💡 请使用方式1或方式2');
                resolve(this.readFromInput());
            });
        });
    }

    // 超级评论解析器
    superParseComments(pageText) {
        console.log('🧠 开始超级评论解析...');
        console.log(`📄 页面文本长度: ${pageText.length}`);
        
        const allComments = [];
        
        // 使用所有可能的解析策略
        const strategies = [
            { name: '时间戳解析', method: () => this.parseByTimestamp(pageText) },
            { name: '用户名解析', method: () => this.parseByUsername(pageText) },
            { name: '关键词解析', method: () => this.parseByKeywords(pageText) },
            { name: '表情符号解析', method: () => this.parseByEmoji(pageText) },
            { name: '数字模式解析', method: () => this.parseByNumbers(pageText) },
            { name: '结构化解析', method: () => this.parseByStructure(pageText) },
            { name: '混合模式解析', method: () => this.parseByMixed(pageText) },
            { name: '兼职专用解析', method: () => this.parseJobComments(pageText) }
        ];
        
        strategies.forEach(strategy => {
            try {
                console.log(`🔍 执行${strategy.name}...`);
                const strategyComments = strategy.method();
                console.log(`   📝 ${strategy.name}提取到 ${strategyComments.length} 条评论`);
                allComments.push(...strategyComments);
            } catch (error) {
                console.log(`   ❌ ${strategy.name}失败: ${error.message}`);
            }
        });
        
        console.log(`📊 总计收集到 ${allComments.length} 条原始评论`);
        
        // 超级去重和清理
        const uniqueComments = this.superDeduplication(allComments);
        
        console.log(`✅ 超级解析完成，最终提取到 ${uniqueComments.length} 条评论`);
        return uniqueComments;
    }

    // 时间戳解析
    parseByTimestamp(pageText) {
        const comments = [];
        
        // 更全面的时间模式
        const timePatterns = [
            /(\d{2}-\d{2})/g,
            /(\d+小时前)/g,
            /(\d+分钟前)/g,
            /(\d+天前)/g,
            /(\d{4}-\d{2}-\d{2})/g,
            /(\d{1,2}:\d{2})/g
        ];
        
        timePatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                const timeStr = match[1];
                const timeIndex = match.index;
                
                // 提取时间前后的内容
                const startIndex = Math.max(0, timeIndex - 300);
                const endIndex = Math.min(pageText.length, timeIndex + 1200);
                const contextText = pageText.substring(startIndex, endIndex);
                
                if (contextText.length > 30 && this.looksLikeComment(contextText)) {
                    const comment = this.createCommentFromContext(contextText, comments.length + 1, 'timestamp');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });
        
        return comments;
    }

    // 用户名解析
    parseByUsername(pageText) {
        const comments = [];
        
        // 用户名模式
        const usernamePatterns = [
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(\d{2}-\d{2})/g,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(求带|宝子|学姐)/g,
            /小红薯([A-F0-9]{8,})/g,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(\d+小时前|\d+分钟前|\d+天前)/g
        ];
        
        usernamePatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                const username = match[1];
                const matchIndex = match.index;
                
                // 提取用户名前后的内容
                const startIndex = Math.max(0, matchIndex - 150);
                const endIndex = Math.min(pageText.length, matchIndex + 1000);
                const contextText = pageText.substring(startIndex, endIndex);
                
                if (contextText.length > 20 && this.looksLikeComment(contextText)) {
                    const comment = this.createCommentFromContext(contextText, comments.length + 1, 'username');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });
        
        return comments;
    }

    // 关键词解析
    parseByKeywords(pageText) {
        const comments = [];
        
        // 兼职相关关键词
        const keywords = [
            '求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯',
            'Carina', '广州', '工作', '赚钱', '日入', '月入', '陪聊',
            '直播', '客服', '代理', '抢票', '演唱会', '游戏', '试玩',
            '大学生', '暑假工', '兼织', '专米', '推荐', '软件', '平台',
            '微信', 'QQ', '联系', '私聊', '加我', '找我', '咨询'
        ];
        
        keywords.forEach(keyword => {
            const regex = new RegExp(keyword, 'g');
            let match;
            
            while ((match = regex.exec(pageText)) !== null) {
                const keywordIndex = match.index;
                
                // 提取关键词前后的内容
                const startIndex = Math.max(0, keywordIndex - 200);
                const endIndex = Math.min(pageText.length, keywordIndex + 800);
                const contextText = pageText.substring(startIndex, endIndex);
                
                if (contextText.length > 25 && this.looksLikeComment(contextText)) {
                    const comment = this.createCommentFromContext(contextText, comments.length + 1, 'keyword');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });
        
        return comments;
    }

    // 表情符号解析
    parseByEmoji(pageText) {
        const comments = [];
        
        // 小红书常见表情符号模式
        const emojiPattern = /[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍🥪😁🥭🐡🥒😛🧁🍙]+/g;
        let match;
        
        while ((match = emojiPattern.exec(pageText)) !== null) {
            const emojiIndex = match.index;
            
            // 提取表情符号前后的内容
            const startIndex = Math.max(0, emojiIndex - 80);
            const endIndex = Math.min(pageText.length, emojiIndex + 600);
            const contextText = pageText.substring(startIndex, endIndex);
            
            if (contextText.length > 20 && this.looksLikeComment(contextText)) {
                const comment = this.createCommentFromContext(contextText, comments.length + 1, 'emoji');
                if (comment) {
                    comments.push(comment);
                }
            }
        }
        
        return comments;
    }

    // 数字模式解析
    parseByNumbers(pageText) {
        const comments = [];
        
        // 数字相关模式（点赞、回复、收入等）
        const numberPatterns = [
            /(\d+)\s*赞/g,
            /(\d+)\s*回复/g,
            /日入\s*(\d+)/g,
            /月入\s*(\d+)/g,
            /(\d+)\s*元/g,
            /(\d+)\s*块/g,
            /一天\s*(\d+)/g
        ];
        
        numberPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                const numberIndex = match.index;
                
                // 提取数字前后的内容
                const startIndex = Math.max(0, numberIndex - 150);
                const endIndex = Math.min(pageText.length, numberIndex + 500);
                const contextText = pageText.substring(startIndex, endIndex);
                
                if (contextText.length > 15 && this.looksLikeComment(contextText)) {
                    const comment = this.createCommentFromContext(contextText, comments.length + 1, 'number');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });
        
        return comments;
    }
