#!/usr/bin/env node

/**
 * 🎯 最终版本：基于测试结果的完整评论采集器
 */

const axios = require('axios');
const WebSocket = require('ws');
const fs = require('fs');

class FinalCommentsCollector {
    constructor() {
        this.debugPort = 63524; // 基于测试确定的端口
    }

    // 🎯 主要采集方法
    async collectAllComments() {
        console.log('🎯 启动最终版评论采集器...\n');

        try {
            // 1. 获取标签页
            const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
                timeout: 5000
            });

            const tab = response.data.find(tab =>
                tab.url && tab.url.includes('xiaohongshu.com/explore/')
            );

            if (!tab) {
                throw new Error('未找到小红书笔记页面');
            }

            console.log(`🎯 目标页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url.substring(0, 80)}...`);

            // 2. 执行采集
            const commentsData = await this.performCollection(tab);
            
            return commentsData;

        } catch (error) {
            console.error('❌ 最终采集失败:', error.message);
            throw error;
        }
    }

    // 🔄 执行采集
    async performCollection(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    console.log('🔄 开始执行最终采集...');
                    
                    const collectionScript = `
                        (function() {
                            try {
                                console.log('🎯 开始最终评论采集...');
                                
                                const commentsData = {
                                    noteUrl: window.location.href,
                                    noteTitle: document.title.replace(' - 小红书', '').trim(),
                                    timestamp: new Date().toISOString(),
                                    comments: [],
                                    summary: {
                                        totalComments: 0,
                                        totalReplies: 0,
                                        totalLikes: 0,
                                        strategy: 'final-optimized'
                                    }
                                };

                                // 第一步：点击所有可见的"更多"按钮
                                console.log('🔘 第一步：点击所有"更多"按钮...');
                                const moreButtonTexts = ['更多', '展开', '查看', '条回复'];
                                let totalClicked = 0;
                                
                                for (const buttonText of moreButtonTexts) {
                                    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                                        const text = el.textContent.trim();
                                        const isVisible = el.offsetParent !== null;
                                        const isClickable = el.tagName === 'BUTTON' ||
                                                          el.tagName === 'A' ||
                                                          el.onclick ||
                                                          (el.className && el.className.includes && el.className.includes('btn')) ||
                                                          getComputedStyle(el).cursor === 'pointer';
                                        
                                        return isVisible && isClickable && text.length > 0 && text.length < 50 && (
                                            text.includes(buttonText) ||
                                            (buttonText === '条回复' && /\\d+条回复/.test(text))
                                        );
                                    });
                                    
                                    elements.forEach(el => {
                                        try {
                                            el.click();
                                            totalClicked++;
                                            console.log('点击:', el.textContent.trim().substring(0, 20));
                                        } catch (e) {
                                            // 忽略点击错误
                                        }
                                    });
                                }
                                
                                console.log(\`总共点击了 \${totalClicked} 个按钮\`);

                                // 第二步：滚动确保内容加载
                                console.log('📜 第二步：滚动确保内容加载...');
                                window.scrollTo(0, document.body.scrollHeight);

                                // 第三步：提取所有评论
                                console.log('🔍 第三步：提取所有评论...');
                                
                                // 使用多种策略查找评论元素
                                const commentElements = new Set();
                                
                                // 策略1: 基于头像查找
                                const avatars = document.querySelectorAll('img[src*="avatar"]');
                                console.log(\`策略1: 找到 \${avatars.length} 个头像\`);
                                avatars.forEach(img => {
                                    const commentDiv = img.closest('div');
                                    if (commentDiv && commentDiv.textContent.trim().length > 20) {
                                        commentElements.add(commentDiv);
                                    }
                                });
                                
                                // 策略2: 基于时间信息查找
                                const timeElements = Array.from(document.querySelectorAll('*')).filter(el => {
                                    const text = el.textContent.trim();
                                    return /\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}-\\d{2}/.test(text);
                                });
                                console.log(\`策略2: 找到 \${timeElements.length} 个时间元素\`);
                                timeElements.forEach(timeEl => {
                                    const commentDiv = timeEl.closest('div');
                                    if (commentDiv && commentDiv.textContent.trim().length > 20) {
                                        commentElements.add(commentDiv);
                                    }
                                });
                                
                                // 策略3: 基于评论div查找
                                const commentDivs = document.querySelectorAll('[class*="comment"], [class*="reply"]');
                                console.log(\`策略3: 找到 \${commentDivs.length} 个评论div\`);
                                commentDivs.forEach(div => {
                                    if (div.textContent.trim().length > 20) {
                                        commentElements.add(div);
                                    }
                                });

                                console.log(\`🔍 总共找到 \${commentElements.size} 个独特的评论元素\`);

                                // 提取评论详细信息
                                Array.from(commentElements).forEach((element, index) => {
                                    try {
                                        const commentData = {
                                            id: index + 1,
                                            username: '',
                                            avatar: '',
                                            content: '',
                                            publishTime: '',
                                            likes: 0,
                                            isReply: false
                                        };

                                        // 提取用户名
                                        const textNodes = Array.from(element.querySelectorAll('*')).filter(el => 
                                            el.children.length === 0 && 
                                            el.textContent.trim().length > 1 && 
                                            el.textContent.trim().length < 30 &&
                                            !el.textContent.includes('天前') &&
                                            !el.textContent.includes('小时前') &&
                                            !el.textContent.includes('分钟前')
                                        );
                                        if (textNodes.length > 0) {
                                            commentData.username = textNodes[0].textContent.trim();
                                        }

                                        // 提取头像
                                        const avatarImg = element.querySelector('img[src*="avatar"]');
                                        if (avatarImg) {
                                            commentData.avatar = avatarImg.src;
                                        }

                                        // 提取评论内容
                                        const allTexts = Array.from(element.querySelectorAll('*')).map(el => 
                                            el.textContent.trim()
                                        ).filter(text => 
                                            text.length > 10 && text.length < 1000 &&
                                            !text.includes('天前') && !text.includes('小时前') &&
                                            !text.includes('点赞') && !text.includes('回复') &&
                                            !text.includes('更多') && !text.includes('展开')
                                        );
                                        
                                        if (allTexts.length > 0) {
                                            commentData.content = allTexts.reduce((a, b) => a.length > b.length ? a : b);
                                        }

                                        // 提取发布时间
                                        const timeMatch = element.textContent.match(/\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}-\\d{2}/);
                                        if (timeMatch) {
                                            commentData.publishTime = timeMatch[0];
                                        }

                                        // 提取点赞数
                                        const likeMatch = element.textContent.match(/\\d+(?=\\s*赞|\\s*❤️)/);
                                        if (likeMatch) {
                                            commentData.likes = parseInt(likeMatch[0]) || 0;
                                        }

                                        // 判断是否为回复
                                        if (element.textContent.includes('回复') ||
                                            (element.className && element.className.includes && element.className.includes('reply'))) {
                                            commentData.isReply = true;
                                        }

                                        // 只添加有内容的评论
                                        if (commentData.content && commentData.content.length > 5) {
                                            commentsData.comments.push(commentData);
                                        }

                                    } catch (e) {
                                        // 忽略提取错误
                                    }
                                });

                                // 去重
                                const uniqueComments = [];
                                const seenContents = new Set();
                                
                                commentsData.comments.forEach(comment => {
                                    const key = comment.content.substring(0, 50);
                                    if (!seenContents.has(key)) {
                                        seenContents.add(key);
                                        uniqueComments.push(comment);
                                    }
                                });
                                
                                commentsData.comments = uniqueComments;

                                // 计算统计信息
                                commentsData.summary.totalComments = commentsData.comments.filter(c => !c.isReply).length;
                                commentsData.summary.totalReplies = commentsData.comments.filter(c => c.isReply).length;
                                commentsData.summary.totalLikes = commentsData.comments.reduce((sum, c) => sum + c.likes, 0);

                                console.log(\`🎉 最终采集完成! 采集到 \${commentsData.comments.length} 条评论\`);
                                console.log(\`📊 主评论: \${commentsData.summary.totalComments}, 回复: \${commentsData.summary.totalReplies}\`);

                                return { success: true, data: commentsData };
                                
                            } catch (error) {
                                console.error('最终采集过程出错:', error);
                                return { 
                                    success: false, 
                                    error: error.message || '未知错误'
                                };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: collectionScript,
                            returnByValue: true
                        }
                    }));
                }, 2000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            console.log('✅ 最终采集成功');
                            console.log(`📊 采集结果: ${result.data.comments.length} 条评论`);
                            ws.close();
                            resolve(result.data);
                        } else {
                            console.log('❌ 最终采集失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理采集结果失败:', error.message);
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ 最终采集WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('最终采集超时'));
            }, 60000); // 1分钟超时
        });
    }

    // 💾 保存采集数据
    async saveCommentsData() {
        try {
            console.log('💾 开始最终采集并保存评论数据...\n');

            const commentsData = await this.collectAllComments();

            // 生成文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const jsonFileName = `final-comments-${timestamp}.json`;
            const txtFileName = `final-comments-${timestamp}.txt`;

            // 保存JSON数据
            fs.writeFileSync(jsonFileName, JSON.stringify(commentsData, null, 2), 'utf8');
            console.log(`✅ JSON数据已保存: ${jsonFileName}`);

            // 生成可读报告
            const report = this.generateReport(commentsData);
            fs.writeFileSync(txtFileName, report, 'utf8');
            console.log(`✅ 可读报告已保存: ${txtFileName}`);

            return { jsonFile: jsonFileName, txtFile: txtFileName, data: commentsData };

        } catch (error) {
            console.error('❌ 保存最终采集数据失败:', error.message);
            throw error;
        }
    }

    // 📝 生成报告
    generateReport(data) {
        const lines = [];

        lines.push('🎯 小红书笔记最终评论采集报告');
        lines.push('=' * 60);
        lines.push(`📅 采集时间: ${data.timestamp}`);
        lines.push(`📝 笔记标题: ${data.noteTitle}`);
        lines.push(`🔗 笔记链接: ${data.noteUrl}`);
        lines.push(`🔧 采集策略: ${data.summary.strategy}`);
        lines.push('');

        lines.push('📊 采集统计:');
        lines.push(`💬 总评论数: ${data.comments.length}`);
        lines.push(`📝 主评论数: ${data.summary.totalComments}`);
        lines.push(`↩️ 回复数: ${data.summary.totalReplies}`);
        lines.push(`👍 总点赞数: ${data.summary.totalLikes}`);
        lines.push(`📈 采集进度: ${Math.round(data.comments.length/1472*100)}% (目标1472条)`);
        lines.push('');

        lines.push('💬 评论详情:');
        lines.push('-' * 60);

        data.comments.forEach((comment, index) => {
            lines.push(`\n💬 评论 ${index + 1}:`);
            lines.push(`👤 用户: ${comment.username || '匿名'}`);
            lines.push(`📄 内容: ${comment.content || '无内容'}`);
            lines.push(`👍 点赞: ${comment.likes}`);
            lines.push(`📅 时间: ${comment.publishTime || '未知'}`);
            lines.push(`🔗 头像: ${comment.avatar || '无'}`);
            lines.push(`↩️ 回复: ${comment.isReply ? '是' : '否'}`);
        });

        lines.push('\n' + '=' * 60);
        lines.push('📅 报告生成时间: ' + new Date().toLocaleString('zh-CN'));

        return lines.join('\n');
    }
}

// 运行最终采集
if (require.main === module) {
    const collector = new FinalCommentsCollector();
    
    const args = process.argv.slice(2);
    if (args.includes('--save')) {
        collector.saveCommentsData().then(result => {
            console.log('\n🎉 最终采集数据保存完成!');
            console.log(`📁 JSON文件: ${result.jsonFile}`);
            console.log(`📄 文本报告: ${result.txtFile}`);
            console.log(`📊 采集到 ${result.data.comments.length} 条评论`);
            console.log(`📈 采集进度: ${Math.round(result.data.comments.length/1472*100)}%`);
        }).catch(console.error);
    } else {
        collector.collectAllComments().then(data => {
            console.log('\n🎉 最终采集完成!');
            console.log(`📊 采集到 ${data.comments.length} 条评论`);
            console.log(`📈 采集进度: ${Math.round(data.comments.length/1472*100)}%`);
        }).catch(console.error);
    }
}

module.exports = FinalCommentsCollector;
