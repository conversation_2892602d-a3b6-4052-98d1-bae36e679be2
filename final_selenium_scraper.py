#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 最终版Selenium小红书评论爬虫
使用获取到的调试端口60811连接比特浏览器19号窗口
"""

import time
import json
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class XiaohongshuSeleniumScraper:
    def __init__(self, debug_port=60811):
        self.debug_port = debug_port
        self.driver = None
        self.comments = []
        
    def connect_to_browser(self):
        """连接到比特浏览器"""
        print(f"🔗 连接到比特浏览器调试端口 {self.debug_port}...")
        
        try:
            # Chrome选项配置
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{self.debug_port}")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 尝试使用本地ChromeDriver（如果存在）
            if os.path.exists('./chromedriver.exe'):
                service = Service('./chromedriver.exe')
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                # 使用系统ChromeDriver
                self.driver = webdriver.Chrome(options=chrome_options)
            
            # 获取当前页面信息
            current_url = self.driver.current_url
            title = self.driver.title
            
            print(f"✅ 连接成功!")
            print(f"📄 当前页面: {current_url}")
            print(f"📝 页面标题: {title}")
            
            # 验证是否是小红书页面
            if 'xiaohongshu.com' in current_url:
                print("🎉 检测到小红书页面!")
                return True
            else:
                print("⚠️ 当前不是小红书页面，请手动导航到目标页面")
                return False
                
        except Exception as e:
            error_msg = str(e)
            print(f"❌ 连接失败: {error_msg}")
            
            if "version" in error_msg.lower():
                print("💡 ChromeDriver版本不匹配，请:")
                print("   1. 下载匹配Chrome 134的ChromeDriver")
                print("   2. 或升级比特浏览器到最新版本")
                print("   3. 或使用浏览器控制台方案")
            elif "cannot connect" in error_msg.lower():
                print("💡 无法连接到调试端口，请:")
                print("   1. 确保比特浏览器19号窗口正在运行")
                print("   2. 确保调试端口已启用")
                print("   3. 重启比特浏览器")
            
            return False
    
    def wait_for_comments_load(self):
        """等待评论区加载"""
        print("⏳ 等待评论区加载...")
        
        try:
            # 等待评论区出现
            wait = WebDriverWait(self.driver, 10)
            
            # 可能的评论区选择器
            comment_selectors = [
                '[class*="comment"]',
                '[class*="Comment"]', 
                '[data-testid*="comment"]',
                '.note-item',
                '.feed-item',
                '[class*="interaction"]'
            ]
            
            for selector in comment_selectors:
                try:
                    wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    print(f"✅ 找到评论区: {selector}")
                    return True
                except TimeoutException:
                    continue
            
            print("⚠️ 未找到明确的评论区，尝试通用方法")
            return True
            
        except Exception as e:
            print(f"⚠️ 等待评论区失败: {e}")
            return True  # 继续尝试
    
    def scroll_and_load_comments(self):
        """滚动页面加载更多评论"""
        print("📜 开始滚动加载评论...")
        
        last_height = self.driver.execute_script("return document.body.scrollHeight")
        scroll_count = 0
        max_scrolls = 50  # 最大滚动次数
        
        while scroll_count < max_scrolls:
            # 滚动到页面底部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            
            # 等待新内容加载
            time.sleep(2)
            
            # 检查是否有新内容
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            
            if new_height == last_height:
                print(f"📄 滚动完成，共滚动 {scroll_count} 次")
                break
            
            last_height = new_height
            scroll_count += 1
            
            if scroll_count % 5 == 0:
                print(f"📊 已滚动 {scroll_count} 次...")
        
        # 滚动回顶部
        self.driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(1)
    
    def extract_comments(self):
        """提取评论数据"""
        print("📝 开始提取评论数据...")
        
        try:
            # 获取页面所有文本
            page_source = self.driver.page_source
            body_text = self.driver.find_element(By.TAG_NAME, "body").text
            
            # 使用多种方法提取评论
            comments = []
            
            # 方法1: 通过常见的评论选择器
            comment_selectors = [
                '[class*="comment"]',
                '[class*="Comment"]',
                '[class*="reply"]',
                '[class*="Reply"]',
                '.note-item',
                '.feed-item'
            ]
            
            for selector in comment_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if text and len(text) > 10:  # 过滤太短的文本
                            comments.append({
                                'content': text,
                                'method': f'selector_{selector}',
                                'timestamp': datetime.now().isoformat()
                            })
                except:
                    continue
            
            # 方法2: 通过文本模式匹配
            lines = body_text.split('\n')
            for i, line in enumerate(lines):
                line = line.strip()
                
                # 检查是否像评论（包含时间标识）
                if any(time_indicator in line for time_indicator in ['分钟前', '小时前', '天前', '月前', '年前']):
                    # 尝试获取前面的文本作为评论内容
                    if i > 0:
                        prev_line = lines[i-1].strip()
                        if len(prev_line) > 10 and not any(skip in prev_line for skip in ['点赞', '关注', '分享']):
                            comments.append({
                                'content': prev_line,
                                'time': line,
                                'method': 'text_pattern',
                                'timestamp': datetime.now().isoformat()
                            })
                
                # 检查是否包含"回复"
                if '回复' in line and len(line) > 15:
                    comments.append({
                        'content': line,
                        'method': 'reply_pattern',
                        'timestamp': datetime.now().isoformat()
                    })
            
            # 去重
            unique_comments = []
            seen_contents = set()
            
            for comment in comments:
                content = comment['content']
                if content not in seen_contents and len(content) > 5:
                    seen_contents.add(content)
                    unique_comments.append(comment)
            
            self.comments = unique_comments
            print(f"✅ 提取完成，共获得 {len(self.comments)} 条评论")
            
            return len(self.comments) > 0
            
        except Exception as e:
            print(f"❌ 提取评论失败: {e}")
            return False
    
    def save_results(self):
        """保存结果"""
        if not self.comments:
            print("⚠️ 没有评论数据可保存")
            return
        
        # 准备保存数据
        result = {
            'scrape_info': {
                'url': self.driver.current_url,
                'title': self.driver.title,
                'debug_port': self.debug_port,
                'scrape_time': datetime.now().isoformat(),
                'total_comments': len(self.comments)
            },
            'comments': self.comments
        }
        
        # 保存为JSON文件
        filename = f"xiaohongshu_comments_selenium_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"💾 结果已保存到: {filename}")
        print(f"📊 总计 {len(self.comments)} 条评论")
    
    def run(self):
        """运行爬虫"""
        print("🎯 小红书评论爬虫 (Selenium版)")
        print("=" * 50)
        
        try:
            # 1. 连接浏览器
            if not self.connect_to_browser():
                return False
            
            # 2. 等待页面加载
            self.wait_for_comments_load()
            
            # 3. 滚动加载评论
            self.scroll_and_load_comments()
            
            # 4. 提取评论
            if not self.extract_comments():
                print("❌ 未能提取到评论数据")
                return False
            
            # 5. 保存结果
            self.save_results()
            
            print("\n🎉 爬取完成!")
            return True
            
        except Exception as e:
            print(f"❌ 爬虫运行失败: {e}")
            return False
        
        finally:
            if self.driver:
                print("🔒 关闭浏览器连接...")
                self.driver.quit()

def main():
    print("🎯 小红书评论爬虫启动器")
    print("=" * 50)
    
    # 使用我们获取到的调试端口
    debug_port = 60811
    
    print(f"🔌 使用调试端口: {debug_port}")
    print("📝 请确保:")
    print("   1. 比特浏览器19号窗口正在运行")
    print("   2. 已打开目标小红书页面")
    print("   3. ChromeDriver版本匹配")
    
    input("\n按回车键开始爬取...")
    
    scraper = XiaohongshuSeleniumScraper(debug_port)
    success = scraper.run()
    
    if success:
        print("\n🎉 爬取成功完成!")
    else:
        print("\n❌ 爬取失败")
        print("💡 建议:")
        print("   1. 检查ChromeDriver版本是否匹配")
        print("   2. 确保比特浏览器正常运行")
        print("   3. 尝试使用浏览器控制台方案")

if __name__ == "__main__":
    import os
    main()
