#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 最终解决方案 - 直接连接比特浏览器爬取评论
"""

import time
import json
import re
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def connect_to_bitbrowser():
    """连接到比特浏览器"""
    print("🔗 连接到比特浏览器...")
    
    # 直接尝试最常见的调试端口
    common_ports = [
        63497,  # 从你的截图看到的WebSocket端口
        9222, 9223, 9224, 9225, 9226, 9227, 9228, 9229,
        55276, 55277, 55278, 55279, 55280,
        54345, 54346, 54347, 54348, 54349,
        56906, 56907, 56908, 56909, 56910
    ]
    
    for port in common_ports:
        try:
            print(f"   🔍 尝试端口 {port}...")
            
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            driver = webdriver.Chrome(options=chrome_options)
            
            print(f"   ✅ 端口 {port} 连接成功!")
            print(f"   📄 当前页面: {driver.current_url}")
            
            return driver, port
            
        except Exception as e:
            print(f"   ❌ 端口 {port} 失败")
            continue
    
    print("❌ 所有端口都失败了")
    print("💡 请确保:")
    print("1. 比特浏览器正在运行")
    print("2. 19号窗口已打开")
    print("3. 窗口启用了调试模式")
    return None, None

def scrape_comments(driver):
    """爬取评论"""
    print("🚀 开始爬取评论...")
    
    # 滚动到评论区域
    try:
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.6);")
        time.sleep(2)
    except:
        pass
    
    current_count = 0
    for i in range(500):  # 最多滚动500次
        print(f"📜 滚动 {i + 1}/500")
        
        # 统计评论
        try:
            page_text = driver.execute_script("return document.body.textContent;")
            time_count = len(re.findall(r'\d{2}-\d{2}', page_text))
            keyword_count = len(re.findall(r'求带|宝子|学姐|兼职', page_text))
            current_count = max(time_count, keyword_count)
            print(f"   💬 当前评论数: {current_count}")
        except:
            current_count = 0
        
        # 检查是否达到目标
        if current_count >= 1400:  # 接近1472
            print(f"🎉 接近目标！{current_count}/1472")
            break
        
        # 点击加载更多
        try:
            load_more = driver.find_elements(By.XPATH, "//*[contains(text(), '加载更多') or contains(text(), '展开更多')]")
            for btn in load_more:
                if btn.is_displayed():
                    btn.click()
                    time.sleep(0.5)
                    break
        except:
            pass
        
        # 滚动
        try:
            driver.execute_script("window.scrollBy(0, 500);")
        except:
            pass
        
        time.sleep(0.8)
    
    return current_count

def extract_comments(driver):
    """提取评论"""
    print("🧠 提取评论...")
    
    try:
        page_text = driver.execute_script("return document.body.textContent;")
        current_url = driver.current_url
        title = driver.title
        
        # 提取笔记ID
        note_id = ''
        url_match = re.search(r'explore/([a-f0-9]+)', current_url)
        if url_match:
            note_id = url_match.group(1)
        
        # 解析评论
        comments = []
        
        # 时间模式解析
        time_pattern = r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)'
        time_matches = list(re.finditer(time_pattern, page_text))
        
        for i, match in enumerate(time_matches):
            time_str = match.group(1)
            time_index = match.start()
            
            start_index = max(0, time_index - 200)
            end_index = time_matches[i + 1].start() if i + 1 < len(time_matches) else time_index + 800
            end_index = min(len(page_text), end_index)
            
            comment_text = page_text[start_index:end_index].strip()
            
            if 20 < len(comment_text) < 1500:
                # 提取用户名
                username = ''
                user_match = re.search(r'^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}', comment_text)
                if user_match:
                    username = user_match.group(1)
                
                # 清理内容
                content = comment_text
                content = re.sub(r'\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前', '', content)
                content = re.sub(r'作者|置顶|回复|赞', '', content)
                if username:
                    content = content.replace(username, '')
                content = re.sub(r'\s+', ' ', content).strip()
                
                if len(content) >= 5:
                    comment = {
                        'id': len(comments) + 1,
                        'username': username,
                        'content': content,
                        'time': time_str,
                        'likes': 0,
                        'replyCount': 0
                    }
                    
                    # 提取点赞数
                    like_match = re.search(r'(\d+)\s*赞', comment_text)
                    if like_match:
                        comment['likes'] = int(like_match.group(1))
                    
                    comments.append(comment)
        
        # 去重
        unique_comments = []
        seen = set()
        for comment in comments:
            key = comment['content'][:30]
            if key not in seen:
                seen.add(key)
                unique_comments.append(comment)
        
        # 重新分配ID
        for i, comment in enumerate(unique_comments):
            comment['id'] = i + 1
        
        result = {
            'noteInfo': {
                'id': note_id,
                'title': title.replace(' - 小红书', ''),
                'totalCommentCount': len(unique_comments)
            },
            'comments': unique_comments,
            'extractTime': datetime.now().isoformat()
        }
        
        return result
        
    except Exception as e:
        print(f"❌ 提取失败: {str(e)}")
        return None

def save_data(data):
    """保存数据"""
    if not data:
        return
    
    output_dir = './scraped_data'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"bitbrowser_comments_{data['noteInfo']['id']}_{timestamp}.json"
    filepath = os.path.join(output_dir, filename)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"💾 数据已保存到: {filepath}")
    return filepath

def main():
    """主函数"""
    print("🎯 比特浏览器评论爬取器 - 最终解决方案")
    print("=" * 50)
    
    # 1. 连接比特浏览器
    driver, port = connect_to_bitbrowser()
    if not driver:
        print("❌ 无法连接到比特浏览器")
        return
    
    try:
        print(f"✅ 已连接到端口 {port}")
        
        # 检查是否在小红书页面
        current_url = driver.current_url
        if 'xiaohongshu.com' not in current_url:
            print("💡 请手动导航到小红书评论页面，然后按回车...")
            input()
        
        # 2. 爬取评论
        comment_count = scrape_comments(driver)
        print(f"📊 滚动完成，检测到约 {comment_count} 条评论")
        
        # 3. 提取评论
        result = extract_comments(driver)
        if not result:
            print("❌ 提取失败")
            return
        
        # 4. 保存数据
        filepath = save_data(result)
        
        # 5. 输出结果
        print("\n🎉 爬取完成！")
        print(f"📝 笔记ID: {result['noteInfo']['id']}")
        print(f"📝 笔记标题: {result['noteInfo']['title']}")
        print(f"💬 提取评论数: {len(result['comments'])}")
        print(f"📁 保存文件: {filepath}")
        
        if result['comments']:
            print("\n👥 评论预览:")
            for i, comment in enumerate(result['comments'][:10]):
                print(f"   {i + 1}. {comment['username'] or '匿名'} ({comment['time']}): {comment['content'][:60]}...")
            
            # 兼职相关统计
            job_keywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱']
            job_comments = [c for c in result['comments'] 
                          if any(keyword in c['content'] for keyword in job_keywords)]
            
            if job_comments:
                job_percentage = round((len(job_comments) / len(result['comments'])) * 100)
                print(f"\n💼 兼职相关评论: {len(job_comments)}/{len(result['comments'])} ({job_percentage}%)")
        
        print(f"\n✅ 任务完成！")
        
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
    finally:
        print("🔄 保持浏览器打开...")
        # 不关闭浏览器

if __name__ == "__main__":
    main()
