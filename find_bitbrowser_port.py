#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 自动查找比特浏览器调试端口
"""

import requests
import json
import subprocess
import re
import psutil

def find_chrome_processes():
    """查找Chrome进程及其端口"""
    print("🔍 查找Chrome进程...")
    
    chrome_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                cmdline = proc.info['cmdline'] or []
                cmdline_str = ' '.join(cmdline)
                
                # 查找调试端口参数
                port_match = re.search(r'--remote-debugging-port=(\d+)', cmdline_str)
                if port_match:
                    port = int(port_match.group(1))
                    chrome_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'port': port,
                        'cmdline': cmdline_str[:100] + '...' if len(cmdline_str) > 100 else cmdline_str
                    })
                    print(f"   ✅ 找到Chrome进程: PID={proc.info['pid']}, 端口={port}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return chrome_processes

def test_debug_port(port):
    """测试调试端口是否可用"""
    try:
        response = requests.get(f"http://localhost:{port}/json/version", timeout=2)
        if response.status_code == 200:
            data = response.json()
            return True, data
    except:
        pass
    return False, None

def find_active_debug_ports():
    """查找活跃的调试端口"""
    print("🔍 扫描活跃的调试端口...")
    
    # 扩展端口范围
    ports = list(range(9222, 9230)) + list(range(55270, 55290)) + list(range(54340, 54360)) + [56906, 56907, 56908, 56909]
    
    active_ports = []
    
    for port in ports:
        print(f"   🔍 测试端口 {port}...", end='')
        is_active, data = test_debug_port(port)
        if is_active:
            print(f" ✅")
            active_ports.append({
                'port': port,
                'browser': data.get('Browser', '未知'),
                'version': data.get('Protocol-Version', '未知'),
                'data': data
            })
        else:
            print(f" ❌")
    
    return active_ports

def get_browser_tabs(port):
    """获取浏览器标签页"""
    try:
        response = requests.get(f"http://localhost:{port}/json", timeout=2)
        if response.status_code == 200:
            return response.json()
    except:
        pass
    return []

def find_xiaohongshu_tabs(port):
    """查找小红书标签页"""
    tabs = get_browser_tabs(port)
    xiaohongshu_tabs = []
    
    for tab in tabs:
        url = tab.get('url', '')
        title = tab.get('title', '')
        
        if 'xiaohongshu.com' in url or 'xhs' in url.lower():
            xiaohongshu_tabs.append({
                'id': tab.get('id'),
                'title': title,
                'url': url,
                'type': tab.get('type', ''),
                'webSocketDebuggerUrl': tab.get('webSocketDebuggerUrl', '')
            })
    
    return xiaohongshu_tabs

def main():
    """主函数"""
    print("🎯 比特浏览器调试端口自动检测工具")
    print("=" * 50)
    
    # 1. 查找Chrome进程
    chrome_processes = find_chrome_processes()
    
    if chrome_processes:
        print(f"\n📊 找到 {len(chrome_processes)} 个Chrome进程:")
        for proc in chrome_processes:
            print(f"   PID: {proc['pid']}, 端口: {proc['port']}, 名称: {proc['name']}")
    
    # 2. 扫描活跃端口
    active_ports = find_active_debug_ports()
    
    if active_ports:
        print(f"\n📊 找到 {len(active_ports)} 个活跃的调试端口:")
        
        best_port = None
        
        for port_info in active_ports:
            port = port_info['port']
            print(f"\n🔗 端口 {port}:")
            print(f"   浏览器: {port_info['browser']}")
            print(f"   版本: {port_info['version']}")
            
            # 查找小红书标签页
            xiaohongshu_tabs = find_xiaohongshu_tabs(port)
            
            if xiaohongshu_tabs:
                print(f"   🎯 找到 {len(xiaohongshu_tabs)} 个小红书标签页:")
                for tab in xiaohongshu_tabs:
                    print(f"      - {tab['title'][:50]}...")
                    print(f"        URL: {tab['url'][:80]}...")
                
                if not best_port:
                    best_port = port
            else:
                # 获取所有标签页
                all_tabs = get_browser_tabs(port)
                print(f"   📄 总共 {len(all_tabs)} 个标签页")
                
                if all_tabs:
                    print("   标签页列表:")
                    for i, tab in enumerate(all_tabs[:5]):  # 只显示前5个
                        title = tab.get('title', '无标题')[:30]
                        url = tab.get('url', '')[:50]
                        print(f"      {i+1}. {title} - {url}...")
        
        # 推荐最佳端口
        if best_port:
            print(f"\n🎉 推荐使用端口: {best_port} (有小红书页面)")
        elif active_ports:
            recommended_port = active_ports[0]['port']
            print(f"\n💡 推荐使用端口: {recommended_port} (第一个活跃端口)")
        
        # 输出可直接使用的端口号
        if active_ports:
            print(f"\n📋 可用端口列表: {', '.join([str(p['port']) for p in active_ports])}")
            
            # 自动选择最佳端口
            final_port = best_port or active_ports[0]['port']
            print(f"\n🚀 建议在脚本中使用端口: {final_port}")
            
            return final_port
    
    else:
        print("\n❌ 未找到任何活跃的调试端口")
        print("\n💡 解决方案:")
        print("1. 确保比特浏览器正在运行")
        print("2. 在比特浏览器中启用调试模式")
        print("3. 或者手动启动Chrome时添加参数: --remote-debugging-port=9222")
    
    return None

if __name__ == "__main__":
    port = main()
    if port:
        print(f"\n✅ 检测完成，推荐端口: {port}")
    else:
        print("\n❌ 未找到可用端口")
