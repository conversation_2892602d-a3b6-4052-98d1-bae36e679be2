#!/usr/bin/env node

/**
 * 🔍 查找可用的调试端口和比特浏览器API
 */

const axios = require('axios');

async function findDebugPort() {
    console.log('🔍 扫描可用的调试端口和比特浏览器API...\n');

    // 1. 扫描比特浏览器API端口
    console.log('1️⃣ 扫描比特浏览器API端口...');
    const bitbrowserPorts = [
        54345, 54346, 54347, 54348, 54349, 54350,
        56906, 56907, 56908, 56909, 56910,
        55000, 55001, 55002, 55003, 55004
    ];

    const apiToken = "ca28ee5ca6de4d209182a83aa16a2044";
    let foundBitbrowserAPI = false;

    for (const port of bitbrowserPorts) {
        try {
            console.log(`   🔍 测试比特浏览器API端口 ${port}...`);
            const response = await axios.get(`http://127.0.0.1:${port}/browser/list`, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`
                },
                timeout: 3000
            });

            if (response.data) {
                console.log(`✅ 找到比特浏览器API在端口 ${port}`);
                console.log(`📊 API响应:`, JSON.stringify(response.data, null, 2));
                foundBitbrowserAPI = true;

                // 尝试启动19号浏览器
                await testBrowser19(port, apiToken);
                break;
            }
        } catch (error) {
            console.log(`❌ 端口 ${port} 不可用: ${error.message}`);
        }
    }

    if (!foundBitbrowserAPI) {
        console.log('❌ 未找到比特浏览器API');
    }

    // 2. 扫描Chrome调试端口
    console.log('\n2️⃣ 扫描Chrome调试端口...');
    const commonPorts = [
        9222, 9223, 9224, 9225, 9226,
        63524, 63525, 63526, 63527, 63528,
        51859, 51860, 51861, 51862, 51863,
        58222, 58223, 58224, 58225, 58226
    ];

    const availablePorts = [];

    for (const port of commonPorts) {
        try {
            console.log(`🔍 测试端口 ${port}...`);
            const response = await axios.get(`http://127.0.0.1:${port}/json`, { 
                timeout: 2000 
            });
            
            if (response.data && Array.isArray(response.data)) {
                console.log(`✅ 端口 ${port} 可用，找到 ${response.data.length} 个标签页`);
                
                // 查找小红书标签页
                const xiaohongshuTabs = response.data.filter(tab =>
                    tab.url && (
                        tab.url.includes('xiaohongshu.com') ||
                        tab.title.includes('小红书')
                    )
                );

                availablePorts.push({
                    port: port,
                    totalTabs: response.data.length,
                    xiaohongshuTabs: xiaohongshuTabs.length,
                    tabs: xiaohongshuTabs
                });

                if (xiaohongshuTabs.length > 0) {
                    console.log(`🎯 找到 ${xiaohongshuTabs.length} 个小红书标签页:`);
                    xiaohongshuTabs.forEach((tab, index) => {
                        console.log(`   ${index + 1}. ${tab.title}`);
                        console.log(`      URL: ${tab.url.substring(0, 80)}...`);
                    });
                }
            }
        } catch (error) {
            console.log(`❌ 端口 ${port} 不可用`);
        }
    }

    console.log('\n📊 扫描结果总结:');
    console.log('=' * 50);

    if (availablePorts.length === 0) {
        console.log('❌ 未找到任何可用的调试端口');
        console.log('\n💡 可能的解决方案:');
        console.log('1. 启动Chrome/Edge浏览器并开启调试模式');
        console.log('2. 使用命令启动: chrome.exe --remote-debugging-port=9222');
        console.log('3. 检查比特浏览器是否正在运行');
        console.log('4. 确保没有防火墙阻止连接');
    } else {
        console.log(`✅ 找到 ${availablePorts.length} 个可用端口:`);
        
        availablePorts.forEach(portInfo => {
            console.log(`\n🔌 端口 ${portInfo.port}:`);
            console.log(`   📋 总标签页: ${portInfo.totalTabs}`);
            console.log(`   🎯 小红书标签页: ${portInfo.xiaohongshuTabs}`);
            
            if (portInfo.xiaohongshuTabs > 0) {
                console.log('   ⭐ 推荐使用此端口进行采集');
            }
        });

        // 推荐最佳端口
        const bestPort = availablePorts.find(p => p.xiaohongshuTabs > 0) || availablePorts[0];
        console.log(`\n🎯 推荐端口: ${bestPort.port}`);
        
        if (bestPort.xiaohongshuTabs > 0) {
            console.log('✅ 可以立即开始评论采集!');
            return bestPort.port;
        } else {
            console.log('⚠️ 请先在浏览器中打开小红书页面');
        }
    }

    return null;
}

// 🧪 测试端口连接
async function testPortConnection(port) {
    console.log(`\n🧪 测试端口 ${port} 的详细连接...\n`);

    try {
        const response = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });

        console.log(`✅ 端口 ${port} 连接成功`);
        console.log(`📋 找到 ${response.data.length} 个标签页\n`);

        response.data.forEach((tab, index) => {
            console.log(`${index + 1}. ${tab.title || '无标题'}`);
            console.log(`   URL: ${tab.url || '无URL'}`);
            console.log(`   类型: ${tab.type}`);
            if (tab.url && tab.url.includes('xiaohongshu.com')) {
                console.log('   🎯 这是小红书页面!');
            }
            console.log('');
        });

        return true;
    } catch (error) {
        console.log(`❌ 端口 ${port} 连接失败: ${error.message}`);
        return false;
    }
}

if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length > 0 && args[0].startsWith('--test=')) {
        const port = parseInt(args[0].split('=')[1]);
        testPortConnection(port);
    } else {
        findDebugPort().then(port => {
            if (port) {
                console.log(`\n🚀 可以使用以下命令开始采集:`);
                console.log(`node comments-collector.js --port=${port} --save`);
            }
        }).catch(error => {
            console.error('❌ 扫描失败:', error.message);
        });
    }
}

async function testBrowser19(apiPort, apiToken) {
    console.log('\n🚀 测试启动19号浏览器...');
    const browser19Id = "0d094596cb404282be3f814b98139c74";

    try {
        const response = await axios.post(`http://127.0.0.1:${apiPort}/browser/open`, {
            id: browser19Id
        }, {
            headers: {
                'Authorization': `Bearer ${apiToken}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });

        if (response.data && response.data.success) {
            console.log('✅ 19号浏览器启动成功');
            console.log('📊 响应数据:', JSON.stringify(response.data, null, 2));
            return response.data;
        } else {
            console.log('❌ 19号浏览器启动失败:', response.data);
            return null;
        }
    } catch (error) {
        console.log('❌ 启动19号浏览器出错:', error.message);
        return null;
    }
}

module.exports = { findDebugPort, testPortConnection, testBrowser19 };
