#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 ChromeDriver版本修复工具
自动下载匹配比特浏览器Chrome版本的ChromeDriver
"""

import requests
import zipfile
import os
import shutil
import json
from pathlib import Path

def get_chrome_version_from_api():
    """从比特浏览器API获取Chrome版本"""
    try:
        # 从之前保存的结果中获取Chrome版本
        if os.path.exists('window19_port_no_auth.json'):
            with open('window19_port_no_auth.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                browsers = data.get('all_browsers', [])
                for browser in browsers:
                    if browser.get('seq') == 19:
                        core_version = browser.get('coreVersion', '')
                        if core_version:
                            print(f"✅ 从API获取到Chrome版本: {core_version}")
                            return core_version
        
        print("⚠️ 未能从API获取Chrome版本，使用默认版本134")
        return "134"
    except Exception as e:
        print(f"❌ 获取Chrome版本失败: {e}")
        return "134"

def download_chromedriver(chrome_version):
    """下载匹配的ChromeDriver"""
    print(f"🔍 为Chrome版本 {chrome_version} 下载ChromeDriver...")
    
    # ChromeDriver下载URL模式
    base_url = "https://chromedriver.storage.googleapis.com"
    
    # 获取可用版本列表
    try:
        # 尝试几个可能的版本号
        possible_versions = [
            f"{chrome_version}.0.6998.103",
            f"{chrome_version}.0.6998.102", 
            f"{chrome_version}.0.6998.101",
            f"{chrome_version}.0.6998.100",
            f"{chrome_version}.0.6998.70",
            f"{chrome_version}.0.6998.0",
        ]
        
        for version in possible_versions:
            download_url = f"{base_url}/{version}/chromedriver_win32.zip"
            print(f"   尝试版本: {version}")
            
            response = requests.head(download_url, timeout=10)
            if response.status_code == 200:
                print(f"   ✅ 找到匹配版本: {version}")
                
                # 下载ChromeDriver
                print(f"   📥 下载中...")
                response = requests.get(download_url, timeout=30)
                
                if response.status_code == 200:
                    # 保存zip文件
                    zip_path = f"chromedriver_{version}.zip"
                    with open(zip_path, 'wb') as f:
                        f.write(response.content)
                    
                    print(f"   ✅ 下载完成: {zip_path}")
                    return zip_path, version
                else:
                    print(f"   ❌ 下载失败: {response.status_code}")
            else:
                print(f"   ❌ 版本不存在: {response.status_code}")
        
        print("❌ 未找到匹配的ChromeDriver版本")
        return None, None
        
    except Exception as e:
        print(f"❌ 下载ChromeDriver失败: {e}")
        return None, None

def install_chromedriver(zip_path, version):
    """安装ChromeDriver"""
    print(f"🔧 安装ChromeDriver {version}...")
    
    try:
        # 解压zip文件
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall('.')
        
        # 检查是否解压成功
        if os.path.exists('chromedriver.exe'):
            print("   ✅ ChromeDriver解压成功")
            
            # 备份旧版本（如果存在）
            if os.path.exists('chromedriver_old.exe'):
                os.remove('chromedriver_old.exe')
            
            # 重命名当前版本为备份
            current_driver = shutil.which('chromedriver')
            if current_driver:
                try:
                    shutil.copy(current_driver, 'chromedriver_old.exe')
                    print("   📦 已备份旧版本ChromeDriver")
                except:
                    pass
            
            print(f"   ✅ ChromeDriver {version} 安装完成")
            print(f"   📍 位置: {os.path.abspath('chromedriver.exe')}")
            
            # 清理zip文件
            os.remove(zip_path)
            
            return True
        else:
            print("   ❌ ChromeDriver解压失败")
            return False
            
    except Exception as e:
        print(f"❌ 安装ChromeDriver失败: {e}")
        return False

def test_chromedriver_with_port(port):
    """测试新的ChromeDriver是否能连接调试端口"""
    print(f"🔍 测试ChromeDriver与端口 {port} 的连接...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # 使用本地的ChromeDriver
        service = Service('./chromedriver.exe')
        
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 获取基本信息
        current_url = driver.current_url
        title = driver.title
        
        print(f"   ✅ 连接成功!")
        print(f"   📄 当前页面: {current_url}")
        print(f"   📝 页面标题: {title}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return False

def main():
    print("🎯 ChromeDriver版本修复工具")
    print("=" * 50)
    
    # 1. 获取Chrome版本
    chrome_version = get_chrome_version_from_api()
    
    # 2. 下载匹配的ChromeDriver
    zip_path, version = download_chromedriver(chrome_version)
    
    if not zip_path:
        print("❌ 无法下载匹配的ChromeDriver")
        print("💡 建议使用浏览器控制台方案")
        return
    
    # 3. 安装ChromeDriver
    if install_chromedriver(zip_path, version):
        print("\n🎉 ChromeDriver安装成功!")
        
        # 4. 测试连接
        print("\n🔍 测试新的ChromeDriver...")
        if test_chromedriver_with_port(60811):
            print("\n🎉 ChromeDriver修复成功!")
            print("🚀 现在可以使用以下命令测试:")
            print("   python test_debug_port.py 60811")
        else:
            print("\n⚠️ ChromeDriver安装成功但连接测试失败")
            print("💡 可能需要重启比特浏览器或使用浏览器控制台方案")
    else:
        print("❌ ChromeDriver安装失败")
        print("💡 建议使用浏览器控制台方案")

if __name__ == "__main__":
    main()
