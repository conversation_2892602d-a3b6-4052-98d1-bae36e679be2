#!/usr/bin/env node

/**
 * 🎯 完整的比特浏览器控制演示
 * 展示从启动浏览器到数据采集的完整流程
 */

const BitBrowserController = require('./browser-controller');

async function fullDemo() {
    console.log('🎯 开始完整的比特浏览器控制演示\n');
    
    const controller = new BitBrowserController();
    
    try {
        // 步骤1: 启动浏览器实例
        console.log('1️⃣ 启动浏览器实例...');
        const started = await controller.startBrowser();
        
        if (!started) {
            console.log('❌ 浏览器启动失败，请检查配置');
            return;
        }
        
        // 等待浏览器完全启动
        console.log('⏱️ 等待浏览器完全启动...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 步骤2: 连接到浏览器
        console.log('\n2️⃣ 连接到浏览器...');
        await controller.connect();
        
        // 步骤3: 导航到小红书
        console.log('\n3️⃣ 导航到小红书网站...');
        await controller.navigateTo('https://www.xiaohongshu.com/');
        
        // 等待页面加载
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 步骤4: 获取页面数据
        console.log('\n4️⃣ 获取页面数据...');
        const pageData = await controller.getPageData();
        console.log('📊 页面信息:', JSON.stringify(pageData, null, 2));
        
        // 步骤5: 模拟用户操作
        console.log('\n5️⃣ 模拟用户操作...');
        
        // 滚动页面
        await controller.scrollPage('down', 500);
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 再次滚动
        await controller.scrollPage('down', 500);
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 步骤6: 获取更新后的数据
        console.log('\n6️⃣ 获取滚动后的数据...');
        const updatedData = await controller.getPageData();
        console.log('📊 更新后数据:', JSON.stringify(updatedData, null, 2));
        
        // 步骤7: 执行自定义脚本
        console.log('\n7️⃣ 执行自定义数据提取脚本...');
        const customData = await controller.executeScript(`
            (function() {
                const data = {
                    pageTitle: document.title,
                    currentUrl: window.location.href,
                    timestamp: new Date().toISOString(),
                    elementCount: document.querySelectorAll('*').length,
                    imageCount: document.querySelectorAll('img').length,
                    linkCount: document.querySelectorAll('a').length
                };
                
                // 尝试提取小红书特有的元素
                const xiaohongshuElements = {
                    searchBox: !!document.querySelector('input[placeholder*="搜索"]'),
                    loginButton: !!document.querySelector('[class*="login"], [class*="登录"]'),
                    userAvatar: !!document.querySelector('.avatar, [class*="avatar"]'),
                    noteCards: document.querySelectorAll('[class*="note"], [class*="card"]').length
                };
                
                data.xiaohongshuElements = xiaohongshuElements;
                return data;
            })();
        `);
        
        console.log('📊 自定义提取数据:', JSON.stringify(customData, null, 2));
        
        console.log('\n🎉 完整演示成功完成！');
        console.log('\n📋 演示总结:');
        console.log('✅ 浏览器启动成功');
        console.log('✅ 页面导航成功');
        console.log('✅ 数据提取成功');
        console.log('✅ 用户操作模拟成功');
        console.log('✅ 自定义脚本执行成功');
        
    } catch (error) {
        console.error('\n❌ 演示过程中出错:', error.message);
        console.log('\n💡 可能的原因:');
        console.log('   1. 浏览器实例未正确启动');
        console.log('   2. 调试端口不可用');
        console.log('   3. 网络连接问题');
        console.log('   4. 页面加载超时');
    }
    
    // 清理：停止浏览器（可选）
    console.log('\n🧹 清理资源...');
    // await controller.stopBrowser(); // 取消注释以自动停止浏览器
}

// 运行演示
if (require.main === module) {
    fullDemo().catch(console.error);
}

module.exports = { fullDemo };
