#!/usr/bin/env node

/**
 * 💪 全量评论加载器
 * 目标：提取接近1472条评论的完整数据
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class FullCommentsLoader {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        // 强力加载配置
        this.config = {
            maxScrollAttempts: 50,      // 增加到50次滚动
            scrollDelay: 3000,          // 3秒间隔
            clickDelay: 2000,           // 点击间隔2秒
            waitForContent: 5000,       // 等待内容5秒
            batchSize: 10,              // 每批处理10次滚动
            maxRetries: 3,              // 最大重试次数
            targetComments: 1472        // 目标评论数
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    async connectToBrowser() {
        try {
            console.log('🔗 连接到比特浏览器...');
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${this.debugPort}`,
                defaultViewport: null
            });
            
            const pages = await browser.pages();
            const page = pages.find(p => p.url().includes('xiaohongshu.com/explore/'));
            
            if (!page) {
                throw new Error('未找到小红书笔记页面');
            }
            
            console.log('✅ 找到小红书笔记页面');
            console.log(`📄 当前URL: ${page.url()}`);
            
            return { browser, page };
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    // 强力滚动和点击加载
    async powerfulLoad(page) {
        console.log('💪 开始强力加载所有评论...');
        console.log(`🎯 目标: ${this.config.targetComments}条评论`);
        
        let totalLoaded = 0;
        let stableCount = 0;
        let previousTextLength = 0;
        
        for (let batch = 0; batch < Math.ceil(this.config.maxScrollAttempts / this.config.batchSize); batch++) {
            console.log(`\n📦 批次 ${batch + 1} 开始...`);
            
            // 每批次进行多次滚动
            for (let i = 0; i < this.config.batchSize; i++) {
                const scrollIndex = batch * this.config.batchSize + i + 1;
                console.log(`   📜 滚动 ${scrollIndex}/${this.config.maxScrollAttempts}`);
                
                // 检查页面文本长度
                const currentTextLength = await page.evaluate(() => document.body.textContent.length);
                console.log(`   📝 页面文本长度: ${currentTextLength}`);
                
                if (currentTextLength === previousTextLength) {
                    stableCount++;
                } else {
                    stableCount = 0;
                    previousTextLength = currentTextLength;
                }
                
                // 如果连续5次没有变化，尝试点击加载更多
                if (stableCount >= 5) {
                    console.log('   🔄 尝试点击加载更多按钮...');
                    await this.clickLoadMoreButtons(page);
                    stableCount = 0;
                }
                
                // 智能滚动
                await this.smartScroll(page);
                
                // 随机延迟
                const delay = this.config.scrollDelay + Math.random() * 1000;
                console.log(`   ⏱️ 等待 ${Math.round(delay)}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                
                // 检查是否达到目标
                const currentCommentCount = await this.estimateCommentCount(page);
                if (currentCommentCount >= this.config.targetComments * 0.8) {
                    console.log(`🎉 接近目标评论数，当前估计: ${currentCommentCount}`);
                    break;
                }
            }
            
            // 批次间休息
            console.log(`   💤 批次完成，休息 ${this.config.waitForContent}ms...`);
            await new Promise(resolve => setTimeout(resolve, this.config.waitForContent));
            
            // 检查是否应该停止
            if (stableCount >= 10) {
                console.log('   ⏹️ 内容长时间稳定，停止加载');
                break;
            }
        }
        
        console.log('✅ 强力加载完成');
        
        // 最终等待内容稳定
        console.log('⏳ 等待最终内容稳定...');
        await new Promise(resolve => setTimeout(resolve, this.config.waitForContent));
    }

    // 智能滚动
    async smartScroll(page) {
        await page.evaluate(() => {
            // 随机滚动距离和方式
            const scrollMethods = [
                () => window.scrollBy(0, 400 + Math.random() * 200),
                () => window.scrollTo(0, document.body.scrollHeight * Math.random()),
                () => {
                    const element = document.querySelector('[class*="comment"]');
                    if (element) element.scrollIntoView({ behavior: 'smooth' });
                }
            ];
            
            const method = scrollMethods[Math.floor(Math.random() * scrollMethods.length)];
            method();
        });
    }

    // 点击加载更多按钮
    async clickLoadMoreButtons(page) {
        try {
            const clickResults = await page.evaluate(() => {
                const results = [];
                
                // 查找各种"加载更多"按钮
                const buttonSelectors = [
                    'button:contains("加载更多")',
                    'button:contains("展开")',
                    'button:contains("更多回复")',
                    'button:contains("查看更多")',
                    '[class*="load-more"]',
                    '[class*="expand"]',
                    '[class*="more"]'
                ];
                
                // 查找包含特定文字的元素
                const textPatterns = ['加载更多', '展开', '更多回复', '查看更多', '显示更多'];
                
                textPatterns.forEach(pattern => {
                    const elements = Array.from(document.querySelectorAll('*')).filter(el => 
                        el.textContent.includes(pattern) && 
                        el.offsetHeight > 0 && 
                        el.offsetWidth > 0
                    );
                    
                    elements.forEach(el => {
                        try {
                            el.click();
                            results.push(`点击了: ${pattern}`);
                        } catch (e) {
                            // 忽略点击失败
                        }
                    });
                });
                
                return results;
            });
            
            if (clickResults.length > 0) {
                console.log(`   ✅ 点击结果: ${clickResults.join(', ')}`);
                await new Promise(resolve => setTimeout(resolve, this.config.clickDelay));
            }
        } catch (error) {
            console.log(`   ⚠️ 点击按钮时出错: ${error.message}`);
        }
    }

    // 估算评论数量
    async estimateCommentCount(page) {
        return await page.evaluate(() => {
            const pageText = document.body.textContent;
            
            // 计算可能的评论标识符数量
            const timeMatches = pageText.match(/\d{2}-\d{2}/g) || [];
            const replyMatches = pageText.match(/\d+回复/g) || [];
            const userMatches = pageText.match(/求带|宝子|学姐/g) || [];
            
            // 估算评论数量
            return Math.max(timeMatches.length, replyMatches.length, Math.floor(userMatches.length / 2));
        });
    }

    // 全量评论提取
    async extractAllComments(page) {
        console.log('🧠 开始全量评论提取...');
        
        const result = await page.evaluate(() => {
            const data = {
                noteInfo: {
                    id: '',
                    title: '',
                    author: '',
                    totalCommentCount: 0
                },
                comments: [],
                extractStats: {
                    totalTextLength: 0,
                    commentSectionLength: 0,
                    estimatedComments: 0
                },
                extractTime: new Date().toISOString()
            };
            
            try {
                // 获取页面文本
                const pageText = document.body.textContent;
                data.extractStats.totalTextLength = pageText.length;
                
                // 提取基本信息
                const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
                if (urlMatch) {
                    data.noteInfo.id = urlMatch[1];
                }
                
                // 提取标题
                const titlePatterns = [
                    /有什么软件可以手机上兼织专米？\s*求推荐！/,
                    /想要一个靠谱的兼织/,
                    /兼织/
                ];
                
                for (const pattern of titlePatterns) {
                    const match = pageText.match(pattern);
                    if (match) {
                        data.noteInfo.title = match[0];
                        break;
                    }
                }
                
                // 提取评论总数
                const countMatch = pageText.match(/共\s*(\d+)\s*条评论/);
                if (countMatch) {
                    data.noteInfo.totalCommentCount = parseInt(countMatch[1]);
                }
                
                // 提取作者
                const authorMatch = pageText.match(/漫娴学姐\s*招暑假工版/);
                if (authorMatch) {
                    data.noteInfo.author = '漫娴学姐 招暑假工版';
                }
                
                // 估算当前评论数
                const timeMatches = pageText.match(/\d{2}-\d{2}/g) || [];
                data.extractStats.estimatedComments = timeMatches.length;
                
                return data;
            } catch (error) {
                console.error('提取基本信息时出错:', error);
                data.error = error.message;
                return data;
            }
        });
        
        // 在Node.js中进行全量解析
        const pageText = await page.evaluate(() => document.body.textContent);
        const comments = this.parseAllComments(pageText);
        result.comments = comments;
        
        return result;
    }

    // 解析所有评论
    parseAllComments(pageText) {
        console.log('🔍 开始解析所有评论...');
        
        const comments = [];
        
        // 查找评论区域（更宽泛的匹配）
        const commentPatterns = [
            /共\s*\d+\s*条评论([\s\S]*?)(?:回到顶部|发现|发布|创作中心|window\.|$)/,
            /\d+\s*条评论([\s\S]*?)(?:回到顶部|发现|发布|$)/,
            /评论([\s\S]*?)(?:回到顶部|发现|发布|$)/,
            /([\s\S]*?)(?:回到顶部|发现|发布|$)/ // 最宽泛的匹配
        ];
        
        let commentText = '';
        for (const pattern of commentPatterns) {
            const match = pageText.match(pattern);
            if (match && match[1].length > 500) { // 增加最小长度要求
                commentText = match[1];
                console.log(`📝 使用模式匹配，评论区域长度: ${commentText.length}`);
                break;
            }
        }
        
        if (!commentText) {
            console.log('❌ 未找到评论区域，使用整个页面文本');
            commentText = pageText;
        }
        
        // 按行分割
        const lines = commentText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);
        
        console.log(`📄 总行数: ${lines.length}`);
        
        let currentComment = null;
        let commentIndex = 0;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            // 跳过系统信息
            if (this.shouldSkipLine(line)) continue;
            
            // 检查是否是新评论
            if (this.isCommentStart(line)) {
                // 保存前一个评论
                if (currentComment && this.isValidComment(currentComment)) {
                    this.finalizeComment(currentComment);
                    comments.push(currentComment);
                }
                
                // 创建新评论
                currentComment = this.createComment(++commentIndex, line);
                
                if (commentIndex <= 10) { // 只显示前10个
                    console.log(`📝 发现评论 ${commentIndex}: ${line.substring(0, 40)}...`);
                }
            } else if (currentComment && line.length > 3) {
                currentComment.rawContent += ' ' + line;
                currentComment.allLines.push(line);
            }
        }
        
        // 保存最后一个评论
        if (currentComment && this.isValidComment(currentComment)) {
            this.finalizeComment(currentComment);
            comments.push(currentComment);
        }
        
        console.log(`✅ 全量解析完成，提取到 ${comments.length} 条评论`);
        return comments;
    }

    // 判断是否应该跳过
    shouldSkipLine(line) {
        const skipPatterns = [
            '加载中', '你还没有', '温馨提示', '广告屏蔽', '我知道了',
            '沪ICP备', '营业执照', '公网安备', '增值电信', '医疗器械',
            '创作服务', '直播管理', '专业号', '商家入驻', 'MCN入驻',
            'window.', 'function', 'console.', 'document.', '更多',
            '行吟信息', '黄浦区', '举报电话', '举报中心', '网络文化'
        ];

        return skipPatterns.some(pattern => line.includes(pattern)) ||
               line.length < 3 ||
               /^[0-9\s\-:\.]+$/.test(line);
    }

    // 判断是否是评论开始
    isCommentStart(line) {
        // 作者标识
        if (line.includes('作者') && line.length < 100) return true;

        // 置顶评论
        if (line.includes('置顶评论')) return true;

        // 时间格式
        if (line.match(/^\d{2}-\d{2}/)) return true;
        if (line.match(/^[^\d\s]{2,20}\s*\d{2}-\d{2}/)) return true;

        // 表情符号开头
        if (line.match(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/)) return true;

        // 用户名模式
        const userPatterns = [
            /^[A-Za-z0-9_]{3,15}\s*\d{2}-\d{2}/,
            /^[\u4e00-\u9fff]{2,8}\s*\d{2}-\d{2}/,
            /^.*学姐.*\d{2}-\d{2}/,
            /^.*宝子.*\d{2}-\d{2}/
        ];

        for (const pattern of userPatterns) {
            if (line.match(pattern)) return true;
        }

        // 关键词
        const keywords = ['求带', '宝子', '广州', '学姐', '兼职', '工作', '聊天员', '陪玩'];
        if (keywords.some(keyword => line.includes(keyword)) && line.length < 80) {
            return true;
        }

        return false;
    }

    // 创建评论对象
    createComment(index, firstLine) {
        const comment = {
            id: index,
            userId: '',
            username: '',
            content: '',
            time: '',
            likes: 0,
            replyCount: 0,
            isAuthor: firstLine.includes('作者'),
            isPinned: firstLine.includes('置顶'),
            rawContent: firstLine,
            allLines: [firstLine]
        };

        // 提取时间
        const timePatterns = [
            /(\d{2}-\d{2})/,
            /(\d+小时前)/,
            /(\d+分钟前)/,
            /(\d+天前)/
        ];

        for (const pattern of timePatterns) {
            const match = firstLine.match(pattern);
            if (match) {
                comment.time = match[1];
                break;
            }
        }

        // 提取用户名
        const userPatterns = [
            /^([\u4e00-\u9fff]{2,8})\s*\d{2}-\d{2}/,
            /^([A-Za-z0-9_]{3,15})\s*\d{2}-\d{2}/,
            /^([^\d\s]{2,20})\s*作者/,
            /^([^\d\s]{2,20})\s*置顶/,
            /^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([^\d\s]{2,15})\s*\d{2}-\d{2}/
        ];

        for (const pattern of userPatterns) {
            const match = firstLine.match(pattern);
            if (match && match[1]) {
                let username = match[1].trim();
                username = username.replace(/[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/g, '').trim();
                if (username.length >= 2 && username.length <= 20) {
                    comment.username = username;
                    break;
                }
            }
        }

        return comment;
    }

    // 完善评论
    finalizeComment(comment) {
        let content = comment.rawContent;

        // 清理内容
        if (comment.username) {
            content = content.replace(new RegExp(comment.username, 'g'), '');
        }
        content = content.replace(/\d{2}-\d{2}/g, '');
        content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');
        content = content.replace(/作者|置顶评论|回复|赞|仅自己可见/g, '');
        content = content.replace(/\s+/g, ' ').trim();

        // 提取数字信息
        const allText = comment.allLines.join(' ');

        // 点赞数
        const likeMatch = allText.match(/(\d+)\s*赞|赞\s*(\d+)|^(\d+)$/);
        if (likeMatch) {
            const num = parseInt(likeMatch[1] || likeMatch[2] || likeMatch[3]);
            if (num > 0 && num < 10000 && !allText.includes(num + '回复')) {
                comment.likes = num;
            }
        }

        // 回复数
        const replyMatch = allText.match(/(\d+)\s*回复|回复\s*(\d+)|展开\s*(\d+)\s*条/);
        if (replyMatch) {
            comment.replyCount = parseInt(replyMatch[1] || replyMatch[2] || replyMatch[3]);
        }

        // 用户ID
        const userIdPatterns = [
            /小红薯([A-F0-9]{8,})/,
            /user\/([a-f0-9]{20,})/,
            /uid[=:]([a-f0-9]{20,})/
        ];

        for (const pattern of userIdPatterns) {
            const match = allText.match(pattern);
            if (match) {
                comment.userId = match[1];
                break;
            }
        }

        comment.content = content;
        delete comment.rawContent;
        delete comment.allLines;
    }

    // 验证评论
    isValidComment(comment) {
        return comment &&
               comment.rawContent &&
               comment.rawContent.length >= 5 &&
               comment.rawContent.length <= 2000;
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        try {
            console.log('💪 启动全量评论加载器...');
            console.log(`🎯 目标: 提取接近 ${this.config.targetComments} 条评论`);
            console.log('🛡️ 策略: 强力滚动 + 智能点击 + 分批处理');

            const { browser, page } = await this.connectToBrowser();

            // 强力加载
            await this.powerfulLoad(page);

            // 全量提取
            const result = await this.extractAllComments(page);

            // 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `full_comments_${result.noteInfo.id}_${timestamp}.json`;
            this.saveToFile(result, filename);

            // 输出结果
            console.log('\n🎉 全量加载完成！');
            console.log('📊 最终统计:');
            console.log(`   🆔 笔记ID: ${result.noteInfo.id}`);
            console.log(`   📝 笔记标题: ${result.noteInfo.title || '未知'}`);
            console.log(`   👤 笔记作者: ${result.noteInfo.author || '未知'}`);
            console.log(`   🎯 目标评论数: ${result.noteInfo.totalCommentCount}`);
            console.log(`   💬 实际提取数: ${result.comments.length}`);
            console.log(`   📈 完成度: ${Math.round(result.comments.length / result.noteInfo.totalCommentCount * 100)}%`);
            console.log(`   📄 页面文本长度: ${result.extractStats.totalTextLength}`);
            console.log(`   📁 保存文件: ${filename}`);

            if (result.comments.length > 0) {
                console.log('\n👥 评论样本预览:');
                result.comments.slice(0, 3).forEach((comment, index) => {
                    console.log(`   ${index + 1}. 用户: ${comment.username || '未知'}`);
                    console.log(`      用户ID: ${comment.userId || '未提取到'}`);
                    console.log(`      时间: ${comment.time || '未知'}`);
                    console.log(`      内容: ${comment.content.substring(0, 50)}...`);
                    console.log(`      点赞: ${comment.likes} | 回复: ${comment.replyCount}`);
                    console.log('');
                });

                // 质量统计
                const withUsername = result.comments.filter(c => c.username).length;
                const withUserId = result.comments.filter(c => c.userId).length;
                const withTime = result.comments.filter(c => c.time).length;

                console.log('📈 数据质量统计:');
                console.log(`   👤 有用户名: ${withUsername}/${result.comments.length} (${Math.round(withUsername/result.comments.length*100)}%)`);
                console.log(`   🆔 有用户ID: ${withUserId}/${result.comments.length} (${Math.round(withUserId/result.comments.length*100)}%)`);
                console.log(`   ⏰ 有时间: ${withTime}/${result.comments.length} (${Math.round(withTime/result.comments.length*100)}%)`);

                if (result.comments.length >= result.noteInfo.totalCommentCount * 0.8) {
                    console.log('\n🎉 恭喜！已提取到大部分评论数据！');
                } else if (result.comments.length >= result.noteInfo.totalCommentCount * 0.5) {
                    console.log('\n👍 不错！已提取到一半以上的评论数据！');
                } else {
                    console.log('\n💡 提示：可能需要更多滚动或点击操作来加载更多评论');
                }
            }

            await browser.disconnect();

        } catch (error) {
            console.error('❌ 全量加载失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行加载器
if (require.main === module) {
    const loader = new FullCommentsLoader();
    loader.run();
}

module.exports = FullCommentsLoader;
