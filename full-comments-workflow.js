#!/usr/bin/env node

/**
 * 🔄 完整的评论采集工作流
 * 1. 点击进入笔记详情页
 * 2. 滚动加载评论
 * 3. 采集所有评论数据
 * 4. 保存结果
 */

const axios = require('axios');
const WebSocket = require('ws');
const fs = require('fs');

class FullCommentsWorkflow {
    constructor() {
        this.debugPort = null;
    }

    // 🔄 完整工作流
    async runFullWorkflow(noteIndex = 0) {
        console.log('🔄 开始完整的评论采集工作流...\n');

        try {
            // 1. 获取调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 当前页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 如果不在笔记详情页，先点击进入
            if (!tab.url.includes('/explore/')) {
                console.log('📝 当前在用户主页，点击进入笔记详情页...');
                await this.clickIntoNoteDetail(tab, noteIndex);
                
                // 等待页面加载
                console.log('⏱️ 等待页面加载...');
                await this.sleep(5000);
            }

            // 4. 滚动加载评论
            console.log('📜 滚动加载评论...');
            await this.scrollToLoadComments(tab);

            // 5. 采集评论数据
            console.log('💬 采集评论数据...');
            const commentsData = await this.extractComments(tab);

            // 6. 保存数据
            console.log('💾 保存评论数据...');
            const savedFiles = await this.saveCommentsData(commentsData);

            return {
                commentsData,
                savedFiles
            };

        } catch (error) {
            console.error('❌ 工作流失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取调试端口
    async getCurrentDebugPort() {
        const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });

        const port = response.data.data.result.http.split(':')[1];
        this.debugPort = port;
        console.log(`✅ 调试端口: ${port}`);
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
            timeout: 5000
        });

        return response.data.find(tab =>
            tab.url && tab.url.includes('xiaohongshu.com')
        );
    }

    // 🖱️ 点击进入笔记详情
    async clickIntoNoteDetail(tab, noteIndex) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Page.enable'
                }));

                setTimeout(() => {
                    const clickScript = `
                        (function() {
                            const noteLinks = document.querySelectorAll('a[href*="/explore/"]');
                            if (noteLinks[${noteIndex}]) {
                                noteLinks[${noteIndex}].click();
                                return { clicked: true, href: noteLinks[${noteIndex}].href };
                            } else {
                                return { clicked: false, totalLinks: noteLinks.length };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: clickScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.method === 'Page.frameNavigated') {
                        console.log('✅ 页面导航完成');
                        setTimeout(() => {
                            ws.close();
                            resolve();
                        }, 2000);
                    }
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        if (result.clicked) {
                            console.log('✅ 点击成功，等待页面加载...');
                        } else {
                            console.log(`❌ 点击失败，找到 ${result.totalLinks} 个笔记链接`);
                            ws.close();
                            reject(new Error('点击失败'));
                        }
                    }
                } catch (error) {
                    console.error('处理消息失败:', error.message);
                }
            });

            ws.on('error', reject);
            setTimeout(() => {
                ws.close();
                reject(new Error('点击超时'));
            }, 15000);
        });
    }

    // 📜 滚动加载评论
    async scrollToLoadComments(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;
            let scrollCount = 0;
            const maxScrolls = 5;

            ws.on('open', () => {
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    this.performScroll(ws, requestId++, scrollCount, maxScrolls, resolve, reject);
                }, 2000);
            });

            ws.on('error', reject);
        });
    }

    // 🔄 执行滚动
    performScroll(ws, requestId, scrollCount, maxScrolls, resolve, reject) {
        if (scrollCount >= maxScrolls) {
            console.log(`✅ 完成 ${scrollCount} 次滚动`);
            ws.close();
            resolve();
            return;
        }

        scrollCount++;
        console.log(`📜 第 ${scrollCount} 次滚动...`);

        const scrollScript = `
            (function() {
                // 滚动到页面底部
                window.scrollTo(0, document.body.scrollHeight);
                
                // 尝试点击"查看更多评论"
                const moreButtons = Array.from(document.querySelectorAll('*')).filter(el => 
                    el.textContent.includes('更多评论') || 
                    el.textContent.includes('查看更多') ||
                    el.textContent.includes('展开')
                );
                
                if (moreButtons.length > 0) {
                    moreButtons[0].click();
                }
                
                return { scrolled: true };
            })();
        `;

        ws.send(JSON.stringify({
            id: requestId,
            method: 'Runtime.evaluate',
            params: {
                expression: scrollScript,
                returnByValue: true
            }
        }));

        setTimeout(() => {
            this.performScroll(ws, requestId + 1, scrollCount, maxScrolls, resolve, reject);
        }, 3000);
    }

    // 💬 提取评论
    async extractComments(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const extractScript = `
                        (function() {
                            try {
                                const commentsData = {
                                    noteUrl: window.location.href,
                                    noteTitle: document.title.replace(' - 小红书', '').trim(),
                                    timestamp: new Date().toISOString(),
                                    comments: []
                                };

                                // 查找评论元素
                                const commentSelectors = [
                                    '[class*="comment"]',
                                    '[class*="reply"]',
                                    '.comment-item'
                                ];

                                let commentElements = [];
                                for (const selector of commentSelectors) {
                                    commentElements = document.querySelectorAll(selector);
                                    if (commentElements.length > 0) break;
                                }

                                // 通用方法
                                if (commentElements.length === 0) {
                                    const allDivs = document.querySelectorAll('div');
                                    commentElements = Array.from(allDivs).filter(div => {
                                        const text = div.textContent.trim();
                                        const hasTime = text.includes('天前') || text.includes('小时前');
                                        const hasContent = text.length > 10 && text.length < 500;
                                        const hasAvatar = div.querySelector('img');
                                        return hasTime && hasContent && hasAvatar;
                                    });
                                }

                                // 提取评论信息
                                commentElements.forEach((element, index) => {
                                    const text = element.textContent.trim();
                                    const lines = text.split('\\n').filter(line => line.trim());
                                    
                                    if (lines.length >= 2) {
                                        const comment = {
                                            id: index + 1,
                                            username: lines[0] || '匿名用户',
                                            content: lines[1] || text.substring(0, 100),
                                            publishTime: '',
                                            likes: 0
                                        };

                                        // 提取时间
                                        const timeMatch = text.match(/(\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天)/);
                                        if (timeMatch) {
                                            comment.publishTime = timeMatch[1];
                                        }

                                        // 提取点赞数
                                        const likeMatch = text.match(/(\\d+)\\s*赞/);
                                        if (likeMatch) {
                                            comment.likes = parseInt(likeMatch[1]);
                                        }

                                        commentsData.comments.push(comment);
                                    }
                                });

                                commentsData.summary = {
                                    totalComments: commentsData.comments.length,
                                    totalLikes: commentsData.comments.reduce((sum, c) => sum + c.likes, 0)
                                };

                                return { success: true, data: commentsData };
                                
                            } catch (error) {
                                return { success: false, error: error.message };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: extractScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            console.log('✅ 评论数据提取成功');
                            ws.close();
                            resolve(result.data);
                        } else {
                            console.log('❌ 评论提取失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    reject(error);
                }
            });

            ws.on('error', reject);
            setTimeout(() => {
                ws.close();
                reject(new Error('评论提取超时'));
            }, 20000);
        });
    }

    // 💾 保存评论数据
    async saveCommentsData(commentsData) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const jsonFileName = `comments-${timestamp}.json`;
        const txtFileName = `comments-${timestamp}.txt`;

        // 保存JSON
        fs.writeFileSync(jsonFileName, JSON.stringify(commentsData, null, 2), 'utf8');
        console.log(`✅ JSON数据已保存: ${jsonFileName}`);

        // 生成文本报告
        const report = this.generateReport(commentsData);
        fs.writeFileSync(txtFileName, report, 'utf8');
        console.log(`✅ 文本报告已保存: ${txtFileName}`);

        return { jsonFile: jsonFileName, txtFile: txtFileName };
    }

    // 📝 生成报告
    generateReport(data) {
        const lines = [];
        
        lines.push('💬 小红书笔记评论采集报告');
        lines.push('=' * 50);
        lines.push(`📅 采集时间: ${data.timestamp}`);
        lines.push(`📝 笔记标题: ${data.noteTitle}`);
        lines.push(`🔗 笔记链接: ${data.noteUrl}`);
        lines.push(`💬 评论总数: ${data.summary.totalComments}`);
        lines.push(`👍 总点赞数: ${data.summary.totalLikes}`);
        lines.push('');
        
        lines.push('💬 评论详情:');
        lines.push('-' * 50);
        
        data.comments.forEach((comment, index) => {
            lines.push(`\n${index + 1}. ${comment.username}`);
            lines.push(`   内容: ${comment.content}`);
            lines.push(`   时间: ${comment.publishTime || '未知'}`);
            lines.push(`   点赞: ${comment.likes}`);
        });
        
        return lines.join('\n');
    }

    // 💤 等待函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 🧪 测试完整工作流
async function testFullWorkflow(noteIndex = 0) {
    const workflow = new FullCommentsWorkflow();
    
    try {
        const result = await workflow.runFullWorkflow(noteIndex);
        
        console.log('\n🎉 完整评论采集工作流完成!');
        console.log('=' * 50);
        console.log(`📝 笔记标题: ${result.commentsData.noteTitle}`);
        console.log(`💬 采集评论数: ${result.commentsData.summary.totalComments}`);
        console.log(`👍 总点赞数: ${result.commentsData.summary.totalLikes}`);
        console.log(`📁 JSON文件: ${result.savedFiles.jsonFile}`);
        console.log(`📄 文本文件: ${result.savedFiles.txtFile}`);
        
        // 显示前5条评论
        if (result.commentsData.comments.length > 0) {
            console.log('\n💬 前5条评论:');
            result.commentsData.comments.slice(0, 5).forEach((comment, index) => {
                console.log(`${index + 1}. ${comment.username}: ${comment.content}`);
            });
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ 完整工作流失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    const noteIndex = process.argv[2] ? parseInt(process.argv[2]) : 0;
    console.log(`🎯 准备采集第 ${noteIndex + 1} 篇笔记的评论...`);
    testFullWorkflow(noteIndex).catch(console.error);
}

module.exports = { FullCommentsWorkflow, testFullWorkflow };
