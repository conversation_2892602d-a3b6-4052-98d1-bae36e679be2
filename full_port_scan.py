#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 全面端口扫描器
扫描更大范围的端口，寻找Chrome调试端口
"""

import socket
import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def check_port_open(port, timeout=1):
    """快速检查端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except:
        return False

def check_chrome_debug_port(port):
    """检查是否是Chrome调试端口"""
    try:
        # 尝试访问Chrome DevTools协议
        url = f"http://127.0.0.1:{port}/json"
        response = requests.get(url, timeout=2)
        
        if response.status_code == 200:
            try:
                data = response.json()
                if isinstance(data, list):
                    return True, data
            except:
                pass
        
        # 尝试访问版本信息
        url = f"http://127.0.0.1:{port}/json/version"
        response = requests.get(url, timeout=2)
        
        if response.status_code == 200:
            try:
                data = response.json()
                if 'Browser' in data or 'User-Agent' in data:
                    return True, [data]
            except:
                pass
                
        return False, None
    except:
        return False, None

def scan_port_range(start_port, end_port, max_workers=50):
    """扫描端口范围"""
    print(f"🔍 扫描端口范围 {start_port}-{end_port}...")
    
    open_ports = []
    total_ports = end_port - start_port + 1
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_port = {executor.submit(check_port_open, port): port 
                         for port in range(start_port, end_port + 1)}
        
        completed = 0
        for future in as_completed(future_to_port):
            port = future_to_port[future]
            completed += 1
            
            try:
                if future.result():
                    open_ports.append(port)
                    print(f"   ✅ 端口 {port} 开放 ({completed}/{total_ports})")
                elif completed % 100 == 0:
                    print(f"   📊 已扫描 {completed}/{total_ports} 个端口...")
            except:
                pass
    
    return sorted(open_ports)

def main():
    print("🎯 全面Chrome调试端口扫描器")
    print("=" * 50)
    
    start_time = time.time()
    
    # 定义扫描范围
    scan_ranges = [
        (9000, 9300),      # 标准调试端口范围
        (54000, 54500),    # 比特浏览器可能的范围
        (55000, 55500),    # 扩展范围1
        (56000, 57000),    # 扩展范围2
        (64000, 65000),    # 你截图中的端口范围
    ]
    
    all_open_ports = []
    
    # 扫描每个范围
    for start_port, end_port in scan_ranges:
        open_ports = scan_port_range(start_port, end_port)
        all_open_ports.extend(open_ports)
        
        if open_ports:
            print(f"   📊 范围 {start_port}-{end_port}: 发现 {len(open_ports)} 个开放端口")
        else:
            print(f"   📊 范围 {start_port}-{end_port}: 无开放端口")
    
    if not all_open_ports:
        print("❌ 未发现任何开放端口")
        return
    
    print(f"\n✅ 总共发现 {len(all_open_ports)} 个开放端口:")
    for port in all_open_ports:
        print(f"   🔌 {port}")
    
    # 检查哪些是调试端口
    print(f"\n🔍 检查调试协议...")
    debug_ports = []
    
    for port in all_open_ports:
        print(f"   检查端口 {port}...")
        is_debug, data = check_chrome_debug_port(port)
        
        if is_debug:
            print(f"   ✅ 端口 {port} 是Chrome调试端口!")
            
            # 分析页面
            xiaohongshu_count = 0
            total_pages = len(data) if data else 0
            
            if data:
                for item in data:
                    if isinstance(item, dict):
                        url = item.get('url', '')
                        title = item.get('title', '')
                        if 'xiaohongshu.com' in url:
                            xiaohongshu_count += 1
                            print(f"      🎉 小红书页面: {title[:50]}...")
            
            debug_ports.append({
                'port': port,
                'xiaohongshu_pages': xiaohongshu_count,
                'total_pages': total_pages
            })
        else:
            print(f"   ❌ 端口 {port} 不是调试端口")
    
    # 输出结果
    scan_time = time.time() - start_time
    print(f"\n" + "=" * 50)
    print(f"🎉 扫描完成! 耗时: {scan_time:.1f}秒")
    print(f"📊 开放端口: {len(all_open_ports)}")
    print(f"🎯 调试端口: {len(debug_ports)}")
    
    if debug_ports:
        print("\n🚀 发现的调试端口:")
        
        best_port = None
        for dp in debug_ports:
            port = dp['port']
            xhs_count = dp['xiaohongshu_pages']
            total_count = dp['total_pages']
            
            if xhs_count > 0:
                print(f"   🎉 端口 {port} - {xhs_count} 个小红书页面 (共{total_count}页) ⭐")
                if not best_port:
                    best_port = port
            else:
                print(f"   📄 端口 {port} - {total_count} 个页面")
                if not best_port:
                    best_port = port
        
        # 保存结果
        result = {
            'scan_time': scan_time,
            'open_ports': all_open_ports,
            'debug_ports': debug_ports,
            'best_port': best_port,
            'timestamp': time.time()
        }
        
        with open('full_scan_results.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 结果已保存到 full_scan_results.json")
        print(f"🎯 推荐使用端口: {best_port}")
        print(f"\n🚀 测试命令:")
        print(f"   python test_debug_port.py {best_port}")
        
    else:
        print("\n❌ 未发现Chrome调试端口")
        print("💡 可能的原因:")
        print("   1. 比特浏览器未启用远程调试")
        print("   2. 调试端口在其他范围")
        print("   3. 防火墙阻止了连接")
        
        if all_open_ports:
            print(f"\n💡 发现的开放端口可能是:")
            for port in all_open_ports:
                print(f"   - {port}: 可能是API端口或其他服务")

if __name__ == "__main__":
    main()
