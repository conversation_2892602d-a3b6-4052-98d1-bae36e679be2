#!/usr/bin/env node

/**
 * 🕊️ 温和评论采集器
 * 使用比特浏览器API，避免反爬检测，精确提取用户信息和评论内容
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class GentleCommentsCollector {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        // 反爬策略配置
        this.antiDetectionConfig = {
            scrollDelay: 2000,        // 滚动间隔2秒
            actionDelay: 1500,        // 操作间隔1.5秒
            randomDelay: 500,         // 随机延迟最大500ms
            maxScrollAttempts: 10,    // 最大滚动次数
            waitForContent: 3000,     // 等待内容加载3秒
            humanLikeScroll: true     // 模拟人类滚动行为
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    // 随机延迟，模拟人类行为
    async randomDelay(baseDelay = 1000) {
        const randomExtra = Math.random() * this.antiDetectionConfig.randomDelay;
        const totalDelay = baseDelay + randomExtra;
        console.log(`   ⏱️ 等待 ${Math.round(totalDelay)}ms...`);
        await new Promise(resolve => setTimeout(resolve, totalDelay));
    }

    async connectToBrowser() {
        try {
            console.log('🔗 连接到比特浏览器...');
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${this.debugPort}`,
                defaultViewport: null
            });
            
            const pages = await browser.pages();
            const page = pages.find(p => p.url().includes('xiaohongshu.com/explore/'));
            
            if (!page) {
                throw new Error('未找到小红书笔记页面');
            }
            
            console.log('✅ 找到小红书笔记页面');
            console.log(`📄 当前URL: ${page.url()}`);
            
            // 设置用户代理和其他反检测措施
            await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
            
            return { browser, page };
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    // 温和滚动，模拟人类行为
    async gentleScroll(page, scrollCount = 3) {
        console.log(`🔄 开始温和滚动加载更多评论 (${scrollCount}次)...`);
        
        for (let i = 0; i < scrollCount; i++) {
            console.log(`   📜 第 ${i + 1}/${scrollCount} 次滚动`);
            
            // 模拟人类滚动：不规则的滚动距离
            const scrollDistance = 300 + Math.random() * 200; // 300-500px
            
            await page.evaluate((distance) => {
                window.scrollBy({
                    top: distance,
                    behavior: 'smooth'
                });
            }, scrollDistance);
            
            // 等待内容加载
            await this.randomDelay(this.antiDetectionConfig.scrollDelay);
            
            // 检查是否有新内容加载
            const commentCount = await page.evaluate(() => {
                return document.querySelectorAll('[class*="comment"], [class*="reply"]').length;
            });
            
            console.log(`   💬 当前检测到 ${commentCount} 个评论元素`);
        }
        
        console.log('✅ 滚动完成，等待内容稳定...');
        await this.randomDelay(this.antiDetectionConfig.waitForContent);
    }

    // 精确提取评论数据
    async extractDetailedComments(page) {
        console.log('🔍 开始精确提取评论数据...');

        // 注入解析函数到页面
        await page.evaluateOnNewDocument(() => {
            // 智能文本解析函数
            window.parseCommentsFromText = function(pageText, results) {
                console.log('🧠 使用智能文本解析...');

                // 查找评论区域
                const commentSectionMatch = pageText.match(/共\s*\d+\s*条评论([\s\S]*?)(?:回到顶部|发现|发布|创作中心|$)/);
                if (!commentSectionMatch) {
                    console.log('未找到评论区域');
                    return results;
                }

                const commentText = commentSectionMatch[1];
                const lines = commentText.split('\n').filter(line => line.trim().length > 2);

                let currentComment = null;
                let commentIndex = 0;

                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();

                    // 跳过无用内容
                    if (this.shouldSkipLine(line)) continue;

                    // 检查是否是新评论开始
                    if (this.isNewCommentStart(line)) {
                        // 保存前一个评论
                        if (currentComment && currentComment.content.length > 5) {
                            this.cleanupComment(currentComment);
                            results.comments.push(currentComment);
                        }

                        // 开始新评论
                        currentComment = this.createNewComment(++commentIndex, line);
                    } else if (currentComment && line.length > 3) {
                        // 继续当前评论
                        currentComment.content += ' ' + line;
                        currentComment.rawText += ' ' + line;
                    }
                }

                // 保存最后一个评论
                if (currentComment && currentComment.content.length > 5) {
                    this.cleanupComment(currentComment);
                    results.comments.push(currentComment);
                }

                console.log(`智能解析提取到 ${results.comments.length} 条评论`);
                return results;
            };

            // 判断是否应该跳过这一行
            window.shouldSkipLine = function(line) {
                const skipPatterns = [
                    '加载中', '你还没有', '温馨提示', '广告屏蔽', '我知道了',
                    '活动', 'window.', 'function', 'console.log', 'document.',
                    '创作服务', '直播管理', '专业号', '商家入驻'
                ];

                return skipPatterns.some(pattern => line.includes(pattern)) ||
                       line.length < 3 ||
                       /^[0-9\s\-:]+$/.test(line);
            };

            // 判断是否是新评论的开始
            window.isNewCommentStart = function(line) {
                return line.includes('作者') ||
                       line.includes('置顶评论') ||
                       line.match(/^\d{2}-\d{2}/) ||
                       (line.includes('回复') && line.length < 50) ||
                       line.match(/^[^\s]{2,10}\s*\d{2}-\d{2}/);
            };

            // 创建新评论对象
            window.createNewComment = function(index, line) {
                const comment = {
                    id: index,
                    userId: '',
                    username: '',
                    avatar: '',
                    content: line,
                    time: '',
                    likes: 0,
                    replyCount: 0,
                    replies: [],
                    isAuthor: line.includes('作者'),
                    isPinned: line.includes('置顶'),
                    rawText: line
                };

                // 提取时间
                const timeMatch = line.match(/(\d{2}-\d{2})/);
                if (timeMatch) {
                    comment.time = timeMatch[1];
                }

                // 尝试提取用户名（在时间之前的文字）
                const userMatch = line.match(/^([^\d]{2,15})\s*\d{2}-\d{2}/);
                if (userMatch) {
                    comment.username = userMatch[1].trim();
                }

                return comment;
            };

            // 清理评论内容
            window.cleanupComment = function(comment) {
                let content = comment.content;

                // 移除用户名
                if (comment.username) {
                    content = content.replace(comment.username, '').trim();
                }

                // 移除时间
                content = content.replace(/\d{2}-\d{2}/g, '').trim();

                // 移除常见的无用文字
                const cleanPatterns = [
                    /^\d+$/, /\d+回复$/, /回复$/, /赞$/, /作者$/, /置顶评论$/,
                    /展开\s*\d+\s*条回复/, /仅自己可见/
                ];

                cleanPatterns.forEach(pattern => {
                    content = content.replace(pattern, '').trim();
                });

                // 清理多余空格和特殊字符
                content = content.replace(/\s+/g, ' ').trim();
                content = content.replace(/^[^\w\u4e00-\u9fff]+/, '').trim();

                comment.content = content;
            };

            // 解析单个评论元素
            window.parseCommentElement = function(element, index) {
                const comment = {
                    id: index,
                    userId: '',
                    username: '',
                    avatar: '',
                    content: '',
                    time: '',
                    likes: 0,
                    replyCount: 0,
                    replies: [],
                    isAuthor: false,
                    isPinned: false,
                    rawHtml: element.outerHTML.substring(0, 300)
                };

                try {
                    // 提取用户名
                    const usernameSelectors = [
                        '.username', '.user-name', '[class*="user"]', '[class*="name"]',
                        'span[class*="nick"]', '.nick-name', '[data-testid*="user"]'
                    ];

                    for (const selector of usernameSelectors) {
                        const usernameEl = element.querySelector(selector);
                        if (usernameEl && usernameEl.textContent.trim()) {
                            comment.username = usernameEl.textContent.trim();
                            break;
                        }
                    }

                    // 提取头像和用户ID
                    const avatarEl = element.querySelector('img[src*="avatar"], img[class*="avatar"], .avatar img');
                    if (avatarEl) {
                        comment.avatar = avatarEl.src;
                        // 从头像URL提取用户ID
                        const userIdMatch = avatarEl.src.match(/\/([a-f0-9]{20,})\//);
                        if (userIdMatch) {
                            comment.userId = userIdMatch[1];
                        }
                    }

                    // 提取评论内容
                    const contentSelectors = [
                        '.content', '.text', '[class*="content"]', '[class*="text"]',
                        '.comment-text', '[data-testid*="content"]'
                    ];

                    for (const selector of contentSelectors) {
                        const contentEl = element.querySelector(selector);
                        if (contentEl) {
                            comment.content = contentEl.textContent.trim();
                            break;
                        }
                    }

                    // 如果没有找到内容，使用整个元素文本并清理
                    if (!comment.content) {
                        comment.content = element.textContent.trim();
                        if (comment.username) {
                            comment.content = comment.content.replace(comment.username, '').trim();
                        }
                    }

                    // 提取时间
                    const timeSelectors = ['.time', '.date', '[class*="time"]', '[class*="date"]'];
                    for (const selector of timeSelectors) {
                        const timeEl = element.querySelector(selector);
                        if (timeEl) {
                            comment.time = timeEl.textContent.trim();
                            break;
                        }
                    }

                    if (!comment.time) {
                        const timeMatch = element.textContent.match(/(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/);
                        if (timeMatch) {
                            comment.time = timeMatch[1];
                        }
                    }

                    // 提取点赞数
                    const likeEl = element.querySelector('[class*="like"], [class*="thumb"]');
                    if (likeEl) {
                        const likeMatch = likeEl.textContent.match(/(\d+)/);
                        if (likeMatch) {
                            comment.likes = parseInt(likeMatch[1]);
                        }
                    }

                    // 检查特殊标记
                    comment.isAuthor = element.textContent.includes('作者') ||
                                      element.querySelector('[class*="author"]') !== null;
                    comment.isPinned = element.textContent.includes('置顶') ||
                                      element.querySelector('[class*="pin"]') !== null;

                } catch (error) {
                    console.error('解析评论元素时出错:', error);
                }

                return comment;
            };
        });

        const commentsData = await page.evaluate(() => {
            const results = {
                noteInfo: {
                    title: '',
                    id: '',
                    author: '',
                    totalCommentCount: 0
                },
                comments: [],
                extractTime: new Date().toISOString(),
                extractMethod: 'gentle-collector-v1'
            };

            try {
                // 提取笔记基本信息
                const titleElement = document.querySelector('h1, .title, [class*="title"]');
                if (titleElement) {
                    results.noteInfo.title = titleElement.textContent.trim();
                }

                // 从URL提取笔记ID
                const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
                if (urlMatch) {
                    results.noteInfo.id = urlMatch[1];
                }

                // 提取评论总数
                const pageText = document.body.textContent;
                const countMatch = pageText.match(/共\s*(\d+)\s*条评论/);
                if (countMatch) {
                    results.noteInfo.totalCommentCount = parseInt(countMatch[1]);
                }

                // 尝试多种选择器策略提取评论
                const commentSelectors = [
                    '[class*="comment-item"]',
                    '[class*="comment"]',
                    '[class*="reply"]',
                    'div[data-testid*="comment"]',
                    '.comment',
                    '.reply-item'
                ];

                let commentElements = [];
                for (const selector of commentSelectors) {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        console.log(`找到 ${elements.length} 个元素使用选择器: ${selector}`);
                        commentElements = Array.from(elements);
                        break;
                    }
                }

                // 如果没有找到标准评论元素，使用智能文本解析
                if (commentElements.length === 0) {
                    console.log('未找到标准评论元素，使用智能文本解析...');
                    return window.parseCommentsFromText(pageText, results);
                }

                // 处理找到的评论元素
                commentElements.forEach((element, index) => {
                    try {
                        const comment = window.parseCommentElement(element, index + 1);
                        if (comment && comment.content.length > 3) {
                            results.comments.push(comment);
                        }
                    } catch (error) {
                        console.error(`解析评论 ${index + 1} 时出错:`, error);
                    }
                });

                console.log(`成功提取 ${results.comments.length} 条评论`);

            } catch (error) {
                console.error('提取评论时出错:', error);
                results.error = error.message;
            }

            return results;
        });

        return commentsData;
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        try {
            console.log('🕊️ 启动温和评论采集器...');
            console.log('🛡️ 反爬策略: 随机延迟 + 人类行为模拟 + 温和滚动');
            
            const { browser, page } = await this.connectToBrowser();
            
            // 温和滚动加载更多内容
            await this.gentleScroll(page, 5);
            
            // 提取评论数据
            const commentsData = await this.extractDetailedComments(page);
            
            // 生成文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `gentle_comments_${commentsData.noteInfo.id}_${timestamp}.json`;
            
            // 保存数据
            this.saveToFile(commentsData, filename);
            
            // 输出统计信息
            console.log('\n🎉 温和采集完成！');
            console.log('📊 采集统计:');
            console.log(`   📝 笔记标题: ${commentsData.noteInfo.title}`);
            console.log(`   🆔 笔记ID: ${commentsData.noteInfo.id}`);
            console.log(`   🎯 目标评论数: ${commentsData.noteInfo.totalCommentCount}`);
            console.log(`   💬 实际采集数: ${commentsData.comments.length}`);
            console.log(`   📁 保存文件: ${filename}`);
            
            if (commentsData.comments.length > 0) {
                console.log('\n👥 用户信息预览:');
                commentsData.comments.slice(0, 3).forEach((comment, index) => {
                    console.log(`   ${index + 1}. 用户: ${comment.username || '未知'}`);
                    console.log(`      内容: ${comment.content.substring(0, 30)}...`);
                    console.log(`      时间: ${comment.time || '未知'}`);
                });
            }
            
            await browser.disconnect();
            
        } catch (error) {
            console.error('❌ 采集失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行采集器
if (require.main === module) {
    const collector = new GentleCommentsCollector();
    collector.run();
}

module.exports = GentleCommentsCollector;
