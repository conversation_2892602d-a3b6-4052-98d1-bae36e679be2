#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 比特浏览器API Token获取和端口测试
交互式获取正确的API Token，然后获取调试端口
"""

import requests
import json

def test_api_with_token(api_port, api_token):
    """测试API Token是否正确"""
    print(f"🔍 测试API Token...")
    print(f"   端口: {api_port}")
    print(f"   Token: {api_token[:20]}...")
    
    base_url = f"http://127.0.0.1:{api_port}"
    headers = {
        'Content-Type': 'application/json',
        'X-API-KEY': api_token
    }
    
    try:
        # 测试获取端口接口
        response = requests.post(f"{base_url}/browser/ports", json={}, headers=headers, timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API Token正确!")
            
            if data.get('success') and data.get('data'):
                ports_data = data['data']
                print(f"   🎉 获取到 {len(ports_data)} 个调试端口:")
                for browser_id, port in ports_data.items():
                    print(f"      🌐 浏览器 {browser_id[:8]}... -> 端口 {port}")
                return list(ports_data.values())
            else:
                print(f"   ⚠️ API响应格式异常: {data}")
                return []
        else:
            response_text = response.text
            print(f"   ❌ API请求失败: {response_text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print(f"   ❌ 无法连接到端口 {api_port}")
        return None
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return None

def find_api_port():
    """查找API端口"""
    print("🔍 查找比特浏览器API端口...")
    
    # 常见API端口
    common_ports = [56906, 54345, 56907, 56908, 56909, 55276]
    
    for port in common_ports:
        print(f"   尝试端口 {port}...")
        try:
            response = requests.get(f"http://127.0.0.1:{port}/", timeout=3)
            if response.status_code in [200, 404, 403]:
                print(f"   ✅ 找到API端口: {port}")
                return port
        except:
            continue
    
    print("   ❌ 未找到API端口")
    return None

def main():
    print("🎯 比特浏览器API Token获取器")
    print("=" * 50)
    
    # 1. 查找API端口
    api_port = find_api_port()
    if not api_port:
        print("❌ 未找到API端口，请手动输入:")
        try:
            api_port = int(input("请输入API端口号 (例如: 56906): "))
        except:
            print("❌ 无效端口号")
            return
    
    print(f"✅ 使用API端口: {api_port}")
    
    # 2. 获取API Token
    print("\n💡 请从比特浏览器中获取API Token:")
    print("   1. 打开比特浏览器")
    print("   2. 点击 Local API")
    print("   3. 复制 API Token")
    print("   4. 粘贴到下面")
    
    api_token = input("\n请输入API Token: ").strip()
    
    if not api_token:
        print("❌ API Token不能为空")
        return
    
    # 3. 测试API Token并获取端口
    ports = test_api_with_token(api_port, api_token)
    
    if ports is None:
        print("❌ API连接失败")
        return
    elif not ports:
        print("⚠️ 没有获取到调试端口，可能没有打开的浏览器窗口")
        return
    
    # 4. 保存配置
    config = {
        'api_port': api_port,
        'api_token': api_token,
        'debug_ports': [int(p) for p in ports]
    }
    
    with open('bitbrowser_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 配置已保存到 bitbrowser_config.json")
    print(f"🎯 可用的调试端口: {config['debug_ports']}")
    print(f"💡 建议使用端口: {config['debug_ports'][0]}")
    
    # 5. 生成测试命令
    print(f"\n🚀 现在可以使用以下端口测试连接:")
    for port in config['debug_ports']:
        print(f"   python test_debug_port.py {port}")

if __name__ == "__main__":
    main()
