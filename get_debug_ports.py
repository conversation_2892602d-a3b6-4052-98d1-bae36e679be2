#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 比特浏览器调试端口获取器
通过比特浏览器API获取所有已打开窗口的调试端口
"""

import requests
import json
import time

class BitBrowserPortGetter:
    def __init__(self):
        # 常见的比特浏览器API端口
        self.api_ports = [56906, 54345, 56907, 56908, 56909, 55276]
        self.api_base = None
        self.api_token = None
        
    def find_api_port(self):
        """查找可用的API端口"""
        print("🔍 查找比特浏览器API端口...")
        
        for port in self.api_ports:
            print(f"   尝试端口 {port}...")
            try:
                url = f"http://127.0.0.1:{port}/"
                response = requests.get(url, timeout=3)
                
                if response.status_code in [200, 404, 403]:
                    print(f"   ✅ 找到API端口: {port}")
                    self.api_base = f"http://127.0.0.1:{port}"
                    return True
                    
            except requests.exceptions.ConnectionError:
                continue
            except Exception as e:
                print(f"   端口 {port} 异常: {e}")
                continue
        
        print("   ❌ 未找到可用的API端口")
        return False
    
    def try_get_ports_without_auth(self):
        """尝试不使用认证获取端口"""
        print("🔍 尝试无认证获取调试端口...")
        
        url = f"{self.api_base}/browser/ports"
        
        try:
            # 尝试POST请求
            response = requests.post(url, json={}, timeout=10)
            print(f"   POST状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    return self.parse_ports_response(data)
            
            # 尝试GET请求
            response = requests.get(url, timeout=10)
            print(f"   GET状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    return self.parse_ports_response(data)
            
            print(f"   响应内容: {response.text[:200]}")
            return []
            
        except Exception as e:
            print(f"   ❌ 无认证请求失败: {e}")
            return []
    
    def get_ports_with_auth(self, token):
        """使用认证获取端口"""
        print("🔍 使用认证获取调试端口...")
        
        url = f"{self.api_base}/browser/ports"
        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': token
        }
        
        try:
            response = requests.post(url, json={}, headers=headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get('success') and data.get('data'):
                    return self.parse_ports_response(data)
                else:
                    print("   ❌ API响应格式错误")
                    return []
            else:
                print(f"   ❌ 请求失败: {response.text}")
                return []
                
        except Exception as e:
            print(f"   ❌ 认证请求失败: {e}")
            return []
    
    def parse_ports_response(self, data):
        """解析端口响应数据"""
        ports_data = data['data']
        ports = []
        
        print(f"   ✅ 获取到 {len(ports_data)} 个浏览器窗口:")
        
        for browser_id, port in ports_data.items():
            port_num = int(port)
            ports.append(port_num)
            print(f"      🌐 浏览器 {browser_id[:8]}... -> 端口 {port_num}")
        
        return ports
    
    def get_browser_list(self, token=None):
        """获取浏览器窗口列表"""
        print("🔍 获取浏览器窗口列表...")
        
        url = f"{self.api_base}/browser/list"
        payload = {"page": 0, "pageSize": 20}
        
        headers = {'Content-Type': 'application/json'}
        if token:
            headers['X-API-KEY'] = token
        
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    browsers = data['data']
                    print(f"   ✅ 找到 {len(browsers)} 个浏览器窗口")
                    
                    for browser in browsers[:5]:  # 只显示前5个
                        browser_id = browser.get('id', 'unknown')
                        name = browser.get('name', 'unnamed')
                        status = browser.get('status', 'unknown')
                        print(f"      📱 {name} ({browser_id[:8]}...) - 状态: {status}")
                    
                    return browsers
                else:
                    print(f"   响应格式异常: {data}")
                    return []
            else:
                print(f"   请求失败: {response.text}")
                return []
                
        except Exception as e:
            print(f"   ❌ 获取窗口列表失败: {e}")
            return []
    
    def test_port_connectivity(self, port):
        """测试端口连通性"""
        print(f"🔍 测试端口 {port} 连通性...")
        
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result == 0:
                print(f"   ✅ 端口 {port} 可连接")
                return True
            else:
                print(f"   ❌ 端口 {port} 不可连接")
                return False
                
        except Exception as e:
            print(f"   ❌ 端口 {port} 测试失败: {e}")
            return False
    
    def run(self):
        """运行端口获取流程"""
        print("🎯 比特浏览器调试端口获取器")
        print("=" * 50)
        
        # 1. 查找API端口
        if not self.find_api_port():
            print("❌ 无法找到比特浏览器API端口")
            print("💡 请确保比特浏览器正在运行")
            return []
        
        print(f"✅ API地址: {self.api_base}")
        
        # 2. 尝试无认证获取端口
        ports = self.try_get_ports_without_auth()
        
        if ports:
            print(f"🎉 无需认证即可获取到 {len(ports)} 个调试端口!")
        else:
            print("⚠️ 无认证获取失败，需要API Token")
            
            # 3. 请求API Token
            print("\n💡 请从比特浏览器获取API Token:")
            print("   1. 打开比特浏览器")
            print("   2. 点击 'Local API' 标签")
            print("   3. 复制完整的 API Token")
            
            token = input("\n请输入API Token (留空跳过): ").strip()
            
            if token:
                ports = self.get_ports_with_auth(token)
                self.api_token = token
            else:
                print("⚠️ 跳过认证，尝试其他方法...")
        
        # 4. 获取浏览器列表（用于参考）
        self.get_browser_list(self.api_token)
        
        # 5. 测试端口连通性
        if ports:
            print(f"\n🔍 测试 {len(ports)} 个端口的连通性...")
            available_ports = []
            
            for port in ports:
                if self.test_port_connectivity(port):
                    available_ports.append(port)
            
            print(f"\n✅ 可用端口: {available_ports}")
            
            # 6. 保存结果
            if available_ports:
                result = {
                    'api_base': self.api_base,
                    'api_token': self.api_token,
                    'debug_ports': available_ports,
                    'all_ports': ports,
                    'timestamp': time.time()
                }
                
                with open('debug_ports.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print(f"💾 结果已保存到 debug_ports.json")
                print(f"🎯 推荐使用端口: {available_ports[0]}")
                
                return available_ports
        
        print("❌ 未获取到可用的调试端口")
        return []

if __name__ == "__main__":
    getter = BitBrowserPortGetter()
    ports = getter.run()
    
    if ports:
        print(f"\n🚀 下一步可以使用以下命令测试端口:")
        for port in ports[:3]:  # 只显示前3个
            print(f"   python test_debug_port.py {port}")
    else:
        print("\n💡 如果仍然无法获取端口，建议使用浏览器控制台方案")
