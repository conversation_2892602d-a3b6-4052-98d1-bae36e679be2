#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 获取比特浏览器19号窗口调试端口
使用正确的API Token连接比特浏览器API
"""

import requests
import json
import time

class BitBrowserWindow19:
    def __init__(self):
        self.api_base = "http://127.0.0.1:56906"
        self.api_token = "ca28eeSca6de4d209182a83ae16a2044"
        
        self.headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': self.api_token
        }
        
    def test_api_connection(self):
        """测试API连接"""
        print("🔍 测试比特浏览器API连接...")
        print(f"   API地址: {self.api_base}")
        print(f"   API Token: {self.api_token}")
        print(f"   请求头: X-API-KEY: {self.api_token}")
        
        try:
            # 测试基本连接
            response = requests.get(f"{self.api_base}/", timeout=5)
            print(f"   基本连接状态码: {response.status_code}")
            return True
        except Exception as e:
            print(f"   ❌ API连接失败: {e}")
            return False
    
    def get_browser_list(self):
        """获取浏览器窗口列表"""
        print("🔍 获取浏览器窗口列表...")
        
        url = f"{self.api_base}/browser/list"
        payload = {
            "page": 0,
            "pageSize": 50  # 增大页面大小确保能获取到19号窗口
        }
        
        try:
            response = requests.post(url, json=payload, headers=self.headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   API响应成功!")
                
                if data.get('success') and data.get('data'):
                    browsers = data['data']
                    print(f"   ✅ 获取到 {len(browsers)} 个浏览器窗口")
                    
                    # 查找19号窗口
                    window19 = None
                    for browser in browsers:
                        browser_id = browser.get('id', '')
                        name = browser.get('name', '')
                        status = browser.get('status', '')
                        
                        print(f"      📱 窗口: {name} (ID: {browser_id}) - 状态: {status}")
                        
                        # 检查是否是19号窗口
                        if '19' in name or browser_id.endswith('19') or name.endswith('19'):
                            window19 = browser
                            print(f"      🎯 找到19号窗口: {name}")
                    
                    return browsers, window19
                else:
                    print(f"   ❌ API响应格式错误: {data}")
                    return [], None
            else:
                print(f"   ❌ API请求失败: {response.text}")
                return [], None
                
        except Exception as e:
            print(f"   ❌ 获取窗口列表失败: {e}")
            return [], None
    
    def get_all_debug_ports(self):
        """获取所有调试端口"""
        print("🔍 获取所有调试端口...")

        url = f"{self.api_base}/browser/ports"

        try:
            # 根据API文档，这个接口不需要请求体
            response = requests.post(url, headers=self.headers, timeout=10)
            print(f"   状态码: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"   API响应: {json.dumps(data, indent=2, ensure_ascii=False)}")

                if data.get('success') and data.get('data'):
                    ports_data = data['data']
                    print(f"   ✅ 获取到 {len(ports_data)} 个调试端口:")

                    for browser_id, port in ports_data.items():
                        print(f"      🌐 浏览器 {browser_id} -> 端口 {port}")

                    return ports_data
                else:
                    print(f"   ❌ 端口数据格式错误: {data}")
                    return {}
            else:
                print(f"   ❌ 获取端口失败: {response.text}")
                return {}

        except Exception as e:
            print(f"   ❌ 获取端口异常: {e}")
            return {}
    
    def find_window19_port(self, browsers, ports_data):
        """查找19号窗口的调试端口"""
        print("🎯 查找19号窗口的调试端口...")
        
        # 方法1: 通过窗口列表查找19号窗口
        window19_candidates = []
        for browser in browsers:
            browser_id = browser.get('id', '')
            name = browser.get('name', '')
            
            # 检查是否可能是19号窗口
            if ('19' in name or 
                browser_id.endswith('19') or 
                name.endswith('19') or
                '19' in browser_id):
                window19_candidates.append({
                    'browser': browser,
                    'browser_id': browser_id,
                    'name': name
                })
                print(f"   🎯 19号窗口候选: {name} (ID: {browser_id})")
        
        # 方法2: 在端口数据中查找对应的端口
        window19_ports = []
        for candidate in window19_candidates:
            browser_id = candidate['browser_id']
            
            # 在端口数据中查找匹配的ID
            for port_browser_id, port in ports_data.items():
                if (browser_id == port_browser_id or 
                    browser_id in port_browser_id or 
                    port_browser_id in browser_id):
                    window19_ports.append({
                        'browser_id': browser_id,
                        'port_browser_id': port_browser_id,
                        'port': int(port),
                        'name': candidate['name']
                    })
                    print(f"   ✅ 找到19号窗口端口: {candidate['name']} -> 端口 {port}")
        
        return window19_ports
    
    def run(self):
        """运行获取流程"""
        print("🎯 比特浏览器19号窗口调试端口获取器")
        print("=" * 60)
        
        # 1. 测试API连接
        if not self.test_api_connection():
            print("❌ API连接失败")
            return None
        
        # 2. 获取浏览器窗口列表
        browsers, window19 = self.get_browser_list()
        
        if not browsers:
            print("❌ 无法获取浏览器窗口列表")
            return None
        
        # 3. 获取所有调试端口
        ports_data = self.get_all_debug_ports()
        
        if not ports_data:
            print("❌ 无法获取调试端口")
            return None
        
        # 4. 查找19号窗口的端口
        window19_ports = self.find_window19_port(browsers, ports_data)
        
        # 5. 输出结果
        print("\n" + "=" * 60)
        print("🎉 查找完成!")
        
        if window19_ports:
            print(f"✅ 找到 {len(window19_ports)} 个19号窗口端口:")
            
            for wp in window19_ports:
                print(f"   🎯 窗口: {wp['name']}")
                print(f"      📱 浏览器ID: {wp['browser_id']}")
                print(f"      🔌 调试端口: {wp['port']}")
                print()
            
            # 推荐使用第一个端口
            recommended_port = window19_ports[0]['port']
            print(f"🚀 推荐使用端口: {recommended_port}")
            
            # 保存结果
            result = {
                'api_token': self.api_token,
                'window19_ports': window19_ports,
                'recommended_port': recommended_port,
                'all_browsers': browsers,
                'all_ports': ports_data,
                'timestamp': time.time()
            }
            
            with open('window19_port.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            print(f"💾 结果已保存到 window19_port.json")
            print(f"\n🚀 下一步测试命令:")
            print(f"   python test_debug_port.py {recommended_port}")
            
            return recommended_port
        else:
            print("❌ 未找到19号窗口的调试端口")
            print("💡 可能的原因:")
            print("   1. 19号窗口未打开")
            print("   2. 19号窗口未启用调试模式")
            print("   3. 窗口命名不包含'19'")
            
            print(f"\n📊 所有可用端口:")
            for browser_id, port in ports_data.items():
                print(f"   🔌 {browser_id} -> 端口 {port}")
            
            return None

if __name__ == "__main__":
    getter = BitBrowserWindow19()
    port = getter.run()
