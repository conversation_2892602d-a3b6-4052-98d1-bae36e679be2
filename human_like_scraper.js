/**
 * 🤖 人工滑动模拟爬虫
 * 模拟真实用户行为，滑动加载所有1472条评论
 */

const puppeteer = require('puppeteer-core');
const axios = require('axios');
const fs = require('fs');

class HumanLikeScraper {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.targetWindowSeq = 19;
        this.browser = null;
        this.page = null;
        this.comments = [];
        this.targetCommentCount = 1472;
    }

    /**
     * 连接到比特浏览器
     */
    async connectToBrowser() {
        console.log('🔗 连接到比特浏览器...');
        
        try {
            // 获取窗口信息
            const windowResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            const browsers = windowResponse.data.data.list;
            const window19 = browsers.find(browser => browser.seq === this.targetWindowSeq);
            
            if (!window19) {
                throw new Error('未找到19号窗口');
            }

            // 获取调试端口
            const portsResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            const ports = portsResponse.data.data;
            const debugPort = ports[window19.id];
            
            if (!debugPort) {
                throw new Error('19号窗口未获取到调试端口');
            }

            // 获取浏览器WebSocket端点
            const versionResponse = await axios.get(`http://localhost:${debugPort}/json/version`);
            const browserWSEndpoint = versionResponse.data.webSocketDebuggerUrl;
            
            this.browser = await puppeteer.connect({
                browserWSEndpoint,
                defaultViewport: null
            });
            
            // 获取小红书页面
            const browserPages = await this.browser.pages();
            this.page = browserPages.find(page => page.url().includes('xiaohongshu.com/explore/'));
            
            if (!this.page) {
                this.page = browserPages.find(page => page.url().includes('xiaohongshu.com'));
            }
            
            if (!this.page) {
                throw new Error('未找到小红书页面');
            }
            
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            console.log('✅ 连接成功!');
            console.log(`📄 页面: ${title}`);
            
            return true;
            
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    /**
     * 随机延迟 - 模拟人类操作间隔
     */
    async randomDelay(min = 1000, max = 3000) {
        const delay = Math.random() * (max - min) + min;
        await new Promise(resolve => setTimeout(resolve, delay));
    }

    /**
     * 模拟人工鼠标移动
     */
    async humanMouseMove(x, y) {
        // 获取当前鼠标位置
        const currentPos = await this.page.evaluate(() => {
            return { x: window.mouseX || 0, y: window.mouseY || 0 };
        });

        // 计算移动路径
        const steps = Math.max(Math.abs(x - currentPos.x), Math.abs(y - currentPos.y)) / 10;
        
        for (let i = 0; i <= steps; i++) {
            const progress = i / steps;
            const currentX = currentPos.x + (x - currentPos.x) * progress;
            const currentY = currentPos.y + (y - currentPos.y) * progress;
            
            await this.page.mouse.move(currentX, currentY);
            await this.randomDelay(10, 30);
        }
    }

    /**
     * 模拟人工滚动
     */
    async humanScroll(distance = 300) {
        // 随机滚动距离 (模拟不规律的滚动)
        const actualDistance = distance + (Math.random() - 0.5) * 100;
        
        // 模拟鼠标滚轮滚动
        await this.page.mouse.wheel({ deltaY: actualDistance });
        
        // 随机停顿
        await this.randomDelay(500, 2000);
    }

    /**
     * 模拟人工点击"展开更多"按钮
     */
    async clickExpandButtons() {
        try {
            // 查找各种可能的"展开更多"按钮
            const expandSelectors = [
                'button:contains("展开")',
                'button:contains("更多")',
                'button:contains("加载更多")',
                '[class*="expand"]',
                '[class*="more"]',
                '[class*="load-more"]',
                'span:contains("展开")',
                'div:contains("展开")',
                '.expand-btn',
                '.more-btn'
            ];

            let clickCount = 0;
            
            for (const selector of expandSelectors) {
                try {
                    const elements = await this.page.$$(selector);
                    
                    for (const element of elements) {
                        const isVisible = await element.isIntersectingViewport();
                        if (isVisible) {
                            // 模拟人工移动到按钮
                            const box = await element.boundingBox();
                            if (box) {
                                await this.humanMouseMove(
                                    box.x + box.width / 2, 
                                    box.y + box.height / 2
                                );
                                
                                // 随机停顿
                                await this.randomDelay(200, 800);
                                
                                // 点击
                                await element.click();
                                clickCount++;
                                
                                console.log(`   🖱️ 点击了展开按钮 (${clickCount})`);
                                
                                // 等待内容加载
                                await this.randomDelay(1000, 3000);
                            }
                        }
                    }
                } catch (e) {
                    // 忽略单个选择器的错误
                    continue;
                }
            }
            
            return clickCount;
        } catch (error) {
            console.log('   ⚠️ 点击展开按钮时出错:', error.message);
            return 0;
        }
    }

    /**
     * 模拟人工阅读停顿
     */
    async simulateReading() {
        // 模拟用户阅读评论的停顿时间
        const readingTime = Math.random() * 2000 + 1000; // 1-3秒
        console.log(`   👀 模拟阅读停顿 ${Math.round(readingTime)}ms`);
        await new Promise(resolve => setTimeout(resolve, readingTime));
    }

    /**
     * 智能滚动策略 - 模拟真实用户行为
     */
    async intelligentScroll() {
        console.log('🎭 开始智能人工滚动，目标加载所有评论...');
        
        let scrollCount = 0;
        let lastCommentCount = 0;
        let noProgressCount = 0;
        let totalClickCount = 0;
        
        // 获取页面尺寸
        const pageHeight = await this.page.evaluate(() => window.innerHeight);
        
        while (scrollCount < 200 && noProgressCount < 15) {
            console.log(`\n🔄 第 ${scrollCount + 1} 轮操作:`);
            
            // 1. 检查当前评论数量
            const currentCommentCount = await this.page.evaluate(() => {
                const commentElements = document.querySelectorAll(
                    '[class*="comment"], [class*="Comment"], [class*="reply"], [class*="Reply"]'
                );
                return commentElements.length;
            });
            
            console.log(`   📊 当前评论元素: ${currentCommentCount}`);
            
            // 2. 模拟人工阅读当前可见内容
            if (scrollCount % 3 === 0) {
                await this.simulateReading();
            }
            
            // 3. 尝试点击展开按钮
            const clickCount = await this.clickExpandButtons();
            totalClickCount += clickCount;
            
            // 4. 模拟人工滚动
            const scrollDistance = Math.random() * 400 + 200; // 200-600px
            await this.humanScroll(scrollDistance);
            
            // 5. 随机进行一些人工操作
            if (Math.random() < 0.3) {
                // 30%概率进行额外操作
                const action = Math.random();
                if (action < 0.5) {
                    // 向上滚动一点 (模拟用户回看)
                    console.log('   ⬆️ 模拟回看，向上滚动');
                    await this.humanScroll(-100);
                    await this.randomDelay(500, 1500);
                } else {
                    // 停顿更长时间 (模拟用户思考)
                    console.log('   🤔 模拟思考停顿');
                    await this.randomDelay(2000, 5000);
                }
            }
            
            // 6. 检查进度
            if (currentCommentCount > lastCommentCount) {
                noProgressCount = 0;
                console.log(`   ✅ 新增 ${currentCommentCount - lastCommentCount} 个评论元素`);
            } else {
                noProgressCount++;
                console.log(`   ⚠️ 无新增评论 (${noProgressCount}/15)`);
            }
            
            lastCommentCount = currentCommentCount;
            scrollCount++;
            
            // 7. 每10次操作显示进度
            if (scrollCount % 10 === 0) {
                const progress = Math.min((currentCommentCount / this.targetCommentCount) * 100, 100);
                console.log(`\n📈 进度报告:`);
                console.log(`   滚动次数: ${scrollCount}`);
                console.log(`   点击次数: ${totalClickCount}`);
                console.log(`   评论元素: ${currentCommentCount}`);
                console.log(`   目标进度: ${progress.toFixed(1)}%`);
            }
            
            // 8. 如果接近目标数量，增加操作频率
            if (currentCommentCount > this.targetCommentCount * 0.8) {
                console.log('   🎯 接近目标，增加操作频率');
                await this.clickExpandButtons();
                await this.randomDelay(500, 1000);
            }
        }
        
        console.log(`\n📄 智能滚动完成！`);
        console.log(`   总滚动次数: ${scrollCount}`);
        console.log(`   总点击次数: ${totalClickCount}`);
        console.log(`   最终评论元素: ${lastCommentCount}`);
        
        // 滚动回顶部
        console.log('🔝 滚动回顶部...');
        await this.page.evaluate(() => window.scrollTo(0, 0));
        await this.randomDelay(2000, 3000);
    }

    /**
     * 高级评论提取
     */
    async advancedExtractComments() {
        console.log('🔍 开始高级评论提取...');
        
        try {
            const comments = await this.page.evaluate(() => {
                const extractedComments = [];
                
                // 更全面的选择器策略
                const strategies = [
                    // 策略1: 直接查找评论容器
                    {
                        name: 'comment_containers',
                        selectors: [
                            '[class*="comment-item"]',
                            '[class*="comment-container"]',
                            '[class*="user-comment"]',
                            '[data-testid*="comment"]'
                        ]
                    },
                    // 策略2: 查找交互元素
                    {
                        name: 'interaction_elements',
                        selectors: [
                            '[class*="interaction"]',
                            '[class*="comment-list"]',
                            '[class*="reply-list"]'
                        ]
                    },
                    // 策略3: 通用评论选择器
                    {
                        name: 'generic_comments',
                        selectors: [
                            '[class*="comment"]',
                            '[class*="Comment"]',
                            '[class*="reply"]',
                            '[class*="Reply"]'
                        ]
                    }
                ];
                
                // 执行每种策略
                strategies.forEach(strategy => {
                    strategy.selectors.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            console.log(`策略 ${strategy.name} - 选择器 ${selector}: ${elements.length} 个元素`);
                            
                            elements.forEach((element, index) => {
                                const text = element.textContent?.trim();
                                if (text && text.length > 10) {
                                    // 智能解析评论结构
                                    const lines = text.split('\n').filter(line => line.trim());
                                    
                                    let username = '';
                                    let content = '';
                                    let time = '';
                                    let likes = '';
                                    
                                    // 解析逻辑
                                    for (let i = 0; i < lines.length; i++) {
                                        const line = lines[i].trim();
                                        
                                        // 跳过无用行
                                        if (line.length < 2 || 
                                            /^(赞|回复|展开|收起|点赞|分享|举报)$/.test(line)) {
                                            continue;
                                        }
                                        
                                        // 时间识别
                                        if (/\d{2}-\d{2}|\d+[分小天月年]前/.test(line)) {
                                            time = line;
                                            continue;
                                        }
                                        
                                        // 点赞数识别
                                        if (/^\d+$/.test(line) && parseInt(line) > 0) {
                                            likes = line;
                                            continue;
                                        }
                                        
                                        // 用户名识别 (通常较短且在前面)
                                        if (!username && line.length < 50 && 
                                            !line.includes('条评论') && 
                                            !line.includes('回复') &&
                                            !line.includes('展开')) {
                                            username = line;
                                        } else if (line.length > 5 && line.length < 2000) {
                                            // 评论内容
                                            if (content) {
                                                content += ' ' + line;
                                            } else {
                                                content = line;
                                            }
                                        }
                                    }
                                    
                                    // 清理内容
                                    if (content) {
                                        content = content
                                            .replace(/展开\s*\d+\s*条回复/g, '')
                                            .replace(/\d+赞回复/g, '')
                                            .replace(/赞回复$/g, '')
                                            .replace(/回复$/g, '')
                                            .trim();
                                        
                                        if (content.length > 5 && content.length < 2000) {
                                            extractedComments.push({
                                                username: username || '未知用户',
                                                content: content,
                                                time: time || '',
                                                likes: likes || '',
                                                method: `${strategy.name}_${selector}`,
                                                element_index: index,
                                                timestamp: new Date().toISOString()
                                            });
                                        }
                                    }
                                }
                            });
                        } catch (e) {
                            console.log(`选择器 ${selector} 处理失败:`, e.message);
                        }
                    });
                });
                
                // 文本分析策略
                const bodyText = document.body.textContent || '';
                const allLines = bodyText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
                
                for (let i = 0; i < allLines.length; i++) {
                    const line = allLines[i];
                    
                    // 查找时间模式
                    if (/\d{2}-\d{2}|\d+[分小天月年]前/.test(line) && line.length < 50) {
                        // 向前查找评论内容
                        for (let j = 1; j <= 5; j++) {
                            if (i - j >= 0) {
                                const contentLine = allLines[i - j];
                                if (contentLine.length > 10 && contentLine.length < 500 &&
                                    !/(赞|回复|展开|收起|点赞|分享|举报|条评论)$/.test(contentLine)) {
                                    
                                    // 查找用户名
                                    let username = '未知用户';
                                    if (i - j - 1 >= 0) {
                                        const userLine = allLines[i - j - 1];
                                        if (userLine.length > 1 && userLine.length < 30) {
                                            username = userLine;
                                        }
                                    }
                                    
                                    // 检查是否已存在
                                    const exists = extractedComments.some(comment => 
                                        comment.content.includes(contentLine.substring(0, 30))
                                    );
                                    
                                    if (!exists) {
                                        extractedComments.push({
                                            username: username,
                                            content: contentLine,
                                            time: line,
                                            likes: '',
                                            method: 'text_analysis',
                                            timestamp: new Date().toISOString()
                                        });
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
                
                // 去重处理
                const uniqueComments = [];
                const seenContents = new Set();
                
                for (const comment of extractedComments) {
                    const contentKey = comment.content.substring(0, 50);
                    if (!seenContents.has(contentKey) && 
                        comment.content.length > 5 && 
                        comment.content.length < 2000) {
                        seenContents.add(contentKey);
                        uniqueComments.push(comment);
                    }
                }
                
                return uniqueComments;
            });
            
            this.comments = comments;
            console.log(`✅ 高级提取完成，共获得 ${this.comments.length} 条评论`);
            
            // 显示提取统计
            const methodStats = {};
            this.comments.forEach(comment => {
                const method = comment.method.split('_')[0];
                methodStats[method] = (methodStats[method] || 0) + 1;
            });
            
            console.log('📊 提取方式统计:');
            Object.entries(methodStats).forEach(([method, count]) => {
                console.log(`   ${method}: ${count} 条`);
            });
            
            return this.comments.length > 0;
            
        } catch (error) {
            console.error('❌ 高级提取失败:', error.message);
            return false;
        }
    }

    /**
     * 保存为完美TXT格式
     */
    async savePerfectTXT() {
        if (this.comments.length === 0) {
            console.log('⚠️ 没有评论数据可保存');
            return;
        }
        
        try {
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            // 生成完美的TXT内容
            let txtContent = '';
            txtContent += '='.repeat(80) + '\n';
            txtContent += '小红书评论完整数据 (人工滑动版)\n';
            txtContent += '='.repeat(80) + '\n';
            txtContent += `页面标题: ${title}\n`;
            txtContent += `页面链接: ${currentUrl}\n`;
            txtContent += `爬取时间: ${new Date().toLocaleString('zh-CN')}\n`;
            txtContent += `评论总数: ${this.comments.length} 条\n`;
            txtContent += `目标数量: ${this.targetCommentCount} 条\n`;
            txtContent += `完成度: ${Math.round((this.comments.length / this.targetCommentCount) * 100)}%\n`;
            txtContent += `爬取方式: 模拟人工滑动 + 智能点击\n`;
            txtContent += '='.repeat(80) + '\n\n';
            
            // 按时间排序
            const sortedComments = this.comments.sort((a, b) => {
                if (a.time && b.time) {
                    return a.time.localeCompare(b.time);
                }
                return 0;
            });
            
            // 添加评论内容
            sortedComments.forEach((comment, index) => {
                txtContent += `【评论 ${index + 1}】\n`;
                txtContent += `用户名: ${comment.username}\n`;
                if (comment.time) {
                    txtContent += `发布时间: ${comment.time}\n`;
                }
                if (comment.likes) {
                    txtContent += `点赞数: ${comment.likes}\n`;
                }
                txtContent += `评论内容: ${comment.content}\n`;
                txtContent += `提取方式: ${comment.method}\n`;
                txtContent += '-'.repeat(60) + '\n\n';
            });
            
            // 添加详细统计
            txtContent += '='.repeat(80) + '\n';
            txtContent += '详细统计信息\n';
            txtContent += '='.repeat(80) + '\n';
            
            // 按提取方式统计
            const methodStats = {};
            this.comments.forEach(comment => {
                methodStats[comment.method] = (methodStats[comment.method] || 0) + 1;
            });
            
            txtContent += '按提取方式统计:\n';
            Object.entries(methodStats).sort((a, b) => b[1] - a[1]).forEach(([method, count]) => {
                const percentage = Math.round((count / this.comments.length) * 100);
                txtContent += `  ${method}: ${count} 条 (${percentage}%)\n`;
            });
            
            // 按时间统计
            const timeStats = {};
            this.comments.forEach(comment => {
                if (comment.time) {
                    const timeKey = comment.time.includes('-') ? comment.time.substring(0, 5) : 
                                   comment.time.includes('前') ? comment.time : '其他';
                    timeStats[timeKey] = (timeStats[timeKey] || 0) + 1;
                }
            });
            
            if (Object.keys(timeStats).length > 0) {
                txtContent += '\n按时间统计:\n';
                Object.entries(timeStats).sort().forEach(([time, count]) => {
                    txtContent += `  ${time}: ${count} 条\n`;
                });
            }
            
            // 内容长度统计
            const lengthRanges = {
                '短评论(1-20字)': 0,
                '中等评论(21-100字)': 0,
                '长评论(101-300字)': 0,
                '超长评论(300字以上)': 0
            };
            
            this.comments.forEach(comment => {
                const len = comment.content.length;
                if (len <= 20) lengthRanges['短评论(1-20字)']++;
                else if (len <= 100) lengthRanges['中等评论(21-100字)']++;
                else if (len <= 300) lengthRanges['长评论(101-300字)']++;
                else lengthRanges['超长评论(300字以上)']++;
            });
            
            txtContent += '\n按内容长度统计:\n';
            Object.entries(lengthRanges).forEach(([range, count]) => {
                if (count > 0) {
                    const percentage = Math.round((count / this.comments.length) * 100);
                    txtContent += `  ${range}: ${count} 条 (${percentage}%)\n`;
                }
            });
            
            txtContent += '\n' + '='.repeat(80) + '\n';
            txtContent += '技术说明:\n';
            txtContent += '- 使用模拟人工滑动技术，包含随机停顿、鼠标移动、点击操作\n';
            txtContent += '- 智能识别并点击"展开更多"按钮\n';
            txtContent += '- 多策略评论提取，确保数据完整性\n';
            txtContent += '- 自动去重处理，避免重复数据\n';
            txtContent += '='.repeat(80) + '\n';
            
            // 保存文件
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const txtFilename = `xiaohongshu_human_like_comments_${timestamp}.txt`;
            
            fs.writeFileSync(txtFilename, txtContent, 'utf8');
            
            console.log(`💾 人工滑动版TXT文件已保存: ${txtFilename}`);
            console.log(`📊 总计 ${this.comments.length} 条评论 (目标: ${this.targetCommentCount} 条)`);
            
            return txtFilename;
            
        } catch (error) {
            console.error('❌ 保存文件失败:', error.message);
        }
    }

    /**
     * 运行人工滑动爬虫
     */
    async run() {
        console.log('🤖 人工滑动模拟爬虫 - 目标1472条评论');
        console.log('='.repeat(80));
        
        try {
            // 1. 连接浏览器
            await this.connectToBrowser();
            
            // 2. 等待页面稳定
            console.log('⏳ 等待页面稳定...');
            await this.randomDelay(3000, 5000);
            
            // 3. 智能滚动加载所有评论
            await this.intelligentScroll();
            
            // 4. 高级提取所有评论
            const success = await this.advancedExtractComments();
            
            if (!success) {
                console.log('❌ 未能提取到评论数据');
                return false;
            }
            
            // 5. 保存为完美TXT格式
            const txtFile = await this.savePerfectTXT();
            
            console.log('\n🎉 人工滑动爬取完成!');
            console.log(`📁 请查看生成的TXT文件: ${txtFile}`);
            console.log(`📊 成功获取 ${this.comments.length} / ${this.targetCommentCount} 条评论`);
            
            const completionRate = Math.round((this.comments.length / this.targetCommentCount) * 100);
            console.log(`🎯 完成度: ${completionRate}%`);
            
            if (completionRate >= 80) {
                console.log('🏆 优秀！获取了大部分评论数据！');
            } else if (completionRate >= 50) {
                console.log('👍 良好！获取了一半以上的评论数据！');
            } else {
                console.log('💡 建议：可能需要更长时间的滑动或手动展开更多评论');
            }
            
            return true;
            
        } catch (error) {
            console.error('❌ 人工滑动爬虫运行失败:', error.message);
            return false;
        } finally {
            if (this.browser) {
                console.log('🔒 断开浏览器连接...');
                await this.browser.disconnect();
            }
        }
    }
}

// 主函数
async function main() {
    console.log('🤖 人工滑动模拟爬虫启动器');
    console.log('='.repeat(80));
    
    console.log('🎭 功能特点:');
    console.log('   ✅ 模拟真实用户滑动行为');
    console.log('   ✅ 随机停顿和鼠标移动');
    console.log('   ✅ 智能点击展开按钮');
    console.log('   ✅ 多策略评论提取');
    console.log('   ✅ 自动去重和数据清理');
    
    const scraper = new HumanLikeScraper();
    await scraper.run();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = HumanLikeScraper;
