#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 交互式API Token测试器
帮助确认正确的API Token
"""

import requests
import json

def test_token(token):
    """测试API Token"""
    api_base = "http://127.0.0.1:56906"
    headers = {
        'Content-Type': 'application/json',
        'X-API-KEY': token
    }
    
    print(f"🔍 测试Token: {token}")
    
    # 测试端口接口
    try:
        url = f"{api_base}/browser/ports"
        response = requests.post(url, json={}, headers=headers, timeout=10)
        print(f"   端口接口状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ Token正确! 获取到数据: {data}")
                return True, data
            else:
                print(f"   ❌ API返回错误: {data}")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 测试窗口列表接口
    try:
        url = f"{api_base}/browser/list"
        payload = {"page": 0, "pageSize": 10}
        response = requests.post(url, json=payload, headers=headers, timeout=10)
        print(f"   窗口列表状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ Token正确! 窗口数量: {len(data.get('data', []))}")
                return True, data
            else:
                print(f"   ❌ API返回错误: {data}")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    return False, None

def main():
    print("🎯 比特浏览器API Token测试器")
    print("=" * 50)
    
    # 从截图中的可能Token值
    possible_tokens = [
        "ca28eeSca6de4d209182a83ae16a2044",  # 截图中显示的值
    ]
    
    print("📝 请提供正确的API Token:")
    print("   从比特浏览器 Local API 页面复制完整的Token值")
    print("   确保复制的是完整的字符串，没有多余的空格")
    
    # 让用户输入Token
    user_token = input("\n请粘贴API Token: ").strip()
    
    if user_token:
        possible_tokens.insert(0, user_token)
    
    # 测试每个可能的Token
    for i, token in enumerate(possible_tokens, 1):
        print(f"\n🔍 测试Token {i}: {token[:20]}...")
        
        success, data = test_token(token)
        
        if success:
            print(f"🎉 找到正确的Token!")
            
            # 保存正确的Token
            config = {
                'api_token': token,
                'api_base': 'http://127.0.0.1:56906',
                'test_result': data
            }
            
            with open('correct_token.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"💾 正确的Token已保存到 correct_token.json")
            print(f"🚀 现在可以使用此Token获取19号窗口端口")
            
            return token
    
    print("❌ 所有Token都测试失败")
    print("💡 请检查:")
    print("   1. 比特浏览器是否正在运行")
    print("   2. Local API是否已启用")
    print("   3. Token是否完整复制")
    print("   4. 是否有特殊字符或空格")
    
    return None

if __name__ == "__main__":
    main()
