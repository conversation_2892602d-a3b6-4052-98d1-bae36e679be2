#!/usr/bin/env node

/**
 * 🎯 手动评论提取器
 * 基于你提供的页面内容进行评论提取
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class ManualCommentExtractor {
    constructor() {
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        this.config = {
            targetComments: 1472
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    // 从文件或输入获取页面内容
    async getPageContent() {
        console.log('📄 获取页面内容...');
        
        // 检查是否有保存的页面内容文件
        const possibleFiles = [
            'page_content.txt',
            'xiaohongshu_content.txt',
            'comments_raw.txt'
        ];
        
        for (const filename of possibleFiles) {
            const filepath = path.join(this.outputDir, filename);
            if (fs.existsSync(filepath)) {
                console.log(`📁 找到保存的内容文件: ${filename}`);
                const content = fs.readFileSync(filepath, 'utf8');
                return content;
            }
        }
        
        // 如果没有文件，提示用户手动输入
        console.log('💡 请将小红书页面的内容复制到文件中，或者直接粘贴：');
        console.log('   1. 在浏览器中按 Ctrl+A 全选页面内容');
        console.log('   2. 按 Ctrl+C 复制');
        console.log('   3. 将内容保存到 scraped_data/page_content.txt 文件中');
        console.log('   4. 或者直接在下面粘贴内容（输入 END 结束）：\n');
        
        return await this.getUserInput();
    }

    // 获取用户输入
    async getUserInput() {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        let content = '';
        let lineCount = 0;
        
        return new Promise((resolve) => {
            console.log('请粘贴页面内容（输入 END 结束）：');
            
            rl.on('line', (line) => {
                if (line.trim() === 'END') {
                    rl.close();
                    resolve(content);
                } else {
                    content += line + '\n';
                    lineCount++;
                    
                    if (lineCount % 100 === 0) {
                        console.log(`已输入 ${lineCount} 行...`);
                    }
                }
            });
        });
    }

    // 超级评论解析器
    superParseComments(pageText) {
        console.log('🧠 开始超级评论解析...');
        console.log(`📄 页面文本长度: ${pageText.length}`);
        
        const allComments = [];
        
        // 使用所有可能的解析策略
        const strategies = [
            { name: '时间戳解析', method: () => this.parseByTimestamp(pageText) },
            { name: '用户名解析', method: () => this.parseByUsername(pageText) },
            { name: '关键词解析', method: () => this.parseByKeywords(pageText) },
            { name: '表情符号解析', method: () => this.parseByEmoji(pageText) },
            { name: '数字模式解析', method: () => this.parseByNumbers(pageText) },
            { name: '结构化解析', method: () => this.parseByStructure(pageText) },
            { name: '混合模式解析', method: () => this.parseByMixed(pageText) }
        ];
        
        strategies.forEach(strategy => {
            try {
                console.log(`🔍 执行${strategy.name}...`);
                const strategyComments = strategy.method();
                console.log(`   📝 ${strategy.name}提取到 ${strategyComments.length} 条评论`);
                allComments.push(...strategyComments);
            } catch (error) {
                console.log(`   ❌ ${strategy.name}失败: ${error.message}`);
            }
        });
        
        console.log(`📊 总计收集到 ${allComments.length} 条原始评论`);
        
        // 超级去重和清理
        const uniqueComments = this.superDeduplication(allComments);
        
        console.log(`✅ 超级解析完成，最终提取到 ${uniqueComments.length} 条评论`);
        return uniqueComments;
    }

    // 时间戳解析
    parseByTimestamp(pageText) {
        const comments = [];
        
        // 更全面的时间模式
        const timePatterns = [
            /(\d{2}-\d{2})/g,
            /(\d+小时前)/g,
            /(\d+分钟前)/g,
            /(\d+天前)/g,
            /(\d{4}-\d{2}-\d{2})/g,
            /(\d{1,2}:\d{2})/g
        ];
        
        timePatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                const timeStr = match[1];
                const timeIndex = match.index;
                
                // 提取时间前后的内容
                const startIndex = Math.max(0, timeIndex - 200);
                const endIndex = Math.min(pageText.length, timeIndex + 1000);
                const contextText = pageText.substring(startIndex, endIndex);
                
                if (contextText.length > 30 && this.looksLikeComment(contextText)) {
                    const comment = this.createCommentFromContext(contextText, comments.length + 1, 'timestamp');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });
        
        return comments;
    }

    // 用户名解析
    parseByUsername(pageText) {
        const comments = [];
        
        // 用户名模式
        const usernamePatterns = [
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(\d{2}-\d{2})/g,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(求带|宝子|学姐)/g,
            /小红薯([A-F0-9]{8,})/g,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(\d+小时前|\d+分钟前|\d+天前)/g
        ];
        
        usernamePatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                const username = match[1];
                const matchIndex = match.index;
                
                // 提取用户名前后的内容
                const startIndex = Math.max(0, matchIndex - 100);
                const endIndex = Math.min(pageText.length, matchIndex + 800);
                const contextText = pageText.substring(startIndex, endIndex);
                
                if (contextText.length > 20 && this.looksLikeComment(contextText)) {
                    const comment = this.createCommentFromContext(contextText, comments.length + 1, 'username');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });
        
        return comments;
    }

    // 关键词解析
    parseByKeywords(pageText) {
        const comments = [];
        
        // 兼职相关关键词
        const keywords = [
            '求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯',
            'Carina', '广州', '工作', '赚钱', '日入', '月入', '陪聊',
            '直播', '客服', '代理', '抢票', '演唱会', '游戏', '试玩',
            '大学生', '暑假工', '兼织', '专米', '推荐', '软件'
        ];
        
        keywords.forEach(keyword => {
            const regex = new RegExp(keyword, 'g');
            let match;
            
            while ((match = regex.exec(pageText)) !== null) {
                const keywordIndex = match.index;
                
                // 提取关键词前后的内容
                const startIndex = Math.max(0, keywordIndex - 150);
                const endIndex = Math.min(pageText.length, keywordIndex + 600);
                const contextText = pageText.substring(startIndex, endIndex);
                
                if (contextText.length > 25 && this.looksLikeComment(contextText)) {
                    const comment = this.createCommentFromContext(contextText, comments.length + 1, 'keyword');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });
        
        return comments;
    }

    // 表情符号解析
    parseByEmoji(pageText) {
        const comments = [];
        
        // 小红书常见表情符号模式
        const emojiPattern = /[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍🥪😁🥭🐡🥒😛🧁🍙]+/g;
        let match;
        
        while ((match = emojiPattern.exec(pageText)) !== null) {
            const emojiIndex = match.index;
            
            // 提取表情符号前后的内容
            const startIndex = Math.max(0, emojiIndex - 50);
            const endIndex = Math.min(pageText.length, emojiIndex + 500);
            const contextText = pageText.substring(startIndex, endIndex);
            
            if (contextText.length > 20 && this.looksLikeComment(contextText)) {
                const comment = this.createCommentFromContext(contextText, comments.length + 1, 'emoji');
                if (comment) {
                    comments.push(comment);
                }
            }
        }
        
        return comments;
    }

    // 数字模式解析
    parseByNumbers(pageText) {
        const comments = [];
        
        // 数字相关模式（点赞、回复、收入等）
        const numberPatterns = [
            /(\d+)\s*赞/g,
            /(\d+)\s*回复/g,
            /日入\s*(\d+)/g,
            /月入\s*(\d+)/g,
            /(\d+)\s*元/g,
            /(\d+)\s*块/g,
            /一天\s*(\d+)/g
        ];
        
        numberPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                const numberIndex = match.index;
                
                // 提取数字前后的内容
                const startIndex = Math.max(0, numberIndex - 120);
                const endIndex = Math.min(pageText.length, numberIndex + 400);
                const contextText = pageText.substring(startIndex, endIndex);
                
                if (contextText.length > 15 && this.looksLikeComment(contextText)) {
                    const comment = this.createCommentFromContext(contextText, comments.length + 1, 'number');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });
        
        return comments;
    }

    // 结构化解析
    parseByStructure(pageText) {
        const comments = [];
        const lines = pageText.split('\n').map(line => line.trim()).filter(line => line.length > 3);
        
        let currentComment = null;
        let commentIndex = 0;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            if (this.shouldSkipLine(line)) continue;
            
            if (this.isCommentStart(line)) {
                // 保存前一个评论
                if (currentComment && this.isValidCommentContent(currentComment.content)) {
                    const comment = this.createCommentFromContext(currentComment.content, ++commentIndex, 'structure');
                    if (comment) {
                        comments.push(comment);
                    }
                }
                
                // 开始新评论
                currentComment = {
                    content: line,
                    lines: [line]
                };
            } else if (currentComment && line.length > 2) {
                currentComment.content += ' ' + line;
                currentComment.lines.push(line);
                
                // 限制评论长度
                if (currentComment.content.length > 2000) {
                    const comment = this.createCommentFromContext(currentComment.content, ++commentIndex, 'structure');
                    if (comment) {
                        comments.push(comment);
                    }
                    currentComment = null;
                }
            }
        }
        
        // 处理最后一个评论
        if (currentComment && this.isValidCommentContent(currentComment.content)) {
            const comment = this.createCommentFromContext(currentComment.content, ++commentIndex, 'structure');
            if (comment) {
                comments.push(comment);
            }
        }
        
        return comments;
    }

    // 混合模式解析
    parseByMixed(pageText) {
        const comments = [];
        
        // 混合模式：寻找包含多个特征的文本段
        const mixedPatterns = [
            // 用户名 + 时间 + 内容
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})[^A-Za-z0-9_\u4e00-\u9fff]*(\d{2}-\d{2})[^A-Za-z0-9_\u4e00-\u9fff]*([^]{20,800})/g,
            
            // 表情 + 内容
            /[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([^]{15,500})/g,
            
            // 关键词 + 内容
            /(求带|宝子|学姐|兼职|聊天员)[^]{10,400}/g
        ];
        
        mixedPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                const matchText = match[0];
                
                if (matchText.length > 25 && this.looksLikeComment(matchText)) {
                    const comment = this.createCommentFromContext(matchText, comments.length + 1, 'mixed');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });
        
        return comments;
    }

    // 判断是否看起来像评论
    looksLikeComment(text) {
        // 评论特征检查
        const commentFeatures = [
            // 包含时间格式
            /\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前/,

            // 包含用户相关词汇
            /求带|宝子|学姐|兼职|聊天员|陪玩/,

            // 包含表情符号
            /[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/,

            // 包含数字（点赞、回复等）
            /\d+赞|\d+回复/,

            // 包含用户名模式
            /[A-Za-z0-9_\u4e00-\u9fff]{2,20}\s*\d{2}-\d{2}/
        ];

        // 至少满足一个特征
        const hasFeature = commentFeatures.some(pattern => pattern.test(text));

        // 排除明显不是评论的内容
        const excludePatterns = [
            /沪ICP备|营业执照|公网安备|增值电信|医疗器械/,
            /window\.|function|console\.|document\./,
            /创作服务|直播管理|专业号|商家入驻/,
            /举报电话|举报中心|网络文化/
        ];

        const isExcluded = excludePatterns.some(pattern => pattern.test(text));

        return hasFeature && !isExcluded && text.length >= 10 && text.length <= 2000;
    }

    // 从上下文创建评论对象
    createCommentFromContext(text, id, source) {
        const comment = {
            id: id,
            userId: '',
            username: '',
            content: '',
            time: '',
            likes: 0,
            replyCount: 0,
            isAuthor: text.includes('作者'),
            isPinned: text.includes('置顶'),
            extractedInfo: {
                source: source,
                originalLength: text.length
            }
        };

        // 提取时间
        const timePatterns = [
            /(\d{2}-\d{2})/,
            /(\d+小时前)/,
            /(\d+分钟前)/,
            /(\d+天前)/
        ];

        for (const pattern of timePatterns) {
            const match = text.match(pattern);
            if (match) {
                comment.time = match[1];
                break;
            }
        }

        // 提取用户名
        const userPatterns = [
            /^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带/,
            /^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})/,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*学姐/,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*宝子/
        ];

        for (const pattern of userPatterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                let username = match[1].trim();
                // 清理用户名
                username = username.replace(/[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/g, '');
                if (username.length >= 2 && username.length <= 20) {
                    comment.username = username;
                    break;
                }
            }
        }

        // 清理内容
        let content = text;

        // 移除时间
        content = content.replace(/\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前/g, '');

        // 移除表情符号开头
        content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');

        // 移除标识词
        content = content.replace(/作者|置顶评论|回复|赞|仅自己可见/g, '');

        // 移除用户名
        if (comment.username) {
            content = content.replace(new RegExp(comment.username, 'g'), '');
        }

        // 清理空格和特殊字符
        content = content.replace(/\s+/g, ' ').trim();
        content = content.replace(/^[^\w\u4e00-\u9fff]+/, '').trim();

        comment.content = content;

        // 提取数字信息
        const likePatterns = [
            /(\d+)\s*赞/,
            /赞\s*(\d+)/,
            /❤️\s*(\d+)/
        ];

        for (const pattern of likePatterns) {
            const match = text.match(pattern);
            if (match) {
                const num = parseInt(match[1]);
                if (num > 0 && num < 50000) {
                    comment.likes = num;
                    break;
                }
            }
        }

        const replyPatterns = [
            /(\d+)\s*回复/,
            /回复\s*(\d+)/,
            /展开\s*(\d+)\s*条/
        ];

        for (const pattern of replyPatterns) {
            const match = text.match(pattern);
            if (match) {
                const num = parseInt(match[1]);
                if (num > 0 && num < 10000) {
                    comment.replyCount = num;
                    break;
                }
            }
        }

        // 提取用户ID
        const userIdPatterns = [
            /小红薯([A-F0-9]{8,})/,
            /user\/([a-f0-9]{20,})/,
            /uid[=:]([a-f0-9]{20,})/
        ];

        for (const pattern of userIdPatterns) {
            const match = text.match(pattern);
            if (match) {
                comment.userId = match[1];
                break;
            }
        }

        // 验证评论有效性
        if (comment.content.length >= 5 && comment.content.length <= 2000) {
            return comment;
        }

        return null;
    }
