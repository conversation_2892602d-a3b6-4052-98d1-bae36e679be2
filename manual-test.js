#!/usr/bin/env node

/**
 * 🧪 手动测试脚本
 * 逐步测试比特浏览器连接和启动
 */

const axios = require('axios');
const { BITBROWSER_CONFIG, ConfigUtils } = require('./bitbrowser-config');

async function manualTest() {
    console.log('🧪 手动测试开始...\n');

    // 1. 测试比特浏览器API连接
    console.log('1️⃣ 测试比特浏览器API连接...');
    try {
        const response = await axios.post(ConfigUtils.getApiUrl('/browser/list'),
            BITBROWSER_CONFIG.list_params,
            ConfigUtils.getRequestConfig());

        if (response.data.success) {
            const browsers = response.data.data?.list || [];
            console.log(`✅ API连接成功，找到 ${browsers.length} 个浏览器实例\n`);
            
            // 显示所有浏览器
            console.log('📋 可用浏览器列表:');
            browsers.forEach((browser, index) => {
                const status = browser.status === 2 ? '未运行' : '运行中';
                const isTarget = browser.id === BITBROWSER_CONFIG.browser_id ? ' ⭐' : '';
                console.log(`   ${index + 1}. ${browser.name} (${status})${isTarget}`);
                console.log(`      ID: ${browser.id}`);
            });

            // 查找目标浏览器
            const targetBrowser = browsers.find(b => b.id === BITBROWSER_CONFIG.browser_id);
            if (targetBrowser) {
                console.log(`\n✅ 找到目标浏览器: ${targetBrowser.name}`);
                console.log(`   状态: ${targetBrowser.status === 2 ? '未运行' : '运行中'}`);
                
                if (targetBrowser.status === 2) {
                    console.log('\n2️⃣ 尝试启动目标浏览器...');
                    await attemptBrowserStart();
                } else {
                    console.log('\n✅ 浏览器已在运行中');
                    await testRunningBrowser();
                }
            } else {
                console.log(`\n❌ 未找到目标浏览器 (ID: ${BITBROWSER_CONFIG.browser_id})`);
                console.log('💡 请检查浏览器ID配置是否正确');
            }
        } else {
            console.log('❌ API连接失败:', response.data.msg);
        }
    } catch (error) {
        console.log('❌ API连接失败:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 请确保比特浏览器应用已启动并开启Local API功能');
        }
    }
}

async function attemptBrowserStart() {
    try {
        console.log('   🚀 发送启动请求...');
        const startResponse = await axios.post(ConfigUtils.getApiUrl('/browser/open'), {
            id: BITBROWSER_CONFIG.browser_id
        }, {
            ...ConfigUtils.getRequestConfig(),
            timeout: 60000  // 增加超时时间
        });

        console.log('   📊 启动响应:', JSON.stringify(startResponse.data, null, 2));

        if (startResponse.data.success) {
            console.log('   ✅ 浏览器启动成功');
            const result = startResponse.data.data;
            
            if (result) {
                const debugPort = result.debug_port || result.selenium_port;
                const wsEndpoint = result.ws_endpoint;
                const httpEndpoint = result.http;
                
                console.log(`   🔌 调试端口: ${debugPort || '未提供'}`);
                console.log(`   🌐 WebSocket: ${wsEndpoint || '未提供'}`);
                console.log(`   🔗 HTTP端点: ${httpEndpoint || '未提供'}`);
                
                if (debugPort) {
                    console.log('\n3️⃣ 测试调试端口连接...');
                    await testDebugPort(debugPort);
                }
                
                return result;
            }
        } else {
            console.log('   ❌ 浏览器启动失败:', startResponse.data.msg);
            
            if (startResponse.data.msg && startResponse.data.msg.includes('限制')) {
                console.log('   💡 浏览器被限制启动，请在比特浏览器界面中手动启动');
                console.log('   💡 或者检查比特浏览器的使用权限设置');
            }
        }
    } catch (error) {
        console.log('   ❌ 启动请求失败:', error.message);
        if (error.code === 'ECONNABORTED') {
            console.log('   💡 启动超时，浏览器可能需要更长时间启动');
        }
    }
}

async function testRunningBrowser() {
    console.log('\n2️⃣ 测试运行中的浏览器...');
    
    // 扫描常见调试端口
    const commonPorts = [9222, 9223, 9224, 9225, 63524, 63525, 51859, 58222];
    
    for (const port of commonPorts) {
        try {
            console.log(`   🔍 测试端口 ${port}...`);
            const response = await axios.get(`http://127.0.0.1:${port}/json`, {
                timeout: 3000
            });
            
            const tabs = response.data;
            console.log(`   ✅ 端口 ${port} 可用，找到 ${tabs.length} 个标签页`);
            
            // 查找小红书标签页
            const xiaohongshuTabs = tabs.filter(tab => 
                tab.url && (
                    tab.url.includes('xiaohongshu.com') || 
                    tab.title.includes('小红书')
                )
            );
            
            if (xiaohongshuTabs.length > 0) {
                console.log(`   🎯 找到 ${xiaohongshuTabs.length} 个小红书标签页`);
                xiaohongshuTabs.forEach((tab, index) => {
                    console.log(`      ${index + 1}. ${tab.title}`);
                });
                return port;
            }
            
        } catch (error) {
            // 端口不可用，继续尝试下一个
        }
    }
    
    console.log('   ⚠️ 未找到可用的调试端口');
    console.log('   💡 请确保浏览器已启动并开启调试模式');
}

async function testDebugPort(port) {
    try {
        console.log(`   🔍 连接调试端口 ${port}...`);
        const response = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });
        
        const tabs = response.data;
        console.log(`   ✅ 调试端口连接成功，找到 ${tabs.length} 个标签页`);
        
        if (tabs.length > 0) {
            console.log('   📋 标签页列表:');
            tabs.slice(0, 3).forEach((tab, index) => {
                console.log(`      ${index + 1}. ${tab.title || '无标题'}`);
                console.log(`         URL: ${(tab.url || '').substring(0, 60)}...`);
            });
            
            if (tabs.length > 3) {
                console.log(`      ... 还有 ${tabs.length - 3} 个标签页`);
            }
        }
        
        // 查找小红书标签页
        const xiaohongshuTabs = tabs.filter(tab => 
            tab.url && (
                tab.url.includes('xiaohongshu.com') || 
                tab.title.includes('小红书')
            )
        );
        
        if (xiaohongshuTabs.length > 0) {
            console.log(`   🎯 找到 ${xiaohongshuTabs.length} 个小红书标签页，可以开始数据采集！`);
        } else {
            console.log('   💡 未找到小红书标签页，请在浏览器中打开小红书网站');
        }
        
        return true;
    } catch (error) {
        console.log(`   ❌ 调试端口 ${port} 连接失败: ${error.message}`);
        return false;
    }
}

if (require.main === module) {
    manualTest().then(() => {
        console.log('\n🎉 手动测试完成！');
        console.log('\n📋 下一步操作建议:');
        console.log('1. 如果浏览器未启动，请在比特浏览器界面中手动启动');
        console.log('2. 在浏览器中打开小红书网站 (https://www.xiaohongshu.com)');
        console.log('3. 登录您的小红书账号');
        console.log('4. 运行数据采集: node click-notes-collector.js');
    }).catch(error => {
        console.error('❌ 测试过程出错:', error.message);
    });
}

module.exports = { manualTest };
