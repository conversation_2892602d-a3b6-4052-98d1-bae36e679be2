#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
📋 比特浏览器手动启动指导
帮助您正确启动比特浏览器并配置19号窗口
"""

import requests
import time
import json
from datetime import datetime


class BitBrowserGuide:
    """比特浏览器手动启动指导"""
    
    def __init__(self):
        self.api_url = "http://127.0.0.1:54345"
        self.api_token = "ca28ee5ca6de4d209182a83aa16a2044"
        self.browser_19_id = "0d094596cb404282be3f814b98139c74"
    
    def show_startup_guide(self):
        """显示启动指导"""
        print("🎯 比特浏览器手动启动指导")
        print("=" * 60)
        print()
        
        print("📋 第一步：启动比特浏览器")
        print("   1. 找到比特浏览器的安装目录")
        print("   2. 双击 BitBrowser.exe 启动程序")
        print("   3. 等待比特浏览器主界面完全加载")
        print()
        
        print("📋 第二步：检查API服务")
        print("   1. 确保比特浏览器的API服务已启动")
        print("   2. 默认端口应该是 54345")
        print("   3. 如果端口不同，请在设置中查看")
        print()
        
        print("📋 第三步：准备19号浏览器")
        print("   1. 在比特浏览器中查看浏览器列表")
        print("   2. 找到ID为 0d094596cb404282be3f814b98139c74 的浏览器")
        print("   3. 或者创建一个新的浏览器配置")
        print()
        
        print("📋 第四步：启动浏览器窗口")
        print("   1. 点击'启动'按钮启动浏览器窗口")
        print("   2. 等待浏览器窗口完全打开")
        print("   3. 记下调试端口号(通常在9222-9999范围)")
        print()
        
        print("🔄 完成上述步骤后，按回车键继续检查...")
        input()
    
    def check_connection_step_by_step(self):
        """分步检查连接"""
        print("\n🔍 开始分步检查连接...\n")
        
        # 步骤1：检查API连接
        print("1️⃣ 检查比特浏览器API连接...")
        api_ok = self.check_api_connection()
        
        if not api_ok:
            print("❌ API连接失败，请确保:")
            print("   - 比特浏览器已启动")
            print("   - API服务正在运行")
            print("   - 端口54345可访问")
            return False
        
        # 步骤2：获取浏览器列表
        print("\n2️⃣ 获取浏览器列表...")
        browsers = self.get_browsers()
        
        if not browsers:
            print("❌ 无法获取浏览器列表")
            return False
        
        # 步骤3：查找目标浏览器
        print("\n3️⃣ 查找目标浏览器...")
        target_browser = self.find_target_browser(browsers)
        
        if not target_browser:
            print("❌ 未找到目标浏览器")
            self.show_browser_creation_guide()
            return False
        
        # 步骤4：检查浏览器状态
        print("\n4️⃣ 检查浏览器状态...")
        if target_browser.get('status') != 'running':
            print("⚠️ 浏览器未运行")
            print("💡 请在比特浏览器中点击'启动'按钮")
            
            print("\n等待您启动浏览器... (启动后按回车)")
            input()
            
            # 重新检查状态
            browsers = self.get_browsers()
            target_browser = self.find_target_browser(browsers)
            
            if not target_browser or target_browser.get('status') != 'running':
                print("❌ 浏览器仍未运行")
                return False
        
        # 步骤5：测试调试端口
        print("\n5️⃣ 测试调试端口...")
        debug_port = target_browser.get('debug_port') or 9222
        
        if self.test_debug_port(debug_port):
            print(f"✅ 调试端口 {debug_port} 连接成功")
            
            # 步骤6：检查小红书页面
            print("\n6️⃣ 检查小红书页面...")
            xiaohongshu_tabs = self.find_xiaohongshu_tabs(debug_port)
            
            if xiaohongshu_tabs:
                print(f"✅ 找到 {len(xiaohongshu_tabs)} 个小红书标签页")
                for i, tab in enumerate(xiaohongshu_tabs):
                    print(f"   {i+1}. {tab['title'][:50]}...")
                
                print("\n🎉 所有检查通过! 系统已准备就绪!")
                
                # 保存配置
                config = {
                    'timestamp': datetime.now().isoformat(),
                    'browser_id': target_browser['id'],
                    'browser_name': target_browser.get('name'),
                    'debug_port': debug_port,
                    'xiaohongshu_tabs': len(xiaohongshu_tabs)
                }
                
                with open('bitbrowser_ready_config.json', 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                
                print("📁 配置已保存到: bitbrowser_ready_config.json")
                return True
            else:
                print("⚠️ 未找到小红书标签页")
                print("💡 请在浏览器中打开小红书网站:")
                print("   https://www.xiaohongshu.com/explore")
                return False
        else:
            print(f"❌ 调试端口 {debug_port} 连接失败")
            return False
    
    def check_api_connection(self):
        """检查API连接"""
        try:
            response = requests.get(f"{self.api_url}/browser/list", 
                                  headers={'Authorization': f'Bearer {self.api_token}'}, 
                                  timeout=5)
            
            if response.status_code == 200:
                print("✅ API连接成功")
                return True
            else:
                print(f"❌ API响应错误: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ API连接失败: {str(e)}")
            return False
    
    def get_browsers(self):
        """获取浏览器列表"""
        try:
            response = requests.get(f"{self.api_url}/browser/list", 
                                  headers={'Authorization': f'Bearer {self.api_token}'}, 
                                  timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    browsers = data.get('data', [])
                    print(f"✅ 找到 {len(browsers)} 个浏览器配置")
                    return browsers
                else:
                    print(f"❌ API返回错误: {data.get('msg')}")
                    return []
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 获取浏览器列表失败: {str(e)}")
            return []
    
    def find_target_browser(self, browsers):
        """查找目标浏览器"""
        # 方法1: 通过ID查找
        for browser in browsers:
            if browser.get('id') == self.browser_19_id:
                print(f"✅ 找到目标浏览器: {browser.get('name', '未命名')}")
                print(f"   ID: {browser['id']}")
                print(f"   状态: {browser.get('status', '未知')}")
                return browser
        
        # 方法2: 通过名称查找包含"19"的浏览器
        for browser in browsers:
            name = browser.get('name', '').lower()
            if '19' in name:
                print(f"✅ 找到疑似19号浏览器: {browser.get('name')}")
                print(f"   ID: {browser['id']}")
                print(f"   状态: {browser.get('status', '未知')}")
                return browser
        
        print("❌ 未找到目标浏览器")
        print("📋 可用的浏览器:")
        for i, browser in enumerate(browsers[:5]):
            print(f"   {i+1}. {browser.get('name', '未命名')} (ID: {browser['id'][:8]}...)")
        
        return None
    
    def show_browser_creation_guide(self):
        """显示浏览器创建指导"""
        print("\n📋 创建19号浏览器指导:")
        print("   1. 在比特浏览器主界面点击'新建浏览器'")
        print("   2. 设置浏览器名称为'19号浏览器'或类似名称")
        print("   3. 配置代理和其他设置(如需要)")
        print("   4. 保存浏览器配置")
        print("   5. 重新运行此脚本")
    
    def test_debug_port(self, port):
        """测试调试端口"""
        try:
            response = requests.get(f"http://127.0.0.1:{port}/json", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def find_xiaohongshu_tabs(self, port):
        """查找小红书标签页"""
        try:
            response = requests.get(f"http://127.0.0.1:{port}/json", timeout=5)
            
            if response.status_code == 200:
                tabs = response.json()
                xiaohongshu_tabs = [tab for tab in tabs 
                                  if tab.get('url') and 'xiaohongshu.com' in tab['url']]
                return xiaohongshu_tabs
            else:
                return []
                
        except:
            return []
    
    def run_complete_guide(self):
        """运行完整指导流程"""
        print("🎯 比特浏览器完整设置指导")
        print("=" * 60)
        
        # 显示启动指导
        self.show_startup_guide()
        
        # 分步检查连接
        success = self.check_connection_step_by_step()
        
        if success:
            print("\n🎉 设置完成! 现在可以运行评论采集脚本了!")
            print("📋 下一步:")
            print("   1. 在浏览器中导航到要采集的小红书笔记页面")
            print("   2. 运行评论采集脚本")
            print("   3. 开始采集评论数据")
        else:
            print("\n❌ 设置未完成，请按照指导重新操作")
        
        return success


def main():
    """主函数"""
    guide = BitBrowserGuide()
    guide.run_complete_guide()


if __name__ == "__main__":
    main()
