#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 矩阵工具集成示例
展示如何将小红书评论爬取模块集成到矩阵工具中
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Optional
from xiaohongshu_scraper_module import XiaohongshuScraperModule

class MatrixToolIntegration:
    """矩阵工具集成类"""
    
    def __init__(self, config_file: str = "matrix_config.json"):
        """
        初始化矩阵工具集成
        
        Args:
            config_file: 配置文件路径
        """
        self.config = self.load_config(config_file)
        self.scraper = None
        self.results = []
        
    def load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        default_config = {
            "bitbrowser": {
                "api_url": "http://127.0.0.1:56906",
                "target_browser_seq": 19,
                "auto_open": True,
                "auto_close": False
            },
            "scraping": {
                "output_directory": "./matrix_scraped_data",
                "target_comments": 1472,
                "max_scroll_attempts": 1000,
                "scroll_delay": 0.6,
                "click_delay": 0.4
            },
            "targets": [
                {
                    "platform": "xiaohongshu",
                    "name": "漫娴学姐兼职帖",
                    "url": "https://www.xiaohongshu.com/explore/67af69ee000000002a003a157",
                    "keywords": ["兼职", "聊天员", "陪玩", "求带", "日入", "赚钱"],
                    "enabled": True
                }
            ],
            "analysis": {
                "extract_job_info": True,
                "extract_contact_info": True,
                "generate_statistics": True,
                "export_formats": ["json", "csv", "excel"]
            }
        }
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                # 合并配置
                default_config.update(user_config)
        except FileNotFoundError:
            print(f"💡 配置文件 {config_file} 不存在，使用默认配置")
            # 保存默认配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            print(f"✅ 已创建默认配置文件: {config_file}")
        
        return default_config
    
    def initialize_scraper(self) -> bool:
        """初始化爬取器"""
        try:
            print("🔧 初始化小红书爬取模块...")
            
            self.scraper = XiaohongshuScraperModule(
                api_url=self.config['bitbrowser']['api_url'],
                output_dir=self.config['scraping']['output_directory']
            )
            
            # 更新配置
            self.scraper.config.update({
                'target_comments': self.config['scraping']['target_comments'],
                'max_scroll_attempts': self.config['scraping']['max_scroll_attempts'],
                'scroll_delay': self.config['scraping']['scroll_delay'],
                'click_delay': self.config['scraping']['click_delay']
            })
            
            print("✅ 爬取模块初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 初始化爬取模块失败: {str(e)}")
            return False
    
    def setup_browser(self) -> bool:
        """设置浏览器"""
        try:
            print("🔍 查找目标浏览器...")
            
            # 查找目标浏览器
            target_seq = self.config['bitbrowser']['target_browser_seq']
            browser = self.scraper.find_browser_by_seq(target_seq)
            
            if not browser:
                print(f"❌ 未找到 {target_seq} 号浏览器窗口")
                
                # 列出可用浏览器
                browsers = self.scraper.list_browsers()
                if browsers:
                    print("📋 可用浏览器窗口:")
                    for b in browsers[:5]:
                        seq = b.get('seq', 'N/A')
                        name = b.get('name', '未命名')
                        print(f"   序号: {seq}, 名称: {name}")
                
                return False
            
            print(f"✅ 找到目标浏览器: {browser.get('name', '未命名')}")
            
            # 打开浏览器（如果配置为自动打开）
            if self.config['bitbrowser']['auto_open']:
                print("🚀 打开浏览器窗口...")
                
                browser_id = browser.get('id')
                open_result = self.scraper.open_browser(browser_id)
                
                if not open_result:
                    print("❌ 打开浏览器失败")
                    return False
                
                debug_port = open_result.get('http', '').split(':')[-1]
                print(f"✅ 浏览器已打开，调试端口: {debug_port}")
                
                # 等待浏览器启动
                time.sleep(5)
                
                # 连接Selenium
                print("🔗 连接Selenium...")
                if not self.scraper.connect_selenium(debug_port):
                    print("❌ Selenium连接失败")
                    return False
                
                print("✅ Selenium连接成功")
            
            return True
            
        except Exception as e:
            print(f"❌ 设置浏览器失败: {str(e)}")
            return False
    
    def execute_scraping_tasks(self) -> List[Dict]:
        """执行爬取任务"""
        results = []
        
        enabled_targets = [t for t in self.config['targets'] if t.get('enabled', True)]
        
        print(f"🎯 开始执行 {len(enabled_targets)} 个爬取任务...")
        
        for i, target in enumerate(enabled_targets):
            print(f"\n📋 任务 {i+1}/{len(enabled_targets)}: {target['name']}")
            
            try:
                # 爬取评论
                result = self.scraper.scrape_comments(target['url'])
                
                # 添加任务信息
                result['taskInfo'] = {
                    'name': target['name'],
                    'platform': target['platform'],
                    'url': target['url'],
                    'keywords': target['keywords'],
                    'executionTime': datetime.now().isoformat()
                }
                
                # 分析结果
                if self.config['analysis']['extract_job_info']:
                    result['jobAnalysis'] = self.analyze_job_comments(result['comments'], target['keywords'])
                
                # 保存结果
                filename = f"matrix_{target['platform']}_{target['name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                filepath = self.scraper.save_to_file(result, filename)
                result['savedFile'] = filepath
                
                results.append(result)
                
                print(f"✅ 任务完成，提取到 {len(result['comments'])} 条评论")
                
            except Exception as e:
                print(f"❌ 任务执行失败: {str(e)}")
                results.append({
                    'taskInfo': target,
                    'error': str(e),
                    'executionTime': datetime.now().isoformat()
                })
        
        return results
    
    def analyze_job_comments(self, comments: List[Dict], keywords: List[str]) -> Dict:
        """分析兼职相关评论"""
        job_comments = []
        contact_info = []
        
        for comment in comments:
            content = comment.get('content', '')
            
            # 检查是否包含关键词
            if any(keyword in content for keyword in keywords):
                job_comments.append(comment)
                
                # 提取联系信息
                contact_patterns = [
                    r'微信[：:]\s*([A-Za-z0-9_-]+)',
                    r'QQ[：:]\s*(\d+)',
                    r'电话[：:]\s*(\d{11})',
                    r'手机[：:]\s*(\d{11})'
                ]
                
                for pattern in contact_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        contact_info.append({
                            'type': pattern.split('[')[0],
                            'value': match,
                            'username': comment.get('username', ''),
                            'commentId': comment.get('id', '')
                        })
        
        return {
            'totalJobComments': len(job_comments),
            'jobPercentage': round((len(job_comments) / len(comments)) * 100, 2) if comments else 0,
            'contactInfo': contact_info,
            'topKeywords': self.get_top_keywords(job_comments, keywords),
            'jobComments': job_comments[:50]  # 只保存前50条
        }
    
    def get_top_keywords(self, comments: List[Dict], keywords: List[str]) -> List[Dict]:
        """获取热门关键词"""
        keyword_counts = {}
        
        for comment in comments:
            content = comment.get('content', '')
            for keyword in keywords:
                if keyword in content:
                    keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
        
        # 排序并返回前10个
        sorted_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)
        
        return [{'keyword': k, 'count': v} for k, v in sorted_keywords[:10]]
    
    def generate_summary_report(self, results: List[Dict]) -> Dict:
        """生成汇总报告"""
        total_comments = sum(len(r.get('comments', [])) for r in results if 'comments' in r)
        successful_tasks = len([r for r in results if 'comments' in r])
        failed_tasks = len([r for r in results if 'error' in r])
        
        job_analysis = {}
        if self.config['analysis']['extract_job_info']:
            all_job_comments = []
            all_contact_info = []
            
            for result in results:
                if 'jobAnalysis' in result:
                    all_job_comments.extend(result['jobAnalysis'].get('jobComments', []))
                    all_contact_info.extend(result['jobAnalysis'].get('contactInfo', []))
            
            job_analysis = {
                'totalJobComments': len(all_job_comments),
                'totalContactInfo': len(all_contact_info),
                'uniqueContacts': len(set(c['value'] for c in all_contact_info))
            }
        
        return {
            'summary': {
                'totalTasks': len(results),
                'successfulTasks': successful_tasks,
                'failedTasks': failed_tasks,
                'totalComments': total_comments,
                'executionTime': datetime.now().isoformat()
            },
            'jobAnalysis': job_analysis,
            'results': results
        }
    
    def run(self) -> Dict:
        """运行矩阵工具集成"""
        print("🚀 启动矩阵工具 - 小红书评论爬取")
        print("=" * 50)
        
        try:
            # 1. 初始化爬取器
            if not self.initialize_scraper():
                return {'error': '初始化爬取器失败'}
            
            # 2. 设置浏览器
            if not self.setup_browser():
                return {'error': '设置浏览器失败'}
            
            # 3. 执行爬取任务
            results = self.execute_scraping_tasks()
            
            # 4. 生成汇总报告
            summary_report = self.generate_summary_report(results)
            
            # 5. 保存汇总报告
            summary_filename = f"matrix_summary_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            summary_filepath = self.scraper.save_to_file(summary_report, summary_filename)
            
            print(f"\n📊 汇总报告:")
            print(f"   ✅ 成功任务: {summary_report['summary']['successfulTasks']}")
            print(f"   ❌ 失败任务: {summary_report['summary']['failedTasks']}")
            print(f"   💬 总评论数: {summary_report['summary']['totalComments']}")
            print(f"   📁 汇总报告: {summary_filepath}")
            
            return summary_report
            
        except Exception as e:
            print(f"❌ 矩阵工具执行失败: {str(e)}")
            return {'error': str(e)}
            
        finally:
            # 清理资源
            if self.scraper:
                self.scraper.cleanup()

def main():
    """主函数"""
    print("🔧 矩阵工具集成示例")
    print("集成小红书评论爬取功能")
    
    # 创建矩阵工具集成实例
    matrix = MatrixToolIntegration()
    
    # 运行集成
    result = matrix.run()
    
    if 'error' not in result:
        print("\n🎉 矩阵工具执行完成！")
    else:
        print(f"\n❌ 矩阵工具执行失败: {result['error']}")

if __name__ == "__main__":
    main()
