#!/usr/bin/env node

/**
 * 🔍 监控19号窗口状态
 * 等待手动启动后自动检测和测试
 */

const axios = require('axios');
const { BITBROWSER_CONFIG, ConfigUtils } = require('./bitbrowser-config');

let isMonitoring = false;
let checkInterval = null;

async function monitorWindow19() {
    console.log('🔍 开始监控19号窗口状态...\n');
    console.log('📋 监控配置:');
    console.log(`   浏览器名称: ${BITBROWSER_CONFIG.browser_name}`);
    console.log(`   浏览器ID: ${BITBROWSER_CONFIG.browser_id}`);
    console.log(`   检查间隔: 3秒`);
    console.log('');
    console.log('💡 请在比特浏览器界面中手动启动 "95362955272 ace1" 浏览器');
    console.log('⏱️ 监控中... (按 Ctrl+C 停止)\n');

    isMonitoring = true;
    let checkCount = 0;

    checkInterval = setInterval(async () => {
        checkCount++;
        
        try {
            const status = await checkBrowserStatus();
            
            if (status.isRunning) {
                console.log(`\n🎉 检测到19号窗口已启动！(第${checkCount}次检查)`);
                console.log(`   启动时间: ${new Date().toLocaleString()}`);
                
                // 停止监控
                clearInterval(checkInterval);
                isMonitoring = false;
                
                // 开始详细测试
                await performDetailedTest();
                
            } else {
                // 每10次检查显示一次状态
                if (checkCount % 10 === 0) {
                    console.log(`⏳ 第${checkCount}次检查 - 19号窗口仍未启动 (${new Date().toLocaleTimeString()})`);
                }
            }
            
        } catch (error) {
            console.log(`❌ 检查状态失败: ${error.message}`);
        }
        
    }, 3000); // 每3秒检查一次

    // 处理程序退出
    process.on('SIGINT', () => {
        console.log('\n\n🛑 监控已停止');
        if (checkInterval) {
            clearInterval(checkInterval);
        }
        process.exit(0);
    });
}

async function checkBrowserStatus() {
    try {
        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/list'), 
            BITBROWSER_CONFIG.list_params, 
            ConfigUtils.getRequestConfig()
        );

        if (response.data.success) {
            const browsers = response.data.data?.list || [];
            const targetBrowser = browsers.find(b => b.id === BITBROWSER_CONFIG.browser_id);
            
            return {
                isRunning: targetBrowser && targetBrowser.status !== 2,
                browser: targetBrowser,
                totalBrowsers: browsers.length
            };
        }
        
        return { isRunning: false, browser: null, totalBrowsers: 0 };
        
    } catch (error) {
        throw new Error(`API请求失败: ${error.message}`);
    }
}

async function performDetailedTest() {
    console.log('\n🧪 开始详细测试...\n');

    // 1. 扫描调试端口
    console.log('1️⃣ 扫描调试端口...');
    const debugPort = await scanForDebugPort();
    
    if (debugPort) {
        console.log(`✅ 找到调试端口: ${debugPort}`);
        
        // 2. 测试端口连接
        console.log('\n2️⃣ 测试端口连接...');
        const connectionSuccess = await testPortConnection(debugPort);
        
        if (connectionSuccess) {
            // 3. 检查小红书页面
            console.log('\n3️⃣ 检查小红书页面...');
            await checkXiaohongshuPages(debugPort);
            
            // 4. 测试数据采集
            console.log('\n4️⃣ 测试数据采集准备...');
            await testCollectionReadiness(debugPort);
            
            console.log('\n🎉 19号窗口测试完成！');
            console.log('\n📋 下一步操作:');
            console.log('1. 在浏览器中打开小红书网站');
            console.log('2. 登录您的账号');
            console.log('3. 运行数据采集: node click-notes-collector.js');
            
        } else {
            console.log('\n❌ 端口连接失败');
        }
        
    } else {
        console.log('❌ 未找到调试端口');
        console.log('💡 请确保浏览器启动时开启了调试模式');
    }
}

async function scanForDebugPort() {
    const portRanges = [
        { start: 53320, end: 53340, name: '比特浏览器动态端口' },
        { start: 63520, end: 63540, name: '常用端口' },
        { start: 51850, end: 51870, name: '动态端口' },
        { start: 9220, end: 9230, name: 'Chrome端口' }
    ];

    for (const range of portRanges) {
        console.log(`   🔍 扫描 ${range.name} (${range.start}-${range.end})...`);
        
        for (let port = range.start; port <= range.end; port++) {
            try {
                const response = await axios.get(`http://127.0.0.1:${port}/json`, {
                    timeout: 1000
                });
                
                if (response.data && Array.isArray(response.data)) {
                    console.log(`   ✅ 端口 ${port} 可用 - ${response.data.length} 个标签页`);
                    return port;
                }
                
            } catch (error) {
                // 端口不可用，继续
            }
        }
    }
    
    return null;
}

async function testPortConnection(port) {
    try {
        const response = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });
        
        const tabs = response.data;
        console.log(`   ✅ 端口 ${port} 连接成功`);
        console.log(`   📋 找到 ${tabs.length} 个标签页`);
        
        if (tabs.length > 0) {
            console.log('   📄 标签页预览:');
            tabs.slice(0, 3).forEach((tab, index) => {
                const title = tab.title || '无标题';
                const url = (tab.url || '').substring(0, 50) + '...';
                console.log(`      ${index + 1}. ${title}`);
                console.log(`         ${url}`);
            });
            
            if (tabs.length > 3) {
                console.log(`      ... 还有 ${tabs.length - 3} 个标签页`);
            }
        }
        
        return true;
        
    } catch (error) {
        console.log(`   ❌ 端口 ${port} 连接失败: ${error.message}`);
        return false;
    }
}

async function checkXiaohongshuPages(port) {
    try {
        const response = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });
        
        const tabs = response.data;
        const xiaohongshuTabs = tabs.filter(tab => 
            tab.url && (
                tab.url.includes('xiaohongshu.com') || 
                tab.title.includes('小红书')
            )
        );
        
        if (xiaohongshuTabs.length > 0) {
            console.log(`   🎯 找到 ${xiaohongshuTabs.length} 个小红书标签页:`);
            xiaohongshuTabs.forEach((tab, index) => {
                console.log(`      ${index + 1}. ${tab.title}`);
                console.log(`         ${tab.url}`);
            });
            console.log('   ✅ 可以开始数据采集！');
        } else {
            console.log('   ⚠️ 未找到小红书标签页');
            console.log('   💡 建议在浏览器中打开:');
            console.log('      - https://www.xiaohongshu.com');
            console.log('      - https://creator.xiaohongshu.com');
        }
        
    } catch (error) {
        console.log(`   ❌ 检查小红书页面失败: ${error.message}`);
    }
}

async function testCollectionReadiness(port) {
    try {
        // 更新数据采集器的端口配置
        console.log(`   🔧 配置数据采集器使用端口 ${port}...`);
        
        // 这里可以更新配置文件或环境变量
        process.env.DEBUG_PORT = port.toString();
        
        console.log('   ✅ 数据采集器配置完成');
        console.log(`   🎯 调试端口: ${port}`);
        
        return true;
        
    } catch (error) {
        console.log(`   ❌ 配置数据采集器失败: ${error.message}`);
        return false;
    }
}

// 快速状态检查
async function quickCheck() {
    console.log('🔍 快速检查19号窗口状态...\n');
    
    try {
        const status = await checkBrowserStatus();
        
        console.log('📊 检查结果:');
        console.log(`   浏览器总数: ${status.totalBrowsers}`);
        console.log(`   19号窗口状态: ${status.isRunning ? '✅ 运行中' : '❌ 未运行'}`);
        
        if (status.browser) {
            console.log(`   浏览器名称: ${status.browser.name}`);
            console.log(`   最后操作: ${status.browser.operTime || '未知'}`);
        }
        
        if (status.isRunning) {
            console.log('\n🎉 19号窗口已在运行！');
            await performDetailedTest();
        } else {
            console.log('\n💡 19号窗口未运行，请手动启动后运行监控模式');
            console.log('   命令: node monitor-window-19.js --monitor');
        }
        
    } catch (error) {
        console.log(`❌ 检查失败: ${error.message}`);
    }
}

// 命令行参数处理
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--monitor')) {
        monitorWindow19();
    } else {
        quickCheck();
    }
}

module.exports = { monitorWindow19, checkBrowserStatus };
