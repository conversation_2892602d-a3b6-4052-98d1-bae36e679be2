#!/usr/bin/env node

/**
 * 🎯 导航到笔记详情页
 * 确保我们在正确的页面进行评论采集
 */

const puppeteer = require('puppeteer');

class NavigateToNoteDetail {
    constructor() {
        this.debugPort = 55276;
        this.targetNoteId = '67af69ee000000002a003a15';
    }

    async connectToBrowser() {
        try {
            console.log('🔗 连接到比特浏览器...');
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${this.debugPort}`,
                defaultViewport: null
            });
            
            const pages = await browser.pages();
            let page = pages.find(p => p.url().includes('xiaohongshu.com'));
            
            if (!page) {
                page = await browser.newPage();
            }
            
            console.log('✅ 成功连接到浏览器');
            console.log(`📄 当前URL: ${page.url()}`);
            
            return { browser, page };
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    async checkCurrentPage(page) {
        console.log('🔍 检查当前页面状态...');
        
        const pageInfo = await page.evaluate(() => {
            const url = window.location.href;
            const title = document.title;
            const isExplorePage = url.includes('/explore/');
            const isDetailPage = url.includes('/explore/') && !url.includes('?');
            const hasCommentSection = document.body.textContent.includes('条评论');
            
            return {
                url,
                title,
                isExplorePage,
                isDetailPage,
                hasCommentSection,
                textLength: document.body.textContent.length
            };
        });
        
        console.log('📊 页面信息:');
        console.log(`   🔗 URL: ${pageInfo.url}`);
        console.log(`   📝 标题: ${pageInfo.title}`);
        console.log(`   📄 是否为笔记页: ${pageInfo.isExplorePage ? '✅' : '❌'}`);
        console.log(`   📋 是否为详情页: ${pageInfo.isDetailPage ? '✅' : '❌'}`);
        console.log(`   💬 有评论区域: ${pageInfo.hasCommentSection ? '✅' : '❌'}`);
        console.log(`   📏 页面文本长度: ${pageInfo.textLength}`);
        
        return pageInfo;
    }

    async navigateToNoteDetail(page) {
        console.log('🧭 导航到笔记详情页...');
        
        // 构建正确的笔记详情页URL
        const detailUrl = `https://www.xiaohongshu.com/explore/${this.targetNoteId}`;
        
        console.log(`🎯 目标URL: ${detailUrl}`);
        
        try {
            await page.goto(detailUrl, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });
            
            console.log('✅ 成功导航到笔记详情页');
            
            // 等待页面完全加载
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            return true;
        } catch (error) {
            console.error('❌ 导航失败:', error.message);
            return false;
        }
    }

    async findAndClickNote(page) {
        console.log('🔍 查找并点击目标笔记...');
        
        try {
            const clickResult = await page.evaluate((noteId) => {
                // 查找包含目标笔记ID的链接
                const links = Array.from(document.querySelectorAll('a'));
                const targetLink = links.find(link => link.href.includes(noteId));
                
                if (targetLink) {
                    targetLink.click();
                    return { success: true, url: targetLink.href };
                }
                
                // 如果没找到链接，尝试查找包含特定文字的元素
                const elements = Array.from(document.querySelectorAll('*'));
                const targetElement = elements.find(el => 
                    el.textContent.includes('兼织') || 
                    el.textContent.includes('求推荐')
                );
                
                if (targetElement) {
                    targetElement.click();
                    return { success: true, element: targetElement.textContent.substring(0, 50) };
                }
                
                return { success: false, message: '未找到目标笔记' };
            }, this.targetNoteId);
            
            if (clickResult.success) {
                console.log('✅ 成功点击目标笔记');
                console.log(`   📄 ${clickResult.url || clickResult.element}`);
                
                // 等待页面跳转
                await new Promise(resolve => setTimeout(resolve, 3000));
                return true;
            } else {
                console.log('❌ 未找到目标笔记');
                return false;
            }
        } catch (error) {
            console.error('❌ 点击笔记时出错:', error.message);
            return false;
        }
    }

    async verifyDetailPage(page) {
        console.log('✅ 验证是否在正确的详情页...');
        
        const verification = await page.evaluate(() => {
            const url = window.location.href;
            const pageText = document.body.textContent;
            
            // 检查是否在详情页
            const isDetailPage = url.includes('/explore/') && 
                                 (pageText.includes('条评论') || pageText.includes('评论'));
            
            // 检查评论相关元素
            const commentCount = (pageText.match(/\d+\s*条评论/) || [])[0];
            const hasComments = pageText.includes('漫娴学姐') || 
                               pageText.includes('求带') || 
                               pageText.includes('兼织');
            
            return {
                url,
                isDetailPage,
                commentCount,
                hasComments,
                textLength: pageText.length
            };
        });
        
        console.log('🔍 详情页验证结果:');
        console.log(`   🔗 当前URL: ${verification.url}`);
        console.log(`   📋 是详情页: ${verification.isDetailPage ? '✅' : '❌'}`);
        console.log(`   💬 评论数量: ${verification.commentCount || '未检测到'}`);
        console.log(`   📝 有评论内容: ${verification.hasComments ? '✅' : '❌'}`);
        console.log(`   📏 页面文本长度: ${verification.textLength}`);
        
        return verification.isDetailPage && verification.hasComments;
    }

    async run() {
        try {
            console.log('🎯 启动笔记详情页导航器...');
            console.log(`📋 目标笔记ID: ${this.targetNoteId}`);
            
            const { browser, page } = await this.connectToBrowser();
            
            // 检查当前页面
            const currentPageInfo = await this.checkCurrentPage(page);
            
            if (currentPageInfo.isDetailPage && currentPageInfo.hasCommentSection) {
                console.log('🎉 已经在正确的详情页！');
            } else {
                console.log('🔄 需要导航到详情页...');
                
                // 方法1: 直接导航
                const directNavSuccess = await this.navigateToNoteDetail(page);
                
                if (!directNavSuccess) {
                    console.log('🔄 直接导航失败，尝试查找并点击笔记...');
                    
                    // 方法2: 查找并点击
                    const clickSuccess = await this.findAndClickNote(page);
                    
                    if (!clickSuccess) {
                        throw new Error('无法导航到笔记详情页');
                    }
                }
            }
            
            // 验证最终页面
            const isCorrectPage = await this.verifyDetailPage(page);
            
            if (isCorrectPage) {
                console.log('\n🎉 成功！现在在正确的笔记详情页');
                console.log('💡 现在可以运行评论采集脚本了');
                console.log('📝 建议运行: node enhanced-direct-extractor.js');
            } else {
                console.log('\n❌ 警告：可能不在正确的详情页');
                console.log('💡 请手动检查页面并重试');
            }
            
            // 不关闭浏览器，让用户继续操作
            console.log('\n🔄 浏览器保持打开状态，可以继续操作...');
            
        } catch (error) {
            console.error('❌ 导航失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行导航器
if (require.main === module) {
    const navigator = new NavigateToNoteDetail();
    navigator.run();
}

module.exports = NavigateToNoteDetail;
