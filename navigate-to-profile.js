#!/usr/bin/env node

/**
 * 🔄 导航到用户主页
 */

const axios = require('axios');
const WebSocket = require('ws');

async function navigateToProfile() {
    console.log('🔄 导航回用户主页...');

    try {
        // 1. 获取调试端口
        const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });

        const port = response.data.data.result.http.split(':')[1];
        console.log(`✅ 调试端口: ${port}`);

        // 2. 获取标签页
        const tabsResponse = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });

        const tab = tabsResponse.data.find(t => 
            t.url && t.url.includes('xiaohongshu.com')
        );

        if (!tab) {
            throw new Error('未找到小红书标签页');
        }

        console.log(`🎯 当前页面: ${tab.title}`);
        console.log(`🔗 当前URL: ${tab.url}`);

        // 3. 导航到用户主页
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: 1,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const navigateScript = `
                        window.location.href = "https://www.xiaohongshu.com/user/profile/676964d20000000018014972";
                        return { navigated: true };
                    `;

                    ws.send(JSON.stringify({
                        id: 2,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: navigateScript,
                            returnByValue: true
                        }
                    }));

                    setTimeout(() => {
                        console.log('✅ 已导航到用户主页');
                        ws.close();
                        resolve();
                    }, 5000);
                }, 1000);
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                ws.close();
                reject(new Error('导航超时'));
            }, 15000);
        });

    } catch (error) {
        console.error('❌ 导航失败:', error.message);
        throw error;
    }
}

// 运行导航
if (require.main === module) {
    navigateToProfile().then(() => {
        console.log('🎉 导航完成！现在可以进行笔记采集了');
    }).catch(console.error);
}

module.exports = { navigateToProfile };
