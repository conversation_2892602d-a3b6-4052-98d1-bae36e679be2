#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 无认证比特浏览器API调用
获取19号窗口的调试端口
"""

import requests
import json
import time

class NoAuthBitBrowserAPI:
    def __init__(self):
        self.api_base = "http://127.0.0.1:56906"
        # 不使用认证头
        self.headers = {
            'Content-Type': 'application/json'
        }
        
    def test_api_connection(self):
        """测试API连接"""
        print("🔍 测试比特浏览器API连接（无认证）...")
        print(f"   API地址: {self.api_base}")
        
        try:
            response = requests.get(f"{self.api_base}/", timeout=5)
            print(f"   基本连接状态码: {response.status_code}")
            return True
        except Exception as e:
            print(f"   ❌ API连接失败: {e}")
            return False
    
    def get_browser_list(self):
        """获取浏览器窗口列表"""
        print("🔍 获取浏览器窗口列表（无认证）...")
        
        url = f"{self.api_base}/browser/list"
        payload = {
            "page": 0,
            "pageSize": 50
        }
        
        try:
            response = requests.post(url, json=payload, headers=self.headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ API响应成功!")
                print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")

                if data.get('success') and data.get('data'):
                    # API返回的数据结构是 {success: true, data: {list: [...], page: 0, ...}}
                    data_obj = data['data']
                    browsers = data_obj.get('list', [])
                    total_num = data_obj.get('totalNum', 0)

                    print(f"   📱 获取到 {len(browsers)} 个浏览器窗口 (总数: {total_num}):")

                    window19_candidates = []
                    for browser in browsers:
                        browser_id = browser.get('id', '')
                        name = browser.get('name', '')
                        seq = browser.get('seq', '')
                        status = browser.get('status', '')

                        print(f"      📱 {name} (序号:{seq}, ID:{browser_id[:8]}...) - 状态:{status}")

                        # 查找19号窗口
                        if (str(seq) == '19'):
                            window19_candidates.append(browser)
                            print(f"         🎯 找到19号窗口!")

                    return browsers, window19_candidates
                else:
                    print(f"   ❌ API响应格式错误: {data}")
                    return [], []
            else:
                print(f"   ❌ API请求失败: {response.text}")
                return [], []
                
        except Exception as e:
            print(f"   ❌ 获取窗口列表失败: {e}")
            return [], []
    
    def get_all_debug_ports(self):
        """获取所有调试端口"""
        print("🔍 获取所有调试端口（无认证）...")
        
        url = f"{self.api_base}/browser/ports"
        
        try:
            response = requests.post(url, headers=self.headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 成功获取端口数据!")
                print(f"   API响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get('success') and data.get('data'):
                    ports_data = data['data']
                    print(f"   🎉 获取到 {len(ports_data)} 个调试端口:")
                    
                    for browser_id, port in ports_data.items():
                        print(f"      🌐 浏览器 {browser_id} -> 端口 {port}")
                    
                    return ports_data
                else:
                    print(f"   ❌ 端口数据格式错误: {data}")
                    return {}
            else:
                print(f"   ❌ 获取端口失败: {response.text}")
                return {}
                
        except Exception as e:
            print(f"   ❌ 获取端口异常: {e}")
            return {}
    
    def find_window19_port(self, browsers, window19_candidates, ports_data):
        """查找19号窗口的调试端口"""
        print("🎯 查找19号窗口的调试端口...")
        
        window19_ports = []
        
        # 方法1: 通过19号窗口候选者查找端口
        for candidate in window19_candidates:
            browser_id = candidate.get('id', '')
            name = candidate.get('name', '')
            seq = candidate.get('seq', '')
            
            print(f"   🔍 检查19号窗口候选: {name} (序号:{seq})")
            
            # 在端口数据中查找匹配的ID
            for port_browser_id, port in ports_data.items():
                if browser_id == port_browser_id:
                    window19_ports.append({
                        'browser_id': browser_id,
                        'name': name,
                        'seq': seq,
                        'port': int(port)
                    })
                    print(f"      ✅ 找到匹配端口: {name} -> 端口 {port}")
                    break
            else:
                print(f"      ⚠️ 未找到对应的调试端口")
        
        # 方法2: 如果没找到明确的19号窗口，显示所有端口供选择
        if not window19_ports:
            print("   💡 未找到明确的19号窗口，显示所有可用端口:")
            for browser_id, port in ports_data.items():
                print(f"      🔌 {browser_id} -> 端口 {port}")
        
        return window19_ports
    
    def run(self):
        """运行获取流程"""
        print("🎯 比特浏览器19号窗口调试端口获取器（无认证版）")
        print("=" * 60)
        
        # 1. 测试API连接
        if not self.test_api_connection():
            print("❌ API连接失败")
            return None
        
        # 2. 获取浏览器窗口列表
        browsers, window19_candidates = self.get_browser_list()
        
        if not browsers:
            print("❌ 无法获取浏览器窗口列表")
            return None
        
        # 3. 获取所有调试端口
        ports_data = self.get_all_debug_ports()
        
        if not ports_data:
            print("❌ 无法获取调试端口")
            return None
        
        # 4. 查找19号窗口的端口
        window19_ports = self.find_window19_port(browsers, window19_candidates, ports_data)
        
        # 5. 输出结果
        print("\n" + "=" * 60)
        print("🎉 查找完成!")
        
        if window19_ports:
            print(f"✅ 找到 {len(window19_ports)} 个19号窗口端口:")
            
            for wp in window19_ports:
                print(f"   🎯 窗口: {wp['name']}")
                print(f"      📱 序号: {wp['seq']}")
                print(f"      🆔 浏览器ID: {wp['browser_id']}")
                print(f"      🔌 调试端口: {wp['port']}")
                print()
            
            # 推荐使用第一个端口
            recommended_port = window19_ports[0]['port']
            print(f"🚀 推荐使用端口: {recommended_port}")
            
            # 保存结果
            result = {
                'window19_ports': window19_ports,
                'recommended_port': recommended_port,
                'all_browsers': browsers,
                'all_ports': ports_data,
                'timestamp': time.time()
            }
            
            with open('window19_port_no_auth.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            print(f"💾 结果已保存到 window19_port_no_auth.json")
            print(f"\n🚀 下一步测试命令:")
            print(f"   python test_debug_port.py {recommended_port}")
            
            return recommended_port
        else:
            print("❌ 未找到19号窗口的调试端口")
            
            if ports_data:
                print(f"\n📊 所有可用端口:")
                for browser_id, port in ports_data.items():
                    print(f"   🔌 {browser_id} -> 端口 {port}")
                
                # 返回第一个可用端口作为备选
                first_port = int(list(ports_data.values())[0])
                print(f"\n💡 可以尝试使用第一个端口: {first_port}")
                return first_port
            
            return None

if __name__ == "__main__":
    api = NoAuthBitBrowserAPI()
    port = api.run()
