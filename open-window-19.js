#!/usr/bin/env node

/**
 * 🚀 打开19号窗口
 * 尝试多种方法打开19号窗口
 */

const axios = require('axios');
const { BITBROWSER_CONFIG, ConfigUtils } = require('./bitbrowser-config');

async function openWindow19() {
    console.log('🚀 尝试打开19号窗口...');
    console.log(`窗口ID: ${BITBROWSER_CONFIG.browser_id}`);
    console.log(`窗口名称: ${BITBROWSER_CONFIG.browser_name}`);
    console.log('');
    
    try {
        // 首先检查当前状态
        console.log('1️⃣ 检查当前窗口状态...');
        const statusResponse = await axios.post(
            ConfigUtils.getApiUrl('/browser/detail'),
            { id: BITBROWSER_CONFIG.browser_id },
            ConfigUtils.getRequestConfig()
        );
        
        if (statusResponse.data.success) {
            const status = statusResponse.data.data.status;
            console.log(`   当前状态: ${getStatusText(status)}`);
            
            if (status === 0) {
                console.log('   ✅ 窗口已经在运行中！');
                return true;
            }
        }
        
        // 方法1: 标准打开
        console.log('\n2️⃣ 方法1: 标准打开...');
        const response1 = await axios.post(
            ConfigUtils.getApiUrl('/browser/open'),
            {
                id: BITBROWSER_CONFIG.browser_id,
                args: [],
                queue: true
            },
            {
                ...ConfigUtils.getRequestConfig(),
                timeout: 60000
            }
        );
        
        console.log('   📊 标准打开响应:', JSON.stringify(response1.data, null, 2));
        
        if (response1.data.success) {
            console.log('   ✅ 19号窗口打开成功！');
            console.log(`   WebSocket: ${response1.data.data.ws}`);
            console.log(`   HTTP端点: ${response1.data.data.http}`);
            console.log(`   进程ID: ${response1.data.data.pid}`);
            
            // 等待窗口完全启动
            console.log('   ⏳ 等待窗口完全启动...');
            await sleep(5000);
            
            // 验证状态
            const verifyResponse = await axios.post(
                ConfigUtils.getApiUrl('/browser/detail'),
                { id: BITBROWSER_CONFIG.browser_id },
                ConfigUtils.getRequestConfig()
            );
            
            if (verifyResponse.data.success) {
                const finalStatus = verifyResponse.data.data.status;
                console.log(`   最终状态: ${getStatusText(finalStatus)}`);
                
                if (finalStatus === 0) {
                    console.log('   🎉 窗口启动验证成功！');
                    return true;
                }
            }
            
            return true;
        }
        
        // 方法2: 重置状态后打开
        console.log('\n3️⃣ 方法2: 重置状态后打开...');
        const resetResponse = await axios.post(
            ConfigUtils.getApiUrl('/browser/closing/reset'),
            { id: BITBROWSER_CONFIG.browser_id },
            ConfigUtils.getRequestConfig()
        );
        
        console.log('   📊 重置响应:', JSON.stringify(resetResponse.data, null, 2));
        
        if (resetResponse.data.success) {
            console.log('   ✅ 状态重置成功，等待2秒...');
            await sleep(2000);
            
            const response2 = await axios.post(
                ConfigUtils.getApiUrl('/browser/open'),
                {
                    id: BITBROWSER_CONFIG.browser_id,
                    args: [],
                    queue: true
                },
                {
                    ...ConfigUtils.getRequestConfig(),
                    timeout: 60000
                }
            );
            
            console.log('   📊 重置后打开响应:', JSON.stringify(response2.data, null, 2));
            
            if (response2.data.success) {
                console.log('   ✅ 重置后19号窗口打开成功！');
                console.log(`   WebSocket: ${response2.data.data.ws}`);
                console.log(`   HTTP端点: ${response2.data.data.http}`);
                console.log(`   进程ID: ${response2.data.data.pid}`);
                return true;
            }
        }
        
        // 方法3: 使用特殊参数
        console.log('\n4️⃣ 方法3: 使用特殊参数打开...');
        const response3 = await axios.post(
            ConfigUtils.getApiUrl('/browser/open'),
            {
                id: BITBROWSER_CONFIG.browser_id,
                args: ['--no-sandbox', '--disable-web-security', '--remote-debugging-address=0.0.0.0'],
                queue: true,
                ignoreDefaultUrls: true
            },
            {
                ...ConfigUtils.getRequestConfig(),
                timeout: 60000
            }
        );
        
        console.log('   📊 特殊参数打开响应:', JSON.stringify(response3.data, null, 2));
        
        if (response3.data.success) {
            console.log('   ✅ 使用特殊参数19号窗口打开成功！');
            console.log(`   WebSocket: ${response3.data.data.ws}`);
            console.log(`   HTTP端点: ${response3.data.data.http}`);
            console.log(`   进程ID: ${response3.data.data.pid}`);
            return true;
        }
        
        // 方法4: 检查是否有其他问题
        console.log('\n5️⃣ 方法4: 诊断问题...');
        
        // 检查是否有其他窗口占用资源
        const listResponse = await axios.post(
            ConfigUtils.getApiUrl('/browser/list'),
            { page: 0, pageSize: 50 },
            ConfigUtils.getRequestConfig()
        );
        
        if (listResponse.data.success) {
            const windows = listResponse.data.data.list || [];
            const runningCount = windows.filter(w => w.status === 0).length;
            console.log(`   📊 当前运行中窗口数: ${runningCount}`);
            
            if (runningCount > 10) {
                console.log('   ⚠️ 运行中窗口较多，可能影响新窗口启动');
            }
        }
        
        console.log('\n❌ 所有自动化方法都失败了');
        console.log('💡 可能的原因:');
        console.log('   1. 窗口被系统限制打开');
        console.log('   2. 需要付费版本才能API启动');
        console.log('   3. 窗口配置有问题');
        console.log('   4. 资源不足或权限限制');
        console.log('');
        console.log('🛠️ 建议解决方案:');
        console.log('   1. 在比特浏览器界面中手动启动19号窗口');
        console.log('   2. 检查窗口权限设置');
        console.log('   3. 确认账户是否有API启动权限');
        
        return false;
        
    } catch (error) {
        console.error('❌ 打开19号窗口失败:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 请确保比特浏览器已启动并开启Local API功能');
        } else if (error.response) {
            console.log('📊 错误响应:', JSON.stringify(error.response.data, null, 2));
        }
        
        return false;
    }
}

function getStatusText(statusCode) {
    switch (statusCode) {
        case 0: return '运行中';
        case 1: return '启动中';
        case 2: return '未运行';
        case 3: return '关闭中';
        default: return `未知状态(${statusCode})`;
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

if (require.main === module) {
    openWindow19().then(success => {
        if (success) {
            console.log('\n🎉 19号窗口打开成功！');
        } else {
            console.log('\n❌ 19号窗口打开失败！');
        }
    }).catch(error => {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { openWindow19 };
