#!/usr/bin/env node

/**
 * 🎯 优化评论解析器
 * 基于已加载的评论进行精确解析，提高提取成功率
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class OptimizedCommentParser {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    async connectToBrowser() {
        try {
            console.log('🔗 连接到比特浏览器...');
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${this.debugPort}`,
                defaultViewport: null
            });
            
            const pages = await browser.pages();
            const page = pages.find(p => p.url().includes('xiaohongshu.com/explore/'));
            
            if (!page) {
                throw new Error('未找到小红书笔记页面');
            }
            
            console.log('✅ 找到小红书笔记页面');
            return { browser, page };
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    // 优化的评论提取
    async optimizedExtractComments(page) {
        console.log('🎯 开始优化评论提取...');
        
        const result = await page.evaluate(() => {
            const data = {
                noteInfo: {
                    id: '',
                    title: '',
                    author: '',
                    totalCommentCount: 0
                },
                comments: [],
                extractStats: {
                    totalTextLength: 0,
                    rawCommentElements: 0,
                    processedComments: 0
                },
                extractTime: new Date().toISOString()
            };
            
            try {
                // 获取基本信息
                const pageText = document.body.textContent;
                data.extractStats.totalTextLength = pageText.length;
                
                // 提取笔记ID
                const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
                if (urlMatch) {
                    data.noteInfo.id = urlMatch[1];
                }
                
                // 提取标题
                const titleElement = document.querySelector('title');
                if (titleElement) {
                    data.noteInfo.title = titleElement.textContent.replace(' - 小红书', '');
                }
                
                // 提取评论总数
                const commentCountMatch = pageText.match(/(\d+)\s*条评论/);
                if (commentCountMatch) {
                    data.noteInfo.totalCommentCount = parseInt(commentCountMatch[1]);
                }
                
                // 提取作者
                if (pageText.includes('漫娴学姐')) {
                    data.noteInfo.author = '漫娴学姐 招暑假工版';
                }
                
                return data;
            } catch (error) {
                console.error('提取基本信息时出错:', error);
                data.error = error.message;
                return data;
            }
        });
        
        // 在Node.js中进行优化解析
        const pageText = await page.evaluate(() => document.body.textContent);
        const comments = this.optimizedParseComments(pageText);
        result.comments = comments;
        result.extractStats.processedComments = comments.length;
        
        return result;
    }

    // 优化的评论解析算法
    optimizedParseComments(pageText) {
        console.log('🧠 开始优化评论解析...');
        console.log(`📄 页面文本长度: ${pageText.length}`);
        
        const comments = [];
        
        // 第一步：使用多种策略分割文本
        const splitStrategies = [
            // 策略1：按时间格式分割
            () => this.splitByTimePattern(pageText),
            
            // 策略2：按关键词分割
            () => this.splitByKeywords(pageText),
            
            // 策略3：按行分割后重组
            () => this.splitByLinesAndRegroup(pageText)
        ];
        
        let bestComments = [];
        let maxCommentCount = 0;
        
        for (let i = 0; i < splitStrategies.length; i++) {
            console.log(`🔍 尝试解析策略 ${i + 1}...`);
            
            try {
                const strategyComments = splitStrategies[i]();
                console.log(`   📝 策略 ${i + 1} 提取到 ${strategyComments.length} 条评论`);
                
                if (strategyComments.length > maxCommentCount) {
                    maxCommentCount = strategyComments.length;
                    bestComments = strategyComments;
                    console.log(`   ✅ 策略 ${i + 1} 效果最佳`);
                }
            } catch (error) {
                console.log(`   ❌ 策略 ${i + 1} 失败: ${error.message}`);
            }
        }
        
        console.log(`✅ 优化解析完成，最终提取到 ${bestComments.length} 条评论`);
        return bestComments;
    }

    // 策略1：按时间格式分割
    splitByTimePattern(pageText) {
        const comments = [];
        
        // 查找所有时间格式的位置
        const timePattern = /(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/g;
        const timeMatches = [];
        let match;
        
        while ((match = timePattern.exec(pageText)) !== null) {
            timeMatches.push({
                time: match[1],
                index: match.index,
                endIndex: match.index + match[0].length
            });
        }
        
        console.log(`   📅 找到 ${timeMatches.length} 个时间标记`);
        
        // 基于时间标记分割评论
        for (let i = 0; i < timeMatches.length; i++) {
            const currentTime = timeMatches[i];
            const nextTime = timeMatches[i + 1];
            
            // 确定评论内容范围
            const startIndex = Math.max(0, currentTime.index - 100); // 时间前100字符
            const endIndex = nextTime ? nextTime.index : currentTime.index + 500; // 到下一个时间或500字符
            
            const commentText = pageText.substring(startIndex, endIndex).trim();
            
            if (commentText.length > 10 && commentText.length < 1000) {
                const comment = this.parseCommentFromText(commentText, comments.length + 1);
                if (comment) {
                    comments.push(comment);
                }
            }
        }
        
        return comments;
    }

    // 策略2：按关键词分割
    splitByKeywords(pageText) {
        const comments = [];
        
        // 关键词模式
        const keywordPatterns = [
            /求带/g,
            /宝子/g,
            /学姐/g,
            /兼职/g,
            /聊天员/g,
            /陪玩/g,
            /小红薯[A-F0-9]{8}/g
        ];
        
        const keywordMatches = [];
        
        keywordPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                keywordMatches.push({
                    keyword: match[0],
                    index: match.index
                });
            }
        });
        
        // 按位置排序
        keywordMatches.sort((a, b) => a.index - b.index);
        
        console.log(`   🔑 找到 ${keywordMatches.length} 个关键词标记`);
        
        // 基于关键词分割评论
        for (let i = 0; i < keywordMatches.length; i++) {
            const currentKeyword = keywordMatches[i];
            const nextKeyword = keywordMatches[i + 1];
            
            const startIndex = Math.max(0, currentKeyword.index - 50);
            const endIndex = nextKeyword ? nextKeyword.index + 50 : currentKeyword.index + 300;
            
            const commentText = pageText.substring(startIndex, endIndex).trim();
            
            if (commentText.length > 15 && commentText.length < 800) {
                const comment = this.parseCommentFromText(commentText, comments.length + 1);
                if (comment && !this.isDuplicateComment(comment, comments)) {
                    comments.push(comment);
                }
            }
        }
        
        return comments;
    }

    // 策略3：按行分割后重组
    splitByLinesAndRegroup(pageText) {
        const comments = [];
        
        // 按行分割
        const lines = pageText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 3);
        
        console.log(`   📄 处理 ${lines.length} 行文本`);
        
        let currentComment = null;
        let commentIndex = 0;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            // 跳过无关行
            if (this.shouldSkipLine(line)) continue;
            
            // 检查是否是新评论开始
            if (this.isCommentStart(line)) {
                // 保存前一个评论
                if (currentComment && this.isValidCommentContent(currentComment.content)) {
                    const finalComment = this.finalizeCommentFromLines(currentComment, ++commentIndex);
                    if (finalComment) {
                        comments.push(finalComment);
                    }
                }
                
                // 开始新评论
                currentComment = {
                    lines: [line],
                    content: line
                };
            } else if (currentComment && line.length > 2) {
                // 添加到当前评论
                currentComment.lines.push(line);
                currentComment.content += ' ' + line;
                
                // 限制评论长度
                if (currentComment.content.length > 1000) {
                    const finalComment = this.finalizeCommentFromLines(currentComment, ++commentIndex);
                    if (finalComment) {
                        comments.push(finalComment);
                    }
                    currentComment = null;
                }
            }
        }
        
        // 处理最后一个评论
        if (currentComment && this.isValidCommentContent(currentComment.content)) {
            const finalComment = this.finalizeCommentFromLines(currentComment, ++commentIndex);
            if (finalComment) {
                comments.push(finalComment);
            }
        }
        
        return comments;
    }

    // 从文本解析单个评论
    parseCommentFromText(text, id) {
        const comment = {
            id: id,
            userId: '',
            username: '',
            content: '',
            time: '',
            likes: 0,
            replyCount: 0,
            isAuthor: text.includes('作者'),
            isPinned: text.includes('置顶'),
            extractedInfo: {}
        };
        
        // 提取时间
        const timeMatch = text.match(/(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/);
        if (timeMatch) {
            comment.time = timeMatch[1];
        }
        
        // 提取用户名
        const userPatterns = [
            /^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(?:求带|宝子|学姐)/
        ];
        
        for (const pattern of userPatterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                comment.username = match[1].trim();
                break;
            }
        }
        
        // 清理内容
        let content = text;
        content = content.replace(/\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前/g, '');
        content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');
        content = content.replace(/作者|置顶评论|回复|赞|仅自己可见/g, '');
        if (comment.username) {
            content = content.replace(comment.username, '');
        }
        content = content.replace(/\s+/g, ' ').trim();
        
        comment.content = content;
        
        // 提取数字信息
        const likeMatch = text.match(/(\d+)\s*赞/);
        if (likeMatch) {
            comment.likes = parseInt(likeMatch[1]);
        }
        
        const replyMatch = text.match(/(\d+)\s*回复/);
        if (replyMatch) {
            comment.replyCount = parseInt(replyMatch[1]);
        }
        
        // 提取用户ID
        const userIdMatch = text.match(/小红薯([A-F0-9]{8,})/);
        if (userIdMatch) {
            comment.userId = userIdMatch[1];
        }
        
        return comment.content.length >= 5 ? comment : null;
    }

    // 从行数组完善评论
    finalizeCommentFromLines(commentData, id) {
        const comment = {
            id: id,
            userId: '',
            username: '',
            content: '',
            time: '',
            likes: 0,
            replyCount: 0,
            isAuthor: commentData.content.includes('作者'),
            isPinned: commentData.content.includes('置顶'),
            extractedInfo: {}
        };

        const allText = commentData.content;

        // 提取时间
        const timeMatch = allText.match(/(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/);
        if (timeMatch) {
            comment.time = timeMatch[1];
        }

        // 提取用户名
        const userPatterns = [
            /^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/,
            /^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/
        ];

        for (const pattern of userPatterns) {
            const match = allText.match(pattern);
            if (match && match[1]) {
                comment.username = match[1].trim();
                break;
            }
        }

        // 清理内容
        let content = allText;
        content = content.replace(/\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前/g, '');
        content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');
        content = content.replace(/作者|置顶评论|回复|赞|仅自己可见/g, '');
        if (comment.username) {
            content = content.replace(comment.username, '');
        }
        content = content.replace(/\s+/g, ' ').trim();

        comment.content = content;

        // 提取数字信息
        const likeMatch = allText.match(/(\d+)\s*赞/);
        if (likeMatch) {
            comment.likes = parseInt(likeMatch[1]);
        }

        const replyMatch = allText.match(/(\d+)\s*回复/);
        if (replyMatch) {
            comment.replyCount = parseInt(replyMatch[1]);
        }

        // 提取用户ID
        const userIdMatch = allText.match(/小红薯([A-F0-9]{8,})/);
        if (userIdMatch) {
            comment.userId = userIdMatch[1];
        }

        return comment;
    }

    // 判断是否应该跳过这一行
    shouldSkipLine(line) {
        const skipPatterns = [
            '加载中', '你还没有', '温馨提示', '广告屏蔽', '我知道了',
            '沪ICP备', '营业执照', '公网安备', '增值电信', '医疗器械',
            '创作服务', '直播管理', '专业号', '商家入驻', 'MCN入驻',
            'window.', 'function', 'console.', 'document.', '更多',
            '行吟信息', '黄浦区', '举报电话', '举报中心', '网络文化',
            '相关推荐', '猜你喜欢', '更多笔记', '发现', '首页', '关注',
            '点赞', '收藏', '分享', '评论', '转发'
        ];

        return skipPatterns.some(pattern => line.includes(pattern)) ||
               line.length < 3 ||
               /^[0-9\s\-:\.]+$/.test(line) ||
               /^[^\w\u4e00-\u9fff]+$/.test(line);
    }

    // 判断是否是评论开始
    isCommentStart(line) {
        // 作者标识
        if (line.includes('作者') && line.length < 100) return true;

        // 置顶评论
        if (line.includes('置顶评论') || line.includes('置顶')) return true;

        // 时间格式
        if (line.match(/\d{2}-\d{2}/)) return true;
        if (line.match(/\d+小时前|\d+分钟前|\d+天前/)) return true;

        // 表情符号开头
        if (line.match(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/)) return true;

        // 关键词开头
        const keywords = ['求带', '宝子', '广州', '学姐', '兼职', '工作', '聊天员', '陪玩', '小红薯'];
        if (keywords.some(keyword => line.includes(keyword)) && line.length < 150) {
            return true;
        }

        return false;
    }

    // 验证评论内容是否有效
    isValidCommentContent(content) {
        return content &&
               content.length >= 10 &&
               content.length <= 1000 &&
               !content.includes('window.') &&
               !content.includes('function');
    }

    // 检查是否是重复评论
    isDuplicateComment(newComment, existingComments) {
        return existingComments.some(existing => {
            const similarity = this.calculateSimilarity(newComment.content, existing.content);
            return similarity > 0.8;
        });
    }

    // 计算文本相似度
    calculateSimilarity(text1, text2) {
        if (!text1 || !text2) return 0;

        const len1 = text1.length;
        const len2 = text2.length;
        const maxLen = Math.max(len1, len2);

        if (maxLen === 0) return 1;

        // 简单的字符匹配相似度
        let matches = 0;
        const minLen = Math.min(len1, len2);

        for (let i = 0; i < minLen; i++) {
            if (text1[i] === text2[i]) {
                matches++;
            }
        }

        return matches / maxLen;
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        try {
            console.log('🎯 启动优化评论解析器...');
            console.log('🛡️ 策略: 多重解析算法 + 智能去重 + 精确提取');

            const { browser, page } = await this.connectToBrowser();

            // 优化评论提取
            const result = await this.optimizedExtractComments(page);

            // 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `optimized_comments_${result.noteInfo.id}_${timestamp}.json`;
            this.saveToFile(result, filename);

            // 输出结果
            console.log('\n🎉 优化解析完成！');
            console.log('📊 最终统计:');
            console.log(`   🆔 笔记ID: ${result.noteInfo.id}`);
            console.log(`   📝 笔记标题: ${result.noteInfo.title || '未知'}`);
            console.log(`   👤 笔记作者: ${result.noteInfo.author || '未知'}`);
            console.log(`   🎯 目标评论数: ${result.noteInfo.totalCommentCount}`);
            console.log(`   💬 实际提取数: ${result.comments.length}`);
            console.log(`   📈 完成度: ${Math.round(result.comments.length / result.noteInfo.totalCommentCount * 100)}%`);
            console.log(`   📄 页面文本长度: ${result.extractStats.totalTextLength}`);
            console.log(`   📁 保存文件: ${filename}`);

            if (result.comments.length > 0) {
                console.log('\n👥 评论详细预览:');
                result.comments.slice(0, 10).forEach((comment, index) => {
                    console.log(`\n   ${index + 1}. 评论ID: ${comment.id}`);
                    console.log(`      👤 用户: ${comment.username || '未知'}`);
                    console.log(`      🆔 用户ID: ${comment.userId || '未提取到'}`);
                    console.log(`      ⏰ 时间: ${comment.time || '未知'}`);
                    console.log(`      💬 内容: ${comment.content.substring(0, 80)}${comment.content.length > 80 ? '...' : ''}`);
                    console.log(`      👍 点赞: ${comment.likes} | 💬 回复: ${comment.replyCount}`);
                    console.log(`      🏷️ 标识: ${comment.isAuthor ? '作者' : ''}${comment.isPinned ? '置顶' : ''}${!comment.isAuthor && !comment.isPinned ? '普通' : ''}`);
                });

                // 质量统计
                const withUsername = result.comments.filter(c => c.username).length;
                const withUserId = result.comments.filter(c => c.userId).length;
                const withTime = result.comments.filter(c => c.time).length;
                const withLikes = result.comments.filter(c => c.likes > 0).length;
                const withReplies = result.comments.filter(c => c.replyCount > 0).length;

                console.log('\n📈 数据质量统计:');
                console.log(`   👤 有用户名: ${withUsername}/${result.comments.length} (${Math.round(withUsername/result.comments.length*100)}%)`);
                console.log(`   🆔 有用户ID: ${withUserId}/${result.comments.length} (${Math.round(withUserId/result.comments.length*100)}%)`);
                console.log(`   ⏰ 有时间: ${withTime}/${result.comments.length} (${Math.round(withTime/result.comments.length*100)}%)`);
                console.log(`   👍 有点赞数: ${withLikes}/${result.comments.length} (${Math.round(withLikes/result.comments.length*100)}%)`);
                console.log(`   💬 有回复数: ${withReplies}/${result.comments.length} (${Math.round(withReplies/result.comments.length*100)}%)`);

                // 成功度评估
                const completionRate = result.comments.length / result.noteInfo.totalCommentCount;
                if (completionRate >= 0.5) {
                    console.log('\n🎉 优秀！已提取到大量评论数据！');
                } else if (completionRate >= 0.2) {
                    console.log('\n👍 良好！提取效果显著改善！');
                } else if (completionRate >= 0.1) {
                    console.log('\n📈 进步！解析算法有效提升！');
                } else {
                    console.log('\n💡 提示：可能需要进一步优化解析策略');
                }

                // 兼职信息分析
                const jobKeywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱'];
                const jobComments = result.comments.filter(c =>
                    jobKeywords.some(keyword => c.content.includes(keyword))
                );

                if (jobComments.length > 0) {
                    console.log(`\n💼 兼职相关评论: ${jobComments.length}/${result.comments.length} (${Math.round(jobComments.length/result.comments.length*100)}%)`);
                }
            }

            await browser.disconnect();

        } catch (error) {
            console.error('❌ 优化解析失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行优化解析器
if (require.main === module) {
    const parser = new OptimizedCommentParser();
    parser.run();
}

module.exports = OptimizedCommentParser;
