{"name": "heimo-tech-platform", "version": "1.0.0", "description": "黑默科技 - 桌面端营销管理平台", "main": "electron-main.js", "homepage": "http://./", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["heimo", "management", "platform", "nodejs"], "author": "BTX科技", "license": "MIT", "dependencies": {"axios": "^1.11.0", "body-parser": "^1.20.2", "concurrently": "^8.2.0", "cors": "^2.8.5", "express": "^4.18.2", "moment": "^2.29.4", "puppeteer": "^24.15.0", "puppeteer-core": "^24.15.0", "selenium-webdriver": "^4.34.0", "socket.io": "^4.7.2", "uuid": "^9.0.1", "wait-on": "^7.0.1", "ws": "^8.18.3"}, "devDependencies": {"electron": "^25.3.1", "electron-builder": "^24.6.3", "nodemon": "^3.0.1"}, "build": {"appId": "com.heimo.tech.platform", "productName": "黑默科技平台", "directories": {"output": "dist"}, "files": ["electron-main.js", "server.js", "public/**/*", "routes/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}