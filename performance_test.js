/**
 * 🚀 爬虫性能测试
 * 测试爬虫的速度、稳定性和资源使用情况
 */

const { performance } = require('perf_hooks');
const fs = require('fs');
const FinalXiaohongshuScraper = require('./puppeteer_final.js');

class PerformanceTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * 记录性能指标
     */
    recordMetric(metric, value, unit = '') {
        const record = {
            metric,
            value,
            unit,
            timestamp: new Date().toISOString()
        };
        this.testResults.push(record);
        console.log(`📊 ${metric}: ${value}${unit}`);
    }

    /**
     * 测试单次爬取性能
     */
    async testSingleScrapePerformance() {
        console.log('🚀 测试单次爬取性能...');
        
        const startTime = performance.now();
        const startMemory = process.memoryUsage();
        
        try {
            const scraper = new FinalXiaohongshuScraper();
            const success = await scraper.run();
            
            const endTime = performance.now();
            const endMemory = process.memoryUsage();
            
            // 计算性能指标
            const duration = Math.round(endTime - startTime);
            const memoryUsed = Math.round((endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024);
            
            this.recordMetric('爬取时间', duration, 'ms');
            this.recordMetric('内存使用', memoryUsed, 'MB');
            this.recordMetric('爬取成功', success ? 'Yes' : 'No');
            
            if (success) {
                // 检查最新结果文件
                const files = fs.readdirSync('.');
                const jsonFiles = files.filter(file => 
                    file.startsWith('xiaohongshu_comments_final_') && 
                    file.endsWith('.json')
                );
                
                if (jsonFiles.length > 0) {
                    const latestFile = jsonFiles.sort().pop();
                    const content = fs.readFileSync(latestFile, 'utf8');
                    const data = JSON.parse(content);
                    
                    const commentCount = data.comments.length;
                    const avgTimePerComment = Math.round(duration / commentCount);
                    
                    this.recordMetric('评论数量', commentCount, '条');
                    this.recordMetric('平均每条评论耗时', avgTimePerComment, 'ms');
                }
            }
            
            return { success, duration, memoryUsed };
            
        } catch (error) {
            const endTime = performance.now();
            const duration = Math.round(endTime - startTime);
            
            this.recordMetric('爬取时间', duration, 'ms');
            this.recordMetric('爬取成功', 'No');
            this.recordMetric('错误信息', error.message);
            
            return { success: false, duration, error: error.message };
        }
    }

    /**
     * 测试连接稳定性
     */
    async testConnectionStability() {
        console.log('\n🔗 测试连接稳定性...');
        
        const testCount = 3;
        let successCount = 0;
        const durations = [];
        
        for (let i = 1; i <= testCount; i++) {
            console.log(`\n📡 连接测试 ${i}/${testCount}`);
            
            try {
                const startTime = performance.now();
                
                // 只测试连接，不进行完整爬取
                const scraper = new FinalXiaohongshuScraper();
                const debugInfo = await scraper.getDebugInfo();
                
                const endTime = performance.now();
                const duration = Math.round(endTime - startTime);
                
                durations.push(duration);
                successCount++;
                
                console.log(`   ✅ 连接成功，耗时: ${duration}ms`);
                
                // 等待一秒再进行下次测试
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                console.log(`   ❌ 连接失败: ${error.message}`);
            }
        }
        
        const successRate = Math.round((successCount / testCount) * 100);
        const avgDuration = durations.length > 0 ? Math.round(durations.reduce((a, b) => a + b, 0) / durations.length) : 0;
        
        this.recordMetric('连接成功率', successRate, '%');
        this.recordMetric('平均连接时间', avgDuration, 'ms');
        
        return { successRate, avgDuration };
    }

    /**
     * 测试资源使用情况
     */
    async testResourceUsage() {
        console.log('\n💾 测试资源使用情况...');
        
        const initialMemory = process.memoryUsage();
        console.log('📊 初始内存使用:');
        console.log(`   堆内存: ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`);
        console.log(`   总内存: ${Math.round(initialMemory.rss / 1024 / 1024)}MB`);
        
        this.recordMetric('初始堆内存', Math.round(initialMemory.heapUsed / 1024 / 1024), 'MB');
        this.recordMetric('初始总内存', Math.round(initialMemory.rss / 1024 / 1024), 'MB');
        
        // 运行一次完整爬取
        await this.testSingleScrapePerformance();
        
        const finalMemory = process.memoryUsage();
        console.log('\n📊 最终内存使用:');
        console.log(`   堆内存: ${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`);
        console.log(`   总内存: ${Math.round(finalMemory.rss / 1024 / 1024)}MB`);
        
        this.recordMetric('最终堆内存', Math.round(finalMemory.heapUsed / 1024 / 1024), 'MB');
        this.recordMetric('最终总内存', Math.round(finalMemory.rss / 1024 / 1024), 'MB');
        
        const memoryIncrease = Math.round((finalMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024);
        this.recordMetric('内存增长', memoryIncrease, 'MB');
        
        return { initialMemory, finalMemory, memoryIncrease };
    }

    /**
     * 生成性能报告
     */
    generatePerformanceReport() {
        console.log('\n📈 生成性能报告...');
        
        const report = {
            test_info: {
                test_time: new Date().toISOString(),
                node_version: process.version,
                platform: process.platform,
                arch: process.arch
            },
            performance_metrics: this.testResults,
            summary: {
                total_metrics: this.testResults.length,
                test_duration: 'Multiple tests completed'
            }
        };
        
        // 保存报告
        const reportFile = `performance_report_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
        fs.writeFileSync(reportFile, JSON.stringify(report, null, 2), 'utf8');
        
        console.log(`📄 性能报告已保存: ${reportFile}`);
        
        return report;
    }

    /**
     * 运行所有性能测试
     */
    async runAllTests() {
        console.log('🚀 小红书评论爬虫性能测试');
        console.log('='.repeat(50));
        
        try {
            // 1. 连接稳定性测试
            await this.testConnectionStability();
            
            // 2. 资源使用测试
            await this.testResourceUsage();
            
            // 3. 生成报告
            const report = this.generatePerformanceReport();
            
            console.log('\n🎯 性能测试完成!');
            console.log('📊 主要指标:');
            
            // 显示关键指标
            const keyMetrics = this.testResults.filter(result => 
                ['爬取时间', '评论数量', '连接成功率', '内存增长'].includes(result.metric)
            );
            
            keyMetrics.forEach(metric => {
                console.log(`   ${metric.metric}: ${metric.value}${metric.unit}`);
            });
            
            return report;
            
        } catch (error) {
            console.error('❌ 性能测试失败:', error.message);
            return null;
        }
    }
}

// 主函数
async function main() {
    const tester = new PerformanceTest();
    await tester.runAllTests();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = PerformanceTest;
