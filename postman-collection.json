{"info": {"name": "黑默科技 - 小红书数据采集API", "description": "Local API接口集合 - 所有接口使用POST方法，body传参JSON格式", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}], "item": [{"name": "🏥 健康检查API", "item": [{"name": "完整健康检查", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/xiaohongshu/health", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "health"]}, "description": "完整的服务器健康检查，包含比特浏览器连接状态"}}, {"name": "简化健康检查", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/xiaohongshu/health/simple", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "health", "simple"]}, "description": "简化的健康检查，快速返回基本状态"}}]}, {"name": "🌐 比特浏览器管理API", "item": [{"name": "获取浏览器列表", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"page\": 1,\n  \"pageSize\": 20\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/xiaohongshu/bitbrowser/list", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bitbrowser", "list"]}, "description": "获取比特浏览器实例列表"}}, {"name": "启动浏览器实例", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"browserId\": \"0d094596cb404282be3f814b98139c74\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/xiaohongshu/bitbrowser/start", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bitbrowser", "start"]}, "description": "启动指定的浏览器实例（browserId可选，默认使用目标浏览器）"}}, {"name": "停止浏览器实例", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"browserId\": \"0d094596cb404282be3f814b98139c74\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/xiaohongshu/bitbrowser/stop", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bitbrowser", "stop"]}, "description": "停止指定的浏览器实例（browserId可选，默认使用目标浏览器）"}}, {"name": "测试浏览器连接", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/xiaohongshu/test-browser-connection", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test-browser-connection"]}, "description": "测试比特浏览器连接和小红书页面状态"}}]}, {"name": "📱 小红书数据采集API", "item": [{"name": "获取账号信息", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"url\": \"https://www.xiaohongshu.com/user/profile/5ff0e4ac000000000100d1b4\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/xiaohongshu/get-account-info", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "get-account-info"]}, "description": "获取小红书账号详细信息"}}, {"name": "获取笔记列表", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"accountId\": \"5ff0e4ac000000000100d1b4\",\n  \"limit\": 20\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/xiaohongshu/get-notes", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "get-notes"]}, "description": "获取指定账号的笔记列表"}}]}]}