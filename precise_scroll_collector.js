#!/usr/bin/env node

/**
 * 🎯 精确滚动评论采集器
 * 专门针对评论区域进行精确滚动控制
 */

const axios = require('axios');
const WebSocket = require('ws');

class PreciseScrollCollector {
    constructor() {
        this.debugPort = null;
    }

    // 🎯 主要采集方法
    async collectWithPreciseScroll() {
        console.log('🎯 启动精确滚动评论采集器...\n');

        try {
            // 1. 获取调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 当前页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 执行精确滚动采集
            console.log('🔄 执行精确滚动评论采集...');
            const commentsData = await this.executePreciseScrollCollection(tab);
            
            return commentsData;

        } catch (error) {
            console.error('❌ 精确滚动采集失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取调试端口
    async getCurrentDebugPort() {
        try {
            const commonPorts = [63524, 51859, 58222, 9222];
            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到端口: ${port}`);
                    return;
                } catch (error) {
                    // 继续尝试
                }
            }
            throw new Error('未找到可用端口');
        } catch (error) {
            throw new Error(`获取端口失败: ${error.message}`);
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
            timeout: 5000
        });

        return response.data.find(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );
    }

    // 🔄 执行精确滚动采集
    async executePreciseScrollCollection(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ 精确滚动采集WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const preciseScrollScript = `
                        (async function() {
                            try {
                                console.log('🎯 开始精确滚动评论采集...');
                                
                                const commentsData = {
                                    noteUrl: window.location.href,
                                    noteTitle: document.title.replace(' - 小红书', '').trim(),
                                    timestamp: new Date().toISOString(),
                                    comments: [],
                                    summary: {
                                        totalComments: 0,
                                        totalReplies: 0,
                                        totalLikes: 0,
                                        scrollStrategy: 'precise-comment-area'
                                    }
                                };

                                // 第一步：定位评论区域
                                console.log('🔍 第一步：定位评论区域...');
                                
                                let commentContainer = null;
                                const containerSelectors = [
                                    '.comments-container',
                                    '.comment-list', 
                                    '.interaction-container',
                                    '[class*="comment-section"]',
                                    '[class*="comment-wrapper"]',
                                    '[class*="comment-area"]'
                                ];

                                // 查找评论容器
                                for (const selector of containerSelectors) {
                                    const containers = document.querySelectorAll(selector);
                                    for (const container of containers) {
                                        if (container.scrollHeight > container.clientHeight) {
                                            commentContainer = container;
                                            console.log(\`✅ 找到可滚动评论容器: \${selector}\`);
                                            break;
                                        }
                                    }
                                    if (commentContainer) break;
                                }

                                // 如果没找到专门的容器，查找包含评论最多的区域
                                if (!commentContainer) {
                                    console.log('🔍 查找评论密集区域...');
                                    const allDivs = document.querySelectorAll('div');
                                    let maxComments = 0;
                                    
                                    allDivs.forEach(div => {
                                        const avatars = div.querySelectorAll('img[src*="avatar"]');
                                        if (avatars.length > maxComments) {
                                            maxComments = avatars.length;
                                            commentContainer = div;
                                        }
                                    });
                                    
                                    if (commentContainer) {
                                        console.log(\`✅ 找到评论密集区域，包含 \${maxComments} 个头像\`);
                                    }
                                }

                                if (!commentContainer) {
                                    console.log('⚠️ 未找到专门的评论容器，使用整个页面');
                                    commentContainer = document.body;
                                }

                                // 第二步：精确滚动评论区域
                                console.log('📜 第二步：精确滚动评论区域...');
                                
                                let totalClicked = 0;
                                let lastCommentCount = 0;
                                let noChangeRounds = 0;
                                const maxRounds = 50;
                                
                                for (let round = 0; round < maxRounds && noChangeRounds < 5; round++) {
                                    console.log(\`🔄 第 \${round + 1} 轮精确滚动...\`);
                                    
                                    // 滚动评论容器到底部
                                    if (commentContainer.scrollHeight > commentContainer.clientHeight) {
                                        commentContainer.scrollTop = commentContainer.scrollHeight;
                                        console.log(\`   📜 容器滚动: \${commentContainer.scrollTop}/\${commentContainer.scrollHeight}\`);
                                    } else {
                                        // 如果容器不可滚动，滚动页面
                                        window.scrollTo(0, document.body.scrollHeight);
                                        console.log(\`   📜 页面滚动: \${window.pageYOffset}/\${document.body.scrollHeight}\`);
                                    }
                                    
                                    // 等待内容加载
                                    await new Promise(resolve => setTimeout(resolve, 1000));
                                    
                                    // 在评论区域内查找并点击"更多"按钮
                                    const moreButtonTexts = [
                                        '查看更多评论', '展开更多', '加载更多', '更多评论',
                                        '查看全部评论', '展开全部', '查看更多回复', '展开回复',
                                        '更多', '展开', '查看', '条回复'
                                    ];
                                    
                                    let clickedInRound = 0;
                                    
                                    for (const buttonText of moreButtonTexts) {
                                        // 只在评论容器内查找按钮
                                        const elements = Array.from(commentContainer.querySelectorAll('*')).filter(el => {
                                            const text = el.textContent.trim();
                                            const isVisible = el.offsetParent !== null;
                                            const isClickable = el.tagName === 'BUTTON' || 
                                                              el.tagName === 'A' ||
                                                              el.onclick || 
                                                              el.className.includes('btn') ||
                                                              el.className.includes('button') ||
                                                              el.className.includes('more') ||
                                                              el.className.includes('expand') ||
                                                              getComputedStyle(el).cursor === 'pointer';
                                            
                                            return isVisible && isClickable && (
                                                text.includes(buttonText) ||
                                                (buttonText === '条回复' && /\\d+条回复/.test(text))
                                            );
                                        });
                                        
                                        for (const el of elements) {
                                            try {
                                                // 滚动到按钮可见
                                                el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                                await new Promise(resolve => setTimeout(resolve, 300));
                                                
                                                // 点击按钮
                                                el.click();
                                                clickedInRound++;
                                                totalClicked++;
                                                
                                                console.log(\`   🖱️ 点击: \${el.textContent.trim().substring(0, 20)}...\`);
                                                
                                                // 等待内容加载
                                                await new Promise(resolve => setTimeout(resolve, 800));
                                                
                                            } catch (e) {
                                                // 忽略点击错误
                                            }
                                        }
                                    }
                                    
                                    // 统计当前评论数量
                                    const currentComments = commentContainer.querySelectorAll('img[src*="avatar"]').length;
                                    console.log(\`   📊 当前评论数: \${currentComments}, 点击按钮: \${clickedInRound}个\`);
                                    
                                    // 检查是否有新评论加载
                                    if (currentComments === lastCommentCount && clickedInRound === 0) {
                                        noChangeRounds++;
                                        console.log(\`   ⚠️ 无变化轮次: \${noChangeRounds}/5\`);
                                    } else {
                                        noChangeRounds = 0;
                                        lastCommentCount = currentComments;
                                    }
                                    
                                    // 随机等待，模拟人类行为
                                    const waitTime = 1000 + Math.random() * 2000;
                                    await new Promise(resolve => setTimeout(resolve, waitTime));
                                }
                                
                                console.log(\`🎉 精确滚动完成! 总共点击了 \${totalClicked} 个按钮\`);

                                // 第三步：最终滚动确保所有内容加载
                                console.log('📜 第三步：最终滚动确保内容加载...');
                                for (let i = 0; i < 10; i++) {
                                    if (commentContainer.scrollHeight > commentContainer.clientHeight) {
                                        commentContainer.scrollTop = commentContainer.scrollHeight;
                                    } else {
                                        window.scrollTo(0, document.body.scrollHeight);
                                    }
                                    await new Promise(resolve => setTimeout(resolve, 500));
                                }

                                // 第四步：提取所有评论
                                console.log('🔍 第四步：提取所有评论...');
                                
                                // 使用多种策略在评论容器内查找评论
                                const commentElements = new Set();
                                
                                // 策略1: 基于头像查找
                                const avatars = commentContainer.querySelectorAll('img[src*="avatar"]');
                                console.log(\`   策略1: 找到 \${avatars.length} 个头像\`);
                                avatars.forEach(img => {
                                    const commentDiv = img.closest('div');
                                    if (commentDiv && commentDiv.textContent.trim().length > 20) {
                                        commentElements.add(commentDiv);
                                    }
                                });
                                
                                // 策略2: 基于时间信息查找
                                const timeElements = Array.from(commentContainer.querySelectorAll('*')).filter(el => {
                                    const text = el.textContent.trim();
                                    return /\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}-\\d{2}/.test(text);
                                });
                                console.log(\`   策略2: 找到 \${timeElements.length} 个时间元素\`);
                                timeElements.forEach(timeEl => {
                                    const commentDiv = timeEl.closest('div');
                                    if (commentDiv && commentDiv.textContent.trim().length > 20) {
                                        commentElements.add(commentDiv);
                                    }
                                });
                                
                                // 策略3: 基于评论关键词查找
                                const keywordDivs = Array.from(commentContainer.querySelectorAll('div')).filter(div => {
                                    const text = div.textContent.trim();
                                    return text.length > 20 && text.length < 1000 && 
                                           (text.includes('回复') || text.includes('点赞') || 
                                            /\\d+天前|\\d+小时前/.test(text));
                                });
                                console.log(\`   策略3: 找到 \${keywordDivs.length} 个关键词div\`);
                                keywordDivs.forEach(div => commentElements.add(div));

                                console.log(\`🔍 总共找到 \${commentElements.size} 个独特的评论元素\`);

                                // 提取评论详细信息
                                Array.from(commentElements).forEach((element, index) => {
                                    try {
                                        const commentData = {
                                            id: index + 1,
                                            username: '',
                                            avatar: '',
                                            content: '',
                                            publishTime: '',
                                            likes: 0
                                        };

                                        // 提取用户名
                                        const textNodes = Array.from(element.querySelectorAll('*')).filter(el => 
                                            el.children.length === 0 && 
                                            el.textContent.trim().length > 1 && 
                                            el.textContent.trim().length < 30 &&
                                            !el.textContent.includes('天前') &&
                                            !el.textContent.includes('小时前') &&
                                            !el.textContent.includes('分钟前')
                                        );
                                        if (textNodes.length > 0) {
                                            commentData.username = textNodes[0].textContent.trim();
                                        }

                                        // 提取头像
                                        const avatarImg = element.querySelector('img[src*="avatar"]');
                                        if (avatarImg) {
                                            commentData.avatar = avatarImg.src;
                                        }

                                        // 提取评论内容
                                        const allTexts = Array.from(element.querySelectorAll('*')).map(el => 
                                            el.textContent.trim()
                                        ).filter(text => 
                                            text.length > 10 && text.length < 1000 &&
                                            !text.includes('天前') && !text.includes('小时前') &&
                                            !text.includes('点赞') && !text.includes('回复') &&
                                            !text.includes('更多') && !text.includes('展开')
                                        );
                                        
                                        if (allTexts.length > 0) {
                                            commentData.content = allTexts.reduce((a, b) => a.length > b.length ? a : b);
                                        }

                                        // 提取发布时间
                                        const timeMatch = element.textContent.match(/\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}-\\d{2}/);
                                        if (timeMatch) {
                                            commentData.publishTime = timeMatch[0];
                                        }

                                        // 提取点赞数
                                        const likeMatch = element.textContent.match(/\\d+(?=\\s*赞|\\s*❤️)/);
                                        if (likeMatch) {
                                            commentData.likes = parseInt(likeMatch[0]) || 0;
                                        }

                                        // 只添加有内容的评论
                                        if (commentData.content && commentData.content.length > 5) {
                                            commentsData.comments.push(commentData);
                                        }

                                    } catch (e) {
                                        // 忽略提取错误
                                    }
                                });

                                // 去重
                                const uniqueComments = [];
                                const seenContents = new Set();
                                
                                commentsData.comments.forEach(comment => {
                                    const key = comment.content.substring(0, 50);
                                    if (!seenContents.has(key)) {
                                        seenContents.add(key);
                                        uniqueComments.push(comment);
                                    }
                                });
                                
                                commentsData.comments = uniqueComments;

                                // 计算统计信息
                                commentsData.summary.totalComments = commentsData.comments.length;
                                commentsData.summary.totalLikes = commentsData.comments.reduce((sum, c) => sum + c.likes, 0);

                                console.log(\`🎉 精确滚动采集完成! 采集到 \${commentsData.summary.totalComments} 条独特评论\`);
                                console.log(\`📊 采集进度: \${Math.round(commentsData.summary.totalComments/1472*100)}% (目标1472条)\`);

                                return { success: true, data: commentsData };
                                
                            } catch (error) {
                                console.error('精确滚动采集过程出错:', error);
                                return { 
                                    success: false, 
                                    error: error.message || '未知错误'
                                };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: preciseScrollScript,
                            returnByValue: true
                        }
                    }));
                }, 2000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            console.log('✅ 精确滚动采集成功');
                            console.log(`📊 最终结果: ${result.data.summary.totalComments} 条评论`);
                            ws.close();
                            resolve(result.data);
                        } else {
                            console.log('❌ 精确滚动采集失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理采集结果失败:', error.message);
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ 精确滚动采集WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('精确滚动采集超时'));
            }, 300000); // 5分钟超时
        });
    }
}

// 💾 保存精确滚动采集数据
async function savePreciseScrollData() {
    const fs = require('fs');
    const collector = new PreciseScrollCollector();

    try {
        console.log('💾 开始精确滚动采集并保存评论数据...\n');

        const commentsData = await collector.collectWithPreciseScroll();

        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const jsonFileName = `precise-scroll-comments-${timestamp}.json`;
        const txtFileName = `precise-scroll-comments-${timestamp}.txt`;

        // 保存JSON数据
        fs.writeFileSync(jsonFileName, JSON.stringify(commentsData, null, 2), 'utf8');
        console.log(`✅ JSON数据已保存: ${jsonFileName}`);

        // 生成可读报告
        const report = generatePreciseScrollReport(commentsData);
        fs.writeFileSync(txtFileName, report, 'utf8');
        console.log(`✅ 可读报告已保存: ${txtFileName}`);

        return { jsonFile: jsonFileName, txtFile: txtFileName, data: commentsData };

    } catch (error) {
        console.error('❌ 保存精确滚动采集数据失败:', error.message);
        throw error;
    }
}

// 📝 生成精确滚动报告
function generatePreciseScrollReport(data) {
    const lines = [];

    lines.push('🎯 小红书笔记精确滚动评论采集报告');
    lines.push('=' * 60);
    lines.push(`📅 采集时间: ${data.timestamp}`);
    lines.push(`📝 笔记标题: ${data.noteTitle}`);
    lines.push(`🔗 笔记链接: ${data.noteUrl}`);
    lines.push(`🔧 采集策略: ${data.summary.scrollStrategy}`);
    lines.push('');

    lines.push('📊 采集统计:');
    lines.push(`💬 总评论数: ${data.summary.totalComments}`);
    lines.push(`👍 总点赞数: ${data.summary.totalLikes}`);
    lines.push(`📈 采集进度: ${Math.round(data.summary.totalComments/1472*100)}% (目标1472条)`);
    lines.push('');

    lines.push('💬 评论详情:');
    lines.push('-' * 60);

    data.comments.forEach((comment, index) => {
        lines.push(`\n💬 评论 ${index + 1}:`);
        lines.push(`👤 用户: ${comment.username || '匿名'}`);
        lines.push(`📄 内容: ${comment.content || '无内容'}`);
        lines.push(`👍 点赞: ${comment.likes}`);
        lines.push(`📅 时间: ${comment.publishTime || '未知'}`);
        lines.push(`🔗 头像: ${comment.avatar || '无'}`);
    });

    lines.push('\n' + '=' * 60);
    lines.push('📅 报告生成时间: ' + new Date().toLocaleString('zh-CN'));

    return lines.join('\n');
}

// 运行测试
if (require.main === module) {
    const args = process.argv.slice(2);
    if (args.includes('--save')) {
        savePreciseScrollData().then(result => {
            console.log('\n🎉 精确滚动采集数据保存完成!');
            console.log(`📁 JSON文件: ${result.jsonFile}`);
            console.log(`📄 文本报告: ${result.txtFile}`);
        }).catch(console.error);
    } else {
        const collector = new PreciseScrollCollector();
        collector.collectWithPreciseScroll().then(data => {
            console.log('\n🎉 精确滚动采集完成!');
            console.log(`📊 采集到 ${data.summary.totalComments} 条评论`);
        }).catch(console.error);
    }
}

module.exports = { PreciseScrollCollector, savePreciseScrollData };
