/**
 * 🎯 精准滑动爬虫
 * 避免触发删除/举报按钮，精准定位评论区域滑动
 */

const puppeteer = require('puppeteer-core');
const axios = require('axios');
const fs = require('fs');

class PreciseScrollScraper {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.targetWindowSeq = 19;
        this.browser = null;
        this.page = null;
        this.comments = [];
        this.targetCommentCount = 1472;
    }

    /**
     * 连接到比特浏览器
     */
    async connectToBrowser() {
        console.log('🔗 连接到比特浏览器...');
        
        try {
            const windowResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            const browsers = windowResponse.data.data.list;
            const window19 = browsers.find(browser => browser.seq === this.targetWindowSeq);
            
            if (!window19) {
                throw new Error('未找到19号窗口');
            }

            const portsResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            const ports = portsResponse.data.data;
            const debugPort = ports[window19.id];
            
            if (!debugPort) {
                throw new Error('19号窗口未获取到调试端口');
            }

            const versionResponse = await axios.get(`http://localhost:${debugPort}/json/version`);
            const browserWSEndpoint = versionResponse.data.webSocketDebuggerUrl;
            
            this.browser = await puppeteer.connect({
                browserWSEndpoint,
                defaultViewport: null
            });
            
            const browserPages = await this.browser.pages();
            this.page = browserPages.find(page => page.url().includes('xiaohongshu.com/explore/'));
            
            if (!this.page) {
                this.page = browserPages.find(page => page.url().includes('xiaohongshu.com'));
            }
            
            if (!this.page) {
                throw new Error('未找到小红书页面');
            }
            
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            console.log('✅ 连接成功!');
            console.log(`📄 页面: ${title}`);
            
            return true;
            
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    /**
     * 精准定位评论区域
     */
    async locateCommentArea() {
        console.log('🎯 精准定位评论区域...');
        
        try {
            const commentArea = await this.page.evaluate(() => {
                // 查找评论区域的多种可能选择器
                const commentSelectors = [
                    '[class*="comment-list"]',
                    '[class*="comment-container"]',
                    '[class*="comment-section"]',
                    '[class*="interaction"]',
                    '[class*="comment"]',
                    '.comments',
                    '#comments'
                ];
                
                for (const selector of commentSelectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        const rect = element.getBoundingClientRect();
                        return {
                            selector: selector,
                            x: rect.x + rect.width / 2,
                            y: rect.y + rect.height / 2,
                            width: rect.width,
                            height: rect.height,
                            top: rect.top,
                            bottom: rect.bottom
                        };
                    }
                }
                
                // 如果找不到特定的评论区域，使用页面中央区域
                return {
                    selector: 'body',
                    x: window.innerWidth / 2,
                    y: window.innerHeight / 2,
                    width: window.innerWidth * 0.6, // 使用页面中央60%宽度
                    height: window.innerHeight,
                    top: 100,
                    bottom: window.innerHeight - 100
                };
            });
            
            console.log(`✅ 找到评论区域: ${commentArea.selector}`);
            console.log(`   位置: (${Math.round(commentArea.x)}, ${Math.round(commentArea.y)})`);
            console.log(`   尺寸: ${Math.round(commentArea.width)} x ${Math.round(commentArea.height)}`);
            
            return commentArea;
            
        } catch (error) {
            console.error('❌ 定位评论区域失败:', error.message);
            return null;
        }
    }

    /**
     * 精准滑动 - 只在评论区域内滑动
     */
    async preciseScroll(commentArea, distance = 800) {
        try {
            // 移动鼠标到评论区域中央
            await this.page.mouse.move(commentArea.x, commentArea.y);
            
            // 在评论区域内进行滚动
            await this.page.mouse.wheel({ deltaY: distance });
            
            // 短暂等待
            await new Promise(resolve => setTimeout(resolve, 100));
            
        } catch (error) {
            console.log('⚠️ 精准滑动出错:', error.message);
        }
    }

    /**
     * 安全点击展开按钮 - 避免删除/举报按钮
     */
    async safeClickExpand() {
        try {
            const clickResults = await this.page.evaluate(() => {
                let clickCount = 0;
                
                // 只查找"展开"相关的按钮，避免删除/举报
                const safeSelectors = [
                    'button:contains("展开")',
                    'span:contains("展开")',
                    'div:contains("展开")',
                    'button:contains("更多回复")',
                    'span:contains("更多回复")',
                    'button:contains("查看更多")',
                    'span:contains("查看更多")',
                    '[class*="expand"]:not([class*="delete"]):not([class*="report"])',
                    '[class*="more"]:not([class*="delete"]):not([class*="report"])',
                    '[class*="show-more"]'
                ];
                
                for (const selector of safeSelectors) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        
                        for (const element of elements) {
                            const text = element.textContent?.trim().toLowerCase();
                            
                            // 确保不是删除或举报按钮
                            if (text && 
                                !text.includes('删除') && 
                                !text.includes('举报') && 
                                !text.includes('delete') && 
                                !text.includes('report') &&
                                (text.includes('展开') || 
                                 text.includes('更多') || 
                                 text.includes('查看') ||
                                 text.includes('expand') ||
                                 text.includes('more'))) {
                                
                                // 检查元素是否可见
                                const rect = element.getBoundingClientRect();
                                if (rect.width > 0 && rect.height > 0 && 
                                    rect.top >= 0 && rect.bottom <= window.innerHeight) {
                                    
                                    element.click();
                                    clickCount++;
                                }
                            }
                        }
                    } catch (e) {
                        continue;
                    }
                }
                
                return clickCount;
            });
            
            return clickResults;
            
        } catch (error) {
            console.log('⚠️ 安全点击出错:', error.message);
            return 0;
        }
    }

    /**
     * 智能滚动策略 - 精准定位，避免误操作
     */
    async smartScroll() {
        console.log('🎯 开始智能精准滚动...');
        
        // 1. 先定位评论区域
        const commentArea = await this.locateCommentArea();
        if (!commentArea) {
            console.log('❌ 无法定位评论区域');
            return;
        }
        
        let scrollCount = 0;
        let lastCommentCount = 0;
        let noProgressCount = 0;
        let totalClickCount = 0;
        
        while (scrollCount < 1000 && noProgressCount < 30) {
            const currentCommentCount = await this.page.evaluate(() => {
                const commentElements = document.querySelectorAll(
                    '[class*="comment"], [class*="Comment"], [class*="reply"], [class*="Reply"]'
                );
                return commentElements.length;
            });
            
            // 每100次显示进度
            if (scrollCount % 100 === 0) {
                const progress = Math.round((currentCommentCount / this.targetCommentCount) * 100);
                console.log(`🎯 精准滚动 ${scrollCount} 次，评论元素: ${currentCommentCount}，进度: ${progress}%`);
            }
            
            // 安全点击展开按钮
            if (scrollCount % 5 === 0) {
                const clickCount = await this.safeClickExpand();
                totalClickCount += clickCount;
                if (clickCount > 0) {
                    console.log(`   🔘 安全点击了 ${clickCount} 个展开按钮`);
                }
            }
            
            // 精准滚动 - 在评论区域内
            const scrollDistance = 600 + Math.random() * 400; // 600-1000px
            await this.preciseScroll(commentArea, scrollDistance);
            
            // 检查进度
            if (currentCommentCount > lastCommentCount) {
                noProgressCount = 0;
                if (scrollCount % 100 === 0) {
                    console.log(`   ✅ 新增 ${currentCommentCount - lastCommentCount} 个评论元素`);
                }
            } else {
                noProgressCount++;
            }
            
            lastCommentCount = currentCommentCount;
            scrollCount++;
            
            // 动态调整策略
            if (currentCommentCount > this.targetCommentCount * 0.3) {
                console.log(`   🚀 达到30%，加速模式！`);
                await this.preciseScroll(commentArea, 1200);
                await this.safeClickExpand();
            }
            
            if (currentCommentCount > this.targetCommentCount * 0.6) {
                console.log(`   ⚡ 达到60%，冲刺模式！`);
                await this.preciseScroll(commentArea, 1500);
                await this.safeClickExpand();
                await this.preciseScroll(commentArea, 1500);
            }
            
            if (currentCommentCount > this.targetCommentCount * 0.8) {
                console.log(`   🔥 达到80%，最后冲刺！`);
                for (let i = 0; i < 3; i++) {
                    await this.preciseScroll(commentArea, 2000);
                    await this.safeClickExpand();
                }
            }
        }
        
        console.log(`\n🏁 智能滚动完成！`);
        console.log(`   总滚动次数: ${scrollCount}`);
        console.log(`   总点击次数: ${totalClickCount}`);
        console.log(`   最终评论元素: ${lastCommentCount}`);
        console.log(`   完成度: ${Math.round((lastCommentCount / this.targetCommentCount) * 100)}%`);
        
        // 滚动回顶部
        await this.page.evaluate(() => window.scrollTo(0, 0));
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    /**
     * 智能评论提取
     */
    async smartExtractComments() {
        console.log('🔍 开始智能评论提取...');
        
        try {
            const comments = await this.page.evaluate(() => {
                const extractedComments = [];
                
                // 智能选择器 - 专注评论内容
                const smartSelectors = [
                    '[class*="comment-item"]',
                    '[class*="comment-content"]',
                    '[class*="comment-text"]',
                    '[class*="user-comment"]',
                    '[class*="comment"]',
                    '[class*="Comment"]',
                    '[class*="reply"]',
                    '[class*="Reply"]',
                    '[class*="interaction"]'
                ];
                
                smartSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        
                        elements.forEach((element, index) => {
                            const text = element.textContent?.trim();
                            if (text && text.length > 5) {
                                const lines = text.split('\n').filter(line => line.trim());
                                
                                let username = '';
                                let content = '';
                                let time = '';
                                let likes = '';
                                
                                for (let i = 0; i < lines.length; i++) {
                                    const line = lines[i].trim();
                                    
                                    if (line.length < 2) continue;
                                    
                                    // 时间识别
                                    if (/\d{2}-\d{2}|\d+[分小天月年]前/.test(line)) {
                                        time = line;
                                        continue;
                                    }
                                    
                                    // 点赞数识别
                                    if (/^\d+$/.test(line) && parseInt(line) > 0 && parseInt(line) < 50000) {
                                        likes = line;
                                        continue;
                                    }
                                    
                                    // 跳过操作按钮
                                    if (/^(赞|回复|展开|收起|点赞|分享|举报|删除)$/.test(line)) {
                                        continue;
                                    }
                                    
                                    // 用户名识别
                                    if (!username && line.length < 100 && 
                                        !line.includes('条评论') && 
                                        !line.includes('回复') &&
                                        !line.includes('展开') &&
                                        !line.includes('删除') &&
                                        !line.includes('举报')) {
                                        username = line;
                                    } else if (line.length > 3 && line.length < 3000) {
                                        if (content) {
                                            content += ' ' + line;
                                        } else {
                                            content = line;
                                        }
                                    }
                                }
                                
                                // 清理内容
                                if (content) {
                                    content = content
                                        .replace(/展开\s*\d+\s*条回复/g, '')
                                        .replace(/\d+赞回复/g, '')
                                        .replace(/赞回复$/g, '')
                                        .replace(/回复$/g, '')
                                        .replace(/删除$/g, '')
                                        .replace(/举报$/g, '')
                                        .replace(/[🥝😃🦁🥦🍟🐭🥪😁🥭🐡🥒😛🦊🥬🥞🍍🧁🍙😷🍉🐟🍊😏🍑]/g, '')
                                        .replace(/\s+/g, ' ')
                                        .trim();
                                    
                                    if (content.length > 3 && content.length < 3000 &&
                                        !content.includes('删除') &&
                                        !content.includes('举报')) {
                                        extractedComments.push({
                                            username: username || '未知用户',
                                            content: content,
                                            time: time || '',
                                            likes: likes || '',
                                            method: `smart_${selector}`,
                                            element_index: index,
                                            timestamp: new Date().toISOString()
                                        });
                                    }
                                }
                            }
                        });
                    } catch (e) {
                        console.log(`选择器 ${selector} 处理失败:`, e.message);
                    }
                });
                
                // 文本分析 - 避免删除/举报相关内容
                const bodyText = document.body.textContent || '';
                const allLines = bodyText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
                
                for (let i = 0; i < allLines.length; i++) {
                    const line = allLines[i];
                    
                    if (/\d{2}-\d{2}|\d+[分小天月年]前/.test(line) && line.length < 100) {
                        for (let j = 1; j <= 5; j++) {
                            if (i - j >= 0) {
                                const contentLine = allLines[i - j];
                                if (contentLine.length > 5 && contentLine.length < 1000 &&
                                    !contentLine.includes('删除') &&
                                    !contentLine.includes('举报') &&
                                    !contentLine.includes('条评论') &&
                                    !/(赞|回复|展开|收起|点赞|分享)$/.test(contentLine)) {
                                    
                                    let username = '未知用户';
                                    if (i - j - 1 >= 0) {
                                        const userLine = allLines[i - j - 1];
                                        if (userLine.length > 1 && userLine.length < 50 && 
                                            !userLine.includes('删除') &&
                                            !userLine.includes('举报')) {
                                            username = userLine;
                                        }
                                    }
                                    
                                    const exists = extractedComments.some(comment => 
                                        comment.content.includes(contentLine.substring(0, 30))
                                    );
                                    
                                    if (!exists) {
                                        extractedComments.push({
                                            username: username,
                                            content: contentLine,
                                            time: line,
                                            likes: '',
                                            method: 'smart_text_analysis',
                                            timestamp: new Date().toISOString()
                                        });
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
                
                // 智能去重
                const uniqueComments = [];
                const seenContents = new Set();
                
                for (const comment of extractedComments) {
                    const contentKey = comment.content.substring(0, 100);
                    if (!seenContents.has(contentKey) && 
                        comment.content.length > 3 && 
                        comment.content.length < 3000 &&
                        !comment.content.includes('删除') &&
                        !comment.content.includes('举报') &&
                        !comment.content.includes('undefined') &&
                        !comment.content.includes('null')) {
                        seenContents.add(contentKey);
                        uniqueComments.push(comment);
                    }
                }
                
                return uniqueComments;
            });
            
            this.comments = comments;
            console.log(`✅ 智能提取完成，共获得 ${this.comments.length} 条评论`);
            
            return this.comments.length > 0;
            
        } catch (error) {
            console.error('❌ 智能提取失败:', error.message);
            return false;
        }
    }

    /**
     * 保存为精准TXT格式
     */
    async savePreciseTXT() {
        if (this.comments.length === 0) {
            console.log('⚠️ 没有评论数据可保存');
            return;
        }
        
        try {
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            let txtContent = '';
            txtContent += '='.repeat(80) + '\n';
            txtContent += '小红书评论完整数据 (精准滑动版)\n';
            txtContent += '='.repeat(80) + '\n';
            txtContent += `页面标题: ${title}\n`;
            txtContent += `页面链接: ${currentUrl}\n`;
            txtContent += `爬取时间: ${new Date().toLocaleString('zh-CN')}\n`;
            txtContent += `评论总数: ${this.comments.length} 条\n`;
            txtContent += `目标数量: ${this.targetCommentCount} 条\n`;
            txtContent += `完成度: ${Math.round((this.comments.length / this.targetCommentCount) * 100)}%\n`;
            txtContent += `爬取方式: 精准定位滑动 + 安全点击\n`;
            txtContent += '='.repeat(80) + '\n\n';
            
            // 按时间排序
            const sortedComments = this.comments.sort((a, b) => {
                if (a.time && b.time) {
                    return a.time.localeCompare(b.time);
                }
                return 0;
            });
            
            // 添加评论内容
            sortedComments.forEach((comment, index) => {
                txtContent += `【评论 ${index + 1}】\n`;
                txtContent += `用户名: ${comment.username}\n`;
                if (comment.time) {
                    txtContent += `发布时间: ${comment.time}\n`;
                }
                if (comment.likes) {
                    txtContent += `点赞数: ${comment.likes}\n`;
                }
                txtContent += `评论内容: ${comment.content}\n`;
                txtContent += `提取方式: ${comment.method}\n`;
                txtContent += '-'.repeat(60) + '\n\n';
            });
            
            // 添加统计
            txtContent += '='.repeat(80) + '\n';
            txtContent += '详细统计信息\n';
            txtContent += '='.repeat(80) + '\n';
            
            const methodStats = {};
            this.comments.forEach(comment => {
                methodStats[comment.method] = (methodStats[comment.method] || 0) + 1;
            });
            
            txtContent += '按提取方式统计:\n';
            Object.entries(methodStats).sort((a, b) => b[1] - a[1]).forEach(([method, count]) => {
                const percentage = Math.round((count / this.comments.length) * 100);
                txtContent += `  ${method}: ${count} 条 (${percentage}%)\n`;
            });
            
            txtContent += '\n' + '='.repeat(80) + '\n';
            txtContent += '精准滑动技术说明:\n';
            txtContent += '- 精准定位评论区域，避免误操作\n';
            txtContent += '- 安全点击策略，避免删除/举报按钮\n';
            txtContent += '- 智能滚动路径，专注评论内容\n';
            txtContent += '- 内容过滤，排除删除/举报相关信息\n';
            txtContent += '- 动态调整策略，提高成功率\n';
            txtContent += '='.repeat(80) + '\n';
            
            // 保存文件
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const txtFilename = `xiaohongshu_precise_scroll_comments_${timestamp}.txt`;
            
            fs.writeFileSync(txtFilename, txtContent, 'utf8');
            
            console.log(`💾 精准滑动版TXT文件已保存: ${txtFilename}`);
            console.log(`📊 总计 ${this.comments.length} 条评论 (目标: ${this.targetCommentCount} 条)`);
            
            return txtFilename;
            
        } catch (error) {
            console.error('❌ 保存文件失败:', error.message);
        }
    }

    /**
     * 运行精准滑动爬虫
     */
    async run() {
        console.log('🎯 精准滑动爬虫 - 避免误操作');
        console.log('='.repeat(80));
        
        try {
            // 1. 连接浏览器
            await this.connectToBrowser();
            
            // 2. 等待页面稳定
            console.log('⏳ 等待页面稳定...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 3. 智能精准滚动
            await this.smartScroll();
            
            // 4. 智能提取评论
            const success = await this.smartExtractComments();
            
            if (!success) {
                console.log('❌ 未能提取到评论数据');
                return false;
            }
            
            // 5. 保存为精准TXT
            const txtFile = await this.savePreciseTXT();
            
            console.log('\n🎉 精准滑动爬取完成!');
            console.log(`📁 请查看生成的TXT文件: ${txtFile}`);
            console.log(`📊 成功获取 ${this.comments.length} / ${this.targetCommentCount} 条评论`);
            
            const completionRate = Math.round((this.comments.length / this.targetCommentCount) * 100);
            console.log(`🎯 完成度: ${completionRate}%`);
            
            if (completionRate >= 90) {
                console.log('🏆 完美！几乎获取了所有评论！');
            } else if (completionRate >= 70) {
                console.log('🥇 优秀！获取了大部分评论！');
            } else if (completionRate >= 50) {
                console.log('🥈 良好！获取了一半以上的评论！');
            } else if (completionRate >= 30) {
                console.log('🥉 不错！获取了相当数量的评论！');
            } else {
                console.log('📈 有进步！避免了误操作，继续优化！');
            }
            
            return true;
            
        } catch (error) {
            console.error('❌ 精准滑动爬虫运行失败:', error.message);
            return false;
        } finally {
            if (this.browser) {
                console.log('🔒 断开浏览器连接...');
                await this.browser.disconnect();
            }
        }
    }
}

// 主函数
async function main() {
    console.log('🎯 精准滑动爬虫启动器');
    console.log('='.repeat(80));
    
    console.log('🛡️ 安全特性:');
    console.log('   🎯 精准定位评论区域');
    console.log('   🔘 安全点击策略');
    console.log('   🚫 避免删除/举报按钮');
    console.log('   📍 智能滚动路径');
    console.log('   🧹 内容安全过滤');
    
    const scraper = new PreciseScrollScraper();
    await scraper.run();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = PreciseScrollScraper;
