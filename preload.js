const { contextBridge, ipcRenderer } = require('electron');

// 向渲染进程暴露安全的API
contextBridge.exposeInMainWorld('electronAPI', {
    // 应用信息
    getVersion: () => process.versions.electron,
    getPlatform: () => process.platform,
    
    // 窗口控制
    minimize: () => ipcRenderer.invoke('window-minimize'),
    maximize: () => ipcRenderer.invoke('window-maximize'),
    close: () => ipcRenderer.invoke('window-close'),
    
    // 文件操作
    openFile: () => ipcRenderer.invoke('dialog-open-file'),
    saveFile: (data) => ipcRenderer.invoke('dialog-save-file', data),
    
    // 系统通知
    showNotification: (title, body) => {
        if (Notification.permission === 'granted') {
            new Notification(title, { body });
        }
    },
    
    // 开发者工具
    toggleDevTools: () => ipcRenderer.invoke('toggle-dev-tools'),
    
    // 应用事件监听
    onAppReady: (callback) => ipcRenderer.on('app-ready', callback),
    onAppError: (callback) => ipcRenderer.on('app-error', callback),
    
    // 移除事件监听
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// 在页面加载完成后执行
window.addEventListener('DOMContentLoaded', () => {
    console.log('🎯 Electron预加载脚本已加载');
    
    // 添加平台特定的CSS类
    document.body.classList.add(`platform-${process.platform}`);
    
    // 添加Electron标识
    document.body.classList.add('electron-app');
    
    // 设置应用版本信息
    const versionElement = document.querySelector('.app-version');
    if (versionElement) {
        versionElement.textContent = `v${process.versions.electron}`;
    }
});

// 错误处理
window.addEventListener('error', (event) => {
    console.error('页面错误:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
});

console.log('✅ Electron预加载脚本初始化完成');
