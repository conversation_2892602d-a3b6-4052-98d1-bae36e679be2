<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📚 API文档 - 黑默科技平台</title>
    <link rel="stylesheet" href="premium-ui.css">
    <style>
        .api-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        .endpoint-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #2196F3;
        }

        .method-get { border-left-color: #4CAF50; }
        .method-post { border-left-color: #2196F3; }
        .method-put { border-left-color: #FF9800; }
        .method-delete { border-left-color: #f44336; }

        .method-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }

        .method-get .method-badge { background: #4CAF50; }
        .method-post .method-badge { background: #2196F3; }
        .method-put .method-badge { background: #FF9800; }
        .method-delete .method-badge { background: #f44336; }

        .code-block {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .test-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }

        .test-btn:hover {
            background: #45a049;
        }

        .nav-menu {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .nav-menu a {
            color: #2196F3;
            text-decoration: none;
            margin-right: 20px;
            font-weight: 500;
        }

        .nav-menu a:hover {
            text-decoration: underline;
        }

        .section-title {
            color: #333;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 10px;
            margin: 30px 0 20px 0;
        }
    </style>
</head>
<body>
    <div class="api-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>📚 API文档</h1>
            <p>黑默科技平台 - 小红书数据采集API接口文档</p>
        </div>

        <!-- 导航菜单 -->
        <div class="nav-menu">
            <strong>快速导航:</strong>
            <a href="#health">健康检查</a>
            <a href="#bitbrowser">比特浏览器管理</a>
            <a href="#xiaohongshu">小红书数据采集</a>
            <a href="#examples">使用示例</a>
        </div>

        <!-- 健康检查API -->
        <h2 id="health" class="section-title">🏥 健康检查API</h2>

        <div class="endpoint-card method-post">
            <h3><span class="method-badge">POST</span>/api/xiaohongshu/health</h3>
            <p><strong>功能:</strong> 完整的服务器健康检查，包含比特浏览器连接状态</p>
            <p><strong>参数:</strong> 无需参数，但必须使用POST方法和JSON body</p>
            <p><strong>返回:</strong> 服务器状态、比特浏览器连接状态、服务信息</p>
            
            <div class="code-block">
// 请求体 (空JSON对象)
{}

// 响应示例
{
  "success": true,
  "message": "Local Server 运行正常",
  "data": {
    "server": {
      "timestamp": "2025-07-30T10:00:00.000Z",
      "uptime": 123.45,
      "memory": {...},
      "version": "v18.17.0",
      "platform": "win32"
    },
    "bitbrowser": {
      "connected": true,
      "message": "连接正常",
      "browserCount": 16,
      "targetBrowserFound": true
    },
    "services": {
      "xiaohongshu_extractor": "已加载",
      "socket_io": "已启用",
      "api_endpoints": "正常"
    }
  }
}
            </div>
            <button class="test-btn" onclick="testAPI('/api/xiaohongshu/health', 'POST', {})">🧪 测试接口</button>
        </div>

        <div class="endpoint-card method-post">
            <h3><span class="method-badge">POST</span>/api/xiaohongshu/health/simple</h3>
            <p><strong>功能:</strong> 简化的健康检查，快速返回基本状态</p>
            <p><strong>参数:</strong> 无需参数，但必须使用POST方法和JSON body</p>
            <p><strong>返回:</strong> 基本状态信息</p>

            <div class="code-block">
// 请求体 (空JSON对象)
{}

// 响应示例
{
  "success": true,
  "timestamp": "2025-07-30T10:00:00.000Z",
  "uptime": 123,
  "status": "healthy"
}
            </div>
            <button class="test-btn" onclick="testAPI('/api/xiaohongshu/health/simple', 'POST', {})">🧪 测试接口</button>
        </div>

        <!-- 比特浏览器管理API -->
        <h2 id="bitbrowser" class="section-title">🌐 比特浏览器管理API</h2>
        
        <div class="endpoint-card method-post">
            <h3><span class="method-badge">POST</span>/api/xiaohongshu/bitbrowser/list</h3>
            <p><strong>功能:</strong> 获取比特浏览器实例列表</p>
            <p><strong>参数:</strong> page (页码), pageSize (每页数量)</p>
            
            <div class="code-block">
// 请求体
{
  "page": 1,
  "pageSize": 20
}

// 响应示例
{
  "success": true,
  "message": "成功获取到 16 个浏览器实例",
  "data": {
    "browsers": [...],
    "targetBrowser": {...},
    "targetBrowserId": "0d094596cb404282be3f814b98139c74",
    "total": 16
  }
}
            </div>
            <button class="test-btn" onclick="testAPI('/api/xiaohongshu/bitbrowser/list', 'POST', {page: 1, pageSize: 20})">🧪 测试接口</button>
        </div>

        <div class="endpoint-card method-post">
            <h3><span class="method-badge">POST</span>/api/xiaohongshu/bitbrowser/start</h3>
            <p><strong>功能:</strong> 启动指定的浏览器实例</p>
            <p><strong>参数:</strong> browserId (可选，默认使用目标浏览器ID)</p>
            
            <div class="code-block">
// 请求体
{
  "browserId": "0d094596cb404282be3f814b98139c74"  // 可选
}

// 响应示例
{
  "success": true,
  "message": "浏览器实例启动成功",
  "data": {
    "browserId": "0d094596cb404282be3f814b98139c74",
    "result": {...}
  }
}
            </div>
            <button class="test-btn" onclick="testAPI('/api/xiaohongshu/bitbrowser/start', 'POST', {})">🧪 测试接口</button>
        </div>

        <div class="endpoint-card method-post">
            <h3><span class="method-badge">POST</span>/api/xiaohongshu/bitbrowser/stop</h3>
            <p><strong>功能:</strong> 停止指定的浏览器实例</p>
            <p><strong>参数:</strong> browserId (可选，默认使用目标浏览器ID)</p>
            
            <div class="code-block">
// 请求体
{
  "browserId": "0d094596cb404282be3f814b98139c74"  // 可选
}

// 响应示例
{
  "success": true,
  "message": "浏览器实例停止成功",
  "data": {
    "browserId": "0d094596cb404282be3f814b98139c74",
    "result": {...}
  }
}
            </div>
            <button class="test-btn" onclick="testAPI('/api/xiaohongshu/bitbrowser/stop', 'POST', {})">🧪 测试接口</button>
        </div>

        <!-- 连接测试API -->
        <div class="endpoint-card method-post">
            <h3><span class="method-badge">POST</span>/api/xiaohongshu/test-browser-connection</h3>
            <p><strong>功能:</strong> 测试比特浏览器连接和小红书页面状态</p>
            <p><strong>参数:</strong> 无</p>
            
            <div class="code-block">
// 响应示例
{
  "success": true,
  "message": "比特浏览器连接成功，所有功能正常",
  "data": {
    "apiStatus": {
      "connected": true,
      "message": "连接正常",
      "browserCount": 16,
      "targetBrowser": {...}
    },
    "debugStatus": {
      "connected": true,
      "port": 9222,
      "xiaohongshuTab": {
        "title": "小红书",
        "url": "https://www.xiaohongshu.com/",
        "id": "..."
      }
    },
    "summary": {
      "apiConnected": true,
      "debugConnected": true,
      "browserCount": 16,
      "targetBrowserFound": true,
      "xiaohongshuPageFound": true
    }
  }
}
            </div>
            <button class="test-btn" onclick="testAPI('/api/xiaohongshu/test-browser-connection', 'POST')">🧪 测试接口</button>
        </div>

        <!-- 使用示例 -->
        <h2 id="examples" class="section-title">💡 使用示例</h2>
        
        <div class="endpoint-card">
            <h3>🔧 基本使用流程</h3>
            <div class="code-block">
⚠️ 重要：所有API接口都必须使用POST方法，body传参JSON格式
不接受request url、formdata、string等类型传参方式

// 1. 检查服务器健康状态
POST /api/xiaohongshu/health/simple
Content-Type: application/json
Body: {}

// 2. 获取浏览器列表
POST /api/xiaohongshu/bitbrowser/list
Content-Type: application/json
Body: {"page": 1, "pageSize": 20}

// 3. 启动目标浏览器
POST /api/xiaohongshu/bitbrowser/start
Content-Type: application/json
Body: {}

// 4. 测试连接状态
POST /api/xiaohongshu/test-browser-connection
Content-Type: application/json
Body: {}

// 5. 开始数据采集
// (其他小红书数据采集API...)
            </div>
        </div>

        <div class="endpoint-card">
            <h3>📮 Postman调试示例</h3>
            <div class="code-block">
1. 设置请求方法为 POST
2. 设置 Headers:
   Content-Type: application/json
3. 设置 Body 类型为 raw (JSON)
4. 在 Body 中输入 JSON 格式参数

示例配置：
Method: POST
URL: http://localhost:3000/api/xiaohongshu/health
Headers:
  Content-Type: application/json
Body (raw JSON):
  {}

建议先用 Postman 调试接口，调通后再进行脚本开发调用
            </div>
        </div>

        <!-- 测试结果显示 -->
        <div class="section">
            <h3>🧪 API测试结果</h3>
            <div id="test-result" class="code-block" style="min-height: 100px;">
                点击上方的"测试接口"按钮来测试API...
            </div>
        </div>
    </div>

    <script>
        // ===== 📚 API文档测试脚本 =====
        
        async function testAPI(endpoint, method, body = {}) {
            const resultDiv = document.getElementById('test-result');

            try {
                resultDiv.textContent = `🔄 正在测试 ${method} ${endpoint}...`;

                // 🔧 所有API都使用POST方法和JSON body传参
                const options = {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(body || {})
                };
                
                const response = await fetch(endpoint, options);
                const data = await response.json();
                
                resultDiv.innerHTML = `
<strong>✅ 测试成功</strong>
<strong>端点:</strong> ${method} ${endpoint}
<strong>状态码:</strong> ${response.status}
<strong>响应时间:</strong> ${Date.now() - startTime}ms

<strong>响应数据:</strong>
${JSON.stringify(data, null, 2)}
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
<strong>❌ 测试失败</strong>
<strong>端点:</strong> ${method} ${endpoint}
<strong>错误:</strong> ${error.message}

<strong>详细信息:</strong>
${error.stack || '无详细信息'}
                `;
            }
        }
        
        let startTime = Date.now();
        
        // 重写测试函数以记录开始时间
        const originalTestAPI = testAPI;
        testAPI = function(endpoint, method, body) {
            startTime = Date.now();
            return originalTestAPI(endpoint, method, body);
        };
    </script>
</body>
</html>
