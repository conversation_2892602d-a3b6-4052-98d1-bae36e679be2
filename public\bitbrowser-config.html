<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 比特浏览器配置验证 - 黑默科技</title>
    <link rel="stylesheet" href="premium-ui.css">
    <style>
        .config-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        .config-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #2196F3;
        }

        .config-card.success {
            border-left-color: #4CAF50;
        }

        .config-card.error {
            border-left-color: #f44336;
        }

        .config-card.warning {
            border-left-color: #FF9800;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }
        .status-warning { background: #FF9800; }

        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .config-item:last-child {
            border-bottom: none;
        }

        .config-value {
            font-family: 'Courier New', monospace;
            background: #f5f5f5;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
        }

        .test-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }

        .test-btn:hover {
            background: #45a049;
        }

        .test-btn.warning {
            background: #FF9800;
        }

        .test-btn.danger {
            background: #f44336;
        }

        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .step-guide {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .step-list {
            list-style: none;
            padding: 0;
        }

        .step-list li {
            padding: 8px 0;
            padding-left: 30px;
            position: relative;
        }

        .step-list li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .step-list {
            counter-reset: step-counter;
        }
    </style>
</head>
<body>
    <div class="config-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>🔧 比特浏览器配置验证</h1>
            <p>验证比特浏览器Local API配置和连接状态</p>
        </div>

        <!-- 配置指南 -->
        <div class="step-guide">
            <h3>📋 配置步骤指南</h3>
            <ol class="step-list">
                <li>确保比特浏览器已启动并运行</li>
                <li>检查Local API接口地址: http://127.0.0.1:54345</li>
                <li>确认API Token配置正确</li>
                <li>创建并启动目标浏览器实例</li>
                <li>在浏览器中打开小红书网站</li>
                <li>验证调试端口连接</li>
            </ol>
        </div>

        <!-- 当前配置显示 -->
        <div class="config-card">
            <h3>⚙️ 当前配置信息</h3>
            <div class="config-item">
                <span><strong>API地址:</strong></span>
                <span class="config-value">http://127.0.0.1:54345</span>
            </div>
            <div class="config-item">
                <span><strong>API Token:</strong></span>
                <span class="config-value">ca28ee5ca6de4d209182a83aa16a2044</span>
            </div>
            <div class="config-item">
                <span><strong>目标浏览器ID:</strong></span>
                <span class="config-value">0d094596cb404282be3f814b98139c74</span>
            </div>
            <div class="config-item">
                <span><strong>调试端口范围:</strong></span>
                <span class="config-value">9222-9240</span>
            </div>
        </div>

        <!-- 连接状态检测 -->
        <div id="connection-status" class="config-card">
            <h3>🔌 连接状态检测</h3>
            <div id="status-content">
                <p>点击下方按钮开始检测...</p>
            </div>
            <div style="margin-top: 15px;">
                <button class="test-btn" onclick="testFullConnection()">🔍 完整连接测试</button>
                <button class="test-btn" onclick="testAPIOnly()">🌐 仅测试API</button>
                <button class="test-btn" onclick="getBrowserList()">📋 获取浏览器列表</button>
                <button class="test-btn warning" onclick="startTargetBrowser()">🚀 启动目标浏览器</button>
            </div>
        </div>

        <!-- 浏览器实例状态 -->
        <div id="browser-status" class="config-card">
            <h3>🌐 浏览器实例状态</h3>
            <div id="browser-content">
                <p>暂无数据，请先获取浏览器列表</p>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="section">
            <h3>📝 操作日志</h3>
            <div id="log-container" class="log-container">
                <div>[启动] 比特浏览器配置验证工具已加载</div>
            </div>
        </div>
    </div>

    <script>
        // ===== 🔧 比特浏览器配置验证脚本 =====
        
        // 📝 日志记录函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 🔍 完整连接测试
        async function testFullConnection() {
            addLog('开始完整连接测试...');
            updateConnectionStatus('🔄 正在进行完整连接测试...', 'loading');
            
            try {
                const response = await fetch('/api/xiaohongshu/test-browser-connection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('完整连接测试成功！', 'success');
                    updateConnectionStatus('✅ 所有连接正常', 'success', result.data);
                } else {
                    addLog(`连接测试失败: ${result.message}`, 'error');
                    updateConnectionStatus('❌ 连接测试失败', 'error', result);
                }
            } catch (error) {
                addLog(`连接测试出错: ${error.message}`, 'error');
                updateConnectionStatus('❌ 连接测试出错', 'error');
            }
        }

        // 🌐 仅测试API连接
        async function testAPIOnly() {
            addLog('测试比特浏览器API连接...');
            
            try {
                const response = await fetch('/api/xiaohongshu/bitbrowser/list', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ page: 1, pageSize: 5 })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog(`API连接成功，找到 ${result.data.total || 0} 个浏览器实例`, 'success');
                    updateConnectionStatus('✅ API连接正常', 'success');
                } else {
                    addLog(`API连接失败: ${result.message}`, 'error');
                    updateConnectionStatus('❌ API连接失败', 'error');
                }
            } catch (error) {
                addLog(`API测试出错: ${error.message}`, 'error');
                updateConnectionStatus('❌ API测试出错', 'error');
            }
        }

        // 📋 获取浏览器列表
        async function getBrowserList() {
            addLog('获取浏览器实例列表...');
            
            try {
                const response = await fetch('/api/xiaohongshu/bitbrowser/list', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ page: 1, pageSize: 20 })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const browsers = result.data.browsers || [];
                    addLog(`成功获取到 ${browsers.length} 个浏览器实例`, 'success');
                    updateBrowserStatus(browsers, result.data.targetBrowser);
                } else {
                    addLog(`获取浏览器列表失败: ${result.message}`, 'error');
                    updateBrowserStatus([], null);
                }
            } catch (error) {
                addLog(`获取列表出错: ${error.message}`, 'error');
                updateBrowserStatus([], null);
            }
        }

        // 🚀 启动目标浏览器
        async function startTargetBrowser() {
            addLog('启动目标浏览器实例...');
            
            try {
                const response = await fetch('/api/xiaohongshu/bitbrowser/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('目标浏览器启动成功！', 'success');
                    setTimeout(() => {
                        addLog('等待2秒后重新检测状态...');
                        getBrowserList();
                    }, 2000);
                } else {
                    addLog(`启动失败: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`启动出错: ${error.message}`, 'error');
            }
        }

        // 📊 更新连接状态显示
        function updateConnectionStatus(message, type, data = null) {
            const statusCard = document.getElementById('connection-status');
            const statusContent = document.getElementById('status-content');
            
            // 更新卡片样式
            statusCard.className = `config-card ${type}`;
            
            if (data && data.summary) {
                statusContent.innerHTML = `
                    <div style="margin-bottom: 15px;">
                        <strong>${message}</strong>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                        <div>
                            <span class="status-indicator ${data.summary.apiConnected ? 'status-online' : 'status-offline'}"></span>
                            <strong>API连接:</strong> ${data.summary.apiConnected ? '正常' : '失败'}
                        </div>
                        <div>
                            <span class="status-indicator ${data.summary.debugConnected ? 'status-online' : 'status-offline'}"></span>
                            <strong>调试端口:</strong> ${data.summary.debugConnected ? '正常' : '失败'}
                        </div>
                        <div>
                            <span class="status-indicator ${data.summary.targetBrowserFound ? 'status-online' : 'status-offline'}"></span>
                            <strong>目标浏览器:</strong> ${data.summary.targetBrowserFound ? '已找到' : '未找到'}
                        </div>
                        <div>
                            <span class="status-indicator ${data.summary.xiaohongshuPageFound ? 'status-online' : 'status-offline'}"></span>
                            <strong>小红书页面:</strong> ${data.summary.xiaohongshuPageFound ? '已打开' : '未打开'}
                        </div>
                    </div>
                `;
            } else {
                statusContent.innerHTML = `<p>${message}</p>`;
            }
        }

        // 🌐 更新浏览器状态显示
        function updateBrowserStatus(browsers, targetBrowser) {
            const browserContent = document.getElementById('browser-content');
            
            if (browsers.length === 0) {
                browserContent.innerHTML = '<p>❌ 未找到任何浏览器实例</p>';
                return;
            }
            
            const targetId = '0d094596cb404282be3f814b98139c74';
            
            browserContent.innerHTML = `
                <div style="margin-bottom: 15px;">
                    <strong>找到 ${browsers.length} 个浏览器实例:</strong>
                </div>
                <div style="max-height: 200px; overflow-y: auto;">
                    ${browsers.map(browser => {
                        const isTarget = browser.id === targetId;
                        const isRunning = browser.status === 'running' || browser.status === 'online';
                        
                        return `
                            <div style="padding: 10px; border: 1px solid #ddd; border-radius: 6px; margin-bottom: 10px; ${isTarget ? 'background: #fff3e0; border-color: #FF9800;' : ''}">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <strong>${browser.name || browser.id}</strong>
                                        ${isTarget ? '<span style="background: #FF9800; color: white; padding: 2px 6px; border-radius: 10px; font-size: 11px; margin-left: 8px;">🎯 目标</span>' : ''}
                                    </div>
                                    <div>
                                        <span class="status-indicator ${isRunning ? 'status-online' : 'status-offline'}"></span>
                                        ${browser.status || '未知'}
                                    </div>
                                </div>
                                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                                    ID: ${browser.id}
                                    ${browser.debug_port ? ` | 调试端口: ${browser.debug_port}` : ''}
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;
        }

        // 🚀 页面加载完成后自动执行
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成，准备进行配置验证');
            
            // 自动进行API连接测试
            setTimeout(testAPIOnly, 1000);
        });
    </script>
</body>
</html>
