<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 比特浏览器管理 - 黑默科技</title>
    <link rel="stylesheet" href="premium-ui.css">
    <style>
        /* ===== 🎨 比特浏览器管理专用样式 ===== */
        .bitbrowser-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .status-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .browser-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .browser-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #4CAF50;
            transition: transform 0.2s ease;
        }

        .browser-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .browser-card.target {
            border-left-color: #FF9800;
            background: linear-gradient(135deg, #fff3e0 0%, #ffffff 100%);
        }

        .browser-card.offline {
            border-left-color: #f44336;
            opacity: 0.7;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn-primary { background: #2196F3; color: white; }
        .btn-success { background: #4CAF50; color: white; }
        .btn-danger { background: #f44336; color: white; }
        .btn-warning { background: #FF9800; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }
        .status-unknown { background: #9E9E9E; }

        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .connection-test {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="bitbrowser-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>🔧 比特浏览器管理中心</h1>
            <p>管理比特浏览器实例，配置小红书数据采集环境</p>
        </div>

        <!-- 连接状态卡片 -->
        <div class="status-card">
            <h2>📊 连接状态总览</h2>
            <div id="connection-status">
                <p>🔄 正在检测连接状态...</p>
            </div>
        </div>

        <!-- 连接测试区域 -->
        <div class="connection-test">
            <h3>🧪 连接测试</h3>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="testConnection()">🔍 测试连接</button>
                <button class="btn btn-success" onclick="refreshBrowserList()">🔄 刷新列表</button>
                <button class="btn btn-warning" onclick="startTargetBrowser()">🚀 启动目标浏览器</button>
                <button class="btn btn-danger" onclick="stopTargetBrowser()">🛑 停止目标浏览器</button>
            </div>
        </div>

        <!-- 浏览器列表 -->
        <div class="section">
            <h3>🌐 浏览器实例列表</h3>
            <div id="browser-list" class="browser-grid">
                <p>🔄 正在加载浏览器列表...</p>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="section">
            <h3>📝 操作日志</h3>
            <div id="log-container" class="log-container">
                <div>🚀 比特浏览器管理器已启动</div>
            </div>
        </div>
    </div>

    <script>
        // ===== 🌐 比特浏览器管理脚本 =====
        
        let currentBrowsers = [];
        const targetBrowserId = '0d094596cb404282be3f814b98139c74';

        // 📝 日志记录函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 🔍 测试连接
        async function testConnection() {
            addLog('开始测试比特浏览器连接...');
            
            try {
                const response = await fetch('/api/xiaohongshu/test-browser-connection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('连接测试成功！', 'success');
                    updateConnectionStatus(result.data);
                } else {
                    addLog(`连接测试失败: ${result.message}`, 'error');
                    updateConnectionStatus(null, result.message);
                }
            } catch (error) {
                addLog(`连接测试出错: ${error.message}`, 'error');
            }
        }

        // 🔄 刷新浏览器列表
        async function refreshBrowserList() {
            addLog('正在刷新浏览器列表...');
            
            try {
                const response = await fetch('/api/xiaohongshu/bitbrowser/list', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentBrowsers = result.data.browsers || [];
                    addLog(`成功获取到 ${currentBrowsers.length} 个浏览器实例`, 'success');
                    renderBrowserList();
                } else {
                    addLog(`获取浏览器列表失败: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`刷新列表出错: ${error.message}`, 'error');
            }
        }

        // 🚀 启动目标浏览器
        async function startTargetBrowser() {
            addLog(`正在启动目标浏览器: ${targetBrowserId}...`);
            
            try {
                const response = await fetch('/api/xiaohongshu/bitbrowser/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ browserId: targetBrowserId })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('目标浏览器启动成功！', 'success');
                    setTimeout(refreshBrowserList, 2000); // 2秒后刷新列表
                } else {
                    addLog(`启动失败: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`启动出错: ${error.message}`, 'error');
            }
        }

        // 🛑 停止目标浏览器
        async function stopTargetBrowser() {
            addLog(`正在停止目标浏览器: ${targetBrowserId}...`);
            
            try {
                const response = await fetch('/api/xiaohongshu/bitbrowser/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ browserId: targetBrowserId })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog('目标浏览器停止成功！', 'success');
                    setTimeout(refreshBrowserList, 2000); // 2秒后刷新列表
                } else {
                    addLog(`停止失败: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`停止出错: ${error.message}`, 'error');
            }
        }

        // 📊 更新连接状态显示
        function updateConnectionStatus(data, errorMessage = null) {
            const statusContainer = document.getElementById('connection-status');
            
            if (data) {
                statusContainer.innerHTML = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div>
                            <strong>🌐 API连接:</strong>
                            <span class="status-indicator ${data.summary.apiConnected ? 'status-online' : 'status-offline'}"></span>
                            ${data.summary.apiConnected ? '正常' : '失败'}
                        </div>
                        <div>
                            <strong>🔧 调试端口:</strong>
                            <span class="status-indicator ${data.summary.debugConnected ? 'status-online' : 'status-offline'}"></span>
                            ${data.summary.debugConnected ? `端口 ${data.debugStatus.port}` : '未连接'}
                        </div>
                        <div>
                            <strong>🌐 浏览器数量:</strong> ${data.summary.browserCount}
                        </div>
                        <div>
                            <strong>🎯 目标浏览器:</strong>
                            <span class="status-indicator ${data.summary.targetBrowserFound ? 'status-online' : 'status-offline'}"></span>
                            ${data.summary.targetBrowserFound ? '已找到' : '未找到'}
                        </div>
                        <div>
                            <strong>📱 小红书页面:</strong>
                            <span class="status-indicator ${data.summary.xiaohongshuPageFound ? 'status-online' : 'status-offline'}"></span>
                            ${data.summary.xiaohongshuPageFound ? '已打开' : '未打开'}
                        </div>
                    </div>
                `;
            } else {
                statusContainer.innerHTML = `
                    <div style="color: #ff6b6b;">
                        <strong>❌ 连接失败:</strong> ${errorMessage || '未知错误'}
                    </div>
                `;
            }
        }

        // 🎨 渲染浏览器列表
        function renderBrowserList() {
            const container = document.getElementById('browser-list');
            
            if (currentBrowsers.length === 0) {
                container.innerHTML = '<p>📭 暂无浏览器实例</p>';
                return;
            }
            
            container.innerHTML = currentBrowsers.map(browser => {
                const isTarget = browser.id === targetBrowserId;
                const isOnline = browser.status === 'running' || browser.status === 'online';
                
                return `
                    <div class="browser-card ${isTarget ? 'target' : ''} ${!isOnline ? 'offline' : ''}">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h4>${browser.name || browser.id}</h4>
                            ${isTarget ? '<span style="background: #FF9800; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">🎯 目标</span>' : ''}
                        </div>
                        
                        <div style="margin-bottom: 10px;">
                            <p><strong>ID:</strong> ${browser.id}</p>
                            <p><strong>状态:</strong> 
                                <span class="status-indicator ${isOnline ? 'status-online' : 'status-offline'}"></span>
                                ${browser.status || '未知'}
                            </p>
                            ${browser.debug_port ? `<p><strong>调试端口:</strong> ${browser.debug_port}</p>` : ''}
                        </div>
                        
                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="startBrowser('${browser.id}')">🚀 启动</button>
                            <button class="btn btn-danger" onclick="stopBrowser('${browser.id}')">🛑 停止</button>
                            ${isTarget ? '<button class="btn btn-primary" onclick="testConnection()">🔍 测试</button>' : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 🚀 启动指定浏览器
        async function startBrowser(browserId) {
            addLog(`正在启动浏览器: ${browserId}...`);
            
            try {
                const response = await fetch('/api/xiaohongshu/bitbrowser/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ browserId })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog(`浏览器 ${browserId} 启动成功！`, 'success');
                    setTimeout(refreshBrowserList, 2000);
                } else {
                    addLog(`启动失败: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`启动出错: ${error.message}`, 'error');
            }
        }

        // 🛑 停止指定浏览器
        async function stopBrowser(browserId) {
            addLog(`正在停止浏览器: ${browserId}...`);
            
            try {
                const response = await fetch('/api/xiaohongshu/bitbrowser/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ browserId })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog(`浏览器 ${browserId} 停止成功！`, 'success');
                    setTimeout(refreshBrowserList, 2000);
                } else {
                    addLog(`停止失败: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`停止出错: ${error.message}`, 'error');
            }
        }

        // 🚀 页面加载完成后自动执行
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成，开始初始化...');
            
            // 自动刷新浏览器列表
            refreshBrowserList();
            
            // 自动测试连接
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
