// ===== 配置管理器 =====
// 统一管理应用配置，支持环境变量和默认值

class ConfigManager {
    constructor() {
        this.config = this.loadConfig();
        this.validateConfig();
    }

    // 加载配置
    loadConfig() {
        // 在浏览器环境中，我们需要从其他地方获取配置
        // 这里先使用默认配置，后续可以从服务器获取
        const defaultConfig = {
            // 比特浏览器配置
            bitBrowser: {
                apiUrl: 'http://127.0.0.1:54345',
                apiToken: 'ca28ee5ca6de4d209182a83aa16a2044',
                timeout: 30000,
                retryCount: 3,
                endpoints: {
                    list: '/api/v1/browser/list',
                    create: '/api/v1/browser/update',
                    start: '/api/v1/browser/start',
                    stop: '/api/v1/browser/stop',
                    delete: '/api/v1/browser/delete'
                }
            },

            // 应用配置
            app: {
                name: '黑默科技平台',
                version: '1.0.0',
                debug: false,
                logLevel: 'INFO'
            },

            // UI配置
            ui: {
                theme: 'dark',
                language: 'zh-CN',
                autoRefresh: true,
                refreshInterval: 30000
            },

            // 安全配置
            security: {
                enableApiKey: true,
                corsEnabled: true,
                rateLimitEnabled: false
            }
        };

        // 尝试从localStorage获取用户自定义配置
        try {
            const savedConfig = localStorage.getItem('app-config');
            if (savedConfig) {
                const userConfig = JSON.parse(savedConfig);
                return this.mergeConfig(defaultConfig, userConfig);
            }
        } catch (error) {
            console.warn('⚠️ 无法加载用户配置，使用默认配置:', error);
        }

        return defaultConfig;
    }

    // 合并配置
    mergeConfig(defaultConfig, userConfig) {
        const merged = { ...defaultConfig };
        
        for (const key in userConfig) {
            if (typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key])) {
                merged[key] = { ...defaultConfig[key], ...userConfig[key] };
            } else {
                merged[key] = userConfig[key];
            }
        }
        
        return merged;
    }

    // 验证配置
    validateConfig() {
        const required = [
            'bitBrowser.apiUrl',
            'bitBrowser.apiToken'
        ];

        for (const path of required) {
            const value = this.getConfigValue(path);
            if (!value) {
                console.error(`❌ 缺少必需的配置项: ${path}`);
            }
        }
    }

    // 获取配置值
    getConfigValue(path) {
        const keys = path.split('.');
        let value = this.config;
        
        for (const key of keys) {
            if (value && typeof value === 'object') {
                value = value[key];
            } else {
                return undefined;
            }
        }
        
        return value;
    }

    // 设置配置值
    setConfigValue(path, value) {
        const keys = path.split('.');
        let current = this.config;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        
        current[keys[keys.length - 1]] = value;
        this.saveConfig();
    }

    // 保存配置到localStorage
    saveConfig() {
        try {
            localStorage.setItem('app-config', JSON.stringify(this.config));
            console.log('✅ 配置已保存');
        } catch (error) {
            console.error('❌ 保存配置失败:', error);
        }
    }

    // 重置配置
    resetConfig() {
        localStorage.removeItem('app-config');
        this.config = this.loadConfig();
        console.log('🔄 配置已重置');
    }

    // 获取比特浏览器配置
    getBitBrowserConfig() {
        return this.getConfigValue('bitBrowser');
    }

    // 更新比特浏览器配置
    updateBitBrowserConfig(updates) {
        const current = this.getBitBrowserConfig();
        const updated = { ...current, ...updates };
        this.setConfigValue('bitBrowser', updated);
        return updated;
    }

    // 获取应用配置
    getAppConfig() {
        return this.getConfigValue('app');
    }

    // 获取UI配置
    getUIConfig() {
        return this.getConfigValue('ui');
    }

    // 更新UI配置
    updateUIConfig(updates) {
        const current = this.getUIConfig();
        const updated = { ...current, ...updates };
        this.setConfigValue('ui', updated);
        return updated;
    }

    // 导出配置
    exportConfig() {
        return JSON.stringify(this.config, null, 2);
    }

    // 导入配置
    importConfig(configJson) {
        try {
            const importedConfig = JSON.parse(configJson);
            this.config = this.mergeConfig(this.loadConfig(), importedConfig);
            this.saveConfig();
            this.validateConfig();
            console.log('✅ 配置导入成功');
            return true;
        } catch (error) {
            console.error('❌ 配置导入失败:', error);
            return false;
        }
    }

    // 获取配置摘要
    getConfigSummary() {
        const bitBrowser = this.getBitBrowserConfig();
        const app = this.getAppConfig();
        
        return {
            appName: app.name,
            appVersion: app.version,
            bitBrowserUrl: bitBrowser.apiUrl,
            hasApiToken: !!bitBrowser.apiToken,
            configValid: this.isConfigValid()
        };
    }

    // 检查配置是否有效
    isConfigValid() {
        try {
            this.validateConfig();
            return true;
        } catch (error) {
            return false;
        }
    }

    // 获取调试信息
    getDebugInfo() {
        return {
            config: this.config,
            summary: this.getConfigSummary(),
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            localStorage: {
                available: typeof Storage !== 'undefined',
                hasConfig: !!localStorage.getItem('app-config')
            }
        };
    }
}

// 创建全局配置管理器实例
window.configManager = new ConfigManager();

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ConfigManager;
}

console.log('⚙️ 配置管理器已加载');
console.log('📋 配置摘要:', window.configManager.getConfigSummary());
