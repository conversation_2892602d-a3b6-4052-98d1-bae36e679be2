<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 健康检查 - Local Server 状态监控</title>
    <link rel="stylesheet" href="premium-ui.css">
    <style>
        /* ===== 🎨 健康检查专用样式 ===== */
        .health-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .status-card {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        .status-card.error {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }

        .status-card.warning {
            background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .detail-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #4CAF50;
        }

        .detail-card.error {
            border-left-color: #f44336;
        }

        .detail-card.warning {
            border-left-color: #FF9800;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }
        .status-warning { background: #FF9800; }

        .refresh-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 0;
            transition: all 0.2s ease;
        }

        .refresh-btn:hover {
            background: #1976D2;
            transform: translateY(-1px);
        }

        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }

        .json-display {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .endpoint-test {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .endpoint-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }

        .endpoint-btn:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="health-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>🏥 Local Server 健康检查</h1>
            <p>实时监控服务器状态和连接情况</p>
        </div>

        <!-- 主要状态卡片 -->
        <div id="main-status" class="status-card">
            <h2>🔄 正在检查服务器状态...</h2>
            <p>请稍候...</p>
        </div>

        <!-- 控制按钮 -->
        <div style="text-align: center;">
            <button class="refresh-btn" onclick="checkHealth()">🔄 立即检查</button>
            
            <div class="auto-refresh">
                <input type="checkbox" id="auto-refresh" onchange="toggleAutoRefresh()">
                <label for="auto-refresh">自动刷新 (每30秒)</label>
                <span id="countdown"></span>
            </div>
        </div>

        <!-- 详细状态 -->
        <div id="details-container" class="details-grid">
            <!-- 动态生成 -->
        </div>

        <!-- 端点测试 -->
        <div class="endpoint-test">
            <h3>🧪 API端点测试</h3>
            <p>测试不同的健康检查端点：</p>
            <button class="endpoint-btn" onclick="testEndpoint('/api/xiaohongshu/health')">完整健康检查</button>
            <button class="endpoint-btn" onclick="testEndpoint('/api/xiaohongshu/health/simple')">简化健康检查</button>
            <button class="endpoint-btn" onclick="testEndpoint('/api/xiaohongshu/test-browser-connection')">浏览器连接测试</button>
            <button class="endpoint-btn" onclick="testEndpoint('/api/xiaohongshu/bitbrowser/list')">浏览器列表</button>
        </div>

        <!-- JSON响应显示 -->
        <div class="section">
            <h3>📋 最新响应数据</h3>
            <div id="json-response" class="json-display">
                等待数据...
            </div>
        </div>
    </div>

    <script>
        // ===== 🏥 健康检查脚本 =====
        
        let autoRefreshInterval = null;
        let countdownInterval = null;
        let countdownSeconds = 30;

        // 🔍 执行健康检查
        async function checkHealth() {
            try {
                updateMainStatus('🔄 正在检查...', '检查服务器健康状态', 'loading');

                const response = await fetch('/api/xiaohongshu/health', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                const data = await response.json();
                
                updateJsonDisplay(data);
                
                if (data.success) {
                    updateMainStatus('✅ 服务器运行正常', data.message, 'success');
                    updateDetails(data.data);
                } else {
                    updateMainStatus('❌ 服务器异常', data.message, 'error');
                    updateDetails(null);
                }
                
            } catch (error) {
                updateMainStatus('❌ 连接失败', `无法连接到服务器: ${error.message}`, 'error');
                updateJsonDisplay({ error: error.message, timestamp: new Date().toISOString() });
                updateDetails(null);
            }
        }

        // 🧪 测试特定端点
        async function testEndpoint(endpoint) {
            try {
                updateMainStatus('🔄 测试中...', `正在测试: ${endpoint}`, 'loading');
                
                // 🔧 所有API都使用POST方法和JSON body传参
                let requestBody = {};

                if (endpoint.includes('bitbrowser/list')) {
                    requestBody = { page: 1, pageSize: 20 };
                } else if (endpoint.includes('test-browser-connection')) {
                    requestBody = {};
                } else {
                    requestBody = {};
                }

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                updateJsonDisplay(data);
                
                if (data.success) {
                    updateMainStatus('✅ 端点测试成功', `${endpoint} 响应正常`, 'success');
                } else {
                    updateMainStatus('⚠️ 端点返回错误', `${endpoint}: ${data.message}`, 'warning');
                }
                
            } catch (error) {
                updateMainStatus('❌ 端点测试失败', `${endpoint}: ${error.message}`, 'error');
                updateJsonDisplay({ error: error.message, endpoint: endpoint, timestamp: new Date().toISOString() });
            }
        }

        // 📊 更新主状态卡片
        function updateMainStatus(title, message, type) {
            const statusCard = document.getElementById('main-status');
            statusCard.className = `status-card ${type === 'error' ? 'error' : type === 'warning' ? 'warning' : ''}`;
            statusCard.innerHTML = `
                <h2>${title}</h2>
                <p>${message}</p>
                <small>最后更新: ${new Date().toLocaleString()}</small>
            `;
        }

        // 📋 更新详细信息
        function updateDetails(data) {
            const container = document.getElementById('details-container');
            
            if (!data) {
                container.innerHTML = '<div class="detail-card error"><h4>❌ 无法获取详细信息</h4></div>';
                return;
            }

            container.innerHTML = `
                <div class="detail-card">
                    <h4>🖥️ 服务器信息</h4>
                    <p><strong>运行时间:</strong> ${Math.floor(data.server.uptime)} 秒</p>
                    <p><strong>Node版本:</strong> ${data.server.version}</p>
                    <p><strong>平台:</strong> ${data.server.platform}</p>
                    <p><strong>内存使用:</strong> ${Math.round(data.server.memory.heapUsed / 1024 / 1024)} MB</p>
                </div>
                
                <div class="detail-card ${data.bitbrowser.connected ? '' : 'error'}">
                    <h4>🌐 比特浏览器</h4>
                    <p>
                        <span class="status-indicator ${data.bitbrowser.connected ? 'status-online' : 'status-offline'}"></span>
                        <strong>连接状态:</strong> ${data.bitbrowser.connected ? '已连接' : '未连接'}
                    </p>
                    <p><strong>消息:</strong> ${data.bitbrowser.message}</p>
                    <p><strong>浏览器数量:</strong> ${data.bitbrowser.browserCount}</p>
                    ${data.bitbrowser.targetBrowserFound !== undefined ? 
                        `<p><strong>目标浏览器:</strong> ${data.bitbrowser.targetBrowserFound ? '已找到' : '未找到'}</p>` : ''}
                </div>
                
                <div class="detail-card">
                    <h4>⚙️ 服务状态</h4>
                    <p><strong>数据提取器:</strong> ${data.services.xiaohongshu_extractor}</p>
                    <p><strong>Socket.IO:</strong> ${data.services.socket_io}</p>
                    <p><strong>API端点:</strong> ${data.services.api_endpoints}</p>
                </div>
            `;
        }

        // 📋 更新JSON显示
        function updateJsonDisplay(data) {
            const jsonDisplay = document.getElementById('json-response');
            jsonDisplay.textContent = JSON.stringify(data, null, 2);
        }

        // ⏰ 切换自动刷新
        function toggleAutoRefresh() {
            const checkbox = document.getElementById('auto-refresh');
            
            if (checkbox.checked) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        }

        // ▶️ 开始自动刷新
        function startAutoRefresh() {
            stopAutoRefresh(); // 清除现有的定时器
            
            autoRefreshInterval = setInterval(checkHealth, 30000);
            startCountdown();
        }

        // ⏹️ 停止自动刷新
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
            
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
            
            document.getElementById('countdown').textContent = '';
        }

        // ⏱️ 开始倒计时
        function startCountdown() {
            countdownSeconds = 30;
            
            countdownInterval = setInterval(() => {
                countdownSeconds--;
                document.getElementById('countdown').textContent = `(${countdownSeconds}s)`;
                
                if (countdownSeconds <= 0) {
                    countdownSeconds = 30;
                }
            }, 1000);
        }

        // 🚀 页面加载完成后自动执行
        document.addEventListener('DOMContentLoaded', function() {
            // 立即执行一次健康检查
            checkHealth();
        });
    </script>
</body>
</html>
