/* ========================================
   全新现代化UI设计系统 - 白牌体矩阵
   ======================================== */

/* CSS Reset & 防蓝框终极方案 */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-focus-ring-color: transparent !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

*:focus,
*:active,
*:hover,
button:focus,
div:focus,
a:focus {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* 现代化设计变量 */
:root {
    /* 主色调 - 现代蓝紫渐变 */
    --primary: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --primary-bg: #f0f4ff;
    
    /* 中性色 - 现代灰度 */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 功能色 */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    
    /* 圆角系统 */
    --radius-sm: 4px;
    --radius: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* 间距系统 */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    
    /* 字体系统 */
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

/* 全局样式 */
body {
    font-family: var(--font-sans);
    background: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.5;
    font-weight: 400;
    letter-spacing: -0.01em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow: hidden;
}

/* 主容器 */
.app-container {
    display: flex;
    height: 100vh;
    background: var(--gray-50);
}

/* 现代化侧边栏 - 小巧精致 */
.sidebar {
    width: 220px;
    background: var(--white);
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 10;
}

/* 品牌标识区域 - 紧凑设计 */
.brand-header {
    padding: var(--space-4) var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: white;
    position: relative;
}

.brand-content {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.brand-icon {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.brand-info h1 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 2px;
    letter-spacing: -0.02em;
}

.brand-info p {
    font-size: 12px;
    opacity: 0.9;
    font-weight: 400;
}

/* 导航菜单 - 紧凑布局 */
.nav-menu {
    flex: 1;
    padding: var(--space-3) 0;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: var(--space-4);
}

.nav-section-title {
    font-size: 10px;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    padding: 0 var(--space-4) var(--space-2) var(--space-4);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    color: var(--gray-600);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    user-select: none;
    border: none;
    outline: none;
    background: none;
}

.nav-item:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.nav-item.active {
    background: var(--primary-bg);
    color: var(--primary);
    font-weight: 600;
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: var(--primary);
    border-radius: 0 2px 2px 0;
}

.nav-item i {
    width: 16px;
    font-size: 14px;
    text-align: center;
}

/* 状态栏 - 紧凑设计 */
.status-bar {
    padding: var(--space-3) var(--space-4);
    border-top: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--gray-50);
}

.status-info {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 11px;
    color: var(--gray-500);
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--success);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--gray-50);
    overflow: hidden;
}

/* 顶部工具栏 - 紧凑设计 */
.toolbar {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-4) var(--space-5);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--gray-900);
    letter-spacing: -0.02em;
}

.toolbar-actions {
    display: flex;
    gap: var(--space-3);
}

/* 现代化按钮 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--radius);
    font-size: 14px;
    font-weight: 500;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    outline: none;
    letter-spacing: -0.01em;
}

.btn-primary {
    background: var(--primary);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

/* 内容区域 - 紧凑布局 */
.content-area {
    flex: 1;
    padding: var(--space-5);
    overflow-y: auto;
    background: var(--gray-50);
}

/* 现代化滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
    transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 220px;
        position: fixed;
        left: -220px;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.open {
        left: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .toolbar {
        padding: var(--space-4);
    }

    .page-title {
        font-size: 20px;
    }

    .content-area {
        padding: var(--space-4);
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slide-up {
    animation: slideInUp 0.3s ease-out;
}

/* 卡片样式 */
.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
}

.card-body {
    padding: var(--space-6);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.card-subtitle {
    font-size: 14px;
    color: var(--gray-600);
}

/* 徽章样式 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-2);
    font-size: 12px;
    font-weight: 500;
    border-radius: var(--radius-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: var(--primary-bg);
    color: var(--primary);
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.badge-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-800);
    color: white;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}
