// ===== 🚀 黑默科技桌面端营销管理平台 【前端核心应用】 =====
// 📝 功能说明：桌面端应用的主要JavaScript文件，负责页面交互和功能实现
// 🎯 核心定位：专业的桌面端社交媒体营销管理系统
// 🔧 技术架构：面向对象设计 + 模块化管理 + 实时通信 + 响应式界面

// ===== 🏗️ 主应用类 【核心控制器】 =====
// 📝 说明：这个类管理整个桌面端应用的核心功能和状态
class PremiumApp {
    constructor() {
        // ===== 🌐 初始化应用状态 【状态管理】 =====
        this.currentPage = 'overview';  // 📄 当前显示页面 - 控制视图切换
        this.socket = null;             // ⚡ WebSocket连接对象 - 实时通信管道
        this.collectedXiaohongshuAccounts = []; // 采集到的小红书账号数据
        this.init(); // 启动初始化流程
    }

    // ===== 应用初始化方法 =====
    // 按顺序初始化各个功能模块
    init() {
        this.initializeSocket(); // 初始化 WebSocket 连接
        this.initializeNavigation(); // 初始化导航功能
        this.loadPage('accounts'); // 默认加载账号管理页面（核心功能）

        console.log('🚀 黑默科技桌面端平台已启动');
    }

    // ===== 初始化 WebSocket 连接 =====
    // WebSocket 用于与服务器进行实时双向通信
    initializeSocket() {
        try {
            // 创建 Socket.IO 连接到服务器
            this.socket = io();

            // 监听连接成功事件
            this.socket.on('connect', () => {
                console.log('✅ 已连接到服务器');
                // 这里可以添加连接成功后的处理逻辑
            });

            // 监听连接断开事件
            this.socket.on('disconnect', () => {
                console.log('❌ 与服务器断开连接');
                // 这里可以添加断线重连逻辑
            });
        } catch (error) {
            // 如果 Socket.IO 不可用，应用仍可正常运行（离线模式）
            console.warn('⚠️ Socket.IO未可用，使用离线模式');
        }
    }

    // ===== 初始化导航功能 =====
    // 为左侧导航栏的每个菜单项添加点击事件监听器
    initializeNavigation() {
        // 获取所有导航菜单项
        const navItems = document.querySelectorAll('.nav-item');

        // 为每个导航项添加点击事件
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault(); // 阻止默认的链接跳转行为

                // 获取点击的页面标识（从 data-page 属性）
                const page = item.dataset.page;

                // 如果页面存在且不是当前页面，则切换页面
                if (page && page !== this.currentPage) {
                    this.loadPage(page); // 加载新页面
                }
            });
        });
    }



    // ===== 页面加载主方法 =====
    // 这是页面切换的核心方法，负责协调各个更新步骤
    loadPage(page) {
        this.currentPage = page; // 更新当前页面状态
        this.updateNavigation(page); // 更新导航栏高亮状态
        this.updatePageTitle(page); // 更新页面标题
        this.loadPageContent(page); // 加载页面具体内容
    }

    // ===== 更新导航栏状态 =====
    // 高亮当前激活的导航项，取消其他项的高亮
    updateNavigation(activePage) {
        const navItems = document.querySelectorAll('.nav-item'); // 获取所有导航项

        navItems.forEach(item => {
            item.classList.remove('active'); // 先移除所有激活样式
            // 检查导航项的页面标识是否与当前激活页面匹配
            if (item.dataset.page === activePage) {
                item.classList.add('active'); // 添加激活样式到当前页面
            }
        });
    }

    // 📝 更新页面标题
    updatePageTitle(page) {
        const titles = {
            'overview': '数据总览',
            'accounts': '账号管理',
            'content': '内容搜索',
            'analytics': '竞品分析',
            'interaction': '互动管理',
            'comments': '评论管理',
            'advanced': '高级功能',
            'data-collection': '数据采集',
            'cloud-phone': '云手机管理',
            'automation': '自动化工具',
            'settings': '系统设置',
            'users': '用户管理',
            'logs': '操作日志'
        };

        const titleElement = document.getElementById('pageTitle');
        if (titleElement) {
            titleElement.textContent = titles[page] || '未知页面';
        }
    }

    // 📄 加载页面内容
    loadPageContent(page) {
        const container = document.getElementById('pageContent');
        
        switch (page) {
            case 'overview':
                this.loadOverviewContent(container);
                break;
            case 'accounts':
                this.loadAccountsContent(container);
                break;
            case 'settings':
                this.loadSettingsContent(container);
                break;
            default:
                this.loadDefaultContent(container, page);
        }
    }

    // 📊 加载总览页面内容
    loadOverviewContent(container) {
        container.innerHTML = `
            <!-- 🎯 关键指标卡片 -->
            <div class="metrics-grid">
                <div class="metric-card metric-primary">
                    <div class="metric-icon">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value">15.68M</div>
                        <div class="metric-label">总平台播放量</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12.5%
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-success">
                    <div class="metric-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value">892K</div>
                        <div class="metric-label">总平台评论量</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +8.3%
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-warning">
                    <div class="metric-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value">1.56M</div>
                        <div class="metric-label">总平台点赞量</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +15.2%
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-info">
                    <div class="metric-icon">
                        <i class="fas fa-bookmark"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value">234K</div>
                        <div class="metric-label">总平台收藏量</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +6.8%
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-secondary">
                    <div class="metric-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value">12,847</div>
                        <div class="metric-label">总平台发布作品数</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +9.4%
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-dark">
                    <div class="metric-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value">2,847</div>
                        <div class="metric-label">管理总账号数</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +3.7%
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-purple">
                    <div class="metric-icon">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value">5.23M</div>
                        <div class="metric-label">总平台粉丝数量</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +18.6%
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-teal">
                    <div class="metric-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value">18,924</div>
                        <div class="metric-label">总平台作品量</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +11.2%
                        </div>
                    </div>
                </div>
            </div>

            <!-- 📈 图表区域 -->
            <div class="charts-section">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>账号增长趋势</h3>
                        <div class="chart-actions">
                            <button class="btn btn-ghost btn-sm">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chart-content">
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-area"></i>
                            <p>图表数据加载中...</p>
                        </div>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-header">
                        <h3>平台分布</h3>
                        <div class="chart-actions">
                            <button class="btn btn-ghost btn-sm">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chart-content">
                        <div class="pie-chart-container">
                            <svg class="pie-chart" viewBox="0 0 200 200" id="platformPieChart">
                                <!-- 扇形将通过JavaScript动态生成 -->
                            </svg>
                            <div class="pie-legend">
                                <div class="legend-item">
                                    <div class="legend-color" style="background: #ff2442;"></div>
                                    <span class="legend-text">小红书 45%</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color" style="background: #000000;"></div>
                                    <span class="legend-text">抖音 35%</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color" style="background: #ff6600;"></div>
                                    <span class="legend-text">快手 20%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 📋 最新活动 -->
            <div class="activity-section">
                <div class="section-header">
                    <h3>最新活动</h3>
                    <button class="btn btn-ghost">查看全部</button>
                </div>
                <div class="activity-list">
                    ${this.generateActivityItems()}
                </div>
            </div>
        `;

        // 延迟初始化扇形图交互
        setTimeout(() => {
            this.initializePieChart();
        }, 100);
    }

    // 👥 加载账号管理页面内容
    loadAccountsContent(container) {
        container.innerHTML = `
            <!-- 📊 账号分组统计 -->
            <div class="account-groups-stats">
                <div class="group-stat-card active" data-group="all">
                    <div class="group-icon all-accounts">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="group-content">
                        <div class="group-value">156</div>
                        <div class="group-label">所有账号</div>
                    </div>
                </div>
                <div class="group-stat-card" data-group="login">
                    <div class="group-icon login-accounts">
                        <i class="fas fa-sign-in-alt"></i>
                    </div>
                    <div class="group-content">
                        <div class="group-value">128</div>
                        <div class="group-label">登录账号</div>
                    </div>
                </div>
                <div class="group-stat-card" data-group="publish">
                    <div class="group-icon publish-accounts">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="group-content">
                        <div class="group-value">89</div>
                        <div class="group-label">发布账号</div>
                    </div>
                </div>
                <div class="group-stat-card" data-group="tool">
                    <div class="group-icon tool-accounts">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="group-content">
                        <div class="group-value">45</div>
                        <div class="group-label">工具号</div>
                    </div>
                </div>
                <div class="group-stat-card" data-group="network-error">
                    <div class="group-icon network-error">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="group-content">
                        <div class="group-value">12</div>
                        <div class="group-label">网络不可用</div>
                    </div>
                </div>
                <div class="group-stat-card" data-group="device-offline">
                    <div class="group-icon device-offline">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="group-content">
                        <div class="group-value">8</div>
                        <div class="group-label">设备掉线</div>
                    </div>
                </div>
            </div>

            <!-- 🎯 平台选择标签 -->
            <div class="platform-tabs">
                <button class="platform-tab active" data-platform="all">
                    <i class="fas fa-globe"></i>
                    全部平台
                    <span class="tab-count">156</span>
                </button>
                <button class="platform-tab" data-platform="xiaohongshu">
                    <i class="fab fa-instagram" style="color: #ff2442;"></i>
                    小红书
                    <span class="tab-count">68</span>
                </button>
                <button class="platform-tab" data-platform="xiaohongshu-manage">
                    <i class="fas fa-cog" style="color: #ff2442;"></i>
                    小红书管理
                    <span class="tab-count">3</span>
                </button>
                <button class="platform-tab" data-platform="douyin">
                    <i class="fab fa-tiktok" style="color: #000000;"></i>
                    抖音
                    <span class="tab-count">52</span>
                </button>
                <button class="platform-tab" data-platform="kuaishou">
                    <i class="fas fa-video" style="color: #ff6600;"></i>
                    快手
                    <span class="tab-count">36</span>
                </button>
            </div>

            <!-- 🔍 搜索和操作栏 -->
            <div class="accounts-toolbar">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="搜索账号昵称、ID、备注信息..." id="accountSearch">
                </div>
                <div class="toolbar-actions">
                    <button class="btn btn-ghost" onclick="window.accountManager.collectXiaohongshuAccounts()">
                        <i class="fas fa-download"></i>
                        采集账号
                    </button>
                    <button class="btn btn-info" onclick="window.accountManager.showDataSourceInfo()">
                        <i class="fas fa-info-circle"></i>
                        数据来源说明
                    </button>
                    <button class="btn btn-ghost" onclick="window.accountManager.refreshAllXiaohongshuAccounts()">
                        <i class="fas fa-sync-alt"></i>
                        刷新全部
                    </button>
                    <button class="btn btn-ghost" onclick="window.accountManager.toggleAutoRefresh()">
                        <i class="fas fa-clock"></i>
                        <span id="autoRefreshText">开启自动刷新</span>
                    </button>
                    <button class="btn btn-ghost" onclick="window.accountManager.extractCookies()">
                        <i class="fas fa-cookie-bite"></i>
                        提取Cookie
                    </button>
                    <button class="btn btn-secondary" onclick="window.app.createCustomGroup()">
                        <i class="fas fa-folder-plus"></i>
                        新建分组
                    </button>
                    <button class="btn btn-primary" onclick="window.accountManager.addAccount()">
                        <i class="fas fa-plus"></i>
                        添加账号
                    </button>
                </div>
            </div>

            <!-- 📋 账号管理表格 -->
            <div class="accounts-management-table">
                ${this.generateAccountManagementTable()}
            </div>
        `;

        // 初始化平台切换和分组功能
        setTimeout(() => {
            this.initializePlatformTabs();
            this.initializeGroupFilters();
            this.initializeBitBrowserIntegration();
        }, 100);
    }

    // ===== 创建自定义分组功能 =====
    createCustomGroup() {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'custom-group-modal';
        modal.innerHTML = `
            <div class="modal-overlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-folder-plus"></i> 新建自定义分组</h3>
                        <button class="modal-close" onclick="this.closeCustomGroupModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="groupName">分组名称</label>
                            <input type="text" id="groupName" placeholder="请输入分组名称，如：美妆达人、科技博主等" maxlength="20">
                            <small>建议使用有意义的名称，方便后续管理（最多20个字符）</small>
                        </div>
                        <div class="form-group">
                            <label for="groupColor">分组颜色</label>
                            <div class="color-picker">
                                <div class="color-option selected" data-color="#007bff" style="background: #007bff;"></div>
                                <div class="color-option" data-color="#28a745" style="background: #28a745;"></div>
                                <div class="color-option" data-color="#dc3545" style="background: #dc3545;"></div>
                                <div class="color-option" data-color="#ffc107" style="background: #ffc107;"></div>
                                <div class="color-option" data-color="#6f42c1" style="background: #6f42c1;"></div>
                                <div class="color-option" data-color="#fd7e14" style="background: #fd7e14;"></div>
                                <div class="color-option" data-color="#20c997" style="background: #20c997;"></div>
                                <div class="color-option" data-color="#e83e8c" style="background: #e83e8c;"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="groupDescription">分组描述（可选）</label>
                            <textarea id="groupDescription" placeholder="描述这个分组的用途..." maxlength="100"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="window.app.closeCustomGroupModal()">取消</button>
                        <button class="btn btn-primary" onclick="window.app.saveCustomGroup()">创建分组</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加事件监听
        this.initCustomGroupModal(modal);
    }

    // 初始化自定义分组模态框事件
    initCustomGroupModal(modal) {
        // 点击背景关闭
        modal.querySelector('.modal-overlay').addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeCustomGroupModal();
            }
        });

        // 颜色选择
        modal.querySelectorAll('.color-option').forEach(option => {
            option.addEventListener('click', () => {
                modal.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
            });
        });

        // 键盘事件
        document.addEventListener('keydown', this.handleModalKeydown.bind(this));

        // 聚焦到名称输入框
        setTimeout(() => {
            const nameInput = modal.querySelector('#groupName');
            if (nameInput) {
                nameInput.focus();
            }
        }, 100);
    }

    // 处理模态框键盘事件
    handleModalKeydown(e) {
        if (e.key === 'Escape') {
            this.closeCustomGroupModal();
        } else if (e.key === 'Enter' && e.ctrlKey) {
            this.saveCustomGroup();
        }
    }

    // 保存自定义分组
    saveCustomGroup() {
        const modal = document.querySelector('.custom-group-modal');
        if (!modal) return;

        const name = modal.querySelector('#groupName').value.trim();
        const description = modal.querySelector('#groupDescription').value.trim();
        const selectedColor = modal.querySelector('.color-option.selected');
        const color = selectedColor ? selectedColor.dataset.color : '#007bff';

        // 验证输入
        if (!name) {
            alert('请输入分组名称！');
            modal.querySelector('#groupName').focus();
            return;
        }

        if (name.length > 20) {
            alert('分组名称不能超过20个字符！');
            modal.querySelector('#groupName').focus();
            return;
        }

        // 检查分组名称是否重复
        const existingGroups = this.getCustomGroups();
        if (existingGroups.find(group => group.name === name)) {
            alert(`分组名称"${name}"已存在，请使用其他名称！`);
            modal.querySelector('#groupName').focus();
            return;
        }

        // 创建新分组
        const newGroup = {
            id: `custom-${Date.now()}`,
            name: name,
            color: color,
            description: description || `自定义分组：${name}`,
            isCustom: true,
            createdAt: new Date().toISOString(),
            accountCount: 0
        };

        // 保存分组
        this.saveCustomGroupToStorage(newGroup);

        // 关闭模态框
        this.closeCustomGroupModal();

        // 显示成功消息
        this.showNotification(`自定义分组"${name}"创建成功！`, 'success');

        // 刷新界面
        this.refreshAccountInterface();
    }

    // 关闭自定义分组模态框
    closeCustomGroupModal() {
        const modal = document.querySelector('.custom-group-modal');
        if (modal) {
            document.removeEventListener('keydown', this.handleModalKeydown.bind(this));
            modal.remove();
        }
    }

    // 获取自定义分组列表
    getCustomGroups() {
        const groups = localStorage.getItem('customGroups');
        return groups ? JSON.parse(groups) : [];
    }

    // 保存自定义分组到本地存储
    saveCustomGroupToStorage(group) {
        const groups = this.getCustomGroups();
        groups.push(group);
        localStorage.setItem('customGroups', JSON.stringify(groups));
    }

    // 显示通知消息
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // 刷新账号界面
    refreshAccountInterface() {
        // 这里可以添加刷新界面的逻辑
        console.log('界面已刷新');
    }

    // 显示分组选择器
    showGroupSelector(accountId) {
        const customGroups = this.getCustomGroups();
        const defaultGroups = [
            { id: 'sports', name: '体育类', color: '#28a745' },
            { id: 'fashion', name: '时尚类', color: '#6f42c1' },
            { id: 'food', name: '美食类', color: '#fd7e14' },
            { id: 'tech', name: '科技类', color: '#007bff' },
            { id: 'travel', name: '旅行类', color: '#20c997' },
            { id: 'lifestyle', name: '生活类', color: '#e83e8c' }
        ];

        const allGroups = [...defaultGroups, ...customGroups];

        const modal = document.createElement('div');
        modal.className = 'group-selector-modal';
        modal.innerHTML = `
            <div class="modal-overlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-folder"></i> 选择分组</h3>
                        <button class="modal-close" onclick="window.app.closeGroupSelector()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="group-list">
                            ${allGroups.map(group => `
                                <div class="group-item" onclick="window.app.assignAccountToGroup(${accountId}, '${group.id}', '${group.name}', '${group.color}')">
                                    <div class="group-color" style="background-color: ${group.color}"></div>
                                    <span class="group-name">${group.name}</span>
                                    ${group.isCustom ? '<i class="fas fa-star custom-indicator" title="自定义分组"></i>' : ''}
                                </div>
                            `).join('')}
                        </div>
                        <div class="group-actions">
                            <button class="btn btn-secondary" onclick="window.app.createCustomGroup(); window.app.closeGroupSelector();">
                                <i class="fas fa-plus"></i> 新建分组
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 点击背景关闭
        modal.querySelector('.modal-overlay').addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeGroupSelector();
            }
        });
    }

    // 关闭分组选择器
    closeGroupSelector() {
        const modal = document.querySelector('.group-selector-modal');
        if (modal) {
            modal.remove();
        }
    }

    // 将账号分配到分组
    assignAccountToGroup(accountId, groupId, groupName, groupColor) {
        // 更新账号的分组信息
        this.updateAccountGroup(accountId, groupId, groupName, groupColor);

        // 关闭选择器
        this.closeGroupSelector();

        // 显示成功消息
        this.showNotification(`账号已移动到"${groupName}"分组`, 'success');

        // 刷新界面
        this.refreshAccountInterface();
    }

    // 更新账号分组
    updateAccountGroup(accountId, groupId, groupName, groupColor) {
        // 这里应该更新账号数据
        // 由于当前是演示数据，我们只在控制台输出
        console.log(`账号 ${accountId} 已分配到分组: ${groupName} (${groupId})`);

        // 在实际应用中，这里应该：
        // 1. 更新本地存储或发送到服务器
        // 2. 更新账号数据中的group和groupName字段
        // 3. 重新渲染账号列表
    }

    // ===== 比特浏览器集成功能 =====

    // 初始化比特浏览器集成
    async initializeBitBrowserIntegration() {
        try {
            console.log('🌐 初始化比特浏览器集成...');

            // 测试API连接
            const connectionResult = await window.bitBrowserAPI.testConnection();

            if (connectionResult.success) {
                console.log('✅ 比特浏览器API连接成功');
                this.showNotification('比特浏览器API连接成功', 'success');

                // 添加比特浏览器相关按钮到工具栏
                this.addBitBrowserButtons();
            } else {
                console.warn('⚠️ 比特浏览器API连接失败:', connectionResult.error);
                this.showNotification('比特浏览器API连接失败，请检查比特浏览器是否启动', 'error');
            }
        } catch (error) {
            console.error('❌ 比特浏览器集成初始化失败:', error);
        }
    }

    // 添加比特浏览器相关按钮
    addBitBrowserButtons() {
        const toolbar = document.querySelector('.toolbar-actions');
        if (!toolbar) return;

        // 创建比特浏览器管理按钮
        const bitBrowserBtn = document.createElement('button');
        bitBrowserBtn.className = 'btn btn-info';
        bitBrowserBtn.innerHTML = '<i class="fas fa-browser"></i> 浏览器管理';
        bitBrowserBtn.onclick = () => this.showBitBrowserManager();

        // 插入到工具栏中
        toolbar.insertBefore(bitBrowserBtn, toolbar.firstChild);
    }

    // 显示比特浏览器管理界面
    showBitBrowserManager() {
        const modal = document.createElement('div');
        modal.className = 'bit-browser-manager-modal';
        modal.innerHTML = `
            <div class="modal-overlay">
                <div class="modal-content" style="max-width: 800px;">
                    <div class="modal-header">
                        <h3><i class="fas fa-browser"></i> 比特浏览器管理</h3>
                        <button class="modal-close" onclick="this.closeBitBrowserManager()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="browser-manager-tabs">
                            <button class="tab-btn active" onclick="window.app.switchBrowserTab('status')">连接状态</button>
                            <button class="tab-btn" onclick="window.app.switchBrowserTab('browsers')">浏览器实例</button>
                            <button class="tab-btn" onclick="window.app.switchBrowserTab('accounts')">账号绑定</button>
                        </div>

                        <div class="tab-content">
                            <div id="status-tab" class="tab-pane active">
                                <div class="status-info">
                                    <div class="status-item">
                                        <span class="status-label">API连接状态:</span>
                                        <span class="status-value" id="apiStatus">检查中...</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">活跃浏览器:</span>
                                        <span class="status-value" id="activeBrowsers">0</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">API地址:</span>
                                        <span class="status-value">http://127.0.0.1:54345</span>
                                    </div>
                                </div>
                                <div class="status-actions">
                                    <button class="btn btn-primary" onclick="window.app.testBitBrowserConnection()">
                                        <i class="fas fa-sync"></i> 重新连接
                                    </button>
                                    <button class="btn btn-secondary" onclick="window.app.refreshBrowserStatus()">
                                        <i class="fas fa-refresh"></i> 刷新状态
                                    </button>
                                </div>
                            </div>

                            <div id="browsers-tab" class="tab-pane">
                                <div class="browser-list-header">
                                    <h4>浏览器实例列表</h4>
                                    <button class="btn btn-primary" onclick="window.app.createNewBrowser()">
                                        <i class="fas fa-plus"></i> 创建浏览器
                                    </button>
                                </div>
                                <div id="browserList" class="browser-list">
                                    <!-- 浏览器列表将在这里动态加载 -->
                                </div>
                            </div>

                            <div id="accounts-tab" class="tab-pane">
                                <div class="account-browser-binding">
                                    <h4>账号浏览器绑定</h4>
                                    <p>为每个账号分配专用的浏览器实例，实现隔离管理</p>
                                    <div id="accountBindingList" class="account-binding-list">
                                        <!-- 账号绑定列表将在这里动态加载 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 初始化管理界面
        this.initBitBrowserManager(modal);
    }

    // 初始化比特浏览器管理界面
    async initBitBrowserManager(modal) {
        // 点击背景关闭
        modal.querySelector('.modal-overlay').addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeBitBrowserManager();
            }
        });

        // 加载初始状态
        await this.refreshBrowserStatus();
        await this.loadBrowserList();
    }

    // 切换浏览器管理标签
    switchBrowserTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[onclick="window.app.switchBrowserTab('${tabName}')"]`).classList.add('active');

        // 更新标签内容
        document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
        document.getElementById(`${tabName}-tab`).classList.add('active');

        // 根据标签加载相应内容
        switch(tabName) {
            case 'browsers':
                this.loadBrowserList();
                break;
            case 'accounts':
                this.loadAccountBindingList();
                break;
        }
    }

    // 测试比特浏览器连接
    async testBitBrowserConnection() {
        const statusElement = document.getElementById('apiStatus');
        if (statusElement) {
            statusElement.textContent = '连接中...';
        }

        const result = await window.bitBrowserAPI.testConnection();

        if (statusElement) {
            statusElement.textContent = result.success ? '✅ 已连接' : '❌ 连接失败';
            statusElement.className = result.success ? 'status-success' : 'status-error';
        }

        this.showNotification(
            result.success ? '比特浏览器连接成功' : `连接失败: ${result.error}`,
            result.success ? 'success' : 'error'
        );

        return result;
    }

    // 刷新浏览器状态
    async refreshBrowserStatus() {
        const activeBrowsers = window.bitBrowserAPI.getActiveBrowsers();
        const activeBrowsersElement = document.getElementById('activeBrowsers');

        if (activeBrowsersElement) {
            activeBrowsersElement.textContent = activeBrowsers.length;
        }

        // 更新API状态
        await this.testBitBrowserConnection();
    }

    // 加载浏览器列表
    async loadBrowserList() {
        const browserListElement = document.getElementById('browserList');
        if (!browserListElement) return;

        browserListElement.innerHTML = '<div class="loading">加载中...</div>';

        try {
            const result = await window.bitBrowserAPI.getBrowserList();

            if (result.success) {
                const browsers = result.browsers;

                if (browsers.length === 0) {
                    browserListElement.innerHTML = '<div class="empty-state">暂无浏览器实例</div>';
                    return;
                }

                browserListElement.innerHTML = browsers.map(browser => `
                    <div class="browser-item">
                        <div class="browser-info">
                            <h5>${browser.name}</h5>
                            <p>${browser.remark || '无备注'}</p>
                            <span class="browser-id">ID: ${browser.id}</span>
                        </div>
                        <div class="browser-actions">
                            <button class="btn btn-sm btn-primary" onclick="window.app.startBrowser('${browser.id}')">
                                <i class="fas fa-play"></i> 启动
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="window.app.stopBrowser('${browser.id}')">
                                <i class="fas fa-stop"></i> 停止
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="window.app.deleteBrowser('${browser.id}')">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                `).join('');
            } else {
                browserListElement.innerHTML = `<div class="error-state">加载失败: ${result.error}</div>`;
            }
        } catch (error) {
            browserListElement.innerHTML = `<div class="error-state">加载失败: ${error.message}</div>`;
        }
    }

    // 关闭比特浏览器管理器
    closeBitBrowserManager() {
        const modal = document.querySelector('.bit-browser-manager-modal');
        if (modal) {
            modal.remove();
        }
    }

    // ===== 配置管理功能 =====

    // 显示配置管理界面
    showConfigManagement() {
        const modal = document.createElement('div');
        modal.className = 'config-manager-modal';
        modal.innerHTML = `
            <div class="modal-overlay">
                <div class="modal-content" style="max-width: 900px;">
                    <div class="modal-header">
                        <h3><i class="fas fa-cog"></i> 配置管理</h3>
                        <button class="modal-close" onclick="window.app.closeConfigManager()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="config-tabs">
                            <button class="tab-btn active" onclick="window.app.switchConfigTab('bitbrowser')">比特浏览器</button>
                            <button class="tab-btn" onclick="window.app.switchConfigTab('app')">应用设置</button>
                            <button class="tab-btn" onclick="window.app.switchConfigTab('ui')">界面设置</button>
                            <button class="tab-btn" onclick="window.app.switchConfigTab('export')">导入导出</button>
                        </div>

                        <div class="config-content">
                            <div id="config-bitbrowser" class="config-panel active">
                                <h4>比特浏览器API配置</h4>
                                <div class="form-group">
                                    <label>API地址:</label>
                                    <input type="text" id="bitbrowser-url" placeholder="http://127.0.0.1:54345">
                                </div>
                                <div class="form-group">
                                    <label>API Token:</label>
                                    <input type="text" id="bitbrowser-token" placeholder="API Token">
                                </div>
                                <div class="form-group">
                                    <label>超时时间 (毫秒):</label>
                                    <input type="number" id="bitbrowser-timeout" placeholder="30000">
                                </div>
                                <div class="form-group">
                                    <label>重试次数:</label>
                                    <input type="number" id="bitbrowser-retry" placeholder="3">
                                </div>
                                <div class="form-actions">
                                    <button class="btn btn-primary" onclick="window.app.testBitBrowserConnection()">测试连接</button>
                                    <button class="btn btn-success" onclick="window.app.saveBitBrowserConfig()">保存配置</button>
                                </div>
                                <div id="connection-status" class="status-display"></div>
                            </div>

                            <div id="config-app" class="config-panel">
                                <h4>应用设置</h4>
                                <div class="form-group">
                                    <label>应用名称:</label>
                                    <input type="text" id="app-name" placeholder="黑默科技平台">
                                </div>
                                <div class="form-group">
                                    <label>日志级别:</label>
                                    <select id="app-log-level">
                                        <option value="DEBUG">DEBUG</option>
                                        <option value="INFO">INFO</option>
                                        <option value="WARN">WARN</option>
                                        <option value="ERROR">ERROR</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="app-debug"> 启用调试模式
                                    </label>
                                </div>
                                <div class="form-actions">
                                    <button class="btn btn-success" onclick="window.app.saveAppConfig()">保存设置</button>
                                </div>
                            </div>

                            <div id="config-ui" class="config-panel">
                                <h4>界面设置</h4>
                                <div class="form-group">
                                    <label>主题:</label>
                                    <select id="ui-theme">
                                        <option value="dark">深色主题</option>
                                        <option value="light">浅色主题</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>语言:</label>
                                    <select id="ui-language">
                                        <option value="zh-CN">简体中文</option>
                                        <option value="en-US">English</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="ui-auto-refresh"> 自动刷新
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>刷新间隔 (毫秒):</label>
                                    <input type="number" id="ui-refresh-interval" placeholder="30000">
                                </div>
                                <div class="form-actions">
                                    <button class="btn btn-success" onclick="window.app.saveUIConfig()">保存设置</button>
                                </div>
                            </div>

                            <div id="config-export" class="config-panel">
                                <h4>配置导入导出</h4>
                                <div class="form-group">
                                    <label>导出配置:</label>
                                    <button class="btn btn-info" onclick="window.app.exportConfig()">导出配置文件</button>
                                </div>
                                <div class="form-group">
                                    <label>导入配置:</label>
                                    <input type="file" id="config-import-file" accept=".json">
                                    <button class="btn btn-warning" onclick="window.app.importConfig()">导入配置</button>
                                </div>
                                <div class="form-group">
                                    <label>重置配置:</label>
                                    <button class="btn btn-danger" onclick="window.app.resetConfig()">重置为默认配置</button>
                                </div>
                                <div class="form-group">
                                    <label>配置信息:</label>
                                    <pre id="config-info" class="config-display"></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        this.initConfigManager(modal);
    }

    // 初始化配置管理器
    initConfigManager(modal) {
        // 点击背景关闭
        modal.querySelector('.modal-overlay').addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeConfigManager();
            }
        });

        // 加载当前配置
        this.loadCurrentConfig();
    }

    // 加载当前配置到表单
    loadCurrentConfig() {
        if (!window.configManager) return;

        const config = window.configManager.config;

        // 比特浏览器配置
        const bitBrowserConfig = config.bitBrowser || {};
        document.getElementById('bitbrowser-url').value = bitBrowserConfig.apiUrl || '';
        document.getElementById('bitbrowser-token').value = bitBrowserConfig.apiToken || '';
        document.getElementById('bitbrowser-timeout').value = bitBrowserConfig.timeout || 30000;
        document.getElementById('bitbrowser-retry').value = bitBrowserConfig.retryCount || 3;

        // 应用配置
        const appConfig = config.app || {};
        document.getElementById('app-name').value = appConfig.name || '';
        document.getElementById('app-log-level').value = appConfig.logLevel || 'INFO';
        document.getElementById('app-debug').checked = appConfig.debug || false;

        // UI配置
        const uiConfig = config.ui || {};
        document.getElementById('ui-theme').value = uiConfig.theme || 'dark';
        document.getElementById('ui-language').value = uiConfig.language || 'zh-CN';
        document.getElementById('ui-auto-refresh').checked = uiConfig.autoRefresh || true;
        document.getElementById('ui-refresh-interval').value = uiConfig.refreshInterval || 30000;

        // 显示配置信息
        this.updateConfigInfo();
    }

    // 切换配置标签
    switchConfigTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.config-tabs .tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[onclick="window.app.switchConfigTab('${tabName}')"]`).classList.add('active');

        // 更新面板显示
        document.querySelectorAll('.config-panel').forEach(panel => panel.classList.remove('active'));
        document.getElementById(`config-${tabName}`).classList.add('active');
    }

    // 测试比特浏览器连接
    async testBitBrowserConnection() {
        const statusElement = document.getElementById('connection-status');
        statusElement.innerHTML = '<div class="status-loading">正在测试连接...</div>';

        try {
            const url = document.getElementById('bitbrowser-url').value;
            const token = document.getElementById('bitbrowser-token').value;

            if (!url || !token) {
                throw new Error('请填写API地址和Token');
            }

            // 首先测试健康检查端点
            const response = await fetch(`${url}/health`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': token
                },
                body: JSON.stringify({})
            });

            const data = await response.json();

            if (data.success) {
                statusElement.innerHTML = '<div class="status-success">✅ 连接成功！健康检查通过</div>';
            } else {
                statusElement.innerHTML = `<div class="status-error">❌ 连接失败: ${data.msg || '健康检查失败'}</div>`;
            }
        } catch (error) {
            statusElement.innerHTML = `<div class="status-error">❌ 连接失败: ${error.message}</div>`;
        }
    }

    // 保存比特浏览器配置
    saveBitBrowserConfig() {
        if (!window.configManager) return;

        const config = {
            apiUrl: document.getElementById('bitbrowser-url').value,
            apiToken: document.getElementById('bitbrowser-token').value,
            timeout: parseInt(document.getElementById('bitbrowser-timeout').value) || 30000,
            retryCount: parseInt(document.getElementById('bitbrowser-retry').value) || 3
        };

        window.configManager.updateBitBrowserConfig(config);

        // 更新API实例配置
        if (window.bitBrowserAPI) {
            window.bitBrowserAPI.config = window.configManager.getBitBrowserConfig();
        }

        this.showNotification('比特浏览器配置已保存', 'success');
        this.updateConfigInfo();
    }

    // 保存应用配置
    saveAppConfig() {
        if (!window.configManager) return;

        const config = {
            name: document.getElementById('app-name').value,
            logLevel: document.getElementById('app-log-level').value,
            debug: document.getElementById('app-debug').checked
        };

        window.configManager.setConfigValue('app', { ...window.configManager.getAppConfig(), ...config });
        this.showNotification('应用配置已保存', 'success');
        this.updateConfigInfo();
    }

    // 保存UI配置
    saveUIConfig() {
        if (!window.configManager) return;

        const config = {
            theme: document.getElementById('ui-theme').value,
            language: document.getElementById('ui-language').value,
            autoRefresh: document.getElementById('ui-auto-refresh').checked,
            refreshInterval: parseInt(document.getElementById('ui-refresh-interval').value) || 30000
        };

        window.configManager.updateUIConfig(config);
        this.showNotification('界面配置已保存', 'success');
        this.updateConfigInfo();
    }

    // 导出配置
    exportConfig() {
        if (!window.configManager) return;

        const configJson = window.configManager.exportConfig();
        const blob = new Blob([configJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `heimo-config-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification('配置已导出', 'success');
    }

    // 导入配置
    importConfig() {
        const fileInput = document.getElementById('config-import-file');
        const file = fileInput.files[0];

        if (!file) {
            this.showNotification('请选择配置文件', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const configJson = e.target.result;
                if (window.configManager && window.configManager.importConfig(configJson)) {
                    this.loadCurrentConfig();
                    this.showNotification('配置导入成功', 'success');
                } else {
                    this.showNotification('配置导入失败', 'error');
                }
            } catch (error) {
                this.showNotification(`配置导入失败: ${error.message}`, 'error');
            }
        };
        reader.readAsText(file);
    }

    // 重置配置
    resetConfig() {
        if (!window.configManager) return;

        if (confirm('确定要重置所有配置为默认值吗？此操作不可撤销。')) {
            window.configManager.resetConfig();
            this.loadCurrentConfig();
            this.showNotification('配置已重置', 'success');
        }
    }

    // 更新配置信息显示
    updateConfigInfo() {
        const infoElement = document.getElementById('config-info');
        if (infoElement && window.configManager) {
            const summary = window.configManager.getConfigSummary();
            infoElement.textContent = JSON.stringify(summary, null, 2);
        }
    }

    // 关闭配置管理器
    closeConfigManager() {
        const modal = document.querySelector('.config-manager-modal');
        if (modal) {
            modal.remove();
        }
    }

    // ===== 系统设置页面 =====

    // 加载系统设置页面内容
    loadSettingsContent(container) {
        container.innerHTML = `
            <div class="settings-page">
                <div class="settings-header">
                    <h2><i class="fas fa-cog"></i> 系统设置</h2>
                    <p>管理应用配置和个性化设置</p>
                </div>

                <div class="settings-grid">
                    <!-- 比特浏览器配置 -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <i class="fas fa-browser"></i>
                            <h3>比特浏览器API配置</h3>
                        </div>
                        <div class="settings-card-body">
                            <p>配置比特浏览器API连接信息，支持自定义API地址和Token</p>

                            <!-- API配置表单 -->
                            <div class="settings-form">
                                <div class="form-group">
                                    <label>API服务地址:</label>
                                    <input type="text" id="bitbrowser-api-url" placeholder="http://127.0.0.1:54345"
                                           value="http://127.0.0.1:54345">
                                    <small class="form-help">比特浏览器本地API服务地址</small>
                                </div>

                                <div class="form-group">
                                    <label>API Token:</label>
                                    <div class="input-group">
                                        <input type="password" id="bitbrowser-api-token"
                                               placeholder="请输入API Token"
                                               value="ca28ee5ca6de4d209182a83aa16a2044">
                                        <button type="button" class="btn-toggle-password" onclick="window.app.togglePasswordVisibility('bitbrowser-api-token')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <small class="form-help">在比特浏览器Local API设置中获取</small>
                                </div>

                                <div class="form-group">
                                    <label>连接超时 (秒):</label>
                                    <input type="number" id="bitbrowser-timeout" placeholder="30" value="30" min="5" max="120">
                                    <small class="form-help">API请求超时时间</small>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="settings-actions">
                                <button class="btn btn-success" onclick="window.app.saveBitBrowserSettings()">
                                    <i class="fas fa-save"></i> 保存配置
                                </button>
                                <button class="btn btn-info" onclick="window.app.testBitBrowserConnection()">
                                    <i class="fas fa-plug"></i> 测试连接
                                </button>
                                <button class="btn btn-secondary" onclick="window.app.showBitBrowserHelp()">
                                    <i class="fas fa-question-circle"></i> 配置帮助
                                </button>
                                <button class="btn btn-warning" onclick="window.app.resetBitBrowserConfig()">
                                    <i class="fas fa-undo"></i> 重置默认
                                </button>
                            </div>

                            <!-- 连接状态显示 -->
                            <div id="bitbrowser-connection-status" class="status-display"></div>

                            <!-- 配置说明 -->
                            <div class="config-info">
                                <h5><i class="fas fa-info-circle"></i> 配置说明</h5>
                                <ul>
                                    <li><strong>API地址:</strong> 比特浏览器本地API服务地址，默认为 http://127.0.0.1:54345</li>
                                    <li><strong>API Token:</strong> 在比特浏览器客户端的"Local API"设置中生成和获取</li>
                                    <li><strong>健康检查:</strong> 系统会自动使用 /health 端点检测连接状态</li>
                                    <li><strong>安全提示:</strong> API Token具有完全控制权限，请妥善保管</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 应用设置 -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <i class="fas fa-desktop"></i>
                            <h3>应用设置</h3>
                        </div>
                        <div class="settings-card-body">
                            <p>应用基本配置和行为设置</p>
                            <div class="settings-form">
                                <div class="form-group">
                                    <label>主题模式:</label>
                                    <select id="theme-select" onchange="window.app.changeTheme(this.value)">
                                        <option value="dark">深色主题</option>
                                        <option value="light">浅色主题</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="auto-refresh-check" onchange="window.app.toggleAutoRefresh(this.checked)">
                                        启用自动刷新
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据管理 -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <i class="fas fa-database"></i>
                            <h3>数据管理</h3>
                        </div>
                        <div class="settings-card-body">
                            <p>管理应用数据和缓存</p>
                            <div class="settings-actions">
                                <button class="btn btn-warning" onclick="window.app.clearCache()">
                                    <i class="fas fa-trash"></i> 清除缓存
                                </button>
                                <button class="btn btn-info" onclick="window.app.exportData()">
                                    <i class="fas fa-download"></i> 导出数据
                                </button>
                                <button class="btn btn-secondary" onclick="window.app.importData()">
                                    <i class="fas fa-upload"></i> 导入数据
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 系统信息 -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <i class="fas fa-info-circle"></i>
                            <h3>系统信息</h3>
                        </div>
                        <div class="settings-card-body">
                            <div class="system-info">
                                <div class="info-item">
                                    <span class="info-label">应用版本:</span>
                                    <span class="info-value">1.0.0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">构建时间:</span>
                                    <span class="info-value">${new Date().toLocaleDateString()}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">运行环境:</span>
                                    <span class="info-value">Electron</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">配置状态:</span>
                                    <span class="info-value" id="config-status">检查中...</span>
                                </div>
                            </div>
                            <div class="settings-actions">
                                <button class="btn btn-info" onclick="window.app.showSystemDiagnostic()">
                                    <i class="fas fa-stethoscope"></i> 系统诊断
                                </button>
                                <button class="btn btn-secondary" onclick="window.app.showAbout()">
                                    <i class="fas fa-question-circle"></i> 关于应用
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 高级设置 -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <i class="fas fa-tools"></i>
                            <h3>高级设置</h3>
                        </div>
                        <div class="settings-card-body">
                            <p>开发者和高级用户选项</p>
                            <div class="settings-form">
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="debug-mode-check" onchange="window.app.toggleDebugMode(this.checked)">
                                        启用调试模式
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="dev-tools-check" onchange="window.app.toggleDevTools(this.checked)">
                                        显示开发者工具
                                    </label>
                                </div>
                            </div>
                            <div class="settings-actions">
                                <button class="btn btn-danger" onclick="window.app.resetAllSettings()">
                                    <i class="fas fa-undo"></i> 重置所有设置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 初始化设置页面
        this.initSettingsPage();
    }

    // 初始化设置页面
    initSettingsPage() {
        // 加载当前配置状态
        this.updateConfigStatus();

        // 加载比特浏览器配置
        this.loadBitBrowserSettings();

        // 加载UI设置
        if (window.configManager) {
            const uiConfig = window.configManager.getUIConfig();
            document.getElementById('theme-select').value = uiConfig.theme || 'dark';
            document.getElementById('auto-refresh-check').checked = uiConfig.autoRefresh || true;

            const appConfig = window.configManager.getAppConfig();
            document.getElementById('debug-mode-check').checked = appConfig.debug || false;
        }
    }

    // 加载比特浏览器设置
    loadBitBrowserSettings() {
        if (window.configManager) {
            const config = window.configManager.getBitBrowserConfig();
            document.getElementById('bitbrowser-api-url').value = config.apiUrl || 'http://127.0.0.1:54345';
            document.getElementById('bitbrowser-api-token').value = config.apiToken || '';
            document.getElementById('bitbrowser-timeout').value = (config.timeout || 30000) / 1000;
        }
    }

    // 保存比特浏览器设置
    saveBitBrowserSettings() {
        const apiUrl = document.getElementById('bitbrowser-api-url').value.trim();
        const apiToken = document.getElementById('bitbrowser-api-token').value.trim();
        const timeout = parseInt(document.getElementById('bitbrowser-timeout').value) || 30;

        if (!apiUrl) {
            this.showNotification('请输入API地址', 'error');
            return;
        }

        if (!apiToken) {
            this.showNotification('请输入API Token', 'error');
            return;
        }

        // 验证URL格式
        try {
            new URL(apiUrl);
        } catch (error) {
            this.showNotification('API地址格式不正确', 'error');
            return;
        }

        // 保存配置
        if (window.configManager) {
            const config = {
                apiUrl: apiUrl,
                apiToken: apiToken,
                timeout: timeout * 1000,
                retryCount: 3
            };

            window.configManager.updateBitBrowserConfig(config);

            // 更新API实例配置
            if (window.bitBrowserAPI) {
                window.bitBrowserAPI.config = window.configManager.getBitBrowserConfig();
            }

            this.showNotification('比特浏览器配置已保存', 'success');
            this.updateConfigStatus();
        }
    }

    // 测试比特浏览器连接
    async testBitBrowserConnection() {
        const statusElement = document.getElementById('bitbrowser-connection-status');
        statusElement.innerHTML = '<div class="status-loading"><i class="fas fa-spinner fa-spin"></i> 正在测试连接...</div>';

        try {
            const apiUrl = document.getElementById('bitbrowser-api-url').value.trim();
            const apiToken = document.getElementById('bitbrowser-api-token').value.trim();

            if (!apiUrl || !apiToken) {
                throw new Error('请先填写API地址和Token');
            }

            // 测试健康检查端点
            const response = await fetch(`${apiUrl}/health`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': apiToken
                },
                body: JSON.stringify({})
            });

            const data = await response.json();

            if (data.success) {
                statusElement.innerHTML = `
                    <div class="status-success">
                        <i class="fas fa-check-circle"></i>
                        连接成功！健康检查通过
                        <br><small>响应时间: ${Date.now() - performance.now()}ms</small>
                    </div>
                `;
            } else {
                statusElement.innerHTML = `
                    <div class="status-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        连接失败: ${data.msg || '健康检查失败'}
                    </div>
                `;
            }
        } catch (error) {
            statusElement.innerHTML = `
                <div class="status-error">
                    <i class="fas fa-times-circle"></i>
                    连接失败: ${error.message}
                    <br><small>请检查API地址和Token是否正确</small>
                </div>
            `;
        }
    }

    // 切换密码可见性
    togglePasswordVisibility(inputId) {
        const input = document.getElementById(inputId);
        const button = input.parentElement.querySelector('.btn-toggle-password i');

        if (input.type === 'password') {
            input.type = 'text';
            button.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            button.className = 'fas fa-eye';
        }
    }

    // 显示比特浏览器配置帮助
    showBitBrowserHelp() {
        const helpContent = `
比特浏览器API配置帮助

📋 配置步骤：
1. 启动比特浏览器客户端
2. 进入"设置" → "Local API"
3. 开启"Local API接收控制"
4. 复制生成的API Token
5. 在此处填写API地址和Token

🔧 常见问题：
• API地址通常为: http://127.0.0.1:54345
• 如果端口不同，请查看比特浏览器设置
• Token具有完全控制权限，请妥善保管
• 连接失败时请检查防火墙设置

🛡️ 安全提示：
• 不要在公共场所暴露API Token
• 定期更换API Token
• 仅在可信网络环境下使用

💡 技术支持：
如需技术支持，请联系客服或查看产品文档。
        `;

        alert(helpContent);
    }

    // 重置比特浏览器配置
    resetBitBrowserConfig() {
        if (confirm('确定要重置比特浏览器配置为默认值吗？')) {
            document.getElementById('bitbrowser-api-url').value = 'http://127.0.0.1:54345';
            document.getElementById('bitbrowser-api-token').value = '';
            document.getElementById('bitbrowser-timeout').value = '30';

            // 清除状态显示
            document.getElementById('bitbrowser-connection-status').innerHTML = '';

            this.showNotification('配置已重置为默认值', 'info');
        }
    }

    // ===== 通知系统 =====

    // 显示通知消息
    showNotification(message, type = 'info', duration = 3000) {
        // 创建通知容器（如果不存在）
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        const icon = this.getNotificationIcon(type);
        notification.innerHTML = `
            <div class="notification-content">
                <i class="${icon}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // 添加到容器
        container.appendChild(notification);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, duration);
        }

        // 添加动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);
    }

    // 获取通知图标
    getNotificationIcon(type) {
        const icons = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // 更新配置状态
    updateConfigStatus() {
        const statusElement = document.getElementById('config-status');
        if (statusElement && window.configManager) {
            const isValid = window.configManager.isConfigValid();
            statusElement.innerHTML = isValid ?
                '<span style="color: #28a745;">✅ 配置正常</span>' :
                '<span style="color: #dc3545;">❌ 配置异常</span>';
        }
    }

    // 快速测试比特浏览器连接
    async testBitBrowserQuick() {
        const resultElement = document.getElementById('quick-test-result');
        resultElement.innerHTML = '<div class="status-loading">正在测试连接...</div>';

        try {
            // 直接测试健康检查端点
            const response = await fetch('http://127.0.0.1:54345/health', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': 'ca28ee5ca6de4d209182a83aa16a2044'
                },
                body: JSON.stringify({})
            });

            const data = await response.json();

            if (data.success) {
                resultElement.innerHTML = '<div class="status-success">✅ 连接成功！健康检查通过</div>';
            } else {
                resultElement.innerHTML = `<div class="status-error">❌ 连接失败: ${data.msg || '健康检查失败'}</div>`;
            }
        } catch (error) {
            resultElement.innerHTML = `<div class="status-error">❌ 测试失败: ${error.message}</div>`;
        }
    }

    // 切换主题
    changeTheme(theme) {
        if (window.configManager) {
            window.configManager.updateUIConfig({ theme: theme });
            // 这里可以添加实际的主题切换逻辑
            document.body.className = theme === 'light' ? 'light-theme' : '';
            this.showNotification(`已切换到${theme === 'dark' ? '深色' : '浅色'}主题`, 'success');
        }
    }

    // 切换自动刷新
    toggleAutoRefresh(enabled) {
        if (window.configManager) {
            window.configManager.updateUIConfig({ autoRefresh: enabled });
            this.showNotification(`自动刷新已${enabled ? '启用' : '禁用'}`, 'success');
        }
    }

    // 切换调试模式
    toggleDebugMode(enabled) {
        if (window.configManager) {
            const appConfig = window.configManager.getAppConfig();
            window.configManager.setConfigValue('app', { ...appConfig, debug: enabled });
            this.showNotification(`调试模式已${enabled ? '启用' : '禁用'}`, 'success');
        }
    }

    // 切换开发者工具
    toggleDevTools(enabled) {
        if (enabled) {
            // 在Electron环境中打开开发者工具
            if (window.require) {
                const { remote } = window.require('electron');
                remote.getCurrentWindow().webContents.openDevTools();
            }
        }
        this.showNotification(`开发者工具已${enabled ? '打开' : '关闭'}`, 'info');
    }

    // 清除缓存
    clearCache() {
        if (confirm('确定要清除所有缓存数据吗？')) {
            try {
                localStorage.clear();
                sessionStorage.clear();
                this.showNotification('缓存已清除', 'success');
            } catch (error) {
                this.showNotification(`清除缓存失败: ${error.message}`, 'error');
            }
        }
    }

    // 导出数据
    exportData() {
        try {
            const data = {
                config: window.configManager ? window.configManager.config : {},
                accounts: [], // 这里可以添加账号数据
                timestamp: new Date().toISOString(),
                version: '1.0.0'
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `heimo-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showNotification('数据已导出', 'success');
        } catch (error) {
            this.showNotification(`导出失败: ${error.message}`, 'error');
        }
    }

    // 导入数据
    importData() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target.result);
                        if (data.config && window.configManager) {
                            window.configManager.importConfig(JSON.stringify(data.config));
                            this.initSettingsPage();
                            this.showNotification('数据导入成功', 'success');
                        } else {
                            this.showNotification('无效的数据文件', 'error');
                        }
                    } catch (error) {
                        this.showNotification(`导入失败: ${error.message}`, 'error');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    // 显示系统诊断
    showSystemDiagnostic() {
        const diagnostic = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            online: navigator.onLine,
            cookieEnabled: navigator.cookieEnabled,
            localStorage: typeof Storage !== 'undefined',
            configManager: !!window.configManager,
            bitBrowserAPI: !!window.bitBrowserAPI,
            socketIO: !!window.io
        };

        alert(`系统诊断信息:\n${JSON.stringify(diagnostic, null, 2)}`);
    }

    // 显示关于信息
    showAbout() {
        alert(`黑默科技营销管理平台
版本: 1.0.0
构建时间: ${new Date().toLocaleDateString()}
技术栈: Electron + Node.js + Socket.IO
开发者: 黑默科技团队

© 2024 黑默科技. 保留所有权利.`);
    }

    // 重置所有设置
    resetAllSettings() {
        if (confirm('确定要重置所有设置吗？此操作将清除所有配置和数据，不可撤销！')) {
            try {
                localStorage.clear();
                sessionStorage.clear();
                if (window.configManager) {
                    window.configManager.resetConfig();
                }
                this.showNotification('所有设置已重置', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } catch (error) {
                this.showNotification(`重置失败: ${error.message}`, 'error');
            }
        }
    }

    // 📋 生成活动项目
    generateActivityItems() {
        const activities = [
            { icon: 'user-plus', text: '新增账号 "美食达人小王"', time: '2分钟前', type: 'success' },
            { icon: 'chart-line', text: '账号 "时尚博主Lisa" 粉丝突破10万', time: '15分钟前', type: 'info' },
            { icon: 'heart', text: '互动率提升 +5.2%', time: '1小时前', type: 'success' },
            { icon: 'exclamation-triangle', text: '账号 "科技评测" 需要审核', time: '2小时前', type: 'warning' }
        ];

        return activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon ${activity.type}">
                    <i class="fas fa-${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-text">${activity.text}</div>
                    <div class="activity-time">${activity.time}</div>
                </div>
            </div>
        `).join('');
    }

    // 📊 生成账号管理表格
    generateAccountManagementTable() {
        // 如果有采集到的小红书账号数据，优先显示
        if (this.collectedXiaohongshuAccounts && this.collectedXiaohongshuAccounts.length > 0) {
            return this.generateXiaohongshuAccountsTable();
        }

        // 否则显示原有的模拟数据
        const accounts = [
            {
                id: 1,
                avatar: 'https://picsum.photos/50/50?random=1',
                nickname: '孙颖莎',
                platform: 'xiaohongshu',
                platformId: '**************',
                platformAccount: '学习代言',
                description: '乒乓球运动员，分享运动生活',
                group: 'sports', // 添加分组信息
                groupName: '体育类',
                redBookId: '***********',
                uid: '61995e17000000001000936',
                location: '广东深圳，城市简介',
                deviceId: '*********',
                registerTime: '2021-11-21 04:44:08',
                endTime: '15223',
                note: '请输入备注',
                proxyIp: '代理 ************** 可用',
                todayLoginSuccess: 0,
                todayLoginFail: 0,
                todayPublishSuccess: 0,
                publishInProgress: 0,
                appOnlineStatus: '正常',
                webOnlineStatus: '异常',
                webClientStatus: '异常',
                deviceInfo: {
                    model: 'APPLE',
                    type: 'iPhone',
                    version: '18.1.1',
                    battery: '6.69'
                },
                deviceId2: 'P9FQWMZ5QTNI83R2',
                openTime: '2025-03-10 17:19:42:10',
                updateTime: '2025-04-17 17:19:42:10',
                proxySuccess: 0,
                proxyFail: 0,
                publishSuccess: 0,
                publishFail: 0,
                status: 'online'
            },
            {
                id: 2,
                avatar: 'https://picsum.photos/50/50?random=2',
                nickname: '郑家纯',
                platform: 'douyin',
                platformId: '**************',
                platformAccount: '时尚博主',
                description: '时尚穿搭分享，生活美学',
                group: 'fashion', // 添加分组信息
                groupName: '时尚类',
                douyinId: '***********',
                uid: '61994050000000100096',
                location: '广东深圳，城市简介',
                deviceId: '391274866D',
                registerTime: '2021-11-21 03:14:20',
                endTime: '15222',
                note: '请输入备注',
                proxyIp: '代理 ************** 可用',
                todayLoginSuccess: 0,
                todayLoginFail: 0,
                todayPublishSuccess: 0,
                publishInProgress: 0,
                appOnlineStatus: '正常',
                webOnlineStatus: '正常',
                webClientStatus: '正常',
                deviceInfo: {
                    model: 'APPLE',
                    type: 'iPhone',
                    version: '18.1.1',
                    battery: '6.69'
                },
                deviceId2: 'TMF148R00CEQOQAB',
                openTime: '2025-03-10 17:19:42:19',
                updateTime: '2025-04-17 17:19:42:19',
                proxySuccess: 0,
                proxyFail: 0,
                publishSuccess: 0,
                publishFail: 0,
                status: 'online'
            },
            {
                id: 3,
                avatar: 'https://picsum.photos/50/50?random=3',
                nickname: '美食探索者',
                platform: 'kuaishou',
                platformId: '*************',
                platformAccount: '美食分享达人',
                description: '探索美食文化，分享烹饪技巧',
                group: 'food', // 添加分组信息
                groupName: '美食类',
                kuaishouId: '***********',
                uid: '71995e17000000001000937',
                location: '北京朝阳，美食之都',
                deviceId: '*********',
                registerTime: '2021-12-15 09:30:15',
                endTime: '15224',
                note: '美食类账号',
                proxyIp: '代理 ************* 异常',
                todayLoginSuccess: 2,
                todayLoginFail: 1,
                todayPublishSuccess: 1,
                publishInProgress: 0,
                appOnlineStatus: '异常',
                webOnlineStatus: '正常',
                webClientStatus: '正常',
                deviceInfo: {
                    model: 'HUAWEI',
                    type: 'P50 Pro',
                    version: '12.0.1',
                    battery: '8.45'
                },
                deviceId2: 'HWP50PRO2023001',
                openTime: '2025-03-08 14:25:30:45',
                updateTime: '2025-04-17 14:25:30:45',
                proxySuccess: 156,
                proxyFail: 3,
                publishSuccess: 23,
                publishFail: 1,
                status: 'offline'
            }
        ];

        return `
            <div class="compact-accounts-container">
                ${accounts.map(account => this.generateCompactAccountCard(account)).join('')}
            </div>
        `;
    }

    // 获取账号数据
    getAccountsData() {
        // 尝试从本地存储加载数据
        const savedData = window.groupManager ? window.groupManager.loadAccountsData() : null;

        if (savedData && savedData.length > 0) {
            return savedData;
        }

        // 默认数据 - 按三大平台分组
        const defaultData = [
            // 小红书账号
            {
                id: 1,
                avatar: 'https://picsum.photos/50/50?random=1',
                nickname: '习天下',
                platform: 'xiaohongshu',
                platformId: '**************',
                platformAccount: '美妆博主',
                description: '美妆内容创作者，分享护肤心得',
                group: 'xhs-beauty',
                groupName: '小红书-美妆博主',
                status: 'online',
                note: '优质美妆内容创作者'
            },
            {
                id: 2,
                avatar: 'https://picsum.photos/50/50?random=2',
                nickname: '小红书达人',
                platform: 'xiaohongshu',
                platformId: '**************',
                platformAccount: '生活方式',
                description: '生活方式分享，日常穿搭',
                group: 'xhs-lifestyle',
                groupName: '小红书-生活方式',
                status: 'online',
                note: '生活方式博主，粉丝活跃度高'
            },
            {
                id: 3,
                avatar: 'https://picsum.photos/50/50?random=3',
                nickname: '美食探店王',
                platform: 'xiaohongshu',
                platformId: '*************',
                platformAccount: '美食达人',
                description: '探店美食分享，烹饪技巧',
                group: 'xhs-food',
                groupName: '小红书-美食达人',
                status: 'online',
                note: '美食达人，探店专家'
            },
            // 抖音账号
            {
                id: 4,
                avatar: 'https://picsum.photos/50/50?random=4',
                nickname: '舞蹈小仙女',
                platform: 'douyin',
                platformId: '*************',
                platformAccount: '舞蹈达人',
                description: '原创舞蹈编排，舞蹈教学',
                group: 'dy-dance',
                groupName: '抖音-舞蹈达人',
                status: 'online',
                note: '舞蹈达人，原创编舞'
            },
            {
                id: 5,
                avatar: 'https://picsum.photos/50/50?random=5',
                nickname: '搞笑段子手',
                platform: 'douyin',
                platformId: '*************',
                platformAccount: '搞笑博主',
                description: '原创搞笑内容，段子创作',
                group: 'dy-comedy',
                groupName: '抖音-搞笑博主',
                status: 'offline',
                note: '搞笑内容创作者'
            },
            {
                id: 6,
                avatar: 'https://picsum.photos/50/50?random=6',
                nickname: '科技老师',
                platform: 'douyin',
                platformId: '*************',
                platformAccount: '知识分享',
                description: '科技知识科普，教育内容',
                group: 'dy-education',
                groupName: '抖音-知识分享',
                status: 'online',
                note: '知识分享，科普内容'
            },
            // 微博账号
            {
                id: 7,
                avatar: 'https://picsum.photos/50/50?random=7',
                nickname: '新闻快报',
                platform: 'weibo',
                platformId: '*************',
                platformAccount: '新闻资讯',
                description: '实时新闻资讯，热点解读',
                group: 'wb-news',
                groupName: '微博-新闻资讯',
                status: 'online',
                note: '新闻资讯发布'
            },
            {
                id: 8,
                avatar: 'https://picsum.photos/50/50?random=8',
                nickname: '娱乐八卦王',
                platform: 'weibo',
                platformId: '*************',
                platformAccount: '娱乐八卦',
                description: '娱乐圈资讯，明星动态',
                group: 'wb-entertainment',
                groupName: '微博-娱乐八卦',
                status: 'online',
                note: '娱乐圈资讯'
            },
            {
                id: 9,
                avatar: 'https://picsum.photos/50/50?random=9',
                nickname: '财经分析师',
                platform: 'weibo',
                platformId: '*************',
                platformAccount: '财经分析',
                description: '财经投资分析，市场解读',
                group: 'wb-finance',
                groupName: '微博-财经分析',
                status: 'pending',
                note: '财经投资分析'
            }
        ];

        // 保存默认数据到本地存储
        if (window.groupManager) {
            window.groupManager.saveAccountsData(defaultData);
        }

        return defaultData;
    }

    // 更新账号备注
    updateAccountNote(accountId, note) {
        const accounts = this.getAccountsData();
        const account = accounts.find(acc => acc.id == accountId);
        if (account) {
            account.note = note;
            if (window.groupManager) {
                window.groupManager.saveAccountsData(accounts);
                window.groupManager.showNotification('备注已保存', 'success');
            }
        }
    }

    // 保存账号备注（用于详细表格）
    saveAccountNote(accountId, note) {
        this.updateAccountNote(accountId, note);
    }

    // 📊 生成紧凑型账号卡片
    generateCompactAccountCard(account) {
        const statusColor = account.status === 'online' ? '#4CAF50' : '#f44336';
        const statusText = account.status === 'online' ? '在线' : '离线';

        return `
            <div class="compact-account-card">
                <div class="account-row">
                    <div class="account-basic">
                        <input type="checkbox" value="${account.id}" class="account-checkbox">
                        <img src="${account.avatar}" alt="头像" class="account-avatar">
                        <div class="account-info">
                            <div class="account-name">${account.nickname}</div>
                            <div class="account-platform">${this.getPlatformName(account.platform)}</div>
                            <div class="account-description">${account.description || account.platformAccount || '暂无简介'}</div>
                            <div class="account-group-tag" style="background-color: ${this.getGroupColor(account.group)}20; color: ${this.getGroupColor(account.group)};">
                                <i class="fas fa-folder"></i> ${account.groupName || '默认分组'}
                            </div>
                            <div class="account-status">
                                <span class="status-dot" style="background-color: ${statusColor}"></span>
                                <span>${statusText}</span>
                            </div>
                        </div>
                    </div>

                    <div class="account-details">
                        <div class="detail-group">
                            <h5>账号信息</h5>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <span class="label">代理:</span>
                                    <span class="value ${account.proxyIp.includes('可用') ? 'success' : 'error'}">${account.proxyIp}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">天可用:</span>
                                    <span class="value">${account.endTime}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">今日成功次数:</span>
                                    <span class="value">${account.todayLoginSuccess}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">总成功次数:</span>
                                    <span class="value">${account.proxySuccess}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">今日成功次数:</span>
                                    <span class="value">${account.todayPublishSuccess}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">市场在线状态:</span>
                                    <span class="value">${account.appOnlineStatus}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">注册时间:</span>
                                    <span class="value">${account.registerTime}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">本平台:</span>
                                    <span class="value">${account.endTime}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">备注:</span>
                                    <span class="value">${account.note}</span>
                                </div>
                            </div>
                        </div>

                        <div class="detail-group">
                            <h5>状态信息</h5>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <span class="label">设备类型:</span>
                                    <span class="value">${account.deviceInfo.model}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">型号:</span>
                                    <span class="value">${account.deviceInfo.type}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">系统版本:</span>
                                    <span class="value">${account.deviceInfo.version}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">正在执行:</span>
                                    <span class="value">${account.publishInProgress}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">粉丝数:</span>
                                    <span class="value">${account.proxyFail}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">正在与收藏数:</span>
                                    <span class="value">${account.publishInProgress}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">我的收藏:</span>
                                    <span class="value">${account.proxyFail}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">我的点赞:</span>
                                    <span class="value">${account.publishFail}</span>
                                </div>
                            </div>
                        </div>

                        <div class="detail-group">
                            <h5>数据</h5>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <span class="label">设备信息:</span>
                                    <span class="value">${account.deviceId2}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">开始时间:</span>
                                    <span class="value">${account.openTime}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">更新时间:</span>
                                    <span class="value">${account.updateTime}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">IP:</span>
                                    <span class="value">${account.platformId}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">我的收藏:</span>
                                    <span class="value">${account.proxyFail}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">我的点赞:</span>
                                    <span class="value">${account.publishFail}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="account-actions">
                        <h5>操作</h5>
                        <div class="action-buttons-compact">
                            <button class="action-btn-compact primary" onclick="window.accountManager.startAutoDedup(${account.id})">启动自动去重</button>
                            <button class="action-btn-compact" onclick="window.accountManager.setDailyPublish(${account.id})">设置今日发布</button>
                            <button class="action-btn-compact" onclick="window.accountManager.setCategory(${account.id})">设置分类</button>
                            <button class="action-btn-compact group-btn" onclick="window.app.showGroupSelector(${account.id})">
                                <i class="fas fa-folder"></i> 设置分组
                            </button>
                            <button class="action-btn-compact" onclick="window.accountManager.viewWeb(${account.id})">查看web</button>
                            <button class="action-btn-compact" onclick="window.accountManager.openView(${account.id})">打开查看</button>
                            <button class="action-btn-compact" onclick="window.accountManager.modifyCookie(${account.id})">修改Cookie</button>
                            <button class="action-btn-compact" onclick="window.accountManager.modifyCollectionLink(${account.id})">修改收藏链接</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 📊 生成小红书账号表格
    generateXiaohongshuAccountsTable() {
        if (!this.collectedXiaohongshuAccounts || this.collectedXiaohongshuAccounts.length === 0) {
            return `
                <div class="no-accounts-message">
                    <div class="empty-state">
                        <i class="fas fa-users fa-3x"></i>
                        <h3>暂无小红书账号数据</h3>
                        <p>点击下方按钮开始采集小红书账号信息</p>
                        <button class="btn btn-primary" onclick="window.accountManager.collectXiaohongshuAccounts()">
                            <i class="fas fa-download"></i>
                            开始采集账号
                        </button>
                    </div>
                </div>
            `;
        }

        return this.collectedXiaohongshuAccounts.map(account => `
            <div class="account-card xiaohongshu-account-card" data-account-id="${account.id}">
                <div class="account-header">
                    <div class="account-basic-info">
                        <div class="account-avatar">
                            <img src="${account.avatar}" alt="${account.nickname}" onerror="this.src='https://sns-avatar-qc.xhscdn.com/avatar/default.jpg'">
                            <div class="platform-badge xiaohongshu">
                                <i class="fab fa-instagram"></i>
                            </div>
                        </div>
                        <div class="account-info">
                            <div class="account-name-row">
                                <h4 class="account-nickname">${account.nickname}</h4>
                                <div class="account-status-badges">
                                    <span class="status-badge ${account.loginStatus === '已登录' ? 'success' : 'warning'}">
                                        ${account.loginStatus}
                                    </span>
                                    <span class="status-badge ${account.status === '正常' ? 'success' : 'error'}">
                                        ${account.status}
                                    </span>
                                    ${account.dataSource ? `<span class="data-source-badge">${this.getDataSourceLabel(account.dataSource)}</span>` : ''}
                                </div>
                            </div>
                            <div class="account-details">
                                <span class="account-id">ID: ${account.xiaohongshuId}</span>
                                <span class="collect-time">采集时间: ${new Date(account.collectTime).toLocaleString()}</span>
                            </div>
                            <div class="account-bio">${account.bio}</div>
                        </div>
                    </div>
                </div>

                <div class="account-content">
                    <div class="account-stats-grid">
                        <div class="stat-item">
                            <div class="stat-icon follow">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-value">${this.formatNumber(account.followCount)}</div>
                                <div class="stat-label">关注数</div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon fans">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-value">${this.formatNumber(account.fansCount)}</div>
                                <div class="stat-label">粉丝数</div>
                                ${account.statistics?.weeklyGrowth?.fans ? `<div class="stat-growth ${account.statistics.weeklyGrowth.fans >= 0 ? 'positive' : 'negative'}">
                                    ${account.statistics.weeklyGrowth.fans >= 0 ? '+' : ''}${account.statistics.weeklyGrowth.fans}
                                </div>` : ''}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon likes">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-value">${this.formatNumber(account.likesAndCollects)}</div>
                                <div class="stat-label">获赞收藏</div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon views">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-value">${this.formatNumber(account.statistics?.recentViews || 0)}</div>
                                <div class="stat-label">近期观看</div>
                                ${account.statistics?.weeklyGrowth?.views ? `<div class="stat-growth ${account.statistics.weeklyGrowth.views >= 0 ? 'positive' : 'negative'}">
                                    ${account.statistics.weeklyGrowth.views >= 0 ? '+' : ''}${account.statistics.weeklyGrowth.views}%
                                </div>` : ''}
                            </div>
                        </div>
                    </div>

                    <div class="account-actions">
                        <div class="action-buttons-grid">
                            <button class="action-btn primary" onclick="window.accountManager.refreshXiaohongshuAccount('${account.id}')">
                                <i class="fas fa-sync-alt"></i>
                                刷新数据
                            </button>
                            <button class="action-btn" onclick="window.accountManager.viewXiaohongshuProfile('${account.id}')">
                                <i class="fas fa-user"></i>
                                查看主页
                            </button>
                            <button class="action-btn" onclick="window.accountManager.openXiaohongshuCreator('${account.id}')">
                                <i class="fas fa-edit"></i>
                                创作中心
                            </button>
                            <button class="action-btn" onclick="window.accountManager.editXiaohongshuNote('${account.id}')">
                                <i class="fas fa-sticky-note"></i>
                                编辑备注
                            </button>
                            <button class="action-btn group-btn" onclick="window.app.showGroupSelector('${account.id}')">
                                <i class="fas fa-folder"></i>
                                设置分组
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // 获取数据源标签
    getDataSourceLabel(dataSource) {
        const labels = {
            'browser_realtime': '浏览器实时',
            'safe_data_file': '安全数据文件',
            'pyppeteer_data_file': 'Pyppeteer数据',
            'mock_data': '模拟数据'
        };
        return labels[dataSource] || '未知来源';
    }

    // 格式化数字显示
    formatNumber(num) {
        if (!num) return '0';
        if (num >= 10000) {
            return (num / 10000).toFixed(1) + '万';
        }
        return num.toString();
    }

    // 📊 生成详细账号卡片
    generateDetailedAccountCard(account) {
        const statusColor = account.status === 'online' ? '#4CAF50' : '#f44336';
        const statusText = account.status === 'online' ? '在线' : '离线';

        return `
            <div class="detailed-account-card">
                <div class="account-header">
                    <input type="checkbox" value="${account.id}" class="account-checkbox">
                    <img src="${account.avatar}" alt="头像" class="account-avatar">
                    <div class="account-basic-info">
                        <h3>${account.nickname}</h3>
                        <p class="platform-info">${this.getPlatformName(account.platform)}</p>
                        <p class="account-bio">${account.description || account.platformAccount || '暂无简介'}</p>
                        <div class="status-indicator">
                            <span class="status-dot" style="background-color: ${statusColor}"></span>
                            <span>${statusText}</span>
                        </div>
                    </div>
                </div>

                <div class="account-details-grid">
                    <div class="detail-section">
                        <h4>账号信息</h4>
                        <div class="detail-row">
                            <span class="label">代理:</span>
                            <span class="value ${account.proxyIp.includes('可用') ? 'status-success' : 'status-error'}">${account.proxyIp}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">天可用:</span>
                            <span class="value">${account.endTime}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">今日成功次数:</span>
                            <span class="value">${account.todayLoginSuccess}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">总成功次数:</span>
                            <span class="value">${account.proxySuccess}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">今日成功次数:</span>
                            <span class="value">${account.todayPublishSuccess}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">市场在线状态:</span>
                            <span class="value">${account.appOnlineStatus === '正常' ? '正常' : '异常'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">注册时间:</span>
                            <span class="value">${account.registerTime}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">本平台:</span>
                            <span class="value">${account.endTime}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">备注:</span>
                            <span class="value">${account.note}</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4>状态信息</h4>
                        <div class="detail-row">
                            <span class="label">设备类型:</span>
                            <span class="value">${account.deviceInfo.model}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">型号:</span>
                            <span class="value">${account.deviceInfo.type}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">系统版本:</span>
                            <span class="value">${account.deviceInfo.version}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">正在执行:</span>
                            <span class="value">${account.publishInProgress}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">粉丝数:</span>
                            <span class="value">${account.proxyFail}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">正在与收藏数:</span>
                            <span class="value">${account.publishInProgress}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">我的收藏:</span>
                            <span class="value">${account.proxyFail}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">我的点赞:</span>
                            <span class="value">${account.publishFail}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">我的点赞:</span>
                            <span class="value">${account.publishFail}</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4>数据</h4>
                        <div class="detail-row">
                            <span class="label">设备信息:</span>
                            <span class="value">${account.deviceId2}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">开始时间:</span>
                            <span class="value">${account.openTime}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">更新时间:</span>
                            <span class="value">${account.updateTime}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">IP:</span>
                            <span class="value">${account.platformId}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">我的收藏:</span>
                            <span class="value">${account.proxyFail}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">我的点赞:</span>
                            <span class="value">${account.publishFail}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">我的点赞:</span>
                            <span class="value">${account.publishFail}</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4>操作</h4>
                        <div class="action-buttons">
                            <button class="action-btn primary" onclick="window.accountManager.startAutoDedup(${account.id})">启动自动去重</button>
                            <button class="action-btn" onclick="window.accountManager.setDailyPublish(${account.id})">设置今日发布</button>
                            <button class="action-btn" onclick="window.accountManager.setCategory(${account.id})">设置分类</button>
                            <button class="action-btn" onclick="window.accountManager.viewWeb(${account.id})">查看web</button>
                            <button class="action-btn" onclick="window.accountManager.openView(${account.id})">打开查看</button>
                            <button class="action-btn" onclick="window.accountManager.modifyCookie(${account.id})">修改Cookie</button>
                            <button class="action-btn" onclick="window.accountManager.modifyCollectionLink(${account.id})">修改收藏链接</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 获取平台名称
    getPlatformName(platform) {
        const platformNames = {
            'xiaohongshu': '小红书',
            'douyin': '抖音',
            'kuaishou': '快手',
            'weibo': '微博'
        };
        return platformNames[platform] || platform;
    }

    // 🎨 获取分组颜色
    getGroupColor(groupId) {
        const groupColors = {
            'default': '#6c757d',
            'sports': '#28a745',
            'fashion': '#e83e8c',
            'food': '#fd7e14',
            'travel': '#20c997',
            'tech': '#6f42c1'
        };
        return groupColors[groupId] || '#6c757d';
    }

    // 生成单个账号行
    generateAccountRow(account) {
        const platformIcons = {
            xiaohongshu: '<i class="fab fa-instagram" style="color: #ff2442;"></i>',
            douyin: '<i class="fab fa-tiktok" style="color: #000000;"></i>',
            kuaishou: '<i class="fas fa-video" style="color: #ff6600;"></i>'
        };

        const statusBadges = {
            online: '<span class="status-badge online">在线</span>',
            offline: '<span class="status-badge offline">离线</span>',
            error: '<span class="status-badge error">异常</span>'
        };

        const appStatusBadges = {
            normal: '<span class="app-status normal">正常</span>',
            error: '<span class="app-status error">异常</span>'
        };

        const webStatusBadges = {
            normal: '<span class="web-status normal">正常</span>',
            error: '<span class="web-status error">异常</span>'
        };

        return `
            <tr data-account-id="${account.id}">
                <td><input type="checkbox" class="account-checkbox" value="${account.id}"></td>
                <td>
                    <div class="account-avatar">
                        <img src="${account.avatar}" alt="${account.nickname}">
                        ${platformIcons[account.platform]}
                    </div>
                </td>
                <td>
                    <div class="account-nickname">
                        <strong>${account.nickname}</strong>
                    </div>
                </td>
                <td>
                    <div class="platform-id">
                        ${account.platformId}
                    </div>
                </td>
                <td>
                    <div class="account-description">
                        ${account.description}
                    </div>
                </td>
                <td>
                    <div class="account-note">
                        ${account.note}
                    </div>
                </td>
                <td>
                    ${statusBadges[account.status]}
                </td>
                <td>
                    <div class="proxy-stats">
                        <span class="success">${account.proxySuccess}</span> /
                        <span class="fail">${account.proxyFail}</span>
                    </div>
                </td>
                <td>
                    <div class="publish-stats">
                        <span class="success">${account.publishSuccess}</span> /
                        <span class="fail">${account.publishFail}</span>
                    </div>
                </td>
                <td>
                    ${appStatusBadges[account.appStatus]}
                </td>
                <td>
                    ${webStatusBadges[account.webStatus]}
                </td>
                <td>
                    <div class="last-active">
                        ${account.lastActive}
                    </div>
                </td>
                <td>
                    <div class="account-actions">
                        <button class="btn-icon" onclick="window.accountManager.editProxy(${account.id})" title="修改代理">
                            <i class="fas fa-globe"></i>
                        </button>
                        <button class="btn-icon" onclick="window.accountManager.refreshStatus(${account.id})" title="刷新状态">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn-icon" onclick="window.accountManager.extractCookie(${account.id})" title="提取Cookie">
                            <i class="fas fa-cookie-bite"></i>
                        </button>
                        <button class="btn-icon" onclick="window.accountManager.editAccount(${account.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon danger" onclick="window.accountManager.deleteAccount(${account.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    // 📄 加载默认内容
    loadDefaultContent(container, page) {
        const pageInfo = {
            'content': { icon: 'search', title: '内容搜索', desc: '智能内容搜索和分析工具' },
            'analytics': { icon: 'chart-line', title: '竞品分析', desc: '深度竞品数据分析平台' },
            'interaction': { icon: 'heart', title: '互动管理', desc: '社交媒体互动管理中心' },
            'comments': { icon: 'comments', title: '评论管理', desc: '评论监控和管理系统' },
            'advanced': { icon: 'magic', title: '高级功能', desc: '专业营销工具集合' },
            'data-collection': { icon: 'database', title: '数据采集', desc: '自动化数据采集系统' },
            'cloud-phone': { icon: 'mobile-alt', title: '云手机管理', desc: '云端设备管理平台' },
            'automation': { icon: 'robot', title: '自动化工具', desc: '智能自动化营销工具' },
            'settings': { icon: 'cog', title: '系统设置', desc: '系统配置和个性化设置' },
            'users': { icon: 'user-shield', title: '用户管理', desc: '用户权限和角色管理' },
            'logs': { icon: 'clipboard-list', title: '操作日志', desc: '系统操作记录和审计' }
        };

        const info = pageInfo[page] || { icon: 'cog', title: '功能页面', desc: '功能开发中...' };

        container.innerHTML = `
            <div class="feature-placeholder">
                <div class="feature-icon">
                    <i class="fas fa-${info.icon}"></i>
                </div>
                <h2>${info.title}</h2>
                <p>${info.desc}</p>
                <div class="feature-actions">
                    <button class="btn btn-primary">开始使用</button>
                    <button class="btn btn-secondary">了解更多</button>
                </div>
            </div>
        `;
    }

    // 🥧 生成扇形图
    generatePieChart() {
        const data = [
            { platform: '小红书', value: 45, color: '#ff2442' },
            { platform: '抖音', value: 35, color: '#000000' },
            { platform: '快手', value: 20, color: '#ff6600' }
        ];

        const svg = document.getElementById('platformPieChart');
        if (!svg) return;

        const centerX = 100;
        const centerY = 100;
        const radius = 70;
        let currentAngle = -90; // 从顶部开始

        // 清空SVG内容
        svg.innerHTML = '';

        data.forEach((item, index) => {
            const angle = (item.value / 100) * 360;
            const startAngle = currentAngle;
            const endAngle = currentAngle + angle;

            // 转换为弧度
            const startAngleRad = (startAngle * Math.PI) / 180;
            const endAngleRad = (endAngle * Math.PI) / 180;

            // 计算起点和终点
            const x1 = centerX + radius * Math.cos(startAngleRad);
            const y1 = centerY + radius * Math.sin(startAngleRad);
            const x2 = centerX + radius * Math.cos(endAngleRad);
            const y2 = centerY + radius * Math.sin(endAngleRad);

            // 判断是否为大弧
            const largeArc = angle > 180 ? 1 : 0;

            // 创建路径
            const pathData = [
                `M ${centerX} ${centerY}`,
                `L ${x1} ${y1}`,
                `A ${radius} ${radius} 0 ${largeArc} 1 ${x2} ${y2}`,
                'Z'
            ].join(' ');

            // 创建path元素
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('d', pathData);
            path.setAttribute('fill', item.color);
            path.setAttribute('class', 'pie-slice');
            path.setAttribute('data-platform', item.platform);
            path.setAttribute('data-value', `${item.value}%`);

            svg.appendChild(path);

            currentAngle += angle;
        });
    }

    // 🥧 初始化扇形图交互
    initializePieChart() {
        // 先生成扇形图
        this.generatePieChart();

        // 然后添加交互
        const pieSlices = document.querySelectorAll('.pie-slice');

        pieSlices.forEach(slice => {
            slice.addEventListener('mouseenter', function() {
                const platform = this.dataset.platform;
                const value = this.dataset.value;

                // 创建提示框
                const tooltip = document.createElement('div');
                tooltip.className = 'pie-tooltip';
                tooltip.innerHTML = `
                    <div class="tooltip-platform">${platform}</div>
                    <div class="tooltip-value">${value}</div>
                `;
                document.body.appendChild(tooltip);

                // 定位提示框
                const rect = this.getBoundingClientRect();
                tooltip.style.left = rect.left + rect.width / 2 + 'px';
                tooltip.style.top = rect.top - 10 + 'px';
            });

            slice.addEventListener('mouseleave', function() {
                const tooltip = document.querySelector('.pie-tooltip');
                if (tooltip) {
                    tooltip.remove();
                }
            });
        });
    }

    // 初始化平台标签切换
    initializePlatformTabs() {
        const tabs = document.querySelectorAll('.platform-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                // 移除所有活跃状态
                tabs.forEach(t => t.classList.remove('active'));
                // 添加当前活跃状态
                e.currentTarget.classList.add('active');

                const platform = e.currentTarget.dataset.platform;
                this.filterAccountsByPlatform(platform);
            });
        });
    }

    // 初始化分组过滤器
    initializeGroupFilters() {
        const groupCards = document.querySelectorAll('.group-stat-card');
        groupCards.forEach(card => {
            card.addEventListener('click', (e) => {
                // 移除所有活跃状态
                groupCards.forEach(c => c.classList.remove('active'));
                // 添加当前活跃状态
                e.currentTarget.classList.add('active');

                const group = e.currentTarget.dataset.group;
                this.filterAccountsByGroup(group);
            });
        });
    }

    // 按平台过滤账号
    filterAccountsByPlatform(platform) {
        // 如果选择小红书管理，显示小红书管理界面
        if (platform === 'xiaohongshu-manage') {
            this.loadXiaohongshuManagement();
            return;
        }

        const cards = document.querySelectorAll('.compact-account-card, .detailed-account-card');
        cards.forEach(card => {
            if (platform === 'all') {
                card.style.display = '';
            } else {
                const accountPlatform = this.getAccountPlatformFromCard(card);
                card.style.display = accountPlatform === platform ? '' : 'none';
            }
        });
    }

    // 按分组过滤账号
    filterAccountsByGroup(group) {
        const cards = document.querySelectorAll('.compact-account-card, .detailed-account-card');
        cards.forEach(card => {
            if (group === 'all') {
                card.style.display = '';
            } else {
                const accountGroup = this.getAccountGroupFromCard(card);
                card.style.display = accountGroup === group ? '' : 'none';
            }
        });
    }

    // 从卡片获取账号平台
    getAccountPlatformFromCard(card) {
        // 支持两种卡片类型
        const platformElement = card.querySelector('.platform-info, .account-platform');
        if (!platformElement) return 'unknown';

        const platformText = platformElement.textContent;
        if (platformText.includes('小红书')) return 'xiaohongshu';
        if (platformText.includes('抖音')) return 'douyin';
        if (platformText.includes('快手')) return 'kuaishou';
        return 'unknown';
    }

    // 从卡片获取账号分组
    getAccountGroupFromCard(card) {
        // 支持两种卡片类型
        const statusElement = card.querySelector('.status-indicator span:last-child, .account-status span:last-child');
        if (!statusElement) return 'all';

        const statusText = statusElement.textContent;
        if (statusText.includes('在线')) return 'login';
        if (statusText.includes('离线')) return 'device-offline';
        return 'all';
    }

    // 获取账号平台（兼容旧版表格）
    getAccountPlatform(row) {
        const platformIcon = row.querySelector('.account-avatar i');
        if (platformIcon && platformIcon.classList.contains('fa-instagram')) return 'xiaohongshu';
        if (platformIcon && platformIcon.classList.contains('fa-tiktok')) return 'douyin';
        if (platformIcon && platformIcon.classList.contains('fa-video')) return 'kuaishou';
        return 'unknown';
    }

    // 获取账号分组（兼容旧版表格）
    getAccountGroup(row) {
        const statusBadge = row.querySelector('.status-badge');
        if (statusBadge && statusBadge.classList.contains('online')) return 'login';
        if (statusBadge && statusBadge.classList.contains('offline')) return 'device-offline';
        if (statusBadge && statusBadge.classList.contains('error')) return 'network-error';
        return 'all';
    }

}

// 📱 账号管理器类
class AccountManager {
    constructor() {
        this.accounts = [];
        this.currentPlatform = 'all';
        this.currentGroup = 'all';
        this.collectedXiaohongshuAccounts = [];
        this.autoRefreshEnabled = false;
        this.autoRefreshInterval = null;
        this.autoRefreshIntervalTime = 5 * 60 * 1000; // 5分钟
    }

    // 刷新所有账号状态
    refreshAllStatus() {
        console.log('刷新所有账号状态...');
        // 模拟API调用
        setTimeout(() => {
            this.showNotification('所有账号状态已刷新', 'success');
        }, 1000);
    }

    // 提取所有Cookie
    extractCookies() {
        console.log('提取所有账号Cookie...');
        // 模拟API调用
        setTimeout(() => {
            this.showNotification('Cookie提取完成', 'success');
        }, 1500);
    }

    // 添加账号
    addAccount() {
        this.showAccountModal();
    }

    // 编辑代理
    editProxy(accountId) {
        console.log('编辑账号代理:', accountId);
        this.showProxyModal(accountId);
    }

    // 刷新单个账号状态
    refreshStatus(accountId) {
        console.log('刷新账号状态:', accountId);
        // 模拟API调用
        setTimeout(() => {
            this.showNotification(`账号 ${accountId} 状态已刷新`, 'success');
        }, 500);
    }

    // 提取单个账号Cookie
    extractCookie(accountId) {
        console.log('提取账号Cookie:', accountId);
        // 模拟API调用
        setTimeout(() => {
            this.showNotification(`账号 ${accountId} Cookie已提取`, 'success');
        }, 800);
    }

    // 编辑账号
    editAccount(accountId) {
        console.log('编辑账号:', accountId);
        this.showAccountModal(accountId);
    }

    // 删除账号
    deleteAccount(accountId) {
        if (confirm('确定要删除这个账号吗？')) {
            console.log('删除账号:', accountId);
            // 模拟API调用
            setTimeout(() => {
                this.showNotification(`账号 ${accountId} 已删除`, 'success');
            }, 500);
        }
    }

    // 显示账号模态框
    showAccountModal(accountId = null) {
        const isEdit = accountId !== null;
        const modalHtml = `
            <div class="modal-overlay" id="accountModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${isEdit ? '编辑账号' : '添加账号'}</h3>
                        <button class="modal-close" onclick="this.closeModal('accountModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="accountForm">
                            <div class="form-group">
                                <label>平台</label>
                                <select name="platform" required>
                                    <option value="">选择平台</option>
                                    <option value="xiaohongshu">小红书</option>
                                    <option value="douyin">抖音</option>
                                    <option value="kuaishou">快手</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>昵称</label>
                                <input type="text" name="nickname" required>
                            </div>
                            <div class="form-group">
                                <label>平台ID</label>
                                <input type="text" name="platformId" required>
                            </div>
                            <div class="form-group">
                                <label>简介</label>
                                <textarea name="description" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label>备注</label>
                                <input type="text" name="note">
                            </div>
                            <div class="form-group">
                                <label>代理地址</label>
                                <input type="text" name="proxy" placeholder="http://proxy:port">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-ghost" onclick="this.closeModal('accountModal')">取消</button>
                        <button class="btn btn-primary" onclick="this.saveAccount(${accountId})">${isEdit ? '保存' : '添加'}</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // 显示代理模态框
    showProxyModal(accountId) {
        const modalHtml = `
            <div class="modal-overlay" id="proxyModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>修改代理设置</h3>
                        <button class="modal-close" onclick="this.closeModal('proxyModal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="proxyForm">
                            <div class="form-group">
                                <label>代理类型</label>
                                <select name="proxyType" required>
                                    <option value="http">HTTP</option>
                                    <option value="https">HTTPS</option>
                                    <option value="socks5">SOCKS5</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>代理地址</label>
                                <input type="text" name="proxyHost" placeholder="127.0.0.1" required>
                            </div>
                            <div class="form-group">
                                <label>端口</label>
                                <input type="number" name="proxyPort" placeholder="8080" required>
                            </div>
                            <div class="form-group">
                                <label>用户名</label>
                                <input type="text" name="proxyUser">
                            </div>
                            <div class="form-group">
                                <label>密码</label>
                                <input type="password" name="proxyPass">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-ghost" onclick="this.closeModal('proxyModal')">取消</button>
                        <button class="btn btn-primary" onclick="this.saveProxy(${accountId})">保存并测试</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // 关闭模态框
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.remove();
        }
    }

    // 保存账号
    saveAccount(accountId) {
        const form = document.getElementById('accountForm');
        const formData = new FormData(form);
        const accountData = Object.fromEntries(formData);

        console.log('保存账号数据:', accountData);

        // 模拟API调用
        setTimeout(() => {
            this.closeModal('accountModal');
            this.showNotification(accountId ? '账号已更新' : '账号已添加', 'success');
        }, 1000);
    }

    // 保存代理设置
    saveProxy(accountId) {
        const form = document.getElementById('proxyForm');
        const formData = new FormData(form);
        const proxyData = Object.fromEntries(formData);

        console.log('保存代理设置:', proxyData);

        // 模拟API调用和比特浏览器API调用
        setTimeout(() => {
            this.closeModal('proxyModal');
            this.showNotification('代理设置已更新并同步到比特浏览器', 'success');
        }, 1500);
    }

    // 启动自动去重
    startAutoDedup(accountId) {
        console.log(`启动账号 ${accountId} 的自动去重功能...`);
        this.showNotification('自动去重功能已启动', 'success');
    }

    // 设置今日发布
    setDailyPublish(accountId) {
        console.log(`设置账号 ${accountId} 的今日发布计划...`);
        this.showNotification('今日发布计划已设置', 'success');
    }

    // 设置分类
    setCategory(accountId) {
        console.log(`设置账号 ${accountId} 的分类...`);
        this.showNotification('账号分类已设置', 'success');
    }

    // 查看web
    viewWeb(accountId) {
        console.log(`打开账号 ${accountId} 的web界面...`);
        // 这里可以打开新窗口或标签页
        window.open(`/web-view/${accountId}`, '_blank');
    }

    // 打开查看
    openView(accountId) {
        console.log(`打开账号 ${accountId} 的详细视图...`);
        this.showNotification('正在打开账号详细视图', 'info');
    }

    // 修改Cookie
    modifyCookie(accountId) {
        console.log(`修改账号 ${accountId} 的Cookie...`);
        // 这里可以打开Cookie编辑模态框
        this.showNotification('Cookie修改功能已打开', 'info');
    }

    // 修改收藏链接
    modifyCollectionLink(accountId) {
        console.log(`修改账号 ${accountId} 的收藏链接...`);
        this.showNotification('收藏链接修改功能已打开', 'info');
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;

        document.body.appendChild(notification);

        // 自动移除通知
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // 生成详细账号信息表格（参考例图样式）
    generateDetailedAccountTable() {
        const accounts = this.getAccountsData();

        return `
            <div class="detailed-accounts-table">
                <div class="table-header">
                    <div class="header-cell account-info-header">账号信息</div>
                    <div class="header-cell status-header">状态</div>
                    <div class="header-cell device-header">设备信息</div>
                    <div class="header-cell data-header">数据</div>
                    <div class="header-cell action-header">操作</div>
                </div>
                ${accounts.map(account => this.generateDetailedAccountRow(account)).join('')}
            </div>
        `;
    }

    // 生成详细账号行
    generateDetailedAccountRow(account) {
        const statusColor = account.status === 'online' ? '#4CAF50' : '#f44336';
        const statusText = account.status === 'online' ? '在线' : '离线';

        return `
            <div class="detailed-account-row">
                <div class="account-info-cell">
                    <div class="account-main-info">
                        <input type="checkbox" class="account-checkbox">
                        <img src="${account.avatar}" alt="头像" class="account-avatar-detailed">
                        <div class="account-details-text">
                            <div class="account-name-detailed">${account.nickname}</div>
                            <div class="account-platform-detailed">${this.getPlatformName(account.platform)}</div>
                            <div class="account-id">红书号: ${account.redBookId || account.platformAccount}</div>
                            <div class="account-uid">uid: ${account.uid || account.platformId}</div>
                            <div class="account-intro">简介: ${account.description || '暂无简介'}</div>
                            <div class="account-device-id">中控码: ${account.deviceId}</div>
                            <div class="account-register">注册时间: ${account.registerTime || '未知'}</div>
                            <div class="account-platform-id">平台id: ${account.endTime || '未设置'}</div>
                        </div>
                    </div>
                    <div class="account-note">
                        <div class="note-label">备注</div>
                        <input type="text" class="note-input" placeholder="请输入备注" value="${account.note || ''}"
                               onkeypress="if(event.key==='Enter') window.app.saveAccountNote(${account.id}, this.value)">
                        <button class="save-note-btn" onclick="window.app.saveAccountNote(${account.id}, this.previousElementSibling.value)">保存</button>
                    </div>
                </div>

                <div class="status-cell">
                    <div class="proxy-status">
                        <div class="status-label">代理 ${account.platformId} 可用</div>
                        <div class="login-status">天河用</div>
                        <div class="login-stats">今日成功次数: ${account.todayLoginSuccess || 0} <span class="success-text">次成功</span></div>
                        <div class="login-fail">今日失败次数: ${account.todayLoginFail || 0}</div>
                        <div class="publish-stats">今日发布次数: ${account.todayPublishSuccess || 0}</div>
                        <div class="publish-progress">发布中: ${account.publishInProgress || 0}</div>
                        <div class="app-status">app在线状态: <span class="status-normal">${account.appOnlineStatus || '正常'}</span> <span class="status-refresh">刷新</span> <span class="status-acid">acid</span></div>
                        <div class="web-status">web在线: <span class="status-error">${account.webOnlineStatus || '异常'}</span> <span class="status-click">点击</span></div>
                        <div class="web-client">web客户端: <span class="status-error">${account.webClientStatus || '异常'}</span></div>
                    </div>
                </div>

                <div class="device-cell">
                    <div class="device-type">设备类型 ${account.deviceInfo?.model || 'APPLE'}</div>
                    <div class="device-model">型号 ${account.deviceInfo?.type || 'iPhone'}</div>
                    <div class="device-version">系统版本: ${account.deviceInfo?.version || '18.1.1'}</div>
                    <div class="device-battery">电量剩余: ${account.deviceInfo?.battery || '8.69'}</div>
                    <div class="device-id-info">DID 0DFD251-A242-42FF-B780-372EBF6A5</div>
                    <div class="device-guid">设备ID: ${account.deviceId2 || 'P9FQWMZ5QTNI83R2'}</div>
                    <div class="device-open">开机时间${account.openTime || '2025-03-18T19:42:10'}</div>
                    <div class="device-update">更新时间 ${account.updateTime || '2025-04-17T19:42:10'}</div>
                </div>

                <div class="data-cell">
                    <div class="data-item">成功导号: 等级0</div>
                    <div class="data-item">移动次数: 0</div>
                    <div class="data-item">粉丝数: 0</div>
                    <div class="data-item">登录与收藏数: 0</div>
                    <div class="data-item">IP</div>
                    <div class="data-item">推荐流量: 0</div>
                    <div class="data-item">搜索流量: 0</div>
                    <div class="data-item">移动流量: 0</div>
                </div>

                <div class="action-cell">
                    <button class="action-btn primary">设置和管理</button>
                    <button class="action-btn">设置关注</button>
                    <button class="action-btn">设置分组</button>
                    <button class="action-btn">设置人工智能</button>
                    <button class="action-btn">登录web</button>
                    <button class="action-btn">打开登录</button>
                    <button class="action-btn">修改Cookie</button>
                    <button class="action-btn">修改无法检测</button>
                </div>
            </div>
        `;
    }
}

// ===== 分组管理器类 =====
// 这个类专门负责管理账号分组功能，是应用的核心功能模块
// 支持三大平台（小红书、抖音、微博）的独立分组管理
class GroupManager {
    constructor() {
        // ===== 初始化分组数据 =====
        // 首先尝试从本地存储加载已保存的分组数据
        const loaded = this.loadGroupsData();

        if (!loaded) {
            // 如果没有保存的数据，初始化默认的三大平台分组系统
            // 每个平台都有独立的分组，互不干扰
            this.platformGroups = {
                // ===== 小红书平台分组 =====
                // 小红书主要以生活方式、美妆、美食等内容为主
                xiaohongshu: [
                    { id: 'xhs-default', name: '小红书-默认分组', color: '#ff2442', count: 0, platform: 'xiaohongshu' },
                    { id: 'xhs-beauty', name: '小红书-美妆博主', color: '#ff69b4', count: 0, platform: 'xiaohongshu' },
                    { id: 'xhs-lifestyle', name: '小红书-生活方式', color: '#ff8c00', count: 0, platform: 'xiaohongshu' },
                    { id: 'xhs-food', name: '小红书-美食达人', color: '#32cd32', count: 0, platform: 'xiaohongshu' },
                    { id: 'xhs-travel', name: '小红书-旅行博主', color: '#1e90ff', count: 0, platform: 'xiaohongshu' }
                ],
                // ===== 抖音平台分组 =====
                // 抖音主要以短视频、娱乐、教育内容为主
                douyin: [
                    { id: 'dy-default', name: '抖音-默认分组', color: '#000000', count: 0, platform: 'douyin' },
                    { id: 'dy-dance', name: '抖音-舞蹈达人', color: '#ff1493', count: 0, platform: 'douyin' },
                    { id: 'dy-comedy', name: '抖音-搞笑博主', color: '#ffd700', count: 0, platform: 'douyin' },
                    { id: 'dy-education', name: '抖音-知识分享', color: '#4169e1', count: 0, platform: 'douyin' },
                    { id: 'dy-tech', name: '抖音-科技评测', color: '#00ced1', count: 0, platform: 'douyin' }
                ],
                // ===== 微博平台分组 =====
                // 微博主要以新闻、娱乐、财经等内容为主
                weibo: [
                    { id: 'wb-default', name: '微博-默认分组', color: '#e6162d', count: 0, platform: 'weibo' },
                    { id: 'wb-news', name: '微博-新闻资讯', color: '#dc143c', count: 0, platform: 'weibo' },
                    { id: 'wb-entertainment', name: '微博-娱乐八卦', color: '#ff6347', count: 0, platform: 'weibo' },
                    { id: 'wb-sports', name: '微博-体育赛事', color: '#228b22', count: 0, platform: 'weibo' },
                    { id: 'wb-finance', name: '微博-财经分析', color: '#4682b4', count: 0, platform: 'weibo' }
                ]
            };

            // 为了兼容性，保留原有的groups属性
            this.groups = [
                ...this.platformGroups.xiaohongshu,
                ...this.platformGroups.douyin,
                ...this.platformGroups.weibo
            ];

            // 保存默认分组数据
            this.saveGroupsData();
        }

        this.init();
    }

    init() {
        this.updateGroupCounts();
    }

    // 获取所有分组
    getAllGroups() {
        return this.groups;
    }

    // 获取分组信息
    getGroup(groupId) {
        return this.groups.find(group => group.id === groupId);
    }

    // 创建新分组
    createGroup(name, color = '#6c757d') {
        const id = 'group_' + Date.now();
        const newGroup = {
            id,
            name,
            color,
            count: 0
        };
        this.groups.push(newGroup);
        this.updateGroupCounts();
        this.renderGroupList();
        return newGroup;
    }

    // 编辑分组
    editGroup(groupId, name, color) {
        const group = this.getGroup(groupId);
        if (group) {
            group.name = name;
            group.color = color;
            this.renderGroupList();
            return true;
        }
        return false;
    }

    // 删除分组
    deleteGroup(groupId) {
        if (groupId === 'default') {
            alert('默认分组不能删除');
            return false;
        }

        const groupIndex = this.groups.findIndex(group => group.id === groupId);
        if (groupIndex > -1) {
            // 将该分组的账号移动到默认分组
            this.moveAccountsToGroup(groupId, 'default');
            this.groups.splice(groupIndex, 1);
            this.updateGroupCounts();
            this.renderGroupList();
            return true;
        }
        return false;
    }

    // 移动账号到分组
    moveAccountToGroup(accountId, targetGroupId) {
        // 更新账号数据中的分组信息
        const accounts = window.app ? window.app.getAccountsData() : [];
        const account = accounts.find(acc => acc.id == accountId);
        if (account) {
            const oldGroupId = account.group || 'default';
            account.group = targetGroupId;

            // 更新分组名称
            const targetGroup = this.getGroup(targetGroupId);
            if (targetGroup) {
                account.groupName = targetGroup.name;
            }

            // 保存到本地存储
            this.saveAccountsData(accounts);

            console.log(`账号 ${account.nickname} 从 ${oldGroupId} 移动到 ${targetGroupId}`);

            // 显示成功通知
            this.showNotification(`账号 "${account.nickname}" 已移动到 "${targetGroup.name}" 分组`, 'success');
        }

        this.updateGroupCounts();
        this.renderGroupList();
    }

    // 保存账号数据到本地存储
    saveAccountsData(accounts) {
        try {
            localStorage.setItem('accountsData', JSON.stringify(accounts));
        } catch (error) {
            console.error('保存账号数据失败:', error);
        }
    }

    // 从本地存储加载账号数据
    loadAccountsData() {
        try {
            const data = localStorage.getItem('accountsData');
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('加载账号数据失败:', error);
            return null;
        }
    }

    // 保存分组数据到本地存储
    saveGroupsData() {
        try {
            localStorage.setItem('groupsData', JSON.stringify({
                groups: this.groups,
                platformGroups: this.platformGroups
            }));
        } catch (error) {
            console.error('保存分组数据失败:', error);
        }
    }

    // 从本地存储加载分组数据
    loadGroupsData() {
        try {
            const data = localStorage.getItem('groupsData');
            if (data) {
                const parsed = JSON.parse(data);
                if (parsed.groups && parsed.platformGroups) {
                    this.groups = parsed.groups;
                    this.platformGroups = parsed.platformGroups;
                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error('加载分组数据失败:', error);
            return false;
        }
    }

    // 清除所有本地存储数据
    clearAllData() {
        if (confirm('确定要清除所有本地数据吗？这将删除所有账号和分组信息。')) {
            try {
                localStorage.removeItem('accountsData');
                localStorage.removeItem('groupsData');
                location.reload(); // 重新加载页面以重置数据
            } catch (error) {
                console.error('清除数据失败:', error);
                this.showNotification('清除数据失败', 'error');
            }
        }
    }

    // 批量移动账号到分组
    moveAccountsToGroup(fromGroupId, toGroupId) {
        console.log(`批量移动账号从分组 ${fromGroupId} 到分组 ${toGroupId}`);
        this.updateGroupCounts();
    }

    // 更新分组账号数量
    updateGroupCounts() {
        // 重置计数
        this.groups.forEach(group => group.count = 0);

        // 模拟计算每个分组的账号数量
        const accounts = window.app ? window.app.getAccountsData() : [];
        accounts.forEach(account => {
            const group = this.getGroup(account.group || 'default');
            if (group) {
                group.count++;
            }
        });
    }

    // 渲染分组列表
    renderGroupList() {
        const container = document.getElementById('groupListContainer');
        if (!container) return;

        let html = '';

        // 小红书分组
        html += this.renderPlatformGroups('xiaohongshu', '小红书分组', '#ff2442', 'fas fa-heart');

        // 抖音分组
        html += this.renderPlatformGroups('douyin', '抖音分组', '#000000', 'fas fa-music');

        // 微博分组
        html += this.renderPlatformGroups('weibo', '微博分组', '#e6162d', 'fas fa-comment');

        container.innerHTML = html;
    }

    // 渲染特定平台的分组
    renderPlatformGroups(platform, platformName, platformColor, platformIcon) {
        const groups = this.platformGroups[platform];

        return `
            <div class="platform-group-section" style="margin-bottom: 24px;">
                <div class="platform-header" style="
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 12px;
                    padding-bottom: 8px;
                    border-bottom: 2px solid ${platformColor};
                ">
                    <i class="${platformIcon}" style="color: ${platformColor}; font-size: 16px;"></i>
                    <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: ${platformColor};">${platformName}</h3>
                    <button onclick="window.groupManager.createPlatformGroupModal('${platform}')" style="
                        background: ${platformColor};
                        color: white;
                        border: none;
                        padding: 4px 8px;
                        border-radius: 4px;
                        font-size: 11px;
                        cursor: pointer;
                        margin-left: auto;
                    ">+ 新建分组</button>
                </div>
                <div class="group-list">
                    ${groups.map(group => `
                        <div class="group-item" data-group-id="${group.id}" style="margin-bottom: 8px;">
                            <div class="group-info">
                                <div class="group-color" style="background-color: ${group.color}"></div>
                                <div class="group-details">
                                    <div class="group-name">${group.name}</div>
                                    <div class="group-count">${group.count} 个账号</div>
                                </div>
                            </div>
                            <div class="group-actions">
                                <button class="group-action-btn" onclick="window.groupManager.editGroupModal('${group.id}')" title="编辑分组">
                                    <i class="fas fa-edit"></i>
                                </button>
                                ${!group.id.includes('-default') ? `
                                    <button class="group-action-btn delete" onclick="window.groupManager.deleteGroupConfirm('${group.id}')" title="删除分组">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // 为特定平台创建分组模态框
    createPlatformGroupModal(platform) {
        const platformNames = {
            'xiaohongshu': '小红书',
            'douyin': '抖音',
            'weibo': '微博'
        };

        const platformColors = {
            'xiaohongshu': '#ff2442',
            'douyin': '#000000',
            'weibo': '#e6162d'
        };

        // 显示自定义分组创建界面
        this.showCustomGroupModal(platform, platformNames[platform], platformColors[platform]);
    }

    // 显示自定义分组创建模态框
    showCustomGroupModal(platform, platformName, platformColor) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 500px;">
                <div class="modal-header">
                    <h3 style="margin: 0; color: ${platformColor};">
                        <i class="fas fa-plus-circle"></i> 创建${platformName}自定义分组
                    </h3>
                    <button class="modal-close" onclick="window.groupManager.closeCustomGroupModal()">&times;</button>
                </div>

                <div class="modal-body" style="padding: 20px;">
                    <!-- 分组名称输入 -->
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                            <i class="fas fa-tag"></i> 分组昵称
                        </label>
                        <input type="text" id="customGroupName" placeholder="请输入自定义分组名称，如：美妆达人、科技博主等"
                               style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 6px; font-size: 14px; box-sizing: border-box; outline: none;"
                               maxlength="20" autocomplete="off">
                        <small style="color: #666; font-size: 12px; margin-top: 4px; display: block;">
                            建议使用有意义的名称，方便后续管理（最多20个字符）
                        </small>
                    </div>

                    <!-- 分组颜色选择 -->
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                            <i class="fas fa-palette"></i> 分组颜色
                        </label>
                        <div class="color-picker" style="display: flex; gap: 8px; flex-wrap: wrap;">
                            ${this.generateColorOptions(platformColor)}
                        </div>
                    </div>

                    <!-- 分组描述 -->
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                            <i class="fas fa-align-left"></i> 分组描述（可选）
                        </label>
                        <textarea id="customGroupDescription" placeholder="描述这个分组的用途，如：专门管理美妆类账号，用于推广化妆品..."
                                  style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 6px; font-size: 14px; resize: vertical; min-height: 80px; box-sizing: border-box;"
                                  maxlength="100"></textarea>
                        <small style="color: #666; font-size: 12px; margin-top: 4px; display: block;">
                            简单描述分组用途，帮助团队成员理解（最多100个字符）
                        </small>
                    </div>
                </div>

                <div class="modal-footer" style="padding: 15px 20px; border-top: 1px solid #e0e0e0; display: flex; gap: 10px; justify-content: flex-end;">
                    <button class="btn btn-secondary" onclick="window.groupManager.closeCustomGroupModal()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button class="btn btn-primary" onclick="window.groupManager.saveCustomGroup('${platform}', '${platformName}', '${platformColor}')"
                            style="background: ${platformColor};">
                        <i class="fas fa-check"></i> 创建分组
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加模态框事件处理
        modal.addEventListener('click', (e) => {
            // 点击背景关闭模态框
            if (e.target === modal) {
                this.closeCustomGroupModal();
            }
        });

        // 防止模态框内容区域的点击事件冒泡
        const modalContent = modal.querySelector('.modal-content');
        modalContent.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // 添加键盘事件支持
        document.addEventListener('keydown', this.handleModalKeydown.bind(this));

        // 聚焦到名称输入框
        setTimeout(() => {
            const nameInput = document.getElementById('customGroupName');
            if (nameInput) {
                nameInput.focus();
                nameInput.select(); // 选中输入框内容（如果有的话）

                // 添加输入框的Enter键支持
                nameInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.saveCustomGroup(platform, platformName, platformColor);
                    }
                });
            }
        }, 200);
    }

    // 处理模态框键盘事件
    handleModalKeydown(e) {
        const modal = document.querySelector('.modal-overlay');
        if (!modal) return;

        if (e.key === 'Escape') {
            e.preventDefault();
            this.closeCustomGroupModal();
        }
    }

    // 生成颜色选择选项
    generateColorOptions(defaultColor) {
        const colors = [
            '#ff2442', '#ff69b4', '#ff8c00', '#32cd32', '#1e90ff',
            '#9c27b0', '#e91e63', '#f44336', '#ff9800', '#4caf50',
            '#2196f3', '#9e9e9e', '#795548', '#607d8b', '#000000'
        ];

        return colors.map(color => `
            <div class="color-option ${color === defaultColor ? 'selected' : ''}"
                 data-color="${color}"
                 onclick="window.groupManager.selectColor('${color}')"
                 style="
                    width: 32px;
                    height: 32px;
                    background: ${color};
                    border-radius: 50%;
                    cursor: pointer;
                    border: 3px solid ${color === defaultColor ? '#333' : 'transparent'};
                    transition: all 0.2s ease;
                 "
                 title="选择颜色">
            </div>
        `).join('');
    }

    // 选择颜色
    selectColor(color) {
        // 移除所有选中状态
        document.querySelectorAll('.color-option').forEach(option => {
            option.classList.remove('selected');
            option.style.border = '3px solid transparent';
        });

        // 添加选中状态
        const selectedOption = document.querySelector(`[data-color="${color}"]`);
        if (selectedOption) {
            selectedOption.classList.add('selected');
            selectedOption.style.border = '3px solid #333';
        }
    }

    // 保存自定义分组
    saveCustomGroup(platform, platformName, defaultColor) {
        const name = document.getElementById('customGroupName').value.trim();
        const description = document.getElementById('customGroupDescription').value.trim();
        const selectedColorElement = document.querySelector('.color-option.selected');
        const color = selectedColorElement ? selectedColorElement.dataset.color : defaultColor;

        // 验证输入
        if (!name) {
            alert('请输入分组名称！');
            document.getElementById('customGroupName').focus();
            return;
        }

        if (name.length > 20) {
            alert('分组名称不能超过20个字符！');
            document.getElementById('customGroupName').focus();
            return;
        }

        // 检查分组名称是否重复
        const existingGroup = this.platformGroups[platform].find(group => group.name === name);
        if (existingGroup) {
            alert(`分组名称"${name}"已存在，请使用其他名称！`);
            document.getElementById('customGroupName').focus();
            return;
        }

        // 创建新分组
        const newGroup = {
            id: `${platform}-${Date.now()}`,
            name: name,
            color: color,
            count: 0,
            platform: platform,
            description: description || `${platformName}自定义分组`,
            isCustom: true,  // 标记为自定义分组
            createdAt: new Date().toISOString()
        };

        // 添加到对应平台的分组中
        this.platformGroups[platform].push(newGroup);

        // 同时添加到总分组列表中（为了兼容性）
        this.groups.push(newGroup);

        // 保存分组数据
        this.saveGroupsData();

        // 更新界面
        this.updateGroupCounts();
        this.renderGroupList();

        // 关闭模态框
        this.closeCustomGroupModal();

        // 显示成功通知
        this.showNotification(`自定义分组"${name}"创建成功！`, 'success');
    }

    // 关闭自定义分组模态框
    closeCustomGroupModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            // 移除键盘事件监听器
            document.removeEventListener('keydown', this.handleModalKeydown.bind(this));
            // 移除模态框
            modal.remove();
        }
    }

    // 显示创建分组模态框
    createGroupModal() {
        this.showGroupModal('create', null, '新分组', '#6c757d');
    }

    // 显示编辑分组模态框
    editGroupModal(groupId) {
        const group = this.getGroup(groupId);
        if (group) {
            this.showGroupModal('edit', groupId, group.name, group.color);
        }
    }

    // 显示分组模态框
    showGroupModal(mode, groupId, name, color, platform = null) {
        const modalHtml = `
            <div class="modal-overlay" id="groupModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${mode === 'create' ? '创建新分组' : '编辑分组'}</h3>
                        <button class="modal-close" onclick="window.groupManager.closeGroupModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>分组名称</label>
                            <input type="text" id="groupName" value="${name}" placeholder="请输入分组名称">
                        </div>
                        <div class="form-group">
                            <label>分组颜色</label>
                            <div class="color-picker">
                                <input type="color" id="groupColor" value="${color}">
                                <div class="color-presets">
                                    ${['#28a745', '#dc3545', '#ffc107', '#17a2b8', '#6f42c1', '#e83e8c', '#fd7e14', '#20c997'].map(c =>
                                        `<div class="color-preset" style="background-color: ${c}" onclick="document.getElementById('groupColor').value='${c}'"></div>`
                                    ).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="window.groupManager.closeGroupModal()">取消</button>
                        <button class="btn btn-primary" onclick="window.groupManager.saveGroup('${mode}', '${groupId}', '${platform}')">
                            ${mode === 'create' ? '创建' : '保存'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // 保存分组
    saveGroup(mode, groupId, platform = null) {
        const name = document.getElementById('groupName').value.trim();
        const color = document.getElementById('groupColor').value;

        if (!name) {
            alert('请输入分组名称');
            return;
        }

        if (mode === 'create') {
            if (platform) {
                this.createPlatformGroup(name, color, platform);
            } else {
                this.createGroup(name, color);
            }
        } else {
            this.editGroup(groupId, name, color);
        }

        this.closeGroupModal();
    }

    // 为特定平台创建分组
    createPlatformGroup(name, color, platform) {
        const id = `${platform}-${Date.now()}`;
        const newGroup = {
            id,
            name,
            color,
            count: 0,
            platform
        };

        // 添加到对应平台的分组中
        this.platformGroups[platform].push(newGroup);

        // 同时添加到总分组列表中（为了兼容性）
        this.groups.push(newGroup);

        // 保存分组数据
        this.saveGroupsData();

        this.updateGroupCounts();
        this.renderGroupList();
        this.showNotification(`${name} 分组创建成功`, 'success');
        return newGroup;
    }

    // 关闭分组模态框
    closeGroupModal() {
        const modal = document.getElementById('groupModal');
        if (modal) {
            modal.remove();
        }
    }

    // 删除分组确认
    deleteGroupConfirm(groupId) {
        const group = this.getGroup(groupId);
        if (!group) {
            this.showNotification('分组不存在', 'error');
            return;
        }

        // 检查是否为默认分组
        if (group.id.includes('-default')) {
            this.showNotification('默认分组不能删除', 'warning');
            return;
        }

        // 计算该分组下的账号数量
        const accounts = window.app ? window.app.getAccountsData() : [];
        const accountsInGroup = accounts.filter(acc => acc.group === groupId);

        let confirmMessage = `确定要删除分组"${group.name}"吗？`;
        if (accountsInGroup.length > 0) {
            confirmMessage += `\n\n该分组下有 ${accountsInGroup.length} 个账号，删除后这些账号将移动到对应平台的默认分组。`;
        }

        if (confirm(confirmMessage)) {
            this.deleteGroup(groupId);
        }
    }

    // 删除分组
    deleteGroup(groupId) {
        const group = this.getGroup(groupId);
        if (!group) {
            this.showNotification('分组不存在', 'error');
            return false;
        }

        // 获取平台信息
        const platform = group.platform;
        let defaultGroupId = 'default';

        // 根据平台确定默认分组ID
        if (platform) {
            defaultGroupId = `${platform}-default`;
        }

        // 将该分组的账号移动到默认分组
        const accounts = window.app ? window.app.getAccountsData() : [];
        const movedAccounts = [];

        accounts.forEach(account => {
            if (account.group === groupId) {
                account.group = defaultGroupId;
                const defaultGroup = this.getGroup(defaultGroupId);
                if (defaultGroup) {
                    account.groupName = defaultGroup.name;
                }
                movedAccounts.push(account.nickname);
            }
        });

        // 保存账号数据
        if (window.app && movedAccounts.length > 0) {
            window.app.saveAccountsData ? window.app.saveAccountsData(accounts) : this.saveAccountsData(accounts);
        }

        // 从分组列表中删除
        const groupIndex = this.groups.findIndex(g => g.id === groupId);
        if (groupIndex > -1) {
            this.groups.splice(groupIndex, 1);
        }

        // 从平台分组中删除
        if (platform && this.platformGroups[platform]) {
            const platformGroupIndex = this.platformGroups[platform].findIndex(g => g.id === groupId);
            if (platformGroupIndex > -1) {
                this.platformGroups[platform].splice(platformGroupIndex, 1);
            }
        }

        // 保存分组数据
        this.saveGroupsData();

        // 更新界面
        this.updateGroupCounts();
        this.renderGroupList();

        // 显示成功通知
        let message = `分组"${group.name}"已删除`;
        if (movedAccounts.length > 0) {
            message += `，${movedAccounts.length}个账号已移动到默认分组`;
        }
        this.showNotification(message, 'success');

        return true;
    }

    // 显示通知
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 3秒后自动消失
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 3000);
    }

    // 获取通知图标
    getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // 🎯 加载小红书管理界面
    loadXiaohongshuManagement() {
        const container = document.querySelector('.accounts-management-table');
        if (!container) return;

        container.innerHTML = `
            <div class="xiaohongshu-management-container">
                <!-- 小红书管理头部 -->
                <div class="xiaohongshu-header">
                    <div class="xiaohongshu-title">
                        <div class="xiaohongshu-icon">
                            <i class="fab fa-instagram"></i>
                        </div>
                        <div>
                            <h2>小红书管理中心</h2>
                            <p>智能化内容管理与数据分析平台</p>
                        </div>
                    </div>
                </div>

                <!-- 统计数据区域 -->
                <div class="xiaohongshu-stats-grid" id="xiaohongshuStatsContainer">
                    <!-- 统计卡片将通过JavaScript动态生成 -->
                </div>

                <!-- 功能按钮区域 -->
                <div class="xiaohongshu-action-buttons">
                    <button class="xiaohongshu-action-btn" onclick="window.app.showXiaohongshuAnalysis()">
                        <div class="action-btn-icon analysis">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="action-btn-title">数据分析</div>
                        <div class="action-btn-desc">查看详细的数据分析报告</div>
                    </button>

                    <button class="xiaohongshu-action-btn" onclick="window.app.showXiaohongshuTopManagement()">
                        <div class="action-btn-icon top">
                            <i class="fas fa-thumbtack"></i>
                        </div>
                        <div class="action-btn-title">置顶管理</div>
                        <div class="action-btn-desc">管理笔记置顶和排序</div>
                    </button>

                    <button class="xiaohongshu-action-btn" onclick="window.app.showXiaohongshuPrivacySettings()">
                        <div class="action-btn-icon privacy">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="action-btn-title">权限设置</div>
                        <div class="action-btn-desc">批量设置笔记可见权限</div>
                    </button>

                    <button class="xiaohongshu-action-btn" onclick="window.app.showXiaohongshuPublish()">
                        <div class="action-btn-icon publish">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <div class="action-btn-title">发布笔记</div>
                        <div class="action-btn-desc">创建并发布新的笔记内容</div>
                    </button>

                    <button class="xiaohongshu-action-btn" onclick="window.app.openXiaohongshuBrowser()">
                        <div class="action-btn-icon browser">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                        <div class="action-btn-title">打开浏览器</div>
                        <div class="action-btn-desc">打开小红书创作者中心</div>
                    </button>
                </div>

                <!-- 笔记列表区域 -->
                <div class="xiaohongshu-notes-section">
                    <div class="section-header">
                        <h3 class="section-title">笔记管理</h3>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="all">全部</button>
                            <button class="filter-btn" data-filter="published">已发布</button>
                            <button class="filter-btn" data-filter="draft">草稿</button>
                            <button class="filter-btn" data-filter="top">已置顶</button>
                        </div>
                    </div>

                    <div class="xiaohongshu-notes-grid" id="xiaohongshuNotesContainer">
                        <!-- 笔记卡片将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        `;

        // 初始化小红书管理功能
        this.initializeXiaohongshuManagement();
    }

    // 🎯 初始化小红书管理功能
    async initializeXiaohongshuManagement() {
        try {
            // 加载数据
            await this.loadXiaohongshuData();

            // 渲染统计数据
            this.renderXiaohongshuStats();

            // 渲染笔记列表
            this.renderXiaohongshuNotes();

            // 绑定事件
            this.bindXiaohongshuEvents();

            this.showNotification('小红书管理模块加载完成', 'success');
        } catch (error) {
            console.error('小红书管理初始化失败:', error);
            this.showNotification('小红书管理模块加载失败', 'error');
        }
    }

    // 🎯 加载小红书数据
    async loadXiaohongshuData() {
        try {
            const response = await fetch('/api/xiaohongshu/overview');
            const data = await response.json();

            if (data.success) {
                this.xiaohongshuData = data.data;
            }
        } catch (error) {
            console.error('加载小红书数据失败:', error);
        }
    }

    // 🎯 渲染小红书统计数据
    renderXiaohongshuStats() {
        const container = document.getElementById('xiaohongshuStatsContainer');
        if (!container || !this.xiaohongshuData) return;

        const stats = [
            {
                title: '总浏览量',
                value: this.formatNumber(this.xiaohongshuData.totalViews || 0),
                growth: this.xiaohongshuData.recentGrowth?.views || '+0%',
                icon: 'fas fa-eye',
                type: 'views'
            },
            {
                title: '总点赞数',
                value: this.formatNumber(this.xiaohongshuData.totalLikes || 0),
                growth: this.xiaohongshuData.recentGrowth?.likes || '+0%',
                icon: 'fas fa-heart',
                type: 'likes'
            },
            {
                title: '总评论数',
                value: this.formatNumber(this.xiaohongshuData.totalComments || 0),
                growth: '+19.4%',
                icon: 'fas fa-comment',
                type: 'comments'
            },
            {
                title: '总分享数',
                value: this.formatNumber(this.xiaohongshuData.totalShares || 0),
                growth: '+22.9%',
                icon: 'fas fa-share',
                type: 'shares'
            }
        ];

        container.innerHTML = stats.map(stat => `
            <div class="xiaohongshu-stat-card">
                <div class="stat-header">
                    <span class="stat-title">${stat.title}</span>
                    <div class="stat-icon ${stat.type}">
                        <i class="${stat.icon}"></i>
                    </div>
                </div>
                <div class="stat-value">${stat.value}</div>
                <div class="stat-growth ${stat.growth.startsWith('+') ? 'positive' : 'negative'}">
                    <i class="fas fa-arrow-${stat.growth.startsWith('+') ? 'up' : 'down'}"></i>
                    ${stat.growth}
                </div>
            </div>
        `).join('');
    }

    // 🎯 渲染小红书笔记列表
    renderXiaohongshuNotes() {
        const container = document.getElementById('xiaohongshuNotesContainer');
        if (!container || !this.xiaohongshuData?.recentNotes) return;

        const notes = this.xiaohongshuData.recentNotes;

        if (notes.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                    <p>暂无笔记数据</p>
                </div>
            `;
            return;
        }

        container.innerHTML = notes.map(note => `
            <div class="xiaohongshu-note-card" data-note-id="${note.id}">
                <div class="note-header">
                    <h4 class="note-title">${note.title}</h4>
                    <span class="note-status ${note.status === '已发布' ? 'published' : 'draft'}">
                        ${note.status}
                    </span>
                </div>

                <div class="note-meta">
                    <span><i class="fas fa-clock"></i> ${this.formatDate(note.publishTime)}</span>
                    <span><i class="fas fa-${note.privacy === '公开可见' ? 'globe' : 'lock'}"></i> ${note.privacy}</span>
                    ${note.isTop ? '<span class="note-top-badge"><i class="fas fa-thumbtack"></i> 已置顶</span>' : ''}
                </div>

                <div class="note-stats">
                    <div class="note-stat">
                        <i class="fas fa-eye"></i>
                        <span>${this.formatNumber(note.views)}</span>
                    </div>
                    <div class="note-stat">
                        <i class="fas fa-heart"></i>
                        <span>${this.formatNumber(note.likes)}</span>
                    </div>
                    <div class="note-stat">
                        <i class="fas fa-comment"></i>
                        <span>${this.formatNumber(note.comments)}</span>
                    </div>
                    <div class="note-stat">
                        <i class="fas fa-share"></i>
                        <span>${this.formatNumber(note.shares)}</span>
                    </div>
                </div>

                <div class="note-actions">
                    <button class="note-action-btn primary" onclick="window.app.editXiaohongshuNote('${note.id}')">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="note-action-btn secondary" onclick="window.app.toggleXiaohongshuTop('${note.id}')">
                        <i class="fas fa-thumbtack"></i> ${note.isTop ? '取消置顶' : '置顶'}
                    </button>
                    <button class="note-action-btn secondary" onclick="window.app.changeXiaohongshuPrivacy('${note.id}')">
                        <i class="fas fa-lock"></i> 权限
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 🎯 绑定小红书管理事件
    bindXiaohongshuEvents() {
        // 筛选按钮
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                // 这里可以添加筛选逻辑
            });
        });
    }

    // 🎯 小红书管理功能方法
    async showXiaohongshuAnalysis() {
        this.showNotification('数据分析功能开发中...', 'info');
    }

    async showXiaohongshuTopManagement() {
        this.showNotification('置顶管理功能开发中...', 'info');
    }

    async showXiaohongshuPrivacySettings() {
        this.showNotification('权限设置功能开发中...', 'info');
    }

    async showXiaohongshuPublish() {
        this.showNotification('发布笔记功能开发中...', 'info');
    }

    async openXiaohongshuBrowser() {
        try {
            this.showNotification('正在打开浏览器...', 'info');

            const response = await fetch('/api/xiaohongshu/browser/open', {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('浏览器已打开，请在浏览器中进行操作', 'success');
            } else {
                this.showNotification('浏览器打开失败', 'error');
            }
        } catch (error) {
            this.showNotification('浏览器操作失败', 'error');
        }
    }

    async toggleXiaohongshuTop(noteId) {
        this.showNotification('置顶功能开发中...', 'info');
    }

    async editXiaohongshuNote(noteId) {
        this.showNotification('编辑功能开发中...', 'info');
    }

    async changeXiaohongshuPrivacy(noteId) {
        this.showNotification('权限设置功能开发中...', 'info');
    }

    // 🎯 工具方法
    formatNumber(num) {
        if (num >= 10000) {
            return (num / 10000).toFixed(1) + 'w';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'k';
        }
        return num.toString();
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    // ===== 小红书账号管理方法 =====

    // 采集小红书账号信息
    async collectXiaohongshuAccounts() {
        try {
            this.showNotification('开始采集小红书账号信息...', 'info');

            const response = await fetch('/api/xiaohongshu/accounts/collect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.collectedXiaohongshuAccounts = result.data.accounts;

                // 更新主应用的数据
                if (window.app) {
                    window.app.collectedXiaohongshuAccounts = this.collectedXiaohongshuAccounts;
                    // 重新加载账号管理页面以显示新数据
                    const container = document.querySelector('.main-content');
                    if (container) {
                        window.app.loadAccountsContent(container);
                    }
                }

                this.showNotification(`成功采集到 ${result.data.total} 个小红书账号`, 'success');
            } else {
                this.showNotification(`采集失败: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('采集小红书账号失败:', error);
            this.showNotification('采集失败，请检查网络连接', 'error');
        }
    }

    // 刷新单个小红书账号
    async refreshXiaohongshuAccount(accountId) {
        try {
            this.showNotification('正在刷新账号信息...', 'info');

            const response = await fetch(`/api/xiaohongshu/accounts/${accountId}/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success) {
                // 更新本地数据
                const accountIndex = this.collectedXiaohongshuAccounts.findIndex(acc => acc.id === accountId);
                if (accountIndex !== -1) {
                    this.collectedXiaohongshuAccounts[accountIndex] = result.data;

                    // 更新主应用的数据
                    if (window.app) {
                        window.app.collectedXiaohongshuAccounts = this.collectedXiaohongshuAccounts;
                        // 重新加载账号管理页面
                        const container = document.querySelector('.main-content');
                        if (container) {
                            window.app.loadAccountsContent(container);
                        }
                    }
                }

                this.showNotification('账号信息刷新成功', 'success');
            } else {
                this.showNotification(`刷新失败: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('刷新账号失败:', error);
            this.showNotification('刷新失败，请检查网络连接', 'error');
        }
    }

    // 查看小红书主页
    viewXiaohongshuProfile(accountId) {
        const account = this.collectedXiaohongshuAccounts.find(acc => acc.id === accountId);
        if (account) {
            const profileUrl = `https://www.xiaohongshu.com/user/profile/${account.xiaohongshuId}`;
            window.open(profileUrl, '_blank');
        }
    }

    // 打开小红书创作中心
    openXiaohongshuCreator(accountId) {
        const creatorUrl = 'https://creator.xiaohongshu.com/new/home';
        window.open(creatorUrl, '_blank');
    }

    // 编辑小红书账号备注
    editXiaohongshuNote(accountId) {
        const account = this.collectedXiaohongshuAccounts.find(acc => acc.id === accountId);
        if (account) {
            const newNote = prompt('请输入备注信息:', account.note || '');
            if (newNote !== null) {
                account.note = newNote;
                this.showNotification('备注已更新', 'success');

                // 重新加载页面显示更新
                const container = document.querySelector('.main-content');
                if (container && window.app) {
                    window.app.loadAccountsContent(container);
                }
            }
        }
    }

    // 移除小红书账号
    removeXiaohongshuAccount(accountId) {
        const account = this.collectedXiaohongshuAccounts.find(acc => acc.id === accountId);
        if (account && confirm(`确定要移除账号 "${account.nickname}" 吗？`)) {
            this.collectedXiaohongshuAccounts = this.collectedXiaohongshuAccounts.filter(acc => acc.id !== accountId);

            // 更新主应用的数据
            if (window.app) {
                window.app.collectedXiaohongshuAccounts = this.collectedXiaohongshuAccounts;
                // 重新加载账号管理页面
                const container = document.querySelector('.main-content');
                if (container) {
                    window.app.loadAccountsContent(container);
                }
            }

            this.showNotification('账号已移除', 'success');
        }
    }

    // 批量刷新所有小红书账号
    async refreshAllXiaohongshuAccounts() {
        try {
            this.showNotification('开始批量刷新所有账号...', 'info');

            const response = await fetch('/api/xiaohongshu/accounts/refresh-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success) {
                // 重新获取最新的账号列表
                await this.loadXiaohongshuAccountsList();
                this.showNotification(`批量刷新完成：成功 ${result.data.success} 个，失败 ${result.data.failed} 个`, 'success');
            } else {
                this.showNotification(`批量刷新失败: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('批量刷新失败:', error);
            this.showNotification('批量刷新失败，请检查网络连接', 'error');
        }
    }

    // 加载小红书账号列表
    async loadXiaohongshuAccountsList() {
        try {
            const response = await fetch('/api/xiaohongshu/accounts/list');
            const result = await response.json();

            if (result.success) {
                this.collectedXiaohongshuAccounts = result.data.accounts;

                // 更新主应用的数据
                if (window.app) {
                    window.app.collectedXiaohongshuAccounts = this.collectedXiaohongshuAccounts;
                }

                return true;
            }
            return false;
        } catch (error) {
            console.error('加载账号列表失败:', error);
            return false;
        }
    }

    // ===== 自动刷新功能 =====

    // 切换自动刷新状态
    toggleAutoRefresh() {
        if (this.autoRefreshEnabled) {
            this.stopAutoRefresh();
        } else {
            this.startAutoRefresh();
        }
    }

    // 开启自动刷新
    startAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
        }

        this.autoRefreshEnabled = true;
        this.updateAutoRefreshUI();

        // 立即执行一次刷新
        this.performAutoRefresh();

        // 设置定时器
        this.autoRefreshInterval = setInterval(() => {
            this.performAutoRefresh();
        }, this.autoRefreshIntervalTime);

        this.showNotification(`自动刷新已开启，每 ${this.autoRefreshIntervalTime / 60000} 分钟刷新一次`, 'success');
        console.log('🔄 自动刷新已开启');
    }

    // 停止自动刷新
    stopAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }

        this.autoRefreshEnabled = false;
        this.updateAutoRefreshUI();

        this.showNotification('自动刷新已关闭', 'info');
        console.log('⏹️ 自动刷新已关闭');
    }

    // 执行自动刷新
    async performAutoRefresh() {
        try {
            console.log('🔄 执行自动刷新...');

            // 如果有小红书账号，则刷新小红书账号数据
            if (this.collectedXiaohongshuAccounts && this.collectedXiaohongshuAccounts.length > 0) {
                await this.refreshAllXiaohongshuAccounts();
            } else {
                // 如果没有账号数据，尝试重新采集
                await this.loadXiaohongshuAccountsList();
            }

            // 更新最后刷新时间显示
            this.updateLastRefreshTime();

        } catch (error) {
            console.error('❌ 自动刷新失败:', error);
            // 不显示错误通知，避免频繁打扰用户
        }
    }

    // 更新自动刷新UI状态
    updateAutoRefreshUI() {
        const autoRefreshText = document.getElementById('autoRefreshText');
        if (autoRefreshText) {
            autoRefreshText.textContent = this.autoRefreshEnabled ? '关闭自动刷新' : '开启自动刷新';
        }

        // 更新按钮样式
        const autoRefreshBtn = document.querySelector('[onclick="window.accountManager.toggleAutoRefresh()"]');
        if (autoRefreshBtn) {
            if (this.autoRefreshEnabled) {
                autoRefreshBtn.classList.add('active');
                autoRefreshBtn.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
                autoRefreshBtn.style.color = 'white';
            } else {
                autoRefreshBtn.classList.remove('active');
                autoRefreshBtn.style.background = '';
                autoRefreshBtn.style.color = '';
            }
        }
    }

    // 更新最后刷新时间
    updateLastRefreshTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN');

        // 在页面上显示最后刷新时间
        let refreshTimeElement = document.getElementById('lastRefreshTime');
        if (!refreshTimeElement) {
            // 如果不存在，创建一个显示元素
            refreshTimeElement = document.createElement('div');
            refreshTimeElement.id = 'lastRefreshTime';
            refreshTimeElement.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 12px;
                z-index: 1000;
                opacity: 0.8;
            `;
            document.body.appendChild(refreshTimeElement);
        }

        refreshTimeElement.textContent = `最后刷新: ${timeString}`;

        // 3秒后淡出
        setTimeout(() => {
            if (refreshTimeElement) {
                refreshTimeElement.style.opacity = '0.3';
            }
        }, 3000);
    }

    // 设置自动刷新间隔
    setAutoRefreshInterval(minutes) {
        this.autoRefreshIntervalTime = minutes * 60 * 1000;

        // 如果自动刷新正在运行，重新启动以应用新间隔
        if (this.autoRefreshEnabled) {
            this.stopAutoRefresh();
            this.startAutoRefresh();
        }

        this.showNotification(`自动刷新间隔已设置为 ${minutes} 分钟`, 'success');
    }

    // 显示自动刷新设置对话框
    showAutoRefreshSettings() {
        const currentMinutes = this.autoRefreshIntervalTime / 60000;
        const newMinutes = prompt(`请输入自动刷新间隔（分钟）：`, currentMinutes);

        if (newMinutes && !isNaN(newMinutes) && newMinutes > 0) {
            this.setAutoRefreshInterval(parseInt(newMinutes));
        }
    }

    // 页面卸载时清理定时器
    cleanup() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
    }

    // 显示数据来源说明
    showDataSourceInfo() {
        const modal = document.createElement('div');
        modal.className = 'data-source-info-modal';
        modal.innerHTML = `
            <div class="modal-overlay">
                <div class="modal-content" style="max-width: 700px;">
                    <div class="modal-header">
                        <h3><i class="fas fa-info-circle"></i> 小红书数据来源说明</h3>
                        <button class="modal-close" onclick="this.closeDataSourceInfo()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="data-source-explanation">
                            <h4>📊 数据采集方式</h4>
                            <div class="source-method">
                                <div class="method-item priority-1">
                                    <div class="method-icon">🌐</div>
                                    <div class="method-content">
                                        <h5>比特浏览器实时采集 (推荐)</h5>
                                        <p>直接从浏览器页面获取最新的账号信息，包括头像、昵称、简介、粉丝数等</p>
                                        <div class="method-status" id="browserStatus">
                                            <span class="status-indicator offline"></span>
                                            <span>当前状态：比特浏览器未连接</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="method-item priority-2">
                                    <div class="method-icon">📁</div>
                                    <div class="method-content">
                                        <h5>本地数据文件 (备用)</h5>
                                        <p>使用之前保存的JSON数据文件，数据可能不是最新的</p>
                                        <div class="method-status">
                                            <span class="status-indicator online"></span>
                                            <span>当前状态：可用</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="setup-guide">
                                <h4>🔧 如何启用实时数据采集</h4>
                                <div class="setup-steps">
                                    <div class="step">
                                        <div class="step-number">1</div>
                                        <div class="step-content">
                                            <h5>启动比特浏览器</h5>
                                            <p>确保比特浏览器应用程序已启动并运行</p>
                                        </div>
                                    </div>

                                    <div class="step">
                                        <div class="step-number">2</div>
                                        <div class="step-content">
                                            <h5>创建并启动浏览器实例</h5>
                                            <p><strong>重要：</strong>在比特浏览器中创建一个浏览器实例，然后点击"启动"按钮启动该实例</p>
                                            <div class="step-warning">
                                                ⚠️ 必须启动浏览器实例，否则无法获取数据
                                            </div>
                                        </div>
                                    </div>

                                    <div class="step">
                                        <div class="step-number">3</div>
                                        <div class="step-content">
                                            <h5>打开小红书页面</h5>
                                            <p>在浏览器中访问小红书创作中心或主站，并登录账号</p>
                                            <div class="url-examples">
                                                <code>https://creator.xiaohongshu.com/new/home</code><br>
                                                <code>https://www.xiaohongshu.com</code>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="step">
                                        <div class="step-number">4</div>
                                        <div class="step-content">
                                            <h5>重新采集数据</h5>
                                            <p>点击"采集账号"按钮，系统将自动从浏览器获取最新数据</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="data-fields">
                                <h4>📋 可采集的数据字段</h4>
                                <div class="fields-grid">
                                    <div class="field-item">
                                        <i class="fas fa-user"></i>
                                        <span>用户昵称</span>
                                    </div>
                                    <div class="field-item">
                                        <i class="fas fa-image"></i>
                                        <span>头像图片</span>
                                    </div>
                                    <div class="field-item">
                                        <i class="fas fa-id-card"></i>
                                        <span>小红书ID</span>
                                    </div>
                                    <div class="field-item">
                                        <i class="fas fa-align-left"></i>
                                        <span>个人简介</span>
                                    </div>
                                    <div class="field-item">
                                        <i class="fas fa-users"></i>
                                        <span>粉丝数量</span>
                                    </div>
                                    <div class="field-item">
                                        <i class="fas fa-user-plus"></i>
                                        <span>关注数量</span>
                                    </div>
                                    <div class="field-item">
                                        <i class="fas fa-heart"></i>
                                        <span>获赞收藏</span>
                                    </div>
                                    <div class="field-item">
                                        <i class="fas fa-chart-line"></i>
                                        <span>统计数据</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="window.accountManager.testBrowserConnection()">
                            <i class="fas fa-plug"></i>
                            测试浏览器连接
                        </button>
                        <button class="btn btn-secondary" onclick="window.accountManager.closeDataSourceInfo()">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 点击背景关闭
        modal.querySelector('.modal-overlay').addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeDataSourceInfo();
            }
        });
    }

    // 关闭数据来源说明
    closeDataSourceInfo() {
        const modal = document.querySelector('.data-source-info-modal');
        if (modal) {
            modal.remove();
        }
    }

    // 测试浏览器连接
    async testBrowserConnection() {
        try {
            this.showNotification('正在测试比特浏览器连接...', 'info');

            // 这里可以调用后端API测试连接
            const response = await fetch('/api/xiaohongshu/test-browser-connection', {
                method: 'POST'
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('比特浏览器连接成功！', 'success');
                // 更新状态显示
                const statusElement = document.getElementById('browserStatus');
                if (statusElement) {
                    statusElement.innerHTML = `
                        <span class="status-indicator online"></span>
                        <span>当前状态：已连接</span>
                    `;
                }
            } else {
                this.showNotification(`连接失败：${result.message}`, 'error');

                // 显示详细的错误信息和建议
                if (result.details && result.details.suggestion) {
                    setTimeout(() => {
                        this.showNotification(result.details.suggestion, 'warning');
                    }, 2000);
                }
            }
        } catch (error) {
            this.showNotification('测试连接时出错', 'error');
        }
    }
}

// 🚀 启动应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new PremiumApp();
    window.accountManager = new AccountManager();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    // 清理账号管理器的定时器
    if (window.accountManager) {
        window.accountManager.cleanup();
    }

    // 清理WebSocket连接
    if (window.app && window.app.socket) {
        window.app.socket.close();
    }
});
