<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- ===== 基本页面信息 ===== -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黑默科技 - 桌面端营销管理平台</title>

    <!-- ===== 样式文件引入 ===== -->
    <link rel="stylesheet" href="premium-ui.css"> <!-- 主要样式文件 -->
    <link rel="stylesheet" href="settings-styles.css"> <!-- 设置页面样式 -->

    <!-- ===== 字体资源 ===== -->
    <link rel="preconnect" href="https://fonts.googleapis.com"> <!-- 预连接Google字体服务器 -->
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet"> <!-- Inter字体 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"> <!-- Font Awesome图标库 -->

    <!-- 🎯 SEO优化 -->
    <meta name="description" content="黑默科技商业营销管理平台 - 专业的社交媒体营销解决方案">
    <meta name="keywords" content="营销管理,社交媒体,数据分析,账号管理">
    <meta name="author" content="黑默科技">
    
    <!-- 🎪 Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><path d='M15 25 L50 10 L85 25 L85 75 L50 90 L15 75 Z' fill='none' stroke='%23333' stroke-width='3'/><path d='M25 32 L50 20 L75 32 L75 68 L50 80 L25 68 Z' fill='none' stroke='%23333' stroke-width='2'/><path d='M50 20 L50 50 M35 38 L50 50 L65 38' stroke='%23333' stroke-width='2.5'/><circle cx='50' cy='32' r='2.5' fill='%234F46E5'/></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 🎯 侧边栏 -->
        <aside class="sidebar">
            <!-- 🏢 品牌区域 -->
            <div class="brand-section">
                <div class="brand-logo">
                    <div class="brand-icon">
                        <svg viewBox="0 0 100 100" class="logo-svg">
                            <!-- 黑默科技立体几何logo -->
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#e8e8e8;stop-opacity:1" />
                                </linearGradient>
                            </defs>

                            <!-- 外层立方体框架 -->
                            <path d="M15 25 L50 10 L85 25 L85 75 L50 90 L15 75 Z"
                                  fill="none" stroke="white" stroke-width="2.5" stroke-linejoin="round"/>

                            <!-- 内层立方体 -->
                            <path d="M25 32 L50 20 L75 32 L75 68 L50 80 L25 68 Z"
                                  fill="none" stroke="white" stroke-width="2" stroke-linejoin="round"/>

                            <!-- 立体连接线 -->
                            <path d="M15 25 L25 32 M50 10 L50 20 M85 25 L75 32 M15 75 L25 68 M50 90 L50 80 M85 75 L75 68"
                                  stroke="white" stroke-width="1.5" stroke-linecap="round"/>

                            <!-- 内部Y形结构 -->
                            <path d="M50 20 L50 50 M35 38 L50 50 L65 38"
                                  stroke="white" stroke-width="2.5" stroke-linecap="round"/>

                            <!-- 内部曲线装饰 -->
                            <path d="M30 40 Q50 35 70 40 M30 60 Q50 55 70 60"
                                  fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round"/>

                            <!-- 蓝色科技点 -->
                            <circle cx="50" cy="32" r="2.5" fill="#4F46E5"/>
                        </svg>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title">黑默科技</div>
                        <div class="brand-subtitle">营销管理平台</div>
                    </div>
                </div>
            </div>

            <!-- ===== 主导航菜单 ===== -->
            <!-- 左侧导航栏，包含所有主要功能模块 -->
            <nav class="navigation">
                <!-- ===== 数据总览页面 ===== -->
                <!-- 显示平台整体数据统计 -->
                <a href="#" class="nav-item" data-page="overview">
                    <div class="nav-icon"><i class="fas fa-chart-pie"></i></div>
                    <span>数据总览</span>
                </a>

                <!-- ===== 账号管理页面（核心功能） ===== -->
                <!-- 管理三大平台的账号分组，默认激活此页面 -->
                <a href="#" class="nav-item active" data-page="accounts">
                    <div class="nav-icon"><i class="fas fa-users"></i></div>
                    <span>账号管理</span>
                </a>

                <!-- ===== 内容搜索页面 ===== -->
                <!-- 搜索和管理发布内容 -->
                <a href="#" class="nav-item" data-page="content">
                    <div class="nav-icon"><i class="fas fa-search"></i></div>
                    <span>内容搜索</span>
                </a>

                <!-- ===== 小红书管理页面 ===== -->
                <!-- 小红书笔记管理、置顶、权限设置、自动发布 -->
                <a href="xiaohongshu.html" class="nav-item" target="_blank">
                    <div class="nav-icon"><i class="fab fa-instagram"></i></div>
                    <span>小红书管理</span>
                </a>

                <!-- ===== 竞品分析页面 ===== -->
                <!-- 分析竞争对手数据 -->
                <a href="#" class="nav-item" data-page="analytics">
                    <div class="nav-icon"><i class="fas fa-chart-line"></i></div>
                    <span>竞品分析</span>
                </a>

                <!-- ===== 互动管理页面 ===== -->
                <!-- 管理点赞、评论等互动行为 -->
                <a href="#" class="nav-item" data-page="interaction">
                    <div class="nav-icon"><i class="fas fa-heart"></i></div>
                    <span>互动管理</span>
                </a>

                <!-- ===== 评论管理页面 ===== -->
                <!-- 管理和回复用户评论 -->
                <a href="#" class="nav-item" data-page="comments">
                    <div class="nav-icon"><i class="fas fa-comments"></i></div>
                    <span>评论管理</span>
                </a>

                <a href="#" class="nav-item" data-page="advanced">
                    <div class="nav-icon"><i class="fas fa-magic"></i></div>
                    <span>高级功能</span>
                </a>

                <a href="#" class="nav-item" data-page="data-collection">
                    <div class="nav-icon"><i class="fas fa-database"></i></div>
                    <span>数据采集</span>
                </a>

                <a href="#" class="nav-item" data-page="cloud-phone">
                    <div class="nav-icon"><i class="fas fa-mobile-alt"></i></div>
                    <span>云手机管理</span>
                </a>

                <a href="#" class="nav-item" data-page="automation">
                    <div class="nav-icon"><i class="fas fa-robot"></i></div>
                    <span>自动化工具</span>
                </a>

                <a href="#" class="nav-item" data-page="settings">
                    <div class="nav-icon"><i class="fas fa-cog"></i></div>
                    <span>系统设置</span>
                </a>

                <a href="#" class="nav-item" data-page="users">
                    <div class="nav-icon"><i class="fas fa-user-shield"></i></div>
                    <span>用户管理</span>
                </a>

                <a href="#" class="nav-item" data-page="logs">
                    <div class="nav-icon"><i class="fas fa-clipboard-list"></i></div>
                    <span>操作日志</span>
                </a>
            </nav>
        </aside>

        <!-- ===== 主内容区域 ===== -->
        <!-- 右侧主要内容显示区域，根据左侧导航切换不同页面 -->
        <main class="main-content">

            <!-- ===== 动态页面内容容器 ===== -->
            <!-- 这个容器的内容会根据用户点击的导航项动态更新 -->
            <div class="page-content" id="pageContent">
                <!-- 页面内容将通过JavaScript动态加载 -->
            </div>
        </main>
    </div>

    <!-- 🚀 JavaScript -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="config-manager.js"></script>
    <script src="bit-browser-debug.js"></script>
    <script src="bit-browser-api.js"></script>
    <script src="premium-app.js"></script>
    <script>
        // 桌面端初始化
        document.addEventListener('DOMContentLoaded', () => {
            try {
                console.log('🖥️ 开始初始化桌面端应用...');

                // 检查必要的类是否存在
                if (typeof PremiumApp === 'undefined') {
                    throw new Error('PremiumApp 类未找到');
                }
                if (typeof GroupManager === 'undefined') {
                    throw new Error('GroupManager 类未找到');
                }

                // 创建应用实例
                window.app = new PremiumApp();
                window.groupManager = new GroupManager();

                console.log('✅ 应用实例创建成功');

                // 初始化应用
                window.app.init();

                console.log('🎉 桌面端应用初始化完成');

            } catch (error) {
                console.error('❌ 桌面端应用初始化失败:', error);
                alert('应用初始化失败，请刷新页面重试');
            }
        });


    </script>
</body>
</html>
