/* ===== 黑默科技桌面端营销管理平台样式文件 =====
 *
 * 这个文件包含了桌面端应用的所有样式定义
 * 采用现代化设计系统，支持深色主题和响应式布局
 *
 * 主要特点：
 * - 企业级设计风格
 * - 深色主题配色
 * - 响应式布局
 * - 动画效果
 * - 组件化样式
 *
 * 文件结构：
 * 1. 设计令牌（颜色、字体、间距等）
 * 2. 基础样式重置
 * 3. 布局组件样式
 * 4. 功能组件样式
 * 5. 动画效果
 * 6. 响应式媒体查询
 *
 * 🎨 Premium Commercial UI Design System
 */

/* ===== 设计令牌 (Design Tokens) ===== */
:root {
    /* 🎯 商业化色彩系统 */
    --primary-50: #f8fafc;
    --primary-100: #f1f5f9;
    --primary-500: #475569;
    --primary-600: #334155;
    --primary-700: #1e293b;
    --primary-900: #0f172a;
    
    /* 🌈 专业商务色彩 */
    --success: #16a34a;
    --warning: #ea580c;
    --error: #dc2626;
    --info: #6366f1;
    
    /* 🎨 中性色彩 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 📐 间距系统 */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    
    /* 🔤 字体系统 */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    
    /* 📏 字体大小 */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    
    /* 🎭 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* 🔄 动画系统 */
    --transition-fast: 150ms ease-out;
    --transition-base: 250ms ease-out;
    --transition-slow: 350ms ease-out;
    
    /* 📱 断点系统 */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
}

/* ===== 基础重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-sans);
    background: var(--gray-50);
    color: var(--gray-900);
    overflow-x: hidden;
}

/* ===== 布局系统 ===== */
.app-container {
    display: flex;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 🎯 紧凑商业化侧边栏 */
.sidebar {
    width: 220px;
    background: #ffffff;
    border-right: 1px solid #e2e8f0;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 10;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

/* 🏢 紧凑品牌区域 */
.brand-section {
    padding: 20px 16px;
    border-bottom: 1px solid #f1f5f9;
    position: relative;
    z-index: 2;
    background: #ffffff;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.brand-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #1a1a1a, #0a0a0a);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-xl);
    box-shadow: var(--shadow-lg);
    padding: 6px;
    border: 1px solid rgba(255,255,255,0.1);
}

.logo-svg {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.brand-text {
    flex: 1;
}

.brand-title {
    font-size: var(--text-lg);
    font-weight: 700;
    color: var(--gray-900);
    line-height: 1.2;
}

.brand-subtitle {
    font-size: var(--text-sm);
    color: var(--gray-500);
    font-weight: 500;
}

/* 🧭 紧凑导航系统 */
.navigation {
    flex: 1;
    padding: 16px 12px;
    overflow-y: auto;
    position: relative;
    z-index: 2;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.navigation::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}



.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    margin-bottom: 1px;
    border-radius: 6px;
    color: #64748b;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.15s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border-left: 3px solid transparent;
    letter-spacing: 0.01em;
    border: 1px solid transparent;
}

.nav-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 0;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: 0 2px 2px 0;
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item:hover::before {
    height: 24px;
}

.nav-item:hover {
    background: #f8fafc;
    color: #1e293b;
    border-left: 3px solid #475569;
    border: 1px solid #e2e8f0;
    font-weight: 600;
}

.nav-item.active {
    background: #334155;
    color: white;
    border-left: 3px solid #1e293b;
    border: 1px solid #475569;
    font-weight: 600;
}

.nav-item.active::before {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-400), var(--primary-500));
    width: 4px;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: var(--primary-400);
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(79, 70, 229, 0.6);
}

.nav-icon {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    opacity: 0.8;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.nav-item:hover .nav-icon {
    opacity: 1;
}

.nav-item.active .nav-icon {
    opacity: 1;
}

/* 专业商务点击效果 */
.nav-item:active {
    background: #e2e8f0;
    transition: all 0.1s ease;
}



/* 🎯 简洁主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8fafc;
    position: relative;
    overflow: hidden; /* 防止出现滚动条 */
}

/* 🎪 顶部工具栏已删除 - 页面内容直接显示 */

/* 📄 简洁页面内容 */
.page-content {
    flex: 1;
    padding: 32px 24px;
    overflow-y: auto;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.page-content::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 🔍 全局滚动条隐藏 */
* {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

*::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 确保body和html也隐藏滚动条 */
html, body {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
    display: none;
}

/* 🎯 简洁商业化设计 - 最小化动画 */

/* 🎨 按钮系统 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: 8px;
    font-size: var(--text-sm);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-200);
}

.btn-secondary:hover {
    background: var(--gray-200);
    border-color: var(--gray-300);
}

.btn-ghost {
    background: transparent;
    color: var(--gray-600);
}

.btn-ghost:hover {
    background: var(--gray-100);
    color: var(--gray-800);
}

/* 📊 指标卡片系统 - 8卡片布局 */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-4);
    margin-bottom: var(--space-6);
}

.metric-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-500);
}

.metric-card.metric-success::before { background: #16a34a; }
.metric-card.metric-warning::before { background: #ea580c; }
.metric-card.metric-info::before { background: #6366f1; }
.metric-card.metric-secondary::before { background: #6b7280; }
.metric-card.metric-dark::before { background: #374151; }
.metric-card.metric-purple::before { background: #7c3aed; }
.metric-card.metric-teal::before { background: #0891b2; }

.metric-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
    border-color: #cbd5e1;
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    background: var(--primary-500);
}

.metric-primary .metric-icon { background: #475569; }
.metric-success .metric-icon { background: #16a34a; }
.metric-warning .metric-icon { background: #ea580c; }
.metric-info .metric-icon { background: #6366f1; }
.metric-secondary .metric-icon { background: #6b7280; }
.metric-dark .metric-icon { background: #374151; }
.metric-purple .metric-icon { background: #7c3aed; }
.metric-teal .metric-icon { background: #0891b2; }

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    line-height: 1;
    margin-bottom: 4px;
}

.metric-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 4px;
}

.metric-change {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: var(--text-xs);
    font-weight: 600;
}

.metric-change.positive {
    color: var(--success);
}

.metric-change.negative {
    color: var(--error);
}

/* 📈 图表区域 - 紧凑版 */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
}

.chart-card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-100);
    overflow: hidden;
}

.chart-header {
    padding: var(--space-4) var(--space-5);
    border-bottom: 1px solid var(--gray-100);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3 {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--gray-900);
}

.chart-content {
    padding: var(--space-4);
    height: 240px;
}

.chart-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    text-align: center;
}

.chart-placeholder i {
    font-size: var(--text-4xl);
    margin-bottom: var(--space-3);
}

/* 🥧 扇形图样式 */
.pie-chart-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
}

.pie-chart {
    width: 120px;
    height: 120px;
    flex-shrink: 0;
}

.pie-slice {
    transition: all 0.3s ease;
    cursor: pointer;
}

.pie-slice:hover {
    opacity: 0.8;
    transform-origin: center;
}

.pie-legend {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #64748b;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
}

.legend-text {
    font-weight: 500;
}

/* 🎯 扇形图提示框 */
.pie-tooltip {
    position: absolute;
    background: #1e293b;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1000;
    transform: translateX(-50%) translateY(-100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pie-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #1e293b;
}

.tooltip-platform {
    font-weight: 600;
    margin-bottom: 2px;
}

.tooltip-value {
    font-size: 11px;
    opacity: 0.9;
}

/* 📋 活动区域 - 紧凑版 */
.activity-section {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-100);
    overflow: hidden;
}

.section-header {
    padding: var(--space-4) var(--space-5);
    border-bottom: 1px solid var(--gray-100);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h3 {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--gray-900);
}

.activity-list {
    padding: var(--space-3);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-3);
    border-radius: 12px;
    transition: all var(--transition-base);
}

.activity-item:hover {
    background: var(--gray-50);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-sm);
}

.activity-icon.success { background: var(--success); }
.activity-icon.info { background: var(--info); }
.activity-icon.warning { background: var(--warning); }
.activity-icon.error { background: var(--error); }

.activity-content {
    flex: 1;
}

.activity-text {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.activity-time {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

/* 🔍 筛选区域 */
.filters-section {
    display: flex;
    gap: var(--space-4);
    align-items: center;
    margin-bottom: var(--space-6);
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    left: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
}

.search-box input {
    width: 100%;
    padding: var(--space-3) var(--space-4) var(--space-3) var(--space-10);
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    font-size: var(--text-sm);
    background: white;
    transition: all var(--transition-base);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.filter-buttons {
    display: flex;
    gap: var(--space-2);
}

/* 📊 表格系统 */
.accounts-table {
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-100);
    overflow: hidden;
}

.table-header {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--gray-100);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
}

.table-content {
    padding: var(--space-8);
}

.table-placeholder {
    text-align: center;
    padding: var(--space-12);
}

.table-placeholder i {
    font-size: 4rem;
    color: var(--gray-300);
    margin-bottom: var(--space-4);
}

.table-placeholder h3 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.table-placeholder p {
    color: var(--gray-600);
    margin-bottom: var(--space-6);
}

/* 🎯 功能占位符 */
.feature-placeholder {
    text-align: center;
    padding: var(--space-16);
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-100);
}

.feature-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto var(--space-6);
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--primary-500);
}

.feature-placeholder h2 {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-3);
}

.feature-placeholder p {
    font-size: var(--text-lg);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
}

.feature-actions {
    display: flex;
    gap: var(--space-3);
    justify-content: center;
}

/* 🎨 按钮尺寸变体 */
.btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
}

.btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: var(--text-lg);
}

/* 📱 响应式设计 */
@media (max-width: 1024px) {
    .sidebar {
        width: 200px;
    }

    .page-content {
        padding: var(--space-4);
    }

    .charts-section {
        grid-template-columns: 1fr;
    }

    .metrics-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
    }

    .toolbar {
        padding: var(--space-3) var(--space-4);
    }

    .page-title {
        font-size: var(--text-xl);
    }

    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 🎯 平台选择标签 */
.platform-tabs {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--gray-200);
    padding-bottom: 16px;
}

.platform-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: var(--gray-600);
}

.platform-tab:hover {
    background: var(--primary-500);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.platform-tab.active {
    background: var(--primary-500);
    color: white;
    border-color: var(--primary-500);
}

.platform-tab .tab-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.platform-tab.active .tab-count {
    background: rgba(255, 255, 255, 0.3);
}

/* 📊 账号分组统计 */
.account-groups-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.group-stat-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 16px;
}

.group-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-500);
}

.group-stat-card.active {
    border-color: var(--primary-500);
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
}

.group-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.group-icon.all-accounts { background: linear-gradient(135deg, #667eea, #764ba2); color: white; }
.group-icon.login-accounts { background: linear-gradient(135deg, #11998e, #38ef7d); color: white; }
.group-icon.publish-accounts { background: linear-gradient(135deg, #ff6b6b, #ffa726); color: white; }
.group-icon.tool-accounts { background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; }
.group-icon.network-error { background: linear-gradient(135deg, #ff9a9e, #fecfef); color: white; }
.group-icon.device-offline { background: linear-gradient(135deg, #a8edea, #fed6e3); color: white; }

.group-content {
    flex: 1;
}

.group-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 4px;
}

.group-label {
    font-size: 14px;
    opacity: 0.8;
}

.group-stat-card.active .group-icon {
    background: rgba(255, 255, 255, 0.2);
}

/* 🔍 搜索和操作栏 */
.accounts-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 12px;
}

.accounts-toolbar .search-box {
    flex: 1;
    max-width: 400px;
    position: relative;
}

.accounts-toolbar .search-box input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    font-size: 14px;
}

.accounts-toolbar .search-box i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
}

.toolbar-actions {
    display: flex;
    gap: 12px;
}

/* 📋 账号管理表格 */
.accounts-management-table {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    overflow: hidden;
}

.table-container {
    overflow-x: auto;
}

.accounts-table {
    width: 100%;
    border-collapse: collapse;
}

.accounts-table th,
.accounts-table td {
    padding: 16px 12px;
    text-align: left;
    border-bottom: 1px solid var(--gray-100);
}

.accounts-table th {
    background: var(--gray-50);
    font-weight: 600;
    font-size: 14px;
    color: var(--gray-600);
}

.accounts-table tbody tr:hover {
    background: var(--gray-50);
}

/* 账号头像 */
.account-avatar {
    position: relative;
    display: flex;
    align-items: center;
}

.account-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.account-avatar i {
    position: absolute;
    bottom: -2px;
    right: -2px;
    background: white;
    border-radius: 50%;
    padding: 2px;
    font-size: 12px;
}

/* 状态徽章 */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.status-badge.online {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-badge.offline {
    background: #ffebee;
    color: #c62828;
}

.status-badge.error {
    background: #fff3e0;
    color: #ef6c00;
}

/* App和Web状态 */
.app-status, .web-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.app-status.normal, .web-status.normal {
    background: #e8f5e8;
    color: #2e7d32;
}

.app-status.error, .web-status.error {
    background: #ffebee;
    color: #c62828;
}

/* 统计数据 */
.proxy-stats, .publish-stats {
    font-size: 13px;
}

.proxy-stats .success, .publish-stats .success {
    color: #2e7d32;
    font-weight: 600;
}

.proxy-stats .fail, .publish-stats .fail {
    color: #c62828;
    font-weight: 600;
}

/* 操作按钮 */
.account-actions {
    display: flex;
    gap: 8px;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--gray-100);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--gray-600);
}

.btn-icon:hover {
    background: var(--primary-500);
    color: white;
    transform: scale(1.1);
}

.btn-icon.danger:hover {
    background: #f44336;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--gray-400);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--gray-700);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--gray-200);
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(71, 85, 105, 0.1);
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    padding: 16px 20px;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1001;
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    border-left: 4px solid #16a34a;
}

.notification.error {
    border-left: 4px solid #dc2626;
}

.notification.info {
    border-left: 4px solid #6366f1;
}

.notification i {
    font-size: 18px;
}

.notification.success i {
    color: #16a34a;
}

.notification.error i {
    color: #dc2626;
}

.notification.info i {
    color: #6366f1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== 紧凑型账号管理样式 ===== */
.detailed-accounts-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    padding: var(--space-4);
}

.detailed-account-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    padding: var(--space-4);
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.detailed-account-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.account-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid var(--gray-100);
}

.account-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: var(--info);
}

.account-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--gray-200);
}

.account-basic-info h3 {
    margin: 0 0 2px 0;
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--gray-900);
}

.platform-info {
    margin: 0 0 4px 0;
    color: var(--gray-600);
    font-size: var(--text-xs);
}

.account-bio {
    margin: 0 0 6px 0;
    color: var(--gray-500);
    font-size: var(--text-xs);
    line-height: 1.3;
    font-style: italic;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: var(--text-xs);
    font-weight: 500;
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
}

.account-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
}

.detail-section {
    background: var(--gray-50);
    border-radius: 6px;
    padding: var(--space-3);
}

.detail-section h4 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--gray-800);
    border-bottom: 1px solid var(--info);
    padding-bottom: var(--space-1);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    border-bottom: 1px solid var(--gray-200);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    font-weight: 500;
    color: var(--gray-600);
    font-size: var(--text-xs);
    min-width: 60px;
}

.detail-row .value {
    font-weight: 400;
    color: var(--gray-800);
    font-size: var(--text-xs);
    text-align: right;
    flex: 1;
}

.status-success {
    color: var(--success) !important;
    font-weight: 600;
}

.status-error {
    color: var(--error) !important;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: var(--space-2);
}

.action-btn {
    padding: 4px 8px;
    border: 1px solid var(--gray-300);
    border-radius: 4px;
    background: white;
    color: var(--gray-700);
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.action-btn:hover {
    background: var(--gray-50);
    border-color: var(--info);
    color: var(--info);
}

.action-btn.primary {
    background: var(--info);
    color: white;
    border-color: var(--info);
}

.action-btn.primary:hover {
    background: #4f46e5;
    border-color: #4f46e5;
}

/* ===== 中等尺寸账号卡片样式 ===== */
.compact-accounts-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    padding: var(--space-5);
}

.compact-account-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.compact-account-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    transform: translateY(-1px);
}

.account-row {
    display: grid;
    grid-template-columns: 240px 1fr 180px;
    gap: var(--space-5);
    padding: var(--space-5);
    align-items: start;
}

.account-basic {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.account-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--gray-200);
}

.account-info {
    flex: 1;
}

.account-name {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 4px;
}

.account-platform {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin-bottom: 4px;
}

.account-description {
    font-size: var(--text-sm);
    color: var(--gray-500);
    margin-bottom: 6px;
    line-height: 1.3;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.account-group-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
    margin-bottom: 4px;
}

.account-group-tag i {
    font-size: 10px;
}

.account-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: var(--text-sm);
}

.account-details {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-4);
}

.detail-group h5 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-300);
    padding-bottom: var(--space-1);
}

.detail-grid {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--text-xs);
    padding: 2px 0;
}

.detail-item .label {
    color: var(--gray-600);
    font-weight: 500;
    min-width: 80px;
}

.detail-item .value {
    color: var(--gray-800);
    text-align: right;
    flex: 1;
    font-weight: 400;
}

.detail-item .value.success {
    color: var(--success);
    font-weight: 600;
}

.detail-item .value.error {
    color: var(--error);
    font-weight: 600;
}

.account-actions h5 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-300);
    padding-bottom: var(--space-1);
}

.action-buttons-compact {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.action-btn-compact {
    padding: 6px 10px;
    border: 1px solid var(--gray-300);
    border-radius: 4px;
    background: white;
    color: var(--gray-700);
    font-size: var(--text-xs);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    font-weight: 500;
}

.action-btn-compact:hover {
    background: var(--gray-50);
    border-color: var(--info);
    color: var(--info);
}

.action-btn-compact.primary {
    background: var(--info);
    color: white;
    border-color: var(--info);
}

.action-btn-compact.primary:hover {
    background: #4f46e5;
}

/* ===== 紧凑型卡片响应式设计 ===== */
@media (max-width: 1200px) {
    .account-row {
        grid-template-columns: 180px 1fr 120px;
        gap: var(--space-2);
    }

    .account-details {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .account-row {
        grid-template-columns: 1fr;
        gap: var(--space-2);
    }

    .account-basic {
        justify-content: space-between;
        padding-bottom: var(--space-2);
        border-bottom: 1px solid var(--gray-200);
    }

    .account-details {
        grid-template-columns: 1fr;
        gap: var(--space-2);
    }

    .account-actions {
        border-top: 1px solid var(--gray-200);
        padding-top: var(--space-2);
    }

    .action-buttons-compact {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 4px;
    }

    .action-btn-compact {
        flex: 1;
        min-width: 80px;
    }
}

@media (max-width: 480px) {
    .compact-accounts-container {
        padding: var(--space-2);
    }

    .account-row {
        padding: var(--space-2);
    }

    .account-avatar {
        width: 28px;
        height: 28px;
    }

    .account-name {
        font-size: 11px;
    }

    .account-platform,
    .account-status {
        font-size: 9px;
    }

    .detail-item {
        font-size: 9px;
    }

    .action-btn-compact {
        font-size: 8px;
        padding: 2px 4px;
    }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .detailed-accounts-container {
        padding: var(--space-4);
        gap: var(--space-4);
    }

    .detailed-account-card {
        padding: var(--space-4);
    }

    .account-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-3);
    }

    .account-details-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-1);
    }

    .detail-row .value {
        text-align: left;
    }

    .action-buttons {
        justify-content: center;
    }

    .action-btn {
        flex: 1;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .account-avatar {
        width: 50px;
        height: 50px;
    }

    .account-basic-info h3 {
        font-size: var(--text-lg);
    }

    .detail-section {
        padding: var(--space-4);
    }

    .action-btn {
        font-size: 11px;
        padding: var(--space-1) var(--space-3);
    }
}

/* ===== 动画效果 ===== */
@keyframes cardFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

.detailed-account-card {
    animation: cardFadeIn 0.5s ease-out;
}

.detailed-account-card:nth-child(1) { animation-delay: 0.1s; }
.detailed-account-card:nth-child(2) { animation-delay: 0.2s; }
.detailed-account-card:nth-child(3) { animation-delay: 0.3s; }

.status-dot {
    animation: statusPulse 2s infinite;
}

.action-btn {
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

/* 加载状态 */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* ===== 详细账号表格样式（参考例图） ===== */
.detailed-accounts-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.table-header {
    display: grid;
    grid-template-columns: 300px 200px 200px 120px 200px;
    background: #f5f5f5;
    border-bottom: 2px solid #e0e0e0;
}

.header-cell {
    padding: 12px 15px;
    font-weight: 600;
    font-size: 14px;
    color: #333;
    border-right: 1px solid #e0e0e0;
    text-align: center;
}

.header-cell:last-child {
    border-right: none;
}

.detailed-account-row {
    display: grid;
    grid-template-columns: 300px 200px 200px 120px 200px;
    border-bottom: 1px solid #e0e0e0;
    min-height: 120px;
}

.detailed-account-row:hover {
    background: #f9f9f9;
}

.account-info-cell {
    padding: 15px;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.account-main-info {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.account-checkbox {
    margin-top: 5px;
}

.account-avatar-detailed {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.account-details-text {
    flex: 1;
    font-size: 12px;
    line-height: 1.4;
}

.account-name-detailed {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-bottom: 3px;
}

.account-platform-detailed {
    color: #666;
    margin-bottom: 3px;
}

.account-id, .account-uid, .account-intro,
.account-device-id, .account-register, .account-platform-id {
    color: #888;
    margin-bottom: 2px;
}

.account-note {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
}

.note-label {
    color: #666;
    white-space: nowrap;
}

.note-input {
    flex: 1;
    padding: 4px 6px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 12px;
}

.save-note-btn {
    padding: 4px 8px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 3px;
    font-size: 12px;
    cursor: pointer;
}

.status-cell, .device-cell, .data-cell, .action-cell {
    padding: 15px;
    border-right: 1px solid #e0e0e0;
    font-size: 12px;
    line-height: 1.4;
}

.action-cell {
    border-right: none;
}

.status-cell div, .device-cell div, .data-cell div {
    margin-bottom: 3px;
    color: #666;
}

.status-label {
    color: #333 !important;
    font-weight: 500;
}

.success-text {
    color: #28a745;
}

.status-normal {
    color: #28a745;
}

.status-error {
    color: #dc3545;
}

.status-refresh, .status-acid, .status-click {
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
}

.action-btn {
    display: block;
    width: 100%;
    padding: 6px 8px;
    margin-bottom: 4px;
    border: 1px solid #ddd;
    background: white;
    color: #333;
    font-size: 12px;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #f0f0f0;
    border-color: #007bff;
}

.action-btn.primary {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.action-btn.primary:hover {
    background: #0056b3;
}

/* ===== 分组管理样式 ===== */
.group-management-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 20px 0;
    overflow: hidden;
}

.group-panel-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-panel-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.group-list {
    padding: 15px 20px;
    max-height: 400px;
    overflow-y: auto;
}

.group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.2s ease;
}

.group-item:hover {
    background: #f8f9fa;
    margin: 0 -20px;
    padding: 12px 20px;
}

.group-item:last-child {
    border-bottom: none;
}

.group-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.group-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
}

.group-details {
    flex: 1;
}

.group-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
}

.group-count {
    font-size: 12px;
    color: #666;
}

.group-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.group-item:hover .group-actions {
    opacity: 1;
}

.group-action-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 4px;
    background: #f8f9fa;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.group-action-btn:hover {
    background: #e9ecef;
    color: #333;
}

.group-action-btn.delete:hover {
    background: #dc3545;
    color: white;
}

.group-add {
    padding: 15px 20px;
    border-top: 1px solid #f0f0f0;
}

.add-group-btn {
    width: 100%;
    padding: 10px;
    border: 2px dashed #ddd;
    border-radius: 6px;
    background: transparent;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.add-group-btn:hover {
    border-color: #007bff;
    color: #007bff;
    background: #f8f9ff;
}

/* 分组模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 20px 20px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: #f8f9fa;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group input[type="text"]:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.color-picker {
    display: flex;
    align-items: center;
    gap: 15px;
}

.color-picker input[type="color"] {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.color-presets {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.color-preset {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.color-preset:hover {
    border-color: #333;
    transform: scale(1.1);
}

.modal-footer {
    padding: 0 20px 20px 20px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-secondary {
    background: white;
    color: #666;
}

.btn-secondary:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

.btn-primary {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

/* ===== 通知样式 ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 10000;
    min-width: 300px;
    max-width: 400px;
    animation: slideInRight 0.3s ease-out;
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-error {
    border-left: 4px solid #dc3545;
}

.notification-warning {
    border-left: 4px solid #ffc107;
}

.notification-info {
    border-left: 4px solid #17a2b8;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.notification-success .notification-content i {
    color: #28a745;
}

.notification-error .notification-content i {
    color: #dc3545;
}

.notification-warning .notification-content i {
    color: #ffc107;
}

.notification-info .notification-content i {
    color: #17a2b8;
}

.notification-close {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: #f0f0f0;
    color: #333;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== 按钮组样式 ===== */
.btn-group {
    display: flex;
    gap: 4px;
    margin-right: 12px;
}

.btn-group .btn {
    border-radius: 4px;
    font-size: 12px;
    padding: 6px 12px;
}

.btn-group .btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group .btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group .btn:not(:first-child):not(:last-child) {
    border-radius: 0;
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    color: #212529;
}

/* ===== 平台分组样式增强 ===== */
.platform-group-section {
    background: #fafafa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.platform-header {
    background: white;
    padding: 12px 16px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.group-list .group-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.2s ease;
}

.group-list .group-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* ===== 分组下拉菜单样式 ===== */
.group-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}

.group-dropdown.show {
    display: block;
}

.group-section-header {
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-size: 11px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.group-option {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.group-option:last-child {
    border-bottom: none;
}

.group-option:hover {
    background-color: #f8f9fa;
}

.group-option-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
}

.group-option span {
    flex: 1;
    font-size: 13px;
    color: #333;
}

.group-option small {
    font-size: 11px;
    color: #666;
    margin-left: 8px;
}

/* ===== 自定义分组模态框样式 ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.modal-close:hover {
    background-color: #e9ecef;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
}

.form-group small {
    color: #666;
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

.color-picker {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.color-option {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.selected {
    border-color: #333 !important;
    transform: scale(1.1);
}

.color-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 0 0 2px rgba(0,0,0,0.5);
}

/* ===== 分组选择器样式 ===== */
.group-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 15px;
}

.group-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.group-item:last-child {
    border-bottom: none;
}

.group-item:hover {
    background-color: #f8f9fa;
}

.group-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 12px;
    flex-shrink: 0;
}

.group-name {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.custom-indicator {
    color: #ffd700;
    font-size: 12px;
    margin-left: 8px;
}

.group-actions {
    text-align: center;
}

/* ===== 通知样式 ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10001;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-error {
    border-left: 4px solid #dc3545;
}

.notification-info {
    border-left: 4px solid #007bff;
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 15px;
    gap: 10px;
}

.notification-content i {
    font-size: 18px;
}

.notification-success .notification-content i {
    color: #28a745;
}

.notification-error .notification-content i {
    color: #dc3545;
}

.notification-info .notification-content i {
    color: #007bff;
}

.notification.fade-out {
    animation: slideOut 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* ===== 分组按钮样式 ===== */
.action-btn-compact.group-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.action-btn-compact.group-btn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

/* ===== 比特浏览器管理界面样式 ===== */
.bit-browser-manager-modal .modal-content {
    max-width: 800px;
    width: 90vw;
}

.browser-manager-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

.tab-btn {
    padding: 12px 20px;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #666;
}

.tab-btn.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.tab-btn:hover {
    background: rgba(0, 123, 255, 0.05);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 状态信息样式 */
.status-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 600;
    color: #333;
}

.status-value {
    color: #666;
    font-family: monospace;
}

.status-value.status-success {
    color: #28a745;
}

.status-value.status-error {
    color: #dc3545;
}

.status-actions {
    display: flex;
    gap: 10px;
}

/* 浏览器列表样式 */
.browser-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.browser-list-header h4 {
    margin: 0;
    color: #333;
}

.browser-list {
    max-height: 400px;
    overflow-y: auto;
}

.browser-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 10px;
    background: white;
    transition: box-shadow 0.2s ease;
}

.browser-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.browser-info h5 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 16px;
}

.browser-info p {
    margin: 0 0 5px 0;
    color: #666;
    font-size: 14px;
}

.browser-id {
    font-size: 12px;
    color: #999;
    font-family: monospace;
}

.browser-actions {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* 账号绑定样式 */
.account-browser-binding h4 {
    margin: 0 0 10px 0;
    color: #333;
}

.account-browser-binding p {
    color: #666;
    margin-bottom: 20px;
}

.account-binding-list {
    max-height: 400px;
    overflow-y: auto;
}

/* 加载和空状态样式 */
.loading, .empty-state, .error-state {
    text-align: center;
    padding: 40px;
    color: #666;
}

.error-state {
    color: #dc3545;
}

/* 信息按钮样式 */
.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border: none;
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    transform: translateY(-1px);
}

/* 备注输入框改进 */
.note-input {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
}

.note-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.save-note-btn {
    padding: 6px 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.save-note-btn:hover {
    background: #0056b3;
}

.save-note-btn:active {
    transform: translateY(1px);
}

/* ===== 桌面端分组管理样式 ===== */
.desktop-group-management {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.content-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.desktop-content-grid {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 20px;
    flex: 1;
    min-height: 0;
}

.groups-panel, .accounts-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.group-count {
    font-size: 12px;
    color: #666;
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 12px;
}

.groups-list, .accounts-list {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}

.filter-tabs {
    display: flex;
    gap: 5px;
}

.filter-tab {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    border-radius: 15px;
    background: white;
    color: #495057;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.filter-tab:hover,
.filter-tab.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.desktop-account-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.2s ease;
}

.desktop-account-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.desktop-account-card .account-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
}

.desktop-account-card .account-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
}

.desktop-account-card .account-info {
    flex: 1;
}

.desktop-account-card .account-info h4 {
    margin: 0 0 4px 0;
    font-size: 15px;
    font-weight: 600;
    color: #333;
}

.desktop-account-card .account-platform {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.desktop-account-card .account-group-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
}

.desktop-account-card .account-status {
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.desktop-account-card .account-status.online {
    color: #28a745;
}

.desktop-account-card .account-status.offline {
    color: #dc3545;
}

.desktop-account-card .account-description {
    font-size: 13px;
    color: #666;
    margin-bottom: 10px;
    line-height: 1.4;
}

.desktop-account-card .account-note {
    background: #f8f9fa;
    padding: 8px 10px;
    border-radius: 4px;
    margin-bottom: 10px;
    font-size: 12px;
    color: #555;
    display: flex;
    align-items: center;
    gap: 6px;
}

.desktop-account-card .account-note i {
    color: #ffc107;
}

.desktop-account-card .account-actions {
    display: flex;
    gap: 8px;
}

.desktop-account-card .action-btn {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
    color: #495057;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.desktop-account-card .action-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
}

.desktop-account-card .group-selector {
    position: relative;
}

.desktop-account-card .group-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 100;
    display: none;
    max-height: 150px;
    overflow-y: auto;
}

.desktop-account-card .group-dropdown.show {
    display: block;
}

.desktop-account-card .group-option {
    padding: 8px 10px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: background 0.2s ease;
}

.desktop-account-card .group-option:hover {
    background: #f8f9fa;
}

.desktop-account-card .group-option-color {
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

/* ===== 小红书管理模块样式 ===== */
.xiaohongshu-management-container {
    padding: 0;
    background: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
}

.xiaohongshu-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    padding: 24px;
    color: white;
}

.xiaohongshu-title {
    display: flex;
    align-items: center;
    gap: 16px;
}

.xiaohongshu-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.xiaohongshu-title h2 {
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 4px 0;
}

.xiaohongshu-title p {
    font-size: 14px;
    opacity: 0.9;
    margin: 0;
}

/* 统计卡片 */
.xiaohongshu-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    padding: 24px;
    background: white;
}

.xiaohongshu-stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #eee;
    transition: all 0.3s ease;
}

.xiaohongshu-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.xiaohongshu-stat-card .stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.xiaohongshu-stat-card .stat-title {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.xiaohongshu-stat-card .stat-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.xiaohongshu-stat-card .stat-icon.views { background: linear-gradient(135deg, #667eea, #764ba2); }
.xiaohongshu-stat-card .stat-icon.likes { background: linear-gradient(135deg, #f093fb, #f5576c); }
.xiaohongshu-stat-card .stat-icon.comments { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.xiaohongshu-stat-card .stat-icon.shares { background: linear-gradient(135deg, #43e97b, #38f9d7); }

.xiaohongshu-stat-card .stat-value {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
}

.xiaohongshu-stat-card .stat-growth {
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.xiaohongshu-stat-card .stat-growth.positive { color: #27ae60; }
.xiaohongshu-stat-card .stat-growth.negative { color: #e74c3c; }

/* 功能按钮 */
.xiaohongshu-action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    padding: 0 24px 24px;
    background: white;
}

.xiaohongshu-action-btn {
    background: white;
    border: 1px solid #eee;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.xiaohongshu-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: #667eea;
}

.xiaohongshu-action-btn .action-btn-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    color: white;
    font-size: 20px;
}

.xiaohongshu-action-btn .action-btn-icon.analysis { background: linear-gradient(135deg, #667eea, #764ba2); }
.xiaohongshu-action-btn .action-btn-icon.top { background: linear-gradient(135deg, #f093fb, #f5576c); }
.xiaohongshu-action-btn .action-btn-icon.privacy { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.xiaohongshu-action-btn .action-btn-icon.publish { background: linear-gradient(135deg, #43e97b, #38f9d7); }
.xiaohongshu-action-btn .action-btn-icon.browser { background: linear-gradient(135deg, #fa709a, #fee140); }

.xiaohongshu-action-btn .action-btn-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.xiaohongshu-action-btn .action-btn-desc {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 笔记列表 */
.xiaohongshu-notes-section {
    background: white;
    padding: 24px;
}

.xiaohongshu-notes-section .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.xiaohongshu-notes-section .section-title {
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.xiaohongshu-notes-section .filter-buttons {
    display: flex;
    gap: 8px;
}

.xiaohongshu-notes-section .filter-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    background: #f8f9fa;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.xiaohongshu-notes-section .filter-btn.active {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.xiaohongshu-notes-grid {
    display: grid;
    gap: 16px;
}

.xiaohongshu-note-card {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #eee;
    transition: all 0.3s ease;
}

.xiaohongshu-note-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.xiaohongshu-note-card .note-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 12px;
}

.xiaohongshu-note-card .note-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    flex: 1;
    line-height: 1.4;
}

.xiaohongshu-note-card .note-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.xiaohongshu-note-card .note-status.published {
    background: #d4edda;
    color: #155724;
}

.xiaohongshu-note-card .note-status.draft {
    background: #fff3cd;
    color: #856404;
}

.xiaohongshu-note-card .note-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 12px;
    font-size: 14px;
    color: #666;
}

.xiaohongshu-note-card .note-stats {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.xiaohongshu-note-card .note-stat {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: #666;
}

.xiaohongshu-note-card .note-actions {
    display: flex;
    gap: 8px;
}

.xiaohongshu-note-card .note-action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.xiaohongshu-note-card .note-action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.xiaohongshu-note-card .note-action-btn.secondary {
    background: #f8f9fa;
    color: #666;
}

.xiaohongshu-note-card .note-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.xiaohongshu-note-card .note-top-badge {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

/* 小红书账号卡片样式 */
.xiaohongshu-account-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #eee;
    margin-bottom: 16px;
    transition: all 0.3s ease;
}

.xiaohongshu-account-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.xiaohongshu-account-card .account-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.xiaohongshu-account-card .account-basic-info {
    display: flex;
    gap: 16px;
}

.xiaohongshu-account-card .account-avatar {
    position: relative;
    flex-shrink: 0;
}

.xiaohongshu-account-card .account-avatar img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.xiaohongshu-account-card .platform-badge.xiaohongshu {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    border: 2px solid white;
}

.xiaohongshu-account-card .account-info {
    flex: 1;
}

.xiaohongshu-account-card .account-name-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.xiaohongshu-account-card .account-nickname {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.xiaohongshu-account-card .account-status-badges {
    display: flex;
    gap: 8px;
}

.xiaohongshu-account-card .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.xiaohongshu-account-card .status-badge.success {
    background: #d4edda;
    color: #155724;
}

.xiaohongshu-account-card .status-badge.warning {
    background: #fff3cd;
    color: #856404;
}

.xiaohongshu-account-card .status-badge.error {
    background: #f8d7da;
    color: #721c24;
}

.xiaohongshu-account-card .data-source-badge {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    background: #e3f2fd;
    color: #1976d2;
}

.xiaohongshu-account-card .account-details {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.xiaohongshu-account-card .account-bio {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.xiaohongshu-account-card .account-content {
    padding: 20px;
}

.xiaohongshu-account-card .account-stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 20px;
}

.xiaohongshu-account-card .stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.xiaohongshu-account-card .stat-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.xiaohongshu-account-card .stat-icon.follow { background: linear-gradient(135deg, #667eea, #764ba2); }
.xiaohongshu-account-card .stat-icon.fans { background: linear-gradient(135deg, #f093fb, #f5576c); }
.xiaohongshu-account-card .stat-icon.likes { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.xiaohongshu-account-card .stat-icon.views { background: linear-gradient(135deg, #43e97b, #38f9d7); }

.xiaohongshu-account-card .stat-content {
    flex: 1;
}

.xiaohongshu-account-card .stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 2px;
}

.xiaohongshu-account-card .stat-label {
    font-size: 12px;
    color: #666;
}

.xiaohongshu-account-card .stat-growth {
    font-size: 11px;
    font-weight: 600;
    margin-top: 2px;
}

.xiaohongshu-account-card .stat-growth.positive { color: #27ae60; }
.xiaohongshu-account-card .stat-growth.negative { color: #e74c3c; }

.xiaohongshu-account-card .action-buttons-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.xiaohongshu-account-card .action-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.xiaohongshu-account-card .action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.xiaohongshu-account-card .action-btn:not(.primary) {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #eee;
}

.xiaohongshu-account-card .action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 空状态样式 */
.no-accounts-message {
    text-align: center;
    padding: 60px 20px;
}

.no-accounts-message .empty-state {
    max-width: 400px;
    margin: 0 auto;
}

.no-accounts-message .empty-state i {
    color: #ccc;
    margin-bottom: 20px;
}

.no-accounts-message .empty-state h3 {
    color: #666;
    margin-bottom: 12px;
}

.no-accounts-message .empty-state p {
    color: #999;
    margin-bottom: 24px;
}

/* 小红书管理响应式设计 */
@media (max-width: 768px) {
    .xiaohongshu-stats-grid {
        grid-template-columns: 1fr;
    }

    .xiaohongshu-action-buttons {
        grid-template-columns: repeat(2, 1fr);
    }

    .xiaohongshu-header {
        padding: 16px;
    }

    .xiaohongshu-notes-section {
        padding: 16px;
    }

    .xiaohongshu-account-card .account-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .xiaohongshu-account-card .action-buttons-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .xiaohongshu-account-card .account-name-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* 数据来源说明模态框样式 */
.data-source-info-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.data-source-explanation {
    padding: 0;
}

.data-source-explanation h4 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-size: 18px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.source-method {
    margin-bottom: 24px;
}

.method-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 12px;
    background: #f8f9fa;
}

.method-item.priority-1 {
    border-color: #3498db;
    background: #e3f2fd;
}

.method-icon {
    font-size: 24px;
    width: 40px;
    text-align: center;
}

.method-content {
    flex: 1;
}

.method-content h5 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 16px;
}

.method-content p {
    margin: 0 0 8px 0;
    color: #666;
    line-height: 1.4;
}

.method-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.online {
    background: #27ae60;
}

.status-indicator.offline {
    background: #e74c3c;
}

.setup-guide {
    margin-bottom: 24px;
}

.setup-steps {
    margin-top: 16px;
}

.step {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;
}

.step-number {
    width: 32px;
    height: 32px;
    background: #3498db;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.step-content h5 {
    margin: 0 0 4px 0;
    color: #2c3e50;
    font-size: 14px;
}

.step-content p {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

.step-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 8px 12px;
    margin-top: 8px;
    font-size: 12px;
    color: #856404;
}

.url-examples {
    margin-top: 8px;
}

.url-examples code {
    background: #f1f2f6;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #2c3e50;
    display: inline-block;
    margin-right: 8px;
}

.data-fields {
    margin-bottom: 16px;
}

.fields-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    margin-top: 16px;
}

.field-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 13px;
    color: #2c3e50;
}

.field-item i {
    color: #3498db;
    width: 16px;
}

@media (max-width: 768px) {
    .fields-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .setup-steps .step {
        flex-direction: column;
        gap: 8px;
    }

    .method-item {
        flex-direction: column;
        gap: 12px;
    }
}
