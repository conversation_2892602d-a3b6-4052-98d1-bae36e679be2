/* ===== 设置页面样式 ===== */
.settings-page {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.settings-header {
    margin-bottom: 30px;
    text-align: center;
}

.settings-header h2 {
    color: #333;
    margin-bottom: 10px;
    font-size: 28px;
}

.settings-header p {
    color: #666;
    font-size: 16px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.settings-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.settings-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.settings-card-header i {
    font-size: 22px;
}

.settings-card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.settings-card-body {
    padding: 24px;
}

.settings-card-body p {
    color: #666;
    margin-bottom: 20px;
    font-size: 14px;
    line-height: 1.5;
}

.settings-form {
    margin-bottom: 20px;
}

.settings-form .form-group {
    margin-bottom: 16px;
}

.settings-form label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.settings-form input,
.settings-form select {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.settings-form input:focus,
.settings-form select:focus {
    outline: none;
    border-color: #667eea;
}

.settings-form input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
    transform: scale(1.2);
}

/* 表单增强样式 */
.form-help {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    font-style: italic;
}

.input-group {
    display: flex;
    align-items: center;
    position: relative;
}

.input-group input {
    flex: 1;
    padding-right: 45px;
}

.btn-toggle-password {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: color 0.3s ease;
}

.btn-toggle-password:hover {
    color: #333;
    background: #f0f0f0;
}

.config-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    margin-top: 20px;
}

.config-info h5 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

.config-info ul {
    margin: 0;
    padding-left: 20px;
}

.config-info li {
    margin-bottom: 8px;
    font-size: 13px;
    line-height: 1.4;
    color: #555;
}

.config-info strong {
    color: #333;
}

/* ===== 通知系统样式 ===== */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    margin-bottom: 10px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    border-left: 4px solid #ddd;
}

.notification-success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.notification-error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.notification-warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.notification-info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.notification-content i {
    font-size: 16px;
}

.notification-success .notification-content i {
    color: #155724;
}

.notification-error .notification-content i {
    color: #721c24;
}

.notification-warning .notification-content i {
    color: #856404;
}

.notification-info .notification-content i {
    color: #0c5460;
}

.notification-content span {
    font-size: 14px;
    font-weight: 500;
}

.notification-success .notification-content span {
    color: #155724;
}

.notification-error .notification-content span {
    color: #721c24;
}

.notification-warning .notification-content span {
    color: #856404;
}

.notification-info .notification-content span {
    color: #0c5460;
}

.notification-close {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.notification-close:hover {
    background: rgba(0,0,0,0.1);
    color: #333;
}

.settings-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.settings-actions .btn {
    padding: 10px 18px;
    font-size: 14px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.status-display {
    margin-top: 16px;
    padding: 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
}

.status-loading {
    background: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.status-success {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.status-error {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}

.system-info {
    background: #f8f9fa;
    padding: 18px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 600;
    color: #333;
}

.info-value {
    color: #666;
    font-family: 'Courier New', monospace;
}

.config-display {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    font-size: 12px;
    font-family: 'Courier New', monospace;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
}

/* ===== 配置管理模态框样式 ===== */
.config-manager-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.config-tabs {
    display: flex;
    border-bottom: 2px solid #e1e5e9;
    margin-bottom: 24px;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
}

.config-tabs .tab-btn {
    padding: 12px 20px;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #666;
}

.config-tabs .tab-btn:hover {
    background: #e9ecef;
    color: #333;
}

.config-tabs .tab-btn.active {
    border-bottom-color: #667eea;
    color: #667eea;
    background: white;
}

.config-content {
    position: relative;
}

.config-panel {
    display: none;
    animation: slideIn 0.3s ease;
}

.config-panel.active {
    display: block;
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(10px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

.config-panel h4 {
    color: #333;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
}

/* ===== 按钮样式增强 ===== */
.btn {
    border: none;
    cursor: pointer;
    font-family: inherit;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: white;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
    }

    .settings-actions {
        flex-direction: column;
    }

    .config-tabs {
        flex-wrap: wrap;
    }

    .config-tabs .tab-btn {
        flex: 1;
        min-width: 120px;
    }

    .settings-page {
        padding: 15px;
    }

    .settings-card-body {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .settings-header h2 {
        font-size: 24px;
    }

    .settings-card-header {
        padding: 16px;
    }

    .settings-card-body {
        padding: 16px;
    }
}
