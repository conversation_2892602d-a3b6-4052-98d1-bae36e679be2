/* 终极解决方案 - 彻底消灭蓝色框框 */
*,
*::before,
*::after,
*:focus,
*:active,
*:hover,
*:visited {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
    -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-focus-ring-color: transparent !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 彻底移除所有元素的focus outline */
*:focus {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* 移除按钮和链接的focus outline */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus,
.nav-item:focus,
.btn:focus,
div:focus {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* 特别针对导航项 */
.nav-item {
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

.nav-item:focus,
.nav-item:active,
.nav-item:hover:focus {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* 移除webkit的tap highlight */
* {
    -webkit-tap-highlight-color: transparent !important;
    -webkit-focus-ring-color: transparent !important;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background: #ffffff;
    color: #2c3e50;
    overflow: hidden;
    font-weight: 400;
    letter-spacing: -0.01em;
}

.app-container {
    display: flex;
    height: 100vh;
}

/* 左侧导航栏 */
.sidebar {
    width: 160px;
    background: #ffffff;
    border-right: 1px solid #e8eaed;
    display: flex;
    flex-direction: column;
    box-shadow: none;
}

/* 品牌标识 - 纯白风格 */
.brand-header {
    display: flex;
    align-items: center;
    padding: 16px 12px;
    border-bottom: 1px solid #e8eaed;
    background: #ffffff;
}

.brand-icon {
    width: 32px;
    height: 32px;
    background: #6366f1;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    margin-right: 12px;
    flex-shrink: 0;
}

.brand-info {
    flex: 1;
}

.brand-title {
    font-size: 14px;
    font-weight: 500;
    color: #1a202c;
    line-height: 1.3;
    letter-spacing: -0.02em;
}

.brand-subtitle {
    font-size: 11px;
    color: #718096;
    line-height: 1.3;
    margin-top: 2px;
    font-weight: 400;
    letter-spacing: 0.01em;
}

/* 导航菜单 */
.nav-menu {
    flex: 1;
    padding: 4px 0;
    overflow-y: auto;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    color: #4a5568;
    font-size: 13px;
    font-weight: 400;
    letter-spacing: -0.01em;
    user-select: none;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: #f5f5f5;
    color: #2d3748;
    font-weight: 450;
}

.nav-item.active {
    background: #e3f2fd;
    color: #1976d2;
    border-left: 3px solid #1976d2;
    font-weight: 500;
}

.nav-item i {
    width: 14px;
    margin-right: 8px;
    text-align: center;
    font-size: 12px;
}

.nav-item span {
    flex: 1;
}

.expand-icon {
    font-size: 12px;
    transition: transform 0.2s ease;
}

.nav-item.expanded .expand-icon {
    transform: rotate(180deg);
}

/* 子菜单 */
.sub-menu {
    background: #f8f9fa;
    border-left: 2px solid #e8eaed;
    margin-left: 16px;
}

.sub-item {
    padding: 8px 16px 8px 32px;
    font-size: 13px;
    color: #5f6368;
    cursor: pointer;
    transition: all 0.2s ease;
}

.sub-item:hover {
    background: #e3f2fd;
    color: #1976d2;
}

.sub-item.active {
    background: #e3f2fd;
    color: #1976d2;
    font-weight: 500;
}

/* 底部状态栏 - 纯白风格 */
.status-bar {
    padding: 12px 16px;
    border-top: 1px solid #e8eaed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #ffffff;
}

.status-time {
    font-size: 12px;
    color: #5f6368;
    font-family: 'Courier New', monospace;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #34a853;
}

.status-indicator.online {
    background: #34a853;
    box-shadow: 0 0 0 2px rgba(52, 168, 83, 0.2);
}

/* 导航分组样式 */
.nav-section {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #e8eaed;
}

.nav-section-title {
    font-size: 11px;
    color: #a0aec0;
    font-weight: 500;
    padding: 0 12px 8px 12px;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #ffffff;
}

/* 顶部工具栏 */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid #e8eaed;
    background: #ffffff;
}

.page-title {
    font-size: 20px;
    font-weight: 500;
    color: #1a202c;
    letter-spacing: -0.02em;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.toolbar-actions {
    display: flex;
    gap: 12px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 450;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    letter-spacing: -0.01em;
    user-select: none;
    outline: none;
}

.btn:focus {
    outline: none;
    box-shadow: none;
}

.btn-primary {
    background: #1a73e8;
    color: white;
}

.btn-primary:hover {
    background: #1557b0;
}

.btn-secondary {
    background: #f1f3f4;
    color: #5f6368;
}

.btn-secondary:hover {
    background: #e8eaed;
}

/* 内容区域 - 纯白风格 */
.content-area {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background: #ffffff;
}

/* 页面特定样式 */

/* 数据总览页面 */
.overview-dashboard {
    padding: 20px;
}

.overview-stats {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #e5e5e5;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.stat-card.primary .stat-icon {
    background: #1976d2;
}

.stat-card.success .stat-icon {
    background: #388e3c;
}

.stat-card.warning .stat-icon {
    background: #f57c00;
}

.stat-card.info .stat-icon {
    background: #7b1fa2;
}

.stat-change {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    margin-top: 4px;
    display: inline-block;
}

.stat-change.positive {
    background: #d4edda;
    color: #155724;
}

.stat-change.negative {
    background: #f8d7da;
    color: #721c24;
}

/* 账号数据总览 */
.accounts-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e8eaed;
    background: #f8f9fa;
}

.section-header h3 {
    margin: 0;
    color: #1a1a1a;
    font-size: 18px;
}

.section-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.section-actions select {
    min-width: 120px;
}

.accounts-data-grid {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.account-data-card {
    border: 1px solid #e8eaed;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.account-data-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.account-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: #fafbfc;
    border-bottom: 1px solid #e8eaed;
}

.account-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.account-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.account-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.account-name {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
}

.account-platform {
    font-size: 14px;
    color: #1a73e8;
    font-weight: 500;
}

.account-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.account-status.online {
    background: #d4edda;
    color: #155724;
}

.account-status.offline {
    background: #f8d7da;
    color: #721c24;
}

.account-summary {
    display: flex;
    gap: 24px;
}

.summary-item {
    text-align: center;
}

.summary-value {
    font-size: 20px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 4px;
}

.summary-label {
    font-size: 12px;
    color: #6c757d;
}

/* 作品列表 */
.works-list {
    border-top: 1px solid #e8eaed;
}

.works-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: #f8f9fa;
    cursor: pointer;
}

.works-header h4 {
    margin: 0;
    font-size: 14px;
    color: #1a1a1a;
}

.works-content {
    padding: 20px;
    transition: all 0.3s ease;
    max-height: 1000px;
    overflow: hidden;
}

.works-content.collapsed {
    max-height: 0;
    padding: 0 20px;
}

.works-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
}

.work-item {
    border: 1px solid #e8eaed;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.work-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.work-thumbnail {
    position: relative;
    width: 100%;
    height: 150px;
    overflow: hidden;
}

.work-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.work-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.work-info {
    padding: 12px;
}

.work-title {
    font-size: 14px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 6px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.work-date {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 8px;
}

.work-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.work-stat {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #6c757d;
}

.work-stat i {
    font-size: 10px;
}

.work-stat span {
    font-weight: 500;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
}

.chart-container {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-placeholder {
    height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 48px;
}

.chart-placeholder p {
    margin-top: 16px;
    font-size: 16px;
}

/* 聊天页面 */
.chat-container {
    display: flex;
    height: calc(100vh - 140px);
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chat-sidebar {
    width: 250px;
    background: #f8f9fa;
    border-right: 1px solid #e8eaed;
    padding: 20px;
}

.chat-sidebar h3 {
    margin-bottom: 16px;
    color: #1a1a1a;
}

.room-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.room-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 12px;
}

.room-item:hover {
    background: #e8f0fe;
}

.room-item.active {
    background: #1a73e8;
    color: white;
}

.room-item i {
    width: 20px;
    text-align: center;
}

.room-item span {
    flex: 1;
}

.user-count {
    background: rgba(0,0,0,0.1);
    color: inherit;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e8eaed;
}

.chat-messages {
    flex: 1;
    padding: 20px 24px;
    overflow-y: auto;
}

.message {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #e8eaed;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #5f6368;
}

.message-content {
    flex: 1;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 4px;
}

.message-user {
    font-weight: 600;
    color: #1a1a1a;
}

.message-time {
    font-size: 12px;
    color: #5f6368;
}

.message-text {
    color: #333;
    line-height: 1.5;
}

.chat-input {
    display: flex;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e8eaed;
}

.chat-input input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #e8eaed;
    border-radius: 24px;
    outline: none;
    font-size: 14px;
}

.chat-input input:focus {
    border-color: #1a73e8;
}

/* 账号管理页面 */
.accounts-container {
    padding: 20px;
}

.accounts-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
}

.accounts-header h3 {
    color: #1a1a1a;
    margin: 0;
}

.accounts-actions {
    display: flex;
    gap: 12px;
}

.accounts-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.account-item {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.2s ease;
}

.account-item:hover {
    transform: translateY(-1px);
}

.account-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.account-info {
    flex: 1;
}

.account-name {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 4px;
}

.account-platform {
    font-size: 14px;
    color: #1a73e8;
    margin-bottom: 8px;
}

.account-stats {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #5f6368;
}

.account-status {
    text-align: center;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.online {
    background: #d4edda;
    color: #155724;
}

.status-badge.offline {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.success {
    background: #d4edda;
    color: #155724;
}

.status-badge.failed {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.account-actions {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #1a1a1a;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    border-color: #1a73e8;
}

/* 错误消息 */
.error-message {
    text-align: center;
    padding: 40px;
    color: #5f6368;
}

.error-message i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #ea4335;
}

.loading {
    text-align: center;
    padding: 40px;
    color: #5f6368;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }

    .brand-text {
        font-size: 16px;
    }

    .nav-item {
        padding: 10px 12px;
        font-size: 13px;
    }

    .toolbar {
        padding: 12px 16px;
    }

    .content-area {
        padding: 16px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .chat-container {
        flex-direction: column;
        height: auto;
    }

    .chat-sidebar {
        width: 100%;
    }

    .account-item {
        flex-direction: column;
        text-align: center;
    }

    .accounts-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
}
