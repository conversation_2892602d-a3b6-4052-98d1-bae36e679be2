// ===== 小红书管理模块 JavaScript =====

class XiaohongshuManager {
    constructor() {
        this.currentFilter = 'all';
        this.notes = [];
        this.analytics = {};
        this.init();
    }

    // 初始化
    async init() {
        await this.loadData();
        this.renderStats();
        this.renderNotes();
        this.bindEvents();
        this.showSuccessMessage('小红书管理模块加载完成');
    }

    // 加载数据
    async loadData() {
        try {
            // 加载概览数据
            const overviewResponse = await fetch('/api/xiaohongshu/overview');
            const overviewData = await overviewResponse.json();
            
            if (overviewData.success) {
                this.analytics = overviewData.data;
                this.notes = overviewData.data.recentNotes || [];
            }

            // 加载完整笔记列表
            const notesResponse = await fetch('/api/xiaohongshu/notes');
            const notesData = await notesResponse.json();
            
            if (notesData.success) {
                this.notes = notesData.data.notes;
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            this.showErrorMessage('数据加载失败，请刷新页面重试');
        }
    }

    // 渲染统计数据
    renderStats() {
        const statsContainer = document.getElementById('statsContainer');
        if (!statsContainer) return;

        const stats = [
            {
                title: '总浏览量',
                value: this.formatNumber(this.analytics.totalViews || 0),
                growth: this.analytics.recentGrowth?.views || '+0%',
                icon: 'fas fa-eye',
                type: 'views'
            },
            {
                title: '总点赞数',
                value: this.formatNumber(this.analytics.totalLikes || 0),
                growth: this.analytics.recentGrowth?.likes || '+0%',
                icon: 'fas fa-heart',
                type: 'likes'
            },
            {
                title: '总评论数',
                value: this.formatNumber(this.analytics.totalComments || 0),
                growth: '+19.4%',
                icon: 'fas fa-comment',
                type: 'comments'
            },
            {
                title: '总分享数',
                value: this.formatNumber(this.analytics.totalShares || 0),
                growth: '+22.9%',
                icon: 'fas fa-share',
                type: 'shares'
            }
        ];

        statsContainer.innerHTML = stats.map(stat => `
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">${stat.title}</span>
                    <div class="stat-icon ${stat.type}">
                        <i class="${stat.icon}"></i>
                    </div>
                </div>
                <div class="stat-value">${stat.value}</div>
                <div class="stat-growth ${stat.growth.startsWith('+') ? 'positive' : 'negative'}">
                    <i class="fas fa-arrow-${stat.growth.startsWith('+') ? 'up' : 'down'}"></i>
                    ${stat.growth}
                </div>
            </div>
        `).join('');
    }

    // 渲染笔记列表
    renderNotes() {
        const notesContainer = document.getElementById('notesContainer');
        if (!notesContainer) return;

        let filteredNotes = this.notes;
        
        // 应用筛选
        if (this.currentFilter !== 'all') {
            filteredNotes = this.notes.filter(note => {
                switch (this.currentFilter) {
                    case 'published':
                        return note.status === '已发布';
                    case 'draft':
                        return note.status === '草稿';
                    case 'top':
                        return note.isTop;
                    default:
                        return true;
                }
            });
        }

        if (filteredNotes.length === 0) {
            notesContainer.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                    <p>暂无笔记数据</p>
                </div>
            `;
            return;
        }

        notesContainer.innerHTML = filteredNotes.map(note => `
            <div class="note-card" data-note-id="${note.id}">
                <div class="note-header">
                    <h3 class="note-title">${note.title}</h3>
                    <span class="note-status ${note.status === '已发布' ? 'published' : 'draft'}">
                        ${note.status}
                    </span>
                </div>
                
                <div class="note-meta">
                    <span><i class="fas fa-clock"></i> ${this.formatDate(note.publishTime)}</span>
                    <span><i class="fas fa-${note.privacy === '公开可见' ? 'globe' : 'lock'}"></i> ${note.privacy}</span>
                    ${note.isTop ? '<span class="note-top-badge"><i class="fas fa-thumbtack"></i> 已置顶</span>' : ''}
                </div>
                
                <div class="note-stats">
                    <div class="note-stat">
                        <i class="fas fa-eye"></i>
                        <span>${this.formatNumber(note.views)}</span>
                    </div>
                    <div class="note-stat">
                        <i class="fas fa-heart"></i>
                        <span>${this.formatNumber(note.likes)}</span>
                    </div>
                    <div class="note-stat">
                        <i class="fas fa-comment"></i>
                        <span>${this.formatNumber(note.comments)}</span>
                    </div>
                    <div class="note-stat">
                        <i class="fas fa-share"></i>
                        <span>${this.formatNumber(note.shares)}</span>
                    </div>
                </div>
                
                <div class="note-actions">
                    <button class="note-action-btn primary" onclick="xiaohongshuManager.editNote('${note.id}')">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="note-action-btn secondary" onclick="xiaohongshuManager.toggleTop('${note.id}')">
                        <i class="fas fa-thumbtack"></i> ${note.isTop ? '取消置顶' : '置顶'}
                    </button>
                    <button class="note-action-btn secondary" onclick="xiaohongshuManager.changePrivacy('${note.id}')">
                        <i class="fas fa-lock"></i> 权限
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 绑定事件
    bindEvents() {
        // 筛选按钮
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentFilter = e.target.dataset.filter;
                this.renderNotes();
            });
        });

        // 功能按钮
        document.getElementById('btnAnalysis')?.addEventListener('click', () => this.showAnalysis());
        document.getElementById('btnTopManagement')?.addEventListener('click', () => this.showTopManagement());
        document.getElementById('btnPrivacySettings')?.addEventListener('click', () => this.showPrivacySettings());
        document.getElementById('btnPublish')?.addEventListener('click', () => this.showPublishModal());
        document.getElementById('btnOpenBrowser')?.addEventListener('click', () => this.openBrowser());
    }

    // 显示数据分析
    async showAnalysis() {
        try {
            const response = await fetch('/api/xiaohongshu/analytics?period=7d');
            const data = await response.json();
            
            if (data.success) {
                this.showModal('数据分析', this.renderAnalyticsContent(data.data));
            }
        } catch (error) {
            this.showErrorMessage('获取分析数据失败');
        }
    }

    // 渲染分析内容
    renderAnalyticsContent(analytics) {
        return `
            <div class="analytics-content">
                <div class="analytics-metrics">
                    <h4>核心指标 (最近7天)</h4>
                    <div class="metrics-grid">
                        ${Object.entries(analytics.metrics).map(([key, metric]) => `
                            <div class="metric-item">
                                <div class="metric-label">${this.getMetricLabel(key)}</div>
                                <div class="metric-value">${this.formatNumber(metric.current)}</div>
                                <div class="metric-growth ${metric.growth.startsWith('+') ? 'positive' : 'negative'}">
                                    ${metric.growth}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                <div class="top-notes">
                    <h4>热门笔记</h4>
                    <div class="top-notes-list">
                        ${analytics.topNotes.map((note, index) => `
                            <div class="top-note-item">
                                <span class="rank">#${index + 1}</span>
                                <span class="title">${note.title}</span>
                                <span class="views">${this.formatNumber(note.views)} 浏览</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    // 显示置顶管理
    showTopManagement() {
        const content = `
            <div class="top-management">
                <h4>置顶策略</h4>
                <div class="strategy-list">
                    <div class="strategy-item">
                        <h5>📌 标题标识法</h5>
                        <p>在重要笔记标题前添加特殊标识，如"📌【置顶】"</p>
                        <button class="btn btn-primary" onclick="xiaohongshuManager.applyTopStrategy('title')">
                            应用策略
                        </button>
                    </div>
                    <div class="strategy-item">
                        <h5>🔥 热门标记法</h5>
                        <p>为热门内容添加"🔥【热门】"标识</p>
                        <button class="btn btn-primary" onclick="xiaohongshuManager.applyTopStrategy('hot')">
                            应用策略
                        </button>
                    </div>
                    <div class="strategy-item">
                        <h5>📚 合集管理法</h5>
                        <p>创建专门的合集来管理重要内容</p>
                        <button class="btn btn-primary" onclick="xiaohongshuManager.createCollection()">
                            创建合集
                        </button>
                    </div>
                </div>
            </div>
        `;
        this.showModal('置顶管理', content);
    }

    // 显示权限设置
    showPrivacySettings() {
        const content = `
            <div class="privacy-settings">
                <h4>批量权限设置</h4>
                <div class="privacy-options">
                    <div class="privacy-option">
                        <input type="radio" id="privacy-public" name="privacy" value="公开可见" checked>
                        <label for="privacy-public">🌍 公开可见</label>
                        <p>所有人都可以看到</p>
                    </div>
                    <div class="privacy-option">
                        <input type="radio" id="privacy-friends" name="privacy" value="仅互关好友可见">
                        <label for="privacy-friends">👥 仅互关好友可见</label>
                        <p>只有互相关注的好友可以看到</p>
                    </div>
                    <div class="privacy-option">
                        <input type="radio" id="privacy-partial" name="privacy" value="部分人可见">
                        <label for="privacy-partial">🎯 部分人可见</label>
                        <p>自定义可见用户范围</p>
                    </div>
                    <div class="privacy-option">
                        <input type="radio" id="privacy-private" name="privacy" value="仅自己可见">
                        <label for="privacy-private">👤 仅自己可见</label>
                        <p>只有自己可以看到</p>
                    </div>
                </div>
                <div class="form-buttons">
                    <button class="btn btn-primary" onclick="xiaohongshuManager.applyBatchPrivacy()">
                        批量应用
                    </button>
                </div>
            </div>
        `;
        this.showModal('权限设置', content);
    }

    // 显示发布模态框
    showPublishModal() {
        const content = `
            <form id="publishForm">
                <div class="form-group">
                    <label class="form-label">标题</label>
                    <input type="text" class="form-input" id="publishTitle" placeholder="输入笔记标题..." required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">内容</label>
                    <textarea class="form-textarea" id="publishContent" placeholder="输入笔记内容..." required></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">标签</label>
                    <input type="text" class="form-input" id="publishTags" placeholder="输入标签，用逗号分隔...">
                </div>
                
                <div class="form-group">
                    <label class="form-label">可见性</label>
                    <select class="form-select" id="publishPrivacy">
                        <option value="公开可见">公开可见</option>
                        <option value="仅互关好友可见">仅互关好友可见</option>
                        <option value="部分人可见">部分人可见</option>
                        <option value="仅自己可见">仅自己可见</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">发布时间</label>
                    <input type="datetime-local" class="form-input" id="publishTime">
                </div>
                
                <div class="form-buttons">
                    <button type="button" class="btn btn-secondary" onclick="xiaohongshuManager.closeModal()">
                        取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        发布笔记
                    </button>
                </div>
            </form>
        `;
        
        this.showModal('发布笔记', content);
        
        // 绑定表单提交事件
        document.getElementById('publishForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.publishNote();
        });
    }

    // 发布笔记
    async publishNote() {
        const formData = {
            title: document.getElementById('publishTitle').value,
            content: document.getElementById('publishContent').value,
            tags: document.getElementById('publishTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
            privacy: document.getElementById('publishPrivacy').value,
            scheduleTime: document.getElementById('publishTime').value || null
        };

        try {
            const response = await fetch('/api/xiaohongshu/notes', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();
            
            if (data.success) {
                this.showSuccessMessage('笔记发布成功！');
                this.closeModal();
                await this.loadData();
                this.renderNotes();
            } else {
                this.showErrorMessage(data.message || '发布失败');
            }
        } catch (error) {
            this.showErrorMessage('发布失败，请重试');
        }
    }

    // 打开浏览器
    async openBrowser() {
        try {
            this.showLoadingMessage('正在打开浏览器...');
            
            const response = await fetch('/api/xiaohongshu/browser/open', {
                method: 'POST'
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccessMessage('浏览器已打开，请在浏览器中进行操作');
            } else {
                this.showErrorMessage('浏览器打开失败');
            }
        } catch (error) {
            this.showErrorMessage('浏览器操作失败');
        }
    }

    // 切换置顶状态
    async toggleTop(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (!note) return;

        try {
            const response = await fetch(`/api/xiaohongshu/notes/${noteId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    isTop: !note.isTop
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.showSuccessMessage(note.isTop ? '已取消置顶' : '已设置置顶');
                await this.loadData();
                this.renderNotes();
            }
        } catch (error) {
            this.showErrorMessage('操作失败');
        }
    }

    // 工具函数
    formatNumber(num) {
        if (num >= 10000) {
            return (num / 10000).toFixed(1) + 'w';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'k';
        }
        return num.toString();
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    getMetricLabel(key) {
        const labels = {
            views: '浏览量',
            likes: '点赞数',
            comments: '评论数',
            shares: '分享数'
        };
        return labels[key] || key;
    }

    // 模态框相关
    showModal(title, content) {
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');
        
        modalTitle.textContent = title;
        modalBody.innerHTML = content;
        modal.classList.add('show');
    }

    closeModal() {
        const modal = document.getElementById('modal');
        modal.classList.remove('show');
    }

    // 消息提示
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }

    showLoadingMessage(message) {
        this.showMessage(message, 'loading');
    }

    showMessage(message, type) {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        
        // 添加到页面
        document.body.appendChild(messageEl);
        
        // 3秒后移除
        setTimeout(() => {
            messageEl.remove();
        }, 3000);
    }
}

// 初始化管理器
let xiaohongshuManager;
document.addEventListener('DOMContentLoaded', () => {
    xiaohongshuManager = new XiaohongshuManager();
});
