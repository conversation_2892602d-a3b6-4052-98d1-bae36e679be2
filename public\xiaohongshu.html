<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书管理 - 黑默科技自媒体矩阵</title>
    
    <!-- 引入字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- 引入样式文件 -->
    <link rel="stylesheet" href="xiaohongshu-styles.css">
    
    <style>
        /* 消息提示样式 */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            animation: slideIn 0.3s ease;
        }
        
        .message-success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }
        
        .message-error {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .message-loading {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        /* 分析内容样式 */
        .analytics-content {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .metric-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 4px;
        }
        
        .metric-growth {
            font-size: 14px;
            font-weight: 600;
        }
        
        .metric-growth.positive { color: #27ae60; }
        .metric-growth.negative { color: #e74c3c; }
        
        .top-notes-list {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .top-note-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-bottom: 1px solid #eee;
        }
        
        .rank {
            font-weight: 700;
            color: #667eea;
            min-width: 30px;
        }
        
        .title {
            flex: 1;
            font-size: 14px;
        }
        
        .views {
            font-size: 12px;
            color: #666;
        }
        
        /* 策略和权限设置样式 */
        .strategy-list,
        .privacy-options {
            display: grid;
            gap: 16px;
        }
        
        .strategy-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }
        
        .strategy-item h5 {
            margin: 0 0 8px 0;
            color: #2c3e50;
        }
        
        .strategy-item p {
            margin: 0 0 16px 0;
            color: #666;
            font-size: 14px;
        }
        
        .privacy-option {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .privacy-option:hover {
            background: #e9ecef;
        }
        
        .privacy-option input[type="radio"] {
            margin-right: 8px;
        }
        
        .privacy-option label {
            font-weight: 600;
            color: #2c3e50;
            cursor: pointer;
        }
        
        .privacy-option p {
            margin: 4px 0 0 24px;
            font-size: 12px;
            color: #666;
        }
        
        .note-top-badge {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="xiaohongshu-container">
        <!-- 头部区域 -->
        <div class="xiaohongshu-header">
            <div class="xiaohongshu-title">
                <div class="xiaohongshu-icon">
                    <i class="fab fa-instagram"></i>
                </div>
                <div>
                    <h1>小红书管理中心</h1>
                    <p class="xiaohongshu-subtitle">智能化内容管理与数据分析平台</p>
                </div>
            </div>
        </div>

        <!-- 统计数据区域 -->
        <div class="stats-grid" id="statsContainer">
            <!-- 统计卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 功能按钮区域 -->
        <div class="action-buttons">
            <button class="action-btn" id="btnAnalysis">
                <div class="action-btn-icon analysis">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="action-btn-title">数据分析</div>
                <div class="action-btn-desc">查看详细的数据分析报告</div>
            </button>
            
            <button class="action-btn" id="btnTopManagement">
                <div class="action-btn-icon top">
                    <i class="fas fa-thumbtack"></i>
                </div>
                <div class="action-btn-title">置顶管理</div>
                <div class="action-btn-desc">管理笔记置顶和排序</div>
            </button>
            
            <button class="action-btn" id="btnPrivacySettings">
                <div class="action-btn-icon privacy">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="action-btn-title">权限设置</div>
                <div class="action-btn-desc">批量设置笔记可见权限</div>
            </button>
            
            <button class="action-btn" id="btnPublish">
                <div class="action-btn-icon publish">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div class="action-btn-title">发布笔记</div>
                <div class="action-btn-desc">创建并发布新的笔记内容</div>
            </button>
            
            <button class="action-btn" id="btnOpenBrowser">
                <div class="action-btn-icon analysis">
                    <i class="fas fa-external-link-alt"></i>
                </div>
                <div class="action-btn-title">打开浏览器</div>
                <div class="action-btn-desc">打开小红书创作者中心</div>
            </button>
        </div>

        <!-- 笔记列表区域 -->
        <div class="notes-section">
            <div class="section-header">
                <h2 class="section-title">笔记管理</h2>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">全部</button>
                    <button class="filter-btn" data-filter="published">已发布</button>
                    <button class="filter-btn" data-filter="draft">草稿</button>
                    <button class="filter-btn" data-filter="top">已置顶</button>
                </div>
            </div>
            
            <div class="notes-grid" id="notesContainer">
                <!-- 笔记卡片将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">标题</h3>
                <button class="modal-close" onclick="xiaohongshuManager.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 模态框内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="xiaohongshu-manager.js"></script>
    
    <script>
        // 页面加载完成后的额外初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加页面切换动画
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease';
            
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
            
            // 模态框点击外部关闭
            document.getElementById('modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    xiaohongshuManager.closeModal();
                }
            });
            
            // ESC键关闭模态框
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    xiaohongshuManager.closeModal();
                }
            });
        });
    </script>
</body>
</html>
