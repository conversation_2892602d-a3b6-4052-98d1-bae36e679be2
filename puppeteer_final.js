/**
 * 🎯 最终版小红书评论爬虫 - Puppeteer版本
 * 自动选择正确的评论页面并爬取评论
 */

const puppeteer = require('puppeteer-core');
const axios = require('axios');
const fs = require('fs');

class FinalXiaohongshuScraper {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.targetWindowSeq = 19;
        this.browser = null;
        this.page = null;
        this.comments = [];
    }

    /**
     * 获取调试信息
     */
    async getDebugInfo() {
        console.log('🔍 获取调试信息...');
        
        try {
            // 1. 获取窗口信息
            const windowResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            const browsers = windowResponse.data.data.list;
            const window19 = browsers.find(browser => browser.seq === this.targetWindowSeq);
            
            if (!window19) {
                throw new Error('未找到19号窗口');
            }

            // 2. 获取调试端口
            const portsResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            const ports = portsResponse.data.data;
            const debugPort = ports[window19.id];
            
            if (!debugPort) {
                throw new Error('19号窗口未获取到调试端口');
            }

            // 3. 获取页面列表
            const pagesResponse = await axios.get(`http://localhost:${debugPort}/json/list`);
            const pages = pagesResponse.data;

            console.log(`✅ 找到 ${pages.length} 个页面:`);
            pages.forEach((page, index) => {
                console.log(`   ${index + 1}. ${page.title}`);
                console.log(`      URL: ${page.url}`);
                if (page.url.includes('xiaohongshu.com')) {
                    if (page.url.includes('/explore/')) {
                        console.log(`      🎯 这是评论页面!`);
                    } else if (page.url.includes('/user/profile/')) {
                        console.log(`      👤 这是用户资料页面`);
                    } else {
                        console.log(`      📄 小红书页面`);
                    }
                }
            });

            return { window19, debugPort, pages };

        } catch (error) {
            console.error('❌ 获取调试信息失败:', error.message);
            throw error;
        }
    }

    /**
     * 选择最佳的小红书页面
     */
    selectBestPage(pages) {
        const xiaohongshuPages = pages.filter(page => 
            page.url.includes('xiaohongshu.com') && 
            page.type === 'page'
        );

        if (xiaohongshuPages.length === 0) {
            throw new Error('未找到小红书页面');
        }

        // 优先选择包含 /explore/ 的页面（这些通常是内容详情页，有评论）
        const explorePage = xiaohongshuPages.find(page => page.url.includes('/explore/'));
        if (explorePage) {
            console.log(`🎯 选择评论页面: ${explorePage.title}`);
            return explorePage;
        }

        // 如果没有explore页面，选择第一个小红书页面
        console.log(`📄 选择页面: ${xiaohongshuPages[0].title}`);
        return xiaohongshuPages[0];
    }

    /**
     * 连接到比特浏览器
     */
    async connectToBrowser() {
        console.log('🔗 连接到比特浏览器...');
        
        try {
            const { window19, debugPort, pages } = await this.getDebugInfo();
            
            // 选择最佳页面
            const targetPage = this.selectBestPage(pages);
            
            // 获取浏览器WebSocket端点
            const versionResponse = await axios.get(`http://localhost:${debugPort}/json/version`);
            const browserWSEndpoint = versionResponse.data.webSocketDebuggerUrl;
            
            console.log('🔗 连接到浏览器...');
            this.browser = await puppeteer.connect({
                browserWSEndpoint,
                defaultViewport: null
            });
            
            // 获取所有页面
            const browserPages = await this.browser.pages();
            
            // 找到目标页面
            this.page = browserPages.find(page => page.url().includes(targetPage.url.split('?')[0]));
            
            if (!this.page) {
                // 如果找不到，使用第一个小红书页面
                this.page = browserPages.find(page => page.url().includes('xiaohongshu.com'));
            }
            
            if (!this.page) {
                throw new Error('未找到可用的小红书页面');
            }
            
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            console.log('✅ 连接成功!');
            console.log(`📄 当前页面: ${currentUrl}`);
            console.log(`📝 页面标题: ${title}`);
            
            // 检查页面类型
            if (currentUrl.includes('/explore/')) {
                console.log('🎉 这是内容详情页，应该有评论!');
                return true;
            } else if (currentUrl.includes('/user/profile/')) {
                console.log('⚠️ 这是用户资料页面，可能没有评论');
                console.log('💡 建议在比特浏览器中打开一个小红书内容详情页');
                return true; // 仍然尝试爬取
            } else {
                console.log('📄 小红书页面，尝试爬取');
                return true;
            }
            
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    /**
     * 滚动页面加载更多评论
     */
    async scrollAndLoadComments() {
        console.log('📜 开始滚动加载评论...');
        
        let scrollCount = 0;
        const maxScrolls = 20; // 减少滚动次数，避免过长时间
        let lastHeight = 0;
        
        while (scrollCount < maxScrolls) {
            // 滚动到页面底部
            const currentHeight = await this.page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
                return document.body.scrollHeight;
            });
            
            // 等待新内容加载
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 检查是否有新内容
            const newHeight = await this.page.evaluate(() => document.body.scrollHeight);
            
            if (newHeight === lastHeight) {
                console.log(`📄 滚动完成，共滚动 ${scrollCount} 次`);
                break;
            }
            
            lastHeight = newHeight;
            scrollCount++;
            
            if (scrollCount % 5 === 0) {
                console.log(`📊 已滚动 ${scrollCount} 次...`);
            }
        }
        
        // 滚动回顶部
        await this.page.evaluate(() => window.scrollTo(0, 0));
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    /**
     * 提取评论数据
     */
    async extractComments() {
        console.log('📝 开始提取评论数据...');
        
        try {
            const comments = await this.page.evaluate(() => {
                const extractedComments = [];
                
                // 方法1: 通过常见的评论选择器
                const commentSelectors = [
                    '[class*="comment"]',
                    '[class*="Comment"]',
                    '[class*="reply"]',
                    '[class*="Reply"]',
                    '.note-item',
                    '.feed-item',
                    '[data-testid*="comment"]'
                ];
                
                for (const selector of commentSelectors) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(element => {
                            const text = element.textContent?.trim();
                            if (text && text.length > 10) {
                                extractedComments.push({
                                    content: text,
                                    method: `selector_${selector}`,
                                    timestamp: new Date().toISOString()
                                });
                            }
                        });
                    } catch (e) {
                        continue;
                    }
                }
                
                // 方法2: 通过文本模式匹配
                const bodyText = document.body.textContent || '';
                const lines = bodyText.split('\n');
                
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();
                    
                    // 检查是否像评论（包含时间标识）
                    if (/[分小天月年]前/.test(line)) {
                        // 尝试获取前面的文本作为评论内容
                        if (i > 0) {
                            const prevLine = lines[i - 1].trim();
                            if (prevLine.length > 10 && 
                                !/(点赞|关注|分享|收藏|笔记|作品)/.test(prevLine)) {
                                extractedComments.push({
                                    content: prevLine,
                                    time: line,
                                    method: 'text_pattern',
                                    timestamp: new Date().toISOString()
                                });
                            }
                        }
                    }
                    
                    // 检查是否包含"回复"
                    if (line.includes('回复') && line.length > 15 && line.length < 200) {
                        extractedComments.push({
                            content: line,
                            method: 'reply_pattern',
                            timestamp: new Date().toISOString()
                        });
                    }
                }
                
                // 去重
                const uniqueComments = [];
                const seenContents = new Set();
                
                for (const comment of extractedComments) {
                    const content = comment.content;
                    if (!seenContents.has(content) && content.length > 5 && content.length < 500) {
                        seenContents.add(content);
                        uniqueComments.push(comment);
                    }
                }
                
                return uniqueComments;
            });
            
            this.comments = comments;
            console.log(`✅ 提取完成，共获得 ${this.comments.length} 条评论`);
            
            // 显示前几条评论作为预览
            if (this.comments.length > 0) {
                console.log('📝 评论预览:');
                this.comments.slice(0, 3).forEach((comment, index) => {
                    console.log(`   ${index + 1}. ${comment.content.substring(0, 50)}...`);
                });
            }
            
            return this.comments.length > 0;
            
        } catch (error) {
            console.error('❌ 提取评论失败:', error.message);
            return false;
        }
    }

    /**
     * 保存结果
     */
    async saveResults() {
        if (this.comments.length === 0) {
            console.log('⚠️ 没有评论数据可保存');
            return;
        }
        
        try {
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            const result = {
                scrape_info: {
                    url: currentUrl,
                    title: title,
                    scrape_time: new Date().toISOString(),
                    total_comments: this.comments.length,
                    method: 'puppeteer_final'
                },
                comments: this.comments
            };
            
            const filename = `xiaohongshu_comments_final_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
            
            fs.writeFileSync(filename, JSON.stringify(result, null, 2), 'utf8');
            
            console.log(`💾 结果已保存到: ${filename}`);
            console.log(`📊 总计 ${this.comments.length} 条评论`);
            
        } catch (error) {
            console.error('❌ 保存结果失败:', error.message);
        }
    }

    /**
     * 运行爬虫
     */
    async run() {
        console.log('🎯 最终版小红书评论爬虫 (Puppeteer版)');
        console.log('='.repeat(50));
        
        try {
            // 1. 连接浏览器
            await this.connectToBrowser();
            
            // 2. 等待页面稳定
            console.log('⏳ 等待页面稳定...');
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 3. 滚动加载评论
            await this.scrollAndLoadComments();
            
            // 4. 提取评论
            const success = await this.extractComments();
            
            if (!success) {
                console.log('❌ 未能提取到评论数据');
                console.log('💡 可能的原因:');
                console.log('   1. 当前页面不是评论页面');
                console.log('   2. 评论需要登录才能查看');
                console.log('   3. 页面结构发生变化');
                return false;
            }
            
            // 5. 保存结果
            await this.saveResults();
            
            console.log('\n🎉 爬取完成!');
            return true;
            
        } catch (error) {
            console.error('❌ 爬虫运行失败:', error.message);
            return false;
        } finally {
            if (this.browser) {
                console.log('🔒 断开浏览器连接...');
                await this.browser.disconnect();
            }
        }
    }
}

// 主函数
async function main() {
    console.log('🎯 最终版小红书评论爬虫启动器');
    console.log('='.repeat(50));
    
    console.log('📝 使用说明:');
    console.log('   1. 确保比特浏览器19号窗口正在运行');
    console.log('   2. 在浏览器中打开小红书内容详情页（包含评论的页面）');
    console.log('   3. 脚本会自动选择最佳页面进行爬取');
    
    const scraper = new FinalXiaohongshuScraper();
    const success = await scraper.run();
    
    if (success) {
        console.log('\n🎉 爬取成功完成!');
        console.log('📁 请查看生成的JSON文件');
    } else {
        console.log('\n❌ 爬取失败');
        console.log('💡 建议:');
        console.log('   1. 确保在比特浏览器中打开了小红书评论页面');
        console.log('   2. 检查页面是否需要登录');
        console.log('   3. 尝试手动滚动页面加载更多评论');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = FinalXiaohongshuScraper;
