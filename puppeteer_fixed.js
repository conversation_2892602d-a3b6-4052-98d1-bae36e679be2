/**
 * 🎯 修复版Puppeteer连接测试
 * 使用正确的方法连接到比特浏览器页面
 */

const puppeteer = require('puppeteer-core');
const axios = require('axios');

class FixedPuppeteerTester {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.targetWindowSeq = 19;
    }

    /**
     * 获取页面列表和调试信息
     */
    async getDebugInfo() {
        console.log('🔍 获取调试信息...');
        
        try {
            // 1. 获取窗口信息
            const windowResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            if (!windowResponse.data.success) {
                throw new Error(`获取窗口列表失败: ${windowResponse.data.msg}`);
            }

            const browsers = windowResponse.data.data.list;
            const window19 = browsers.find(browser => browser.seq === this.targetWindowSeq);
            
            if (!window19) {
                throw new Error('未找到19号窗口');
            }

            // 2. 获取调试端口
            const portsResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            
            if (!portsResponse.data.success) {
                throw new Error(`获取端口失败: ${portsResponse.data.msg}`);
            }

            const ports = portsResponse.data.data;
            const debugPort = ports[window19.id];
            
            if (!debugPort) {
                throw new Error('19号窗口未获取到调试端口');
            }

            // 3. 获取页面列表
            const pagesResponse = await axios.get(`http://localhost:${debugPort}/json/list`);
            const pages = pagesResponse.data;

            console.log(`✅ 调试信息获取成功:`);
            console.log(`   窗口ID: ${window19.id}`);
            console.log(`   调试端口: ${debugPort}`);
            console.log(`   页面数量: ${pages.length}`);

            return { window19, debugPort, pages };

        } catch (error) {
            console.error('❌ 获取调试信息失败:', error.message);
            throw error;
        }
    }

    /**
     * 测试Puppeteer连接 - 方法1: 连接到浏览器
     */
    async testBrowserConnection(debugPort) {
        console.log('\n🔍 方法1: 连接到浏览器...');
        
        try {
            // 获取浏览器WebSocket端点
            const versionResponse = await axios.get(`http://localhost:${debugPort}/json/version`);
            const browserWSEndpoint = versionResponse.data.webSocketDebuggerUrl;
            
            console.log(`   浏览器WebSocket: ${browserWSEndpoint}`);
            
            const browser = await puppeteer.connect({
                browserWSEndpoint,
                defaultViewport: null
            });
            
            console.log('   ✅ 浏览器连接成功!');
            
            // 获取所有页面
            const pages = await browser.pages();
            console.log(`   📄 找到 ${pages.length} 个页面`);
            
            // 查找小红书页面
            let xiaohongshuPage = null;
            for (const page of pages) {
                const url = page.url();
                if (url.includes('xiaohongshu.com')) {
                    xiaohongshuPage = page;
                    break;
                }
            }
            
            if (xiaohongshuPage) {
                const title = await xiaohongshuPage.title();
                console.log(`   🎯 找到小红书页面: ${title}`);
                
                // 简单测试
                const bodyText = await xiaohongshuPage.evaluate(() => document.body.textContent);
                const commentCount = (bodyText.match(/条评论/g) || []).length;
                console.log(`   📊 "条评论"出现次数: ${commentCount}`);
                
                await browser.disconnect();
                return true;
            } else {
                console.log('   ⚠️ 未找到小红书页面');
                await browser.disconnect();
                return false;
            }
            
        } catch (error) {
            console.log(`   ❌ 方法1失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 测试Puppeteer连接 - 方法2: 直接连接到页面
     */
    async testPageConnection(pages) {
        console.log('\n🔍 方法2: 直接连接到页面...');
        
        try {
            // 查找小红书页面
            const xiaohongshuPages = pages.filter(page => 
                page.url.includes('xiaohongshu.com') && 
                page.type === 'page'
            );
            
            if (xiaohongshuPages.length === 0) {
                console.log('   ⚠️ 未找到小红书页面');
                return false;
            }
            
            const targetPage = xiaohongshuPages[0];
            console.log(`   🎯 目标页面: ${targetPage.title}`);
            console.log(`   📄 URL: ${targetPage.url}`);
            console.log(`   🔗 WebSocket: ${targetPage.webSocketDebuggerUrl}`);
            
            // 尝试连接到页面
            const browser = await puppeteer.connect({
                browserWSEndpoint: targetPage.webSocketDebuggerUrl,
                defaultViewport: null
            });
            
            console.log('   ✅ 页面连接成功!');
            
            // 获取页面对象
            const pages_connected = await browser.pages();
            const page = pages_connected[0];
            
            if (page) {
                const url = page.url();
                const title = await page.title();
                
                console.log(`   📝 页面信息:`);
                console.log(`      URL: ${url}`);
                console.log(`      标题: ${title}`);
                
                // 页面内容分析
                const bodyText = await page.evaluate(() => document.body.textContent);
                const commentCount = (bodyText.match(/条评论/g) || []).length;
                const replyCount = (bodyText.match(/回复/g) || []).length;
                
                console.log(`   📊 内容分析:`);
                console.log(`      "条评论": ${commentCount}`);
                console.log(`      "回复": ${replyCount}`);
                
                if (commentCount > 0 || replyCount > 0) {
                    console.log('   ✅ 页面包含评论内容，适合爬取!');
                } else {
                    console.log('   ⚠️ 页面可能不在评论区');
                }
            }
            
            await browser.disconnect();
            return true;
            
        } catch (error) {
            console.log(`   ❌ 方法2失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 运行完整测试
     */
    async runTest() {
        console.log('🎯 修复版Puppeteer连接测试');
        console.log('='.repeat(50));
        
        try {
            // 1. 获取调试信息
            const { window19, debugPort, pages } = await this.getDebugInfo();
            
            // 2. 显示页面信息
            console.log('\n📄 页面列表:');
            pages.forEach((page, index) => {
                console.log(`   ${index + 1}. ${page.title}`);
                console.log(`      URL: ${page.url}`);
                if (page.url.includes('xiaohongshu.com')) {
                    console.log(`      🎯 小红书页面!`);
                }
            });
            
            // 3. 测试方法1: 连接到浏览器
            const method1Success = await this.testBrowserConnection(debugPort);
            
            // 4. 测试方法2: 直接连接到页面
            const method2Success = await this.testPageConnection(pages);
            
            // 5. 总结
            console.log('\n' + '='.repeat(50));
            console.log('🎯 测试结果:');
            console.log(`   方法1 (连接浏览器): ${method1Success ? '✅ 成功' : '❌ 失败'}`);
            console.log(`   方法2 (连接页面): ${method2Success ? '✅ 成功' : '❌ 失败'}`);
            
            if (method1Success || method2Success) {
                console.log('\n🎉 Puppeteer连接测试成功!');
                console.log('🚀 现在可以运行完整的爬虫脚本');
                return true;
            } else {
                console.log('\n❌ 所有连接方法都失败了');
                return false;
            }
            
        } catch (error) {
            console.error('❌ 测试失败:', error.message);
            return false;
        }
    }
}

// 主函数
async function main() {
    const tester = new FixedPuppeteerTester();
    const success = await tester.runTest();
    
    if (!success) {
        console.log('\n💡 可能的解决方案:');
        console.log('   1. 确保比特浏览器19号窗口正在运行');
        console.log('   2. 确保已打开小红书页面');
        console.log('   3. 重启比特浏览器');
        console.log('   4. 检查防火墙设置');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = FixedPuppeteerTester;
