/**
 * 🎯 小红书评论爬虫 - Puppeteer版本
 * 使用比特浏览器API获取调试端口，然后用Puppeteer连接
 */

const puppeteer = require('puppeteer-core');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

class XiaohongshuPuppeteerScraper {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.targetWindowSeq = 19; // 目标19号窗口
        this.browser = null;
        this.page = null;
        this.comments = [];
    }

    /**
     * 获取比特浏览器窗口列表
     */
    async getBrowserWindows() {
        console.log('🔍 获取比特浏览器窗口列表...');
        
        try {
            const response = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            if (response.data.success) {
                const browsers = response.data.data.list;
                console.log(`✅ 获取到 ${browsers.length} 个浏览器窗口`);
                
                // 查找19号窗口
                const window19 = browsers.find(browser => browser.seq === this.targetWindowSeq);
                
                if (window19) {
                    console.log(`🎯 找到19号窗口:`);
                    console.log(`   ID: ${window19.id}`);
                    console.log(`   名称: ${window19.name || '未命名'}`);
                    console.log(`   状态: ${window19.status}`);
                    console.log(`   分组: ${window19.groupName}`);
                    return window19;
                } else {
                    throw new Error('未找到19号窗口');
                }
            } else {
                throw new Error(`API调用失败: ${response.data.msg}`);
            }
        } catch (error) {
            console.error('❌ 获取窗口列表失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取调试端口
     */
    async getDebugPorts() {
        console.log('🔌 获取调试端口...');
        
        try {
            const response = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            
            if (response.data.success) {
                const ports = response.data.data;
                console.log(`✅ 获取到 ${Object.keys(ports).length} 个调试端口`);
                return ports;
            } else {
                throw new Error(`获取端口失败: ${response.data.msg}`);
            }
        } catch (error) {
            console.error('❌ 获取调试端口失败:', error.message);
            throw error;
        }
    }

    /**
     * 连接到比特浏览器
     */
    async connectToBrowser() {
        console.log('🔗 连接到比特浏览器...');

        try {
            // 1. 获取19号窗口信息
            const window19 = await this.getBrowserWindows();

            // 2. 获取调试端口
            const ports = await this.getDebugPorts();
            const debugPort = ports[window19.id];

            if (!debugPort) {
                throw new Error('19号窗口未打开或未获取到调试端口');
            }

            console.log(`🎯 19号窗口调试端口: ${debugPort}`);

            // 3. 获取浏览器WebSocket端点
            const versionResponse = await axios.get(`http://localhost:${debugPort}/json/version`);
            const browserWSEndpoint = versionResponse.data.webSocketDebuggerUrl;

            console.log('🔗 连接到浏览器...');
            this.browser = await puppeteer.connect({
                browserWSEndpoint,
                defaultViewport: null
            });

            // 4. 获取页面
            const pages = await this.browser.pages();
            if (pages.length === 0) {
                throw new Error('浏览器中没有打开的页面');
            }

            // 查找小红书页面，优先选择包含评论的页面
            let xiaohongshuPages = pages.filter(page => page.url().includes('xiaohongshu.com'));

            if (xiaohongshuPages.length === 0) {
                throw new Error('未找到小红书页面');
            }

            // 如果有多个小红书页面，选择第一个
            this.page = xiaohongshuPages[0];

            const currentUrl = this.page.url();
            const title = await this.page.title();

            console.log('✅ 连接成功!');
            console.log(`📄 当前页面: ${currentUrl}`);
            console.log(`📝 页面标题: ${title}`);
            console.log('🎉 检测到小红书页面!');

            return true;

        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    /**
     * 等待页面加载完成
     */
    async waitForPageLoad() {
        console.log('⏳ 等待页面加载完成...');

        try {
            // 等待页面稳定
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 等待评论区域出现
            const commentSelectors = [
                '[class*="comment"]',
                '[class*="Comment"]',
                '[data-testid*="comment"]',
                '.note-item',
                '.feed-item'
            ];

            for (const selector of commentSelectors) {
                try {
                    await this.page.waitForSelector(selector, { timeout: 5000 });
                    console.log(`✅ 找到评论区: ${selector}`);
                    return true;
                } catch (e) {
                    continue;
                }
            }

            console.log('⚠️ 未找到明确的评论区，继续尝试');
            return true;

        } catch (error) {
            console.log('⚠️ 页面加载超时，继续尝试');
            return true;
        }
    }

    /**
     * 滚动页面加载更多评论
     */
    async scrollAndLoadComments() {
        console.log('📜 开始滚动加载评论...');
        
        let scrollCount = 0;
        const maxScrolls = 50;
        let lastHeight = 0;
        
        while (scrollCount < maxScrolls) {
            // 滚动到页面底部
            const currentHeight = await this.page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
                return document.body.scrollHeight;
            });
            
            // 等待新内容加载
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 检查是否有新内容
            const newHeight = await this.page.evaluate(() => document.body.scrollHeight);
            
            if (newHeight === lastHeight) {
                console.log(`📄 滚动完成，共滚动 ${scrollCount} 次`);
                break;
            }
            
            lastHeight = newHeight;
            scrollCount++;
            
            if (scrollCount % 5 === 0) {
                console.log(`📊 已滚动 ${scrollCount} 次...`);
            }
        }
        
        // 滚动回顶部
        await this.page.evaluate(() => window.scrollTo(0, 0));
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    /**
     * 提取评论数据
     */
    async extractComments() {
        console.log('📝 开始提取评论数据...');
        
        try {
            const comments = await this.page.evaluate(() => {
                const extractedComments = [];
                
                // 方法1: 通过常见的评论选择器
                const commentSelectors = [
                    '[class*="comment"]',
                    '[class*="Comment"]',
                    '[class*="reply"]',
                    '[class*="Reply"]',
                    '.note-item',
                    '.feed-item'
                ];
                
                for (const selector of commentSelectors) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(element => {
                            const text = element.textContent?.trim();
                            if (text && text.length > 10) {
                                extractedComments.push({
                                    content: text,
                                    method: `selector_${selector}`,
                                    timestamp: new Date().toISOString()
                                });
                            }
                        });
                    } catch (e) {
                        continue;
                    }
                }
                
                // 方法2: 通过文本模式匹配
                const bodyText = document.body.textContent || '';
                const lines = bodyText.split('\n');
                
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();
                    
                    // 检查是否像评论（包含时间标识）
                    if (/[分小天月年]前/.test(line)) {
                        // 尝试获取前面的文本作为评论内容
                        if (i > 0) {
                            const prevLine = lines[i - 1].trim();
                            if (prevLine.length > 10 && 
                                !/(点赞|关注|分享|收藏)/.test(prevLine)) {
                                extractedComments.push({
                                    content: prevLine,
                                    time: line,
                                    method: 'text_pattern',
                                    timestamp: new Date().toISOString()
                                });
                            }
                        }
                    }
                    
                    // 检查是否包含"回复"
                    if (line.includes('回复') && line.length > 15) {
                        extractedComments.push({
                            content: line,
                            method: 'reply_pattern',
                            timestamp: new Date().toISOString()
                        });
                    }
                }
                
                // 去重
                const uniqueComments = [];
                const seenContents = new Set();
                
                for (const comment of extractedComments) {
                    const content = comment.content;
                    if (!seenContents.has(content) && content.length > 5) {
                        seenContents.add(content);
                        uniqueComments.push(comment);
                    }
                }
                
                return uniqueComments;
            });
            
            this.comments = comments;
            console.log(`✅ 提取完成，共获得 ${this.comments.length} 条评论`);
            
            return this.comments.length > 0;
            
        } catch (error) {
            console.error('❌ 提取评论失败:', error.message);
            return false;
        }
    }

    /**
     * 保存结果
     */
    async saveResults() {
        if (this.comments.length === 0) {
            console.log('⚠️ 没有评论数据可保存');
            return;
        }
        
        try {
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            const result = {
                scrape_info: {
                    url: currentUrl,
                    title: title,
                    scrape_time: new Date().toISOString(),
                    total_comments: this.comments.length,
                    method: 'puppeteer'
                },
                comments: this.comments
            };
            
            const filename = `xiaohongshu_comments_puppeteer_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
            
            fs.writeFileSync(filename, JSON.stringify(result, null, 2), 'utf8');
            
            console.log(`💾 结果已保存到: ${filename}`);
            console.log(`📊 总计 ${this.comments.length} 条评论`);
            
        } catch (error) {
            console.error('❌ 保存结果失败:', error.message);
        }
    }

    /**
     * 运行爬虫
     */
    async run() {
        console.log('🎯 小红书评论爬虫 (Puppeteer版)');
        console.log('='.repeat(50));
        
        try {
            // 1. 连接浏览器
            const isXiaohongshu = await this.connectToBrowser();
            
            if (!isXiaohongshu) {
                console.log('⚠️ 请在比特浏览器中导航到小红书评论页面，然后重新运行');
                return false;
            }
            
            // 2. 等待页面加载
            await this.waitForPageLoad();
            
            // 3. 滚动加载评论
            await this.scrollAndLoadComments();
            
            // 4. 提取评论
            const success = await this.extractComments();
            
            if (!success) {
                console.log('❌ 未能提取到评论数据');
                return false;
            }
            
            // 5. 保存结果
            await this.saveResults();
            
            console.log('\n🎉 爬取完成!');
            return true;
            
        } catch (error) {
            console.error('❌ 爬虫运行失败:', error.message);
            return false;
        } finally {
            if (this.browser) {
                console.log('🔒 断开浏览器连接...');
                await this.browser.disconnect();
            }
        }
    }
}

// 主函数
async function main() {
    console.log('🎯 小红书评论爬虫启动器 (Puppeteer版)');
    console.log('='.repeat(50));
    
    console.log('📝 请确保:');
    console.log('   1. 比特浏览器正在运行');
    console.log('   2. 19号窗口已打开');
    console.log('   3. 已导航到目标小红书页面');
    console.log('   4. 已安装 puppeteer-core 和 axios');
    
    console.log('\n按任意键开始爬取...');
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on('data', async () => {
        process.stdin.setRawMode(false);
        process.stdin.pause();
        
        const scraper = new XiaohongshuPuppeteerScraper();
        const success = await scraper.run();
        
        if (success) {
            console.log('\n🎉 爬取成功完成!');
        } else {
            console.log('\n❌ 爬取失败');
            console.log('💡 建议:');
            console.log('   1. 检查比特浏览器是否正常运行');
            console.log('   2. 确保19号窗口已打开小红书页面');
            console.log('   3. 检查网络连接');
        }
        
        process.exit(0);
    });
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = XiaohongshuPuppeteerScraper;
