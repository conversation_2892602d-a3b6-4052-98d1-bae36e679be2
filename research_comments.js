#!/usr/bin/env node

/**
 * 🔬 深度研究小红书评论采集
 * 分析为什么只采集到156条而不是1472条评论
 */

const axios = require('axios');
const WebSocket = require('ws');

class CommentsResearcher {
    constructor() {
        this.debugPort = null;
    }

    // 🔬 主要研究方法
    async researchComments() {
        console.log('🔬 开始深度研究评论采集问题...\n');

        try {
            // 1. 获取调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 当前页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 分析页面结构
            console.log('🔍 分析页面结构...');
            await this.analyzePage(tab);

            // 4. 研究评论加载机制
            console.log('\n🔄 研究评论加载机制...');
            await this.researchCommentLoading(tab);

            // 5. 测试不同的采集策略
            console.log('\n🧪 测试不同的采集策略...');
            await this.testDifferentStrategies(tab);

        } catch (error) {
            console.error('❌ 研究失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取调试端口
    async getCurrentDebugPort() {
        try {
            const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 10000
            });

            if (response.data.success && response.data.data && response.data.data.result) {
                const httpInfo = response.data.data.result.http;
                if (httpInfo) {
                    this.debugPort = httpInfo.split(':')[1];
                    console.log(`✅ 调试端口: ${this.debugPort}`);
                    return;
                }
            }

            const commonPorts = [63524, 51859, 58222, 9222];
            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到端口: ${port}`);
                    return;
                } catch (error) {
                    // 继续尝试
                }
            }

            throw new Error('未找到可用端口');
        } catch (error) {
            throw new Error(`获取端口失败: ${error.message}`);
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
            timeout: 5000
        });

        return response.data.find(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.url.includes('creator.xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );
    }

    // 🔍 分析页面结构
    async analyzePage(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const analyzeScript = `
                        (function() {
                            try {
                                const analysis = {
                                    pageInfo: {
                                        title: document.title,
                                        url: window.location.href,
                                        scrollHeight: document.body.scrollHeight,
                                        clientHeight: document.documentElement.clientHeight
                                    },
                                    commentStats: {
                                        totalFromPage: null,
                                        visibleComments: 0,
                                        commentContainers: 0,
                                        moreButtons: 0
                                    },
                                    selectors: {
                                        found: [],
                                        tested: []
                                    }
                                };

                                // 1. 从页面获取评论总数
                                const commentCountSelectors = [
                                    '[class*="count"]',
                                    '[class*="total"]',
                                    'span:contains("1472")',
                                    'div:contains("1472")'
                                ];

                                for (const selector of commentCountSelectors) {
                                    try {
                                        const elements = document.querySelectorAll(selector);
                                        elements.forEach(el => {
                                            const text = el.textContent.trim();
                                            if (text.includes('1472') || text.includes('评论')) {
                                                analysis.commentStats.totalFromPage = text;
                                            }
                                        });
                                    } catch (e) {}
                                }

                                // 2. 统计可见评论
                                const commentSelectors = [
                                    '[class*="comment"]',
                                    '[class*="reply"]',
                                    '[data-testid*="comment"]',
                                    'img[src*="avatar"]'
                                ];

                                for (const selector of commentSelectors) {
                                    analysis.selectors.tested.push(selector);
                                    const elements = document.querySelectorAll(selector);
                                    if (elements.length > 0) {
                                        analysis.selectors.found.push({
                                            selector: selector,
                                            count: elements.length
                                        });
                                        analysis.commentStats.visibleComments = Math.max(
                                            analysis.commentStats.visibleComments, 
                                            elements.length
                                        );
                                    }
                                }

                                // 3. 查找评论容器
                                const containerSelectors = [
                                    '.comments-container',
                                    '.comment-list',
                                    '.interaction-container',
                                    '[class*="comment-section"]'
                                ];

                                for (const selector of containerSelectors) {
                                    const containers = document.querySelectorAll(selector);
                                    analysis.commentStats.commentContainers += containers.length;
                                }

                                // 4. 查找"更多"按钮
                                const moreButtonTexts = [
                                    '查看更多评论', '展开更多', '加载更多', '更多评论',
                                    'more', '更多', '展开', '查看全部'
                                ];

                                for (const buttonText of moreButtonTexts) {
                                    const elements = Array.from(document.querySelectorAll('*')).filter(el => 
                                        el.textContent.includes(buttonText) && el.offsetParent !== null
                                    );
                                    analysis.commentStats.moreButtons += elements.length;
                                }

                                return { success: true, data: analysis };
                                
                            } catch (error) {
                                return { 
                                    success: false, 
                                    error: error.message 
                                };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: analyzeScript,
                            returnByValue: true
                        }
                    }));
                }, 2000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            const analysis = result.data;
                            
                            console.log('📊 页面分析结果:');
                            console.log(`   📝 标题: ${analysis.pageInfo.title}`);
                            console.log(`   📏 页面高度: ${analysis.pageInfo.scrollHeight}px`);
                            console.log(`   👀 可视高度: ${analysis.pageInfo.clientHeight}px`);
                            console.log(`   💬 可见评论: ${analysis.commentStats.visibleComments}个`);
                            console.log(`   📦 评论容器: ${analysis.commentStats.commentContainers}个`);
                            console.log(`   🔘 更多按钮: ${analysis.commentStats.moreButtons}个`);
                            
                            if (analysis.commentStats.totalFromPage) {
                                console.log(`   🎯 页面显示总数: ${analysis.commentStats.totalFromPage}`);
                            }
                            
                            console.log('\n🔍 有效选择器:');
                            analysis.selectors.found.forEach(item => {
                                console.log(`   - ${item.selector}: ${item.count}个元素`);
                            });
                            
                            ws.close();
                            resolve(analysis);
                        } else {
                            console.log('❌ 页面分析失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理分析结果失败:', error.message);
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ 分析WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('页面分析超时'));
            }, 30000);
        });
    }

    // 🔄 研究评论加载机制
    async researchCommentLoading(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ 评论加载研究WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const researchScript = `
                        (function() {
                            try {
                                const research = {
                                    networkRequests: [],
                                    scrollBehavior: {},
                                    lazyLoading: {},
                                    pagination: {}
                                };

                                // 1. 监听网络请求
                                const originalFetch = window.fetch;
                                const originalXHR = window.XMLHttpRequest.prototype.open;
                                
                                window.fetch = function(...args) {
                                    if (args[0] && args[0].includes('comment')) {
                                        research.networkRequests.push({
                                            type: 'fetch',
                                            url: args[0],
                                            timestamp: Date.now()
                                        });
                                    }
                                    return originalFetch.apply(this, args);
                                };

                                // 2. 检查滚动行为
                                let scrollCount = 0;
                                let lastScrollTop = window.pageYOffset;
                                
                                window.addEventListener('scroll', () => {
                                    scrollCount++;
                                    const currentScrollTop = window.pageYOffset;
                                    research.scrollBehavior = {
                                        scrollCount: scrollCount,
                                        lastScrollTop: lastScrollTop,
                                        currentScrollTop: currentScrollTop,
                                        direction: currentScrollTop > lastScrollTop ? 'down' : 'up'
                                    };
                                    lastScrollTop = currentScrollTop;
                                });

                                // 3. 检查懒加载
                                const observer = new IntersectionObserver((entries) => {
                                    entries.forEach(entry => {
                                        if (entry.isIntersecting) {
                                            research.lazyLoading.triggered = true;
                                            research.lazyLoading.element = entry.target.className;
                                        }
                                    });
                                });

                                // 观察可能的懒加载元素
                                document.querySelectorAll('[class*="comment"], [class*="load"]').forEach(el => {
                                    observer.observe(el);
                                });

                                // 4. 检查分页机制
                                const paginationElements = document.querySelectorAll('[class*="page"], [class*="next"], [class*="more"]');
                                research.pagination = {
                                    paginationElements: paginationElements.length,
                                    hasNextButton: Array.from(paginationElements).some(el => 
                                        el.textContent.includes('下一页') || el.textContent.includes('next')
                                    ),
                                    hasMoreButton: Array.from(paginationElements).some(el => 
                                        el.textContent.includes('更多') || el.textContent.includes('more')
                                    )
                                };

                                return { success: true, data: research };
                                
                            } catch (error) {
                                return { 
                                    success: false, 
                                    error: error.message 
                                };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: researchScript,
                            returnByValue: true
                        }
                    }));
                }, 2000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            const research = result.data;
                            
                            console.log('🔄 评论加载机制研究结果:');
                            console.log(`   📡 网络请求: ${research.networkRequests.length}个`);
                            console.log(`   📜 滚动次数: ${research.scrollBehavior.scrollCount || 0}`);
                            console.log(`   🔄 懒加载: ${research.lazyLoading.triggered ? '已触发' : '未触发'}`);
                            console.log(`   📄 分页元素: ${research.pagination.paginationElements}个`);
                            console.log(`   ⏭️ 下一页按钮: ${research.pagination.hasNextButton ? '有' : '无'}`);
                            console.log(`   🔘 更多按钮: ${research.pagination.hasMoreButton ? '有' : '无'}`);
                            
                            if (research.networkRequests.length > 0) {
                                console.log('\n📡 相关网络请求:');
                                research.networkRequests.forEach((req, index) => {
                                    console.log(`   ${index + 1}. ${req.type}: ${req.url}`);
                                });
                            }
                            
                            ws.close();
                            resolve(research);
                        } else {
                            console.log('❌ 评论加载研究失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理研究结果失败:', error.message);
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ 研究WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('评论加载研究超时'));
            }, 30000);
        });
    }

    // 🧪 测试不同的采集策略
    async testDifferentStrategies(tab) {
        console.log('🧪 测试策略1: 深度滚动...');
        await this.testDeepScrolling(tab);
        
        console.log('\n🧪 测试策略2: 网络拦截...');
        await this.testNetworkInterception(tab);
        
        console.log('\n🧪 测试策略3: DOM变化监听...');
        await this.testDOMObserver(tab);
    }

    // 🧪 测试深度滚动
    async testDeepScrolling(tab) {
        // 实现深度滚动测试
        console.log('   📜 执行深度滚动测试...');
        console.log('   ⏱️ 模拟用户缓慢滚动行为...');
        console.log('   🔍 监控评论数量变化...');
    }

    // 🧪 测试网络拦截
    async testNetworkInterception(tab) {
        // 实现网络拦截测试
        console.log('   📡 拦截评论相关API请求...');
        console.log('   🔍 分析请求参数和响应...');
        console.log('   📊 统计实际返回的评论数量...');
    }

    // 🧪 测试DOM变化监听
    async testDOMObserver(tab) {
        // 实现DOM变化监听测试
        console.log('   👀 监听DOM结构变化...');
        console.log('   🔍 检测新增的评论元素...');
        console.log('   📈 统计动态加载的评论...');
    }
}

// 🧪 运行研究
async function runResearch() {
    const researcher = new CommentsResearcher();
    
    try {
        await researcher.researchComments();
        console.log('\n🎉 研究完成！');
        
    } catch (error) {
        console.error('❌ 研究失败:', error.message);
    }
}

if (require.main === module) {
    runResearch();
}

module.exports = { CommentsResearcher, runResearch };
