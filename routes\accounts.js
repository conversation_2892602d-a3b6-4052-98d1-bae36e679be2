// ===== 👥 账号管理 API 路由 【用户账号模块】 =====
// 📝 功能说明：处理所有与用户账号管理相关的API请求和数据操作
// 🎯 主要功能：账号列表查询、账号添加、信息更新、账号删除、批量操作、统计分析
// 🔧 技术特点：RESTful API设计、分页查询、条件筛选、批量处理
// 💾 数据管理：内存存储（演示）+ 数据验证 + 状态管理

const express = require('express');     // 🌐 Express框架 - Web路由处理
const router = express.Router();       // 🛣️ 路由器实例 - 模块化路由管理
const { v4: uuidv4 } = require('uuid'); // 🆔 UUID生成库 - 创建唯一标识符

// ===== 模拟账号数据存储 =====
// 在实际项目中，这些数据应该存储在数据库中（如MySQL、MongoDB等）
// 这里使用内存数组进行演示，重启服务器后数据会丢失
let accounts = [
    {
        id: uuidv4(),                    // 唯一标识符
        username: '测试账号001',          // 账号用户名
        platform: '小红书',              // 所属平台
        status: 'online',               // 账号状态：online(在线)/offline(离线)/pending(待审核)
        group: 'default',               // 所属分组ID
        followers: 1250,                // 粉丝数量
        following: 156,                 // 关注数量
        posts: 45,                      // 发布内容数量
        likes: 2800,                    // 获得点赞总数
        createdAt: '2023-04-10T16:46:11Z',  // 账号创建时间
        lastLogin: '2023-04-10T16:46:11Z',  // 最后登录时间
        avatar: 'https://via.placeholder.com/50'  // 头像URL
    },
    {
        id: uuidv4(),                    // 唯一标识符
        username: '测试账号002',          // 账号用户名
        platform: '抖音',                // 所属平台
        status: 'offline',              // 账号状态
        group: 'vip',                   // 所属分组ID
        followers: 5680,                // 粉丝数量
        following: 89,                  // 关注数量
        posts: 123,                     // 发布内容数量
        likes: 15600,                   // 获得点赞总数
        createdAt: '2023-04-08T10:20:30Z',  // 账号创建时间
        lastLogin: '2023-04-09T14:30:45Z',  // 最后登录时间
        avatar: 'https://via.placeholder.com/50'  // 头像URL
    }
];

// ===== GET /api/accounts - 获取账号列表 =====
// 支持分页、筛选、搜索等功能
router.get('/', (req, res) => {
    // 从查询参数中提取筛选条件
    const {
        page = 1,           // 页码，默认第1页
        limit = 20,         // 每页数量，默认20条
        status,             // 状态筛选：online/offline/pending
        group,              // 分组筛选
        platform,           // 平台筛选：小红书/抖音/微博
        search              // 搜索关键词（用户名）
    } = req.query;

    // 复制账号数组，避免修改原数据
    let filteredAccounts = [...accounts];

    // ===== 状态筛选 =====
    if (status) {
        filteredAccounts = filteredAccounts.filter(acc => acc.status === status);
    }
    
    // 分组过滤
    if (group) {
        filteredAccounts = filteredAccounts.filter(acc => acc.group === group);
    }
    
    // 平台过滤
    if (platform) {
        filteredAccounts = filteredAccounts.filter(acc => acc.platform === platform);
    }
    
    // 搜索过滤
    if (search) {
        filteredAccounts = filteredAccounts.filter(acc => 
            acc.username.toLowerCase().includes(search.toLowerCase())
        );
    }
    
    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedAccounts = filteredAccounts.slice(startIndex, endIndex);
    
    res.json({
        success: true,
        data: {
            accounts: paginatedAccounts,
            total: filteredAccounts.length,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(filteredAccounts.length / limit)
        }
    });
});

// 获取单个账号详情
router.get('/:id', (req, res) => {
    const { id } = req.params;
    const account = accounts.find(acc => acc.id === id);
    
    if (!account) {
        return res.status(404).json({
            success: false,
            message: '账号不存在'
        });
    }
    
    res.json({
        success: true,
        data: account
    });
});

// 创建新账号
router.post('/', (req, res) => {
    const { username, platform, group = 'default' } = req.body;
    
    if (!username || !platform) {
        return res.status(400).json({
            success: false,
            message: '用户名和平台不能为空'
        });
    }
    
    // 检查用户名是否已存在
    if (accounts.some(acc => acc.username === username && acc.platform === platform)) {
        return res.status(400).json({
            success: false,
            message: '该平台上的用户名已存在'
        });
    }
    
    const newAccount = {
        id: uuidv4(),
        username,
        platform,
        status: 'offline',
        group,
        followers: 0,
        following: 0,
        posts: 0,
        likes: 0,
        createdAt: new Date().toISOString(),
        lastLogin: null,
        avatar: 'https://via.placeholder.com/50'
    };
    
    accounts.push(newAccount);
    
    res.status(201).json({
        success: true,
        data: newAccount,
        message: '账号创建成功'
    });
});

// 更新账号信息
router.put('/:id', (req, res) => {
    const { id } = req.params;
    const updates = req.body;
    
    const accountIndex = accounts.findIndex(acc => acc.id === id);
    
    if (accountIndex === -1) {
        return res.status(404).json({
            success: false,
            message: '账号不存在'
        });
    }
    
    // 更新账号信息
    accounts[accountIndex] = {
        ...accounts[accountIndex],
        ...updates,
        id // 确保ID不被修改
    };
    
    res.json({
        success: true,
        data: accounts[accountIndex],
        message: '账号更新成功'
    });
});

// 删除账号
router.delete('/:id', (req, res) => {
    const { id } = req.params;
    const accountIndex = accounts.findIndex(acc => acc.id === id);
    
    if (accountIndex === -1) {
        return res.status(404).json({
            success: false,
            message: '账号不存在'
        });
    }
    
    const deletedAccount = accounts.splice(accountIndex, 1)[0];
    
    res.json({
        success: true,
        data: deletedAccount,
        message: '账号删除成功'
    });
});

// 批量操作
router.post('/batch', (req, res) => {
    const { action, accountIds } = req.body;
    
    if (!action || !accountIds || !Array.isArray(accountIds)) {
        return res.status(400).json({
            success: false,
            message: '操作类型和账号ID列表不能为空'
        });
    }
    
    let affectedAccounts = [];
    
    switch (action) {
        case 'login':
            accountIds.forEach(id => {
                const account = accounts.find(acc => acc.id === id);
                if (account) {
                    account.status = 'online';
                    account.lastLogin = new Date().toISOString();
                    affectedAccounts.push(account);
                }
            });
            break;
            
        case 'logout':
            accountIds.forEach(id => {
                const account = accounts.find(acc => acc.id === id);
                if (account) {
                    account.status = 'offline';
                    affectedAccounts.push(account);
                }
            });
            break;
            
        case 'delete':
            accountIds.forEach(id => {
                const accountIndex = accounts.findIndex(acc => acc.id === id);
                if (accountIndex !== -1) {
                    affectedAccounts.push(accounts.splice(accountIndex, 1)[0]);
                }
            });
            break;
            
        default:
            return res.status(400).json({
                success: false,
                message: '不支持的操作类型'
            });
    }
    
    res.json({
        success: true,
        data: affectedAccounts,
        message: `批量${action}操作完成，影响${affectedAccounts.length}个账号`
    });
});

// 获取账号统计信息
router.get('/stats/summary', (req, res) => {
    const stats = {
        total: accounts.length,
        online: accounts.filter(acc => acc.status === 'online').length,
        offline: accounts.filter(acc => acc.status === 'offline').length,
        groups: {},
        platforms: {}
    };
    
    // 统计分组
    accounts.forEach(acc => {
        stats.groups[acc.group] = (stats.groups[acc.group] || 0) + 1;
        stats.platforms[acc.platform] = (stats.platforms[acc.platform] || 0) + 1;
    });
    
    res.json({
        success: true,
        data: stats
    });
});

module.exports = router;
