// ===== 聊天功能 API 路由 =====
// 这个文件处理聊天相关的API请求
// 提供聊天室管理、消息发送、用户管理等功能

const express = require('express');
const router = express.Router();

// ===== 聊天室数据存储 =====
// 在实际项目中，这些数据应该存储在数据库中（如Redis、MongoDB等）
// 这里使用内存对象进行演示，重启服务器后数据会丢失
let chatRooms = {
    'general': {                // 综合讨论聊天室
        name: '综合讨论',        // 聊天室名称
        messages: [],           // 消息列表
        users: []              // 在线用户列表
    },
    'tech': {                  // 技术交流聊天室
        name: '技术交流',        // 聊天室名称
        messages: [],          // 消息列表
        users: []             // 在线用户列表
    },
    'support': {              // 客服支持聊天室
        name: '客服支持',        // 聊天室名称
        messages: [],         // 消息列表
        users: []            // 在线用户列表
    }
};

// 获取聊天室列表
router.get('/rooms', (req, res) => {
    const rooms = Object.keys(chatRooms).map(id => ({
        id,
        name: chatRooms[id].name,
        userCount: chatRooms[id].users.length,
        messageCount: chatRooms[id].messages.length
    }));
    
    res.json({
        success: true,
        data: rooms
    });
});

// 获取聊天室消息
router.get('/rooms/:roomId/messages', (req, res) => {
    const { roomId } = req.params;
    const { limit = 50, offset = 0 } = req.query;
    
    if (!chatRooms[roomId]) {
        return res.status(404).json({
            success: false,
            message: '聊天室不存在'
        });
    }
    
    const messages = chatRooms[roomId].messages
        .slice(-limit - offset, -offset || undefined)
        .reverse();
    
    res.json({
        success: true,
        data: messages
    });
});

// 发送消息
router.post('/rooms/:roomId/messages', (req, res) => {
    const { roomId } = req.params;
    const { user, message } = req.body;
    
    if (!chatRooms[roomId]) {
        return res.status(404).json({
            success: false,
            message: '聊天室不存在'
        });
    }
    
    if (!user || !message) {
        return res.status(400).json({
            success: false,
            message: '用户名和消息内容不能为空'
        });
    }
    
    const newMessage = {
        id: Date.now(),
        user,
        message,
        timestamp: new Date().toISOString()
    };
    
    chatRooms[roomId].messages.push(newMessage);
    
    // 只保留最近1000条消息
    if (chatRooms[roomId].messages.length > 1000) {
        chatRooms[roomId].messages = chatRooms[roomId].messages.slice(-1000);
    }
    
    res.json({
        success: true,
        data: newMessage
    });
});

// 加入聊天室
router.post('/rooms/:roomId/join', (req, res) => {
    const { roomId } = req.params;
    const { user } = req.body;
    
    if (!chatRooms[roomId]) {
        return res.status(404).json({
            success: false,
            message: '聊天室不存在'
        });
    }
    
    if (!user) {
        return res.status(400).json({
            success: false,
            message: '用户名不能为空'
        });
    }
    
    // 检查用户是否已在聊天室中
    if (!chatRooms[roomId].users.includes(user)) {
        chatRooms[roomId].users.push(user);
    }
    
    res.json({
        success: true,
        message: '成功加入聊天室'
    });
});

// 离开聊天室
router.post('/rooms/:roomId/leave', (req, res) => {
    const { roomId } = req.params;
    const { user } = req.body;
    
    if (!chatRooms[roomId]) {
        return res.status(404).json({
            success: false,
            message: '聊天室不存在'
        });
    }
    
    chatRooms[roomId].users = chatRooms[roomId].users.filter(u => u !== user);
    
    res.json({
        success: true,
        message: '已离开聊天室'
    });
});

module.exports = router;
