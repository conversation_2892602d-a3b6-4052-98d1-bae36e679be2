// ===== 消息管理 API 路由 =====
// 这个文件处理消息管理相关的API请求
// 提供消息模板管理、消息任务管理、批量发送等功能

const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid'); // UUID生成库，用于创建唯一ID

// ===== 消息数据存储 =====
// 在实际项目中，这些数据应该存储在数据库中
let messageTasks = [];          // 消息任务列表
let messageTemplates = [        // 消息模板列表
    {
        id: uuidv4(),                    // 模板唯一ID
        name: '欢迎消息模板',             // 模板名称
        content: '欢迎关注我们！我们会定期分享有价值的内容。',  // 模板内容
        type: 'welcome',                 // 模板类型：welcome(欢迎)/promotion(推广)/notification(通知)
        createdAt: new Date().toISOString()  // 创建时间
    },
    {
        id: uuidv4(),                    // 模板唯一ID
        name: '产品推广模板',             // 模板名称
        content: '我们的新产品已经上线，限时优惠中！点击链接了解详情：{link}',  // 模板内容（支持变量）
        type: 'promotion',               // 模板类型
        createdAt: new Date().toISOString()  // 创建时间
    }
];

// 获取消息模板列表
router.get('/templates', (req, res) => {
    res.json({
        success: true,
        data: messageTemplates
    });
});

// 创建消息模板
router.post('/templates', (req, res) => {
    const { name, content, type = 'custom' } = req.body;
    
    if (!name || !content) {
        return res.status(400).json({
            success: false,
            message: '模板名称和内容不能为空'
        });
    }
    
    const newTemplate = {
        id: uuidv4(),
        name,
        content,
        type,
        createdAt: new Date().toISOString()
    };
    
    messageTemplates.push(newTemplate);
    
    res.status(201).json({
        success: true,
        data: newTemplate,
        message: '模板创建成功'
    });
});

// 更新消息模板
router.put('/templates/:id', (req, res) => {
    const { id } = req.params;
    const updates = req.body;
    
    const templateIndex = messageTemplates.findIndex(t => t.id === id);
    
    if (templateIndex === -1) {
        return res.status(404).json({
            success: false,
            message: '模板不存在'
        });
    }
    
    messageTemplates[templateIndex] = {
        ...messageTemplates[templateIndex],
        ...updates,
        id
    };
    
    res.json({
        success: true,
        data: messageTemplates[templateIndex],
        message: '模板更新成功'
    });
});

// 删除消息模板
router.delete('/templates/:id', (req, res) => {
    const { id } = req.params;
    const templateIndex = messageTemplates.findIndex(t => t.id === id);
    
    if (templateIndex === -1) {
        return res.status(404).json({
            success: false,
            message: '模板不存在'
        });
    }
    
    const deletedTemplate = messageTemplates.splice(templateIndex, 1)[0];
    
    res.json({
        success: true,
        data: deletedTemplate,
        message: '模板删除成功'
    });
});

// 创建群发任务
router.post('/tasks', (req, res) => {
    const { 
        name, 
        templateId, 
        customMessage, 
        targetAccounts, 
        scheduledTime,
        settings = {} 
    } = req.body;
    
    if (!name || (!templateId && !customMessage) || !targetAccounts || targetAccounts.length === 0) {
        return res.status(400).json({
            success: false,
            message: '任务名称、消息内容和目标账号不能为空'
        });
    }
    
    let messageContent = customMessage;
    
    // 如果使用模板，获取模板内容
    if (templateId) {
        const template = messageTemplates.find(t => t.id === templateId);
        if (!template) {
            return res.status(404).json({
                success: false,
                message: '消息模板不存在'
            });
        }
        messageContent = template.content;
    }
    
    const newTask = {
        id: uuidv4(),
        name,
        templateId,
        messageContent,
        targetAccounts,
        scheduledTime: scheduledTime || new Date().toISOString(),
        status: 'pending', // pending, running, completed, failed, paused
        progress: {
            total: targetAccounts.length,
            sent: 0,
            failed: 0,
            success: 0
        },
        settings: {
            delay: settings.delay || 1000, // 发送间隔（毫秒）
            retryCount: settings.retryCount || 3, // 重试次数
            ...settings
        },
        createdAt: new Date().toISOString(),
        startedAt: null,
        completedAt: null,
        results: []
    };
    
    messageTasks.push(newTask);
    
    res.status(201).json({
        success: true,
        data: newTask,
        message: '群发任务创建成功'
    });
});

// 获取群发任务列表
router.get('/tasks', (req, res) => {
    const { page = 1, limit = 20, status } = req.query;
    
    let filteredTasks = [...messageTasks];
    
    if (status) {
        filteredTasks = filteredTasks.filter(task => task.status === status);
    }
    
    // 按创建时间倒序排列
    filteredTasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    
    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedTasks = filteredTasks.slice(startIndex, endIndex);
    
    res.json({
        success: true,
        data: {
            tasks: paginatedTasks,
            total: filteredTasks.length,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(filteredTasks.length / limit)
        }
    });
});

// 获取单个任务详情
router.get('/tasks/:id', (req, res) => {
    const { id } = req.params;
    const task = messageTasks.find(t => t.id === id);
    
    if (!task) {
        return res.status(404).json({
            success: false,
            message: '任务不存在'
        });
    }
    
    res.json({
        success: true,
        data: task
    });
});

// 启动群发任务
router.post('/tasks/:id/start', (req, res) => {
    const { id } = req.params;
    const task = messageTasks.find(t => t.id === id);
    
    if (!task) {
        return res.status(404).json({
            success: false,
            message: '任务不存在'
        });
    }
    
    if (task.status !== 'pending' && task.status !== 'paused') {
        return res.status(400).json({
            success: false,
            message: '只能启动待执行或暂停的任务'
        });
    }
    
    task.status = 'running';
    task.startedAt = new Date().toISOString();
    
    // 模拟异步执行任务
    simulateTaskExecution(task);
    
    res.json({
        success: true,
        data: task,
        message: '任务已启动'
    });
});

// 暂停群发任务
router.post('/tasks/:id/pause', (req, res) => {
    const { id } = req.params;
    const task = messageTasks.find(t => t.id === id);
    
    if (!task) {
        return res.status(404).json({
            success: false,
            message: '任务不存在'
        });
    }
    
    if (task.status !== 'running') {
        return res.status(400).json({
            success: false,
            message: '只能暂停正在执行的任务'
        });
    }
    
    task.status = 'paused';
    
    res.json({
        success: true,
        data: task,
        message: '任务已暂停'
    });
});

// 停止群发任务
router.post('/tasks/:id/stop', (req, res) => {
    const { id } = req.params;
    const task = messageTasks.find(t => t.id === id);
    
    if (!task) {
        return res.status(404).json({
            success: false,
            message: '任务不存在'
        });
    }
    
    if (task.status !== 'running' && task.status !== 'paused') {
        return res.status(400).json({
            success: false,
            message: '只能停止正在执行或暂停的任务'
        });
    }
    
    task.status = 'failed';
    task.completedAt = new Date().toISOString();
    
    res.json({
        success: true,
        data: task,
        message: '任务已停止'
    });
});

// 删除群发任务
router.delete('/tasks/:id', (req, res) => {
    const { id } = req.params;
    const taskIndex = messageTasks.findIndex(t => t.id === id);
    
    if (taskIndex === -1) {
        return res.status(404).json({
            success: false,
            message: '任务不存在'
        });
    }
    
    const task = messageTasks[taskIndex];
    
    if (task.status === 'running') {
        return res.status(400).json({
            success: false,
            message: '不能删除正在执行的任务'
        });
    }
    
    const deletedTask = messageTasks.splice(taskIndex, 1)[0];
    
    res.json({
        success: true,
        data: deletedTask,
        message: '任务删除成功'
    });
});

// 模拟任务执行
function simulateTaskExecution(task) {
    const executeStep = () => {
        if (task.status !== 'running') return;
        
        if (task.progress.sent < task.progress.total) {
            // 模拟发送消息
            const success = Math.random() > 0.1; // 90% 成功率
            
            if (success) {
                task.progress.success++;
            } else {
                task.progress.failed++;
            }
            
            task.progress.sent++;
            
            // 添加结果记录
            task.results.push({
                accountId: task.targetAccounts[task.progress.sent - 1],
                status: success ? 'success' : 'failed',
                timestamp: new Date().toISOString(),
                error: success ? null : '发送失败'
            });
            
            // 继续下一步
            setTimeout(executeStep, task.settings.delay);
        } else {
            // 任务完成
            task.status = 'completed';
            task.completedAt = new Date().toISOString();
        }
    };
    
    // 开始执行
    setTimeout(executeStep, task.settings.delay);
}

module.exports = router;
