// ===== 系统监控 API 路由 =====
// 这个文件处理系统监控相关的API请求
// 提供系统状态、性能指标、实时图表数据等功能

const express = require('express');
const router = express.Router();

// ===== GET /api/monitor/stats - 获取监控统计数据 =====
// 返回系统运行状态的关键指标
router.get('/stats', (req, res) => {
    // ===== 模拟监控数据 =====
    // 在实际项目中，这些数据应该从监控系统中获取
    const stats = {
        totalAccounts: 1234,        // 总账号数量
        onlineAccounts: 856,        // 当前在线账号数
        todayMessages: 2468,        // 今日消息数量
        successRate: 98.5,          // 成功率（百分比）
        timestamp: new Date().toISOString()  // 数据时间戳
    };

    // 返回标准API响应格式
    res.json({
        success: true,      // 请求是否成功
        data: stats         // 监控数据
    });
});

// 获取实时图表数据
router.get('/charts', (req, res) => {
    // 模拟图表数据
    const chartData = {
        labels: [],
        datasets: [{
            label: '在线账号数',
            data: [],
            borderColor: '#1a73e8',
            backgroundColor: 'rgba(26, 115, 232, 0.1)'
        }]
    };
    
    // 生成最近24小时的数据
    const now = new Date();
    for (let i = 23; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000);
        chartData.labels.push(time.getHours() + ':00');
        chartData.datasets[0].data.push(Math.floor(Math.random() * 200) + 800);
    }
    
    res.json({
        success: true,
        data: chartData
    });
});

// 获取系统状态
router.get('/system', (req, res) => {
    const systemInfo = {
        cpu: Math.floor(Math.random() * 30) + 20,
        memory: Math.floor(Math.random() * 40) + 30,
        disk: Math.floor(Math.random() * 20) + 10,
        network: Math.floor(Math.random() * 100) + 500,
        uptime: process.uptime()
    };
    
    res.json({
        success: true,
        data: systemInfo
    });
});

module.exports = router;
