// ===== 数据总览 API 路由 =====
// 这个文件处理数据总览页面的API请求
// 提供平台整体数据统计、趋势分析、账号详情等功能

const express = require('express');
const router = express.Router();

// ===== GET /api/overview/stats - 获取数据总览统计 =====
// 返回平台整体的数据统计信息
router.get('/stats', (req, res) => {
    // ===== 模拟总体数据统计 =====
    // 在实际项目中，这些数据应该从数据库中实时计算获得
    const stats = {
        // ===== 基础数据统计 =====
        totalViews: 15680000,      // 总播放量/浏览量（所有平台累计）
        totalLikes: 892000,        // 总点赞量（所有平台累计）
        totalComments: 156000,     // 总评论量（所有平台累计）
        totalCollections: 89000,   // 总收藏量（所有平台累计）

        // ===== 增长率数据 =====
        // 相比上一周期的增长百分比
        viewsGrowth: 12.5,         // 播放量增长率（%）
        likesGrowth: 8.3,          // 点赞量增长率（%）
        commentsGrowth: 15.2,      // 评论量增长率（%）
        collectionsGrowth: 6.8,    // 收藏量增长率（%）

        // ===== 数据时间戳 =====
        timestamp: new Date().toISOString()  // 数据生成时间
    };

    // 返回标准API响应格式
    res.json({
        success: true,      // 请求是否成功
        data: stats         // 统计数据
    });
});

// 获取账号详细数据
router.get('/accounts', (req, res) => {
    // 模拟账号数据，包含作品信息
    const accountsData = [
        {
            id: 'acc-001',
            username: '美食达人小王',
            platform: '小红书',
            status: 'online',
            avatar: 'https://picsum.photos/50/50?random=1',
            works: [
                {
                    id: 'work-001',
                    title: '超简单的家常菜做法',
                    thumbnail: 'https://picsum.photos/200/150?random=101',
                    duration: '02:30',
                    publishDate: '2024-07-20T10:30:00Z',
                    views: 125000,
                    likes: 8500,
                    comments: 320,
                    collections: 1200
                },
                {
                    id: 'work-002',
                    title: '夏日清爽饮品制作',
                    thumbnail: 'https://picsum.photos/200/150?random=102',
                    duration: '01:45',
                    publishDate: '2024-07-18T14:20:00Z',
                    views: 89000,
                    likes: 6200,
                    comments: 180,
                    collections: 890
                },
                {
                    id: 'work-003',
                    title: '烘焙新手必学技巧',
                    thumbnail: 'https://picsum.photos/200/150?random=103',
                    duration: '03:15',
                    publishDate: '2024-07-15T09:15:00Z',
                    views: 156000,
                    likes: 12000,
                    comments: 450,
                    collections: 2100
                }
            ]
        },
        {
            id: 'acc-002',
            username: '时尚穿搭师',
            platform: '抖音',
            status: 'online',
            avatar: 'https://picsum.photos/50/50?random=2',
            works: [
                {
                    id: 'work-004',
                    title: '秋季穿搭指南',
                    thumbnail: 'https://picsum.photos/200/150?random=201',
                    duration: '01:20',
                    publishDate: '2024-07-22T16:45:00Z',
                    views: 234000,
                    likes: 18500,
                    comments: 680,
                    collections: 3200
                },
                {
                    id: 'work-005',
                    title: '职场女性穿搭技巧',
                    thumbnail: 'https://picsum.photos/200/150?random=202',
                    duration: '02:10',
                    publishDate: '2024-07-19T11:30:00Z',
                    views: 178000,
                    likes: 14200,
                    comments: 520,
                    collections: 2800
                }
            ]
        },
        {
            id: 'acc-003',
            username: '健身教练阿强',
            platform: '快手',
            status: 'offline',
            avatar: 'https://picsum.photos/50/50?random=3',
            works: [
                {
                    id: 'work-006',
                    title: '居家健身30分钟',
                    thumbnail: 'https://picsum.photos/200/150?random=301',
                    duration: '30:00',
                    publishDate: '2024-07-21T07:00:00Z',
                    views: 345000,
                    likes: 25000,
                    comments: 890,
                    collections: 4500
                },
                {
                    id: 'work-007',
                    title: '新手减脂训练计划',
                    thumbnail: 'https://picsum.photos/200/150?random=302',
                    duration: '15:30',
                    publishDate: '2024-07-17T18:20:00Z',
                    views: 267000,
                    likes: 19800,
                    comments: 650,
                    collections: 3600
                },
                {
                    id: 'work-008',
                    title: '腹肌训练专项课程',
                    thumbnail: 'https://picsum.photos/200/150?random=303',
                    duration: '20:15',
                    publishDate: '2024-07-14T20:10:00Z',
                    views: 198000,
                    likes: 16500,
                    comments: 420,
                    collections: 2900
                }
            ]
        }
    ];
    
    res.json({
        success: true,
        data: {
            accounts: accountsData,
            total: accountsData.length
        }
    });
});

// 获取平台数据统计
router.get('/platforms', (req, res) => {
    const platformStats = {
        '小红书': {
            accounts: 8,
            totalViews: 5200000,
            totalLikes: 320000,
            totalComments: 45000,
            totalCollections: 28000
        },
        '抖音': {
            accounts: 12,
            totalViews: 7800000,
            totalLikes: 450000,
            totalComments: 78000,
            totalCollections: 42000
        },
        '快手': {
            accounts: 5,
            totalViews: 2680000,
            totalLikes: 122000,
            totalComments: 33000,
            totalCollections: 19000
        }
    };
    
    res.json({
        success: true,
        data: platformStats
    });
});

// 获取趋势数据
router.get('/trends', (req, res) => {
    const { period = '7d' } = req.query;
    
    // 生成趋势数据
    const trendData = {
        labels: [],
        datasets: [
            {
                label: '播放量',
                data: [],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)'
            },
            {
                label: '点赞量',
                data: [],
                borderColor: '#e74c3c',
                backgroundColor: 'rgba(231, 76, 60, 0.1)'
            },
            {
                label: '评论量',
                data: [],
                borderColor: '#f39c12',
                backgroundColor: 'rgba(243, 156, 18, 0.1)'
            },
            {
                label: '收藏量',
                data: [],
                borderColor: '#27ae60',
                backgroundColor: 'rgba(39, 174, 96, 0.1)'
            }
        ]
    };
    
    // 根据时间周期生成数据
    const days = period === '30d' ? 30 : 7;
    const now = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        trendData.labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
        
        // 生成模拟数据
        trendData.datasets[0].data.push(Math.floor(Math.random() * 50000) + 100000); // 播放量
        trendData.datasets[1].data.push(Math.floor(Math.random() * 5000) + 10000);   // 点赞量
        trendData.datasets[2].data.push(Math.floor(Math.random() * 1000) + 2000);    // 评论量
        trendData.datasets[3].data.push(Math.floor(Math.random() * 500) + 1000);     // 收藏量
    }
    
    res.json({
        success: true,
        data: trendData
    });
});

// 获取热门作品
router.get('/hot-works', (req, res) => {
    const { limit = 10, sortBy = 'views' } = req.query;
    
    // 模拟热门作品数据
    const hotWorks = [
        {
            id: 'work-hot-001',
            title: '爆款减脂餐制作教程',
            author: '美食达人小王',
            platform: '小红书',
            thumbnail: 'https://picsum.photos/200/150?random=hot1',
            views: 1250000,
            likes: 89000,
            comments: 3200,
            collections: 12000,
            publishDate: '2024-07-20T10:30:00Z'
        },
        {
            id: 'work-hot-002',
            title: '夏日清爽穿搭合集',
            author: '时尚穿搭师',
            platform: '抖音',
            thumbnail: 'https://picsum.photos/200/150?random=hot2',
            views: 980000,
            likes: 76000,
            comments: 2800,
            collections: 9500,
            publishDate: '2024-07-19T14:20:00Z'
        },
        {
            id: 'work-hot-003',
            title: '10分钟全身燃脂训练',
            author: '健身教练阿强',
            platform: '快手',
            thumbnail: 'https://picsum.photos/200/150?random=hot3',
            views: 1580000,
            likes: 125000,
            comments: 4500,
            collections: 18000,
            publishDate: '2024-07-18T07:00:00Z'
        }
    ];
    
    // 根据排序方式排序
    hotWorks.sort((a, b) => b[sortBy] - a[sortBy]);
    
    res.json({
        success: true,
        data: hotWorks.slice(0, parseInt(limit))
    });
});

module.exports = router;
