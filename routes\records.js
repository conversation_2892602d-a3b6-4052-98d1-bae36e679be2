
// ===== 记录管理 API 路由 =====
// 这个文件处理记录管理相关的API请求
// 提供发送记录查询、统计分析、错误日志等功能

const express = require('express');
const router = express.Router();
const moment = require('moment'); // 时间处理库，用于格式化和计算时间

// ===== 模拟发送记录数据 =====
// 在实际项目中，这些数据应该存储在数据库中
let sendRecords = [
    {
        id: '1',
        taskId: 'task-001',
        taskName: '新用户欢迎消息',
        accountId: 'acc-001',
        accountName: '测试账号001',
        platform: '小红书',
        targetUser: 'user123',
        messageContent: '欢迎关注我们！我们会定期分享有价值的内容。',
        status: 'success', // success, failed, pending
        sentAt: moment().subtract(2, 'hours').toISOString(),
        error: null,
        retryCount: 0
    },
    {
        id: '2',
        taskId: 'task-001',
        taskName: '新用户欢迎消息',
        accountId: 'acc-002',
        accountName: '测试账号002',
        platform: '抖音',
        targetUser: 'user456',
        messageContent: '欢迎关注我们！我们会定期分享有价值的内容。',
        status: 'failed',
        sentAt: moment().subtract(1, 'hour').toISOString(),
        error: '账号被限制发送消息',
        retryCount: 3
    },
    {
        id: '3',
        taskId: 'task-002',
        taskName: '产品推广活动',
        accountId: 'acc-001',
        accountName: '测试账号001',
        platform: '小红书',
        targetUser: 'user789',
        messageContent: '我们的新产品已经上线，限时优惠中！',
        status: 'success',
        sentAt: moment().subtract(30, 'minutes').toISOString(),
        error: null,
        retryCount: 1
    }
];

// 获取发送记录列表
router.get('/', (req, res) => {
    const { 
        page = 1, 
        limit = 20, 
        status, 
        platform,
        taskId,
        accountId,
        startDate,
        endDate,
        search
    } = req.query;
    
    let filteredRecords = [...sendRecords];
    
    // 状态过滤
    if (status) {
        filteredRecords = filteredRecords.filter(record => record.status === status);
    }
    
    // 平台过滤
    if (platform) {
        filteredRecords = filteredRecords.filter(record => record.platform === platform);
    }
    
    // 任务过滤
    if (taskId) {
        filteredRecords = filteredRecords.filter(record => record.taskId === taskId);
    }
    
    // 账号过滤
    if (accountId) {
        filteredRecords = filteredRecords.filter(record => record.accountId === accountId);
    }
    
    // 日期范围过滤
    if (startDate) {
        filteredRecords = filteredRecords.filter(record => 
            moment(record.sentAt).isAfter(moment(startDate))
        );
    }
    
    if (endDate) {
        filteredRecords = filteredRecords.filter(record => 
            moment(record.sentAt).isBefore(moment(endDate))
        );
    }
    
    // 搜索过滤
    if (search) {
        filteredRecords = filteredRecords.filter(record => 
            record.taskName.toLowerCase().includes(search.toLowerCase()) ||
            record.accountName.toLowerCase().includes(search.toLowerCase()) ||
            record.targetUser.toLowerCase().includes(search.toLowerCase()) ||
            record.messageContent.toLowerCase().includes(search.toLowerCase())
        );
    }
    
    // 按发送时间倒序排列
    filteredRecords.sort((a, b) => new Date(b.sentAt) - new Date(a.sentAt));
    
    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedRecords = filteredRecords.slice(startIndex, endIndex);
    
    res.json({
        success: true,
        data: {
            records: paginatedRecords,
            total: filteredRecords.length,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(filteredRecords.length / limit)
        }
    });
});

// 获取单条发送记录详情
router.get('/:id', (req, res) => {
    const { id } = req.params;
    const record = sendRecords.find(r => r.id === id);
    
    if (!record) {
        return res.status(404).json({
            success: false,
            message: '发送记录不存在'
        });
    }
    
    res.json({
        success: true,
        data: record
    });
});

// 获取发送统计信息
router.get('/stats/summary', (req, res) => {
    const { startDate, endDate, platform, accountId } = req.query;
    
    let filteredRecords = [...sendRecords];
    
    // 日期范围过滤
    if (startDate) {
        filteredRecords = filteredRecords.filter(record => 
            moment(record.sentAt).isAfter(moment(startDate))
        );
    }
    
    if (endDate) {
        filteredRecords = filteredRecords.filter(record => 
            moment(record.sentAt).isBefore(moment(endDate))
        );
    }
    
    // 平台过滤
    if (platform) {
        filteredRecords = filteredRecords.filter(record => record.platform === platform);
    }
    
    // 账号过滤
    if (accountId) {
        filteredRecords = filteredRecords.filter(record => record.accountId === accountId);
    }
    
    const stats = {
        total: filteredRecords.length,
        success: filteredRecords.filter(r => r.status === 'success').length,
        failed: filteredRecords.filter(r => r.status === 'failed').length,
        pending: filteredRecords.filter(r => r.status === 'pending').length,
        successRate: 0,
        platforms: {},
        accounts: {},
        hourlyStats: {}
    };
    
    // 计算成功率
    if (stats.total > 0) {
        stats.successRate = ((stats.success / stats.total) * 100).toFixed(2);
    }
    
    // 统计各平台数据
    filteredRecords.forEach(record => {
        if (!stats.platforms[record.platform]) {
            stats.platforms[record.platform] = {
                total: 0,
                success: 0,
                failed: 0,
                pending: 0
            };
        }
        
        stats.platforms[record.platform].total++;
        stats.platforms[record.platform][record.status]++;
        
        // 统计各账号数据
        if (!stats.accounts[record.accountId]) {
            stats.accounts[record.accountId] = {
                name: record.accountName,
                total: 0,
                success: 0,
                failed: 0,
                pending: 0
            };
        }
        
        stats.accounts[record.accountId].total++;
        stats.accounts[record.accountId][record.status]++;
        
        // 统计每小时数据
        const hour = moment(record.sentAt).format('YYYY-MM-DD HH:00');
        if (!stats.hourlyStats[hour]) {
            stats.hourlyStats[hour] = {
                total: 0,
                success: 0,
                failed: 0,
                pending: 0
            };
        }
        
        stats.hourlyStats[hour].total++;
        stats.hourlyStats[hour][record.status]++;
    });
    
    res.json({
        success: true,
        data: stats
    });
});

// 重新发送失败的消息
router.post('/:id/retry', (req, res) => {
    const { id } = req.params;
    const record = sendRecords.find(r => r.id === id);
    
    if (!record) {
        return res.status(404).json({
            success: false,
            message: '发送记录不存在'
        });
    }
    
    if (record.status !== 'failed') {
        return res.status(400).json({
            success: false,
            message: '只能重新发送失败的消息'
        });
    }
    
    // 模拟重新发送
    record.status = 'pending';
    record.retryCount++;
    record.error = null;
    
    // 模拟异步处理
    setTimeout(() => {
        const success = Math.random() > 0.3; // 70% 成功率
        record.status = success ? 'success' : 'failed';
        record.sentAt = new Date().toISOString();
        
        if (!success) {
            record.error = '重新发送失败';
        }
    }, 2000);
    
    res.json({
        success: true,
        data: record,
        message: '已提交重新发送请求'
    });
});

// 批量重新发送
router.post('/batch/retry', (req, res) => {
    const { recordIds } = req.body;
    
    if (!recordIds || !Array.isArray(recordIds)) {
        return res.status(400).json({
            success: false,
            message: '记录ID列表不能为空'
        });
    }
    
    const affectedRecords = [];
    
    recordIds.forEach(id => {
        const record = sendRecords.find(r => r.id === id);
        if (record && record.status === 'failed') {
            record.status = 'pending';
            record.retryCount++;
            record.error = null;
            affectedRecords.push(record);
            
            // 模拟异步处理
            setTimeout(() => {
                const success = Math.random() > 0.3;
                record.status = success ? 'success' : 'failed';
                record.sentAt = new Date().toISOString();
                
                if (!success) {
                    record.error = '重新发送失败';
                }
            }, Math.random() * 5000 + 1000);
        }
    });
    
    res.json({
        success: true,
        data: affectedRecords,
        message: `已提交${affectedRecords.length}条记录的重新发送请求`
    });
});

// 导出发送记录
router.get('/export/csv', (req, res) => {
    const { startDate, endDate, status, platform } = req.query;
    
    let filteredRecords = [...sendRecords];
    
    // 应用过滤条件
    if (startDate) {
        filteredRecords = filteredRecords.filter(record => 
            moment(record.sentAt).isAfter(moment(startDate))
        );
    }
    
    if (endDate) {
        filteredRecords = filteredRecords.filter(record => 
            moment(record.sentAt).isBefore(moment(endDate))
        );
    }
    
    if (status) {
        filteredRecords = filteredRecords.filter(record => record.status === status);
    }
    
    if (platform) {
        filteredRecords = filteredRecords.filter(record => record.platform === platform);
    }
    
    // 生成CSV内容
    const csvHeader = 'ID,任务名称,账号名称,平台,目标用户,消息内容,状态,发送时间,错误信息,重试次数\n';
    const csvContent = filteredRecords.map(record => {
        return [
            record.id,
            record.taskName,
            record.accountName,
            record.platform,
            record.targetUser,
            `"${record.messageContent.replace(/"/g, '""')}"`,
            record.status,
            moment(record.sentAt).format('YYYY-MM-DD HH:mm:ss'),
            record.error || '',
            record.retryCount
        ].join(',');
    }).join('\n');
    
    const csv = csvHeader + csvContent;
    
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename=send_records_${moment().format('YYYYMMDD_HHmmss')}.csv`);
    res.send('\ufeff' + csv); // 添加BOM以支持中文
});

module.exports = router;

