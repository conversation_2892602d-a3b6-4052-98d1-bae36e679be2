// ===== 📱 小红书管理 API 路由 【核心业务模块】 =====
// 📝 功能说明：处理所有与小红书平台相关的API请求和数据管理
// 🎯 主要功能：笔记数据分析、账号管理、置顶管理、权限设置、自动发布、数据采集
// 🔧 技术栈：Express Router + Axios + UUID + 比特浏览器集成
// 💾 数据源：比特浏览器实时采集 + 本地数据文件备份

const express = require('express');                                          // 🌐 Express框架 - Web路由处理
const router = express.Router();                                            // 🛣️ 路由器实例 - 模块化路由管理
const { v4: uuidv4 } = require('uuid');                                     // 🆔 UUID生成器 - 唯一标识符创建
const axios = require('axios');                                             // 🌐 HTTP客户端 - API请求处理
const XiaohongshuBrowserExtractor = require('../xiaohongshu_browser_extractor'); // 🔍 小红书数据提取器 - 浏览器数据采集

// ===== 🔧 比特浏览器API调用函数 【标准化接口】 =====
// 📝 功能说明：统一的比特浏览器API调用封装，符合POST+JSON规范
async function callBitBrowserAPI(endpoint, data = {}) {
    try {
        // 🔧 构建请求配置
        const requestConfig = {
            method: 'POST',
            url: `${BITBROWSER_CONFIG.api_url}${endpoint}`,
            headers: {
                ...BITBROWSER_CONFIG.request_config.headers,
                'Authorization': `Bearer ${BITBROWSER_CONFIG.api_token}`, // 🔑 Bearer Token认证
                'X-API-Key': BITBROWSER_CONFIG.api_token                   // 🔑 备用API Key认证
            },
            data: data,                                                    // 📋 JSON格式请求体
            timeout: BITBROWSER_CONFIG.request_config.timeout
        };

        console.log(`🌐 调用比特浏览器API: ${endpoint}`);
        console.log(`📤 请求数据:`, JSON.stringify(data, null, 2));

        // 📡 发送API请求
        const response = await axios(requestConfig);

        console.log(`📥 响应状态: ${response.status}`);
        console.log(`📥 响应数据:`, JSON.stringify(response.data, null, 2));

        // 🔍 检查响应格式
        if (response.data && typeof response.data === 'object') {
            if (response.data.success === true) {
                // ✅ 成功响应
                return {
                    success: true,
                    data: response.data.data || response.data,
                    message: response.data.msg || '操作成功'
                };
            } else if (response.data.success === false) {
                // ❌ 业务失败
                return {
                    success: false,
                    message: response.data.msg || '操作失败',
                    error: response.data.error || '未知错误'
                };
            } else {
                // 🔄 兼容旧格式响应
                return {
                    success: true,
                    data: response.data,
                    message: '操作成功'
                };
            }
        } else {
            throw new Error('响应数据格式错误');
        }

    } catch (error) {
        console.error(`❌ 比特浏览器API调用失败: ${endpoint}`, error.message);

        // 🚨 错误处理
        if (error.response) {
            // HTTP错误响应
            return {
                success: false,
                message: `API请求失败: ${error.response.status} ${error.response.statusText}`,
                error: error.response.data || error.message
            };
        } else if (error.code === 'ECONNREFUSED') {
            // 连接被拒绝
            return {
                success: false,
                message: '无法连接到比特浏览器服务，请确保比特浏览器已启动',
                error: 'CONNECTION_REFUSED'
            };
        } else {
            // 其他错误
            return {
                success: false,
                message: `请求失败: ${error.message}`,
                error: error.code || 'UNKNOWN_ERROR'
            };
        }
    }
}

// ===== 🌐 比特浏览器配置 【外部服务连接】 =====
// 📝 说明：比特浏览器是专业的反检测浏览器，用于模拟真实用户行为采集数据
// 🔧 API规范：所有接口使用POST请求，body传参JSON格式，返回success字段标识状态
const BITBROWSER_CONFIG = {
    api_url: "http://127.0.0.1:54345",              // 🔗 比特浏览器API地址 - 本地服务端口
    api_token: "ca28ee5ca6de4d209182a83aa16a2044",  // 🔑 API访问令牌 - 身份验证密钥
    browser_19_id: "0d094596cb404282be3f814b98139c74", // 🆔 目标浏览器实例ID - 19号浏览器(测试成功)

    // 📋 API端点配置
    endpoints: {
        browser_list: "/browser/list",               // 📋 获取浏览器列表
        browser_start: "/browser/open",              // 🚀 启动浏览器实例 (修正为open)
        browser_stop: "/browser/close",              // 🛑 停止浏览器实例 (修正为close)
        browser_delete: "/browser/delete",           // 🗑️ 删除浏览器实例
        browser_update: "/browser/update"            // ✏️ 更新浏览器配置
    },

    // 🔧 请求配置
    request_config: {
        timeout: 30000,                              // ⏱️ 请求超时时间(毫秒)
        headers: {
            'Content-Type': 'application/json'       // 📋 请求头 - JSON格式
        }
    }
};

// ===== 小红书账号数据存储 =====
let collectedAccounts = [];

// ===== 模拟小红书数据存储 =====
let xiaohongshuData = {
    notes: [
        {
            id: uuidv4(),
            title: "分享进厂经验，帮助新手了解厂情况",
            content: "详细介绍了工厂的工作环境、薪资待遇、注意事项等...",
            status: "已发布",
            views: 3774,
            likes: 84,
            comments: 23,
            shares: 12,
            isTop: false,
            privacy: "公开可见",
            tags: ["工厂经验", "新手指南", "职场分享"],
            publishTime: "2025-01-15T10:30:00Z",
            lastModified: "2025-01-15T10:30:00Z"
        },
        {
            id: uuidv4(),
            title: "市场卖菜的经验，想要生活的朋友！！！",
            content: "分享在市场卖菜的真实经历和经验技巧...",
            status: "已发布",
            views: 2156,
            likes: 67,
            comments: 18,
            shares: 8,
            isTop: true,
            privacy: "公开可见",
            tags: ["生活经验", "创业分享", "市场经验"],
            publishTime: "2025-01-14T14:20:00Z",
            lastModified: "2025-01-14T14:20:00Z"
        },
        {
            id: uuidv4(),
            title: "出厂工作的，存了7万块",
            content: "分享工厂工作的存钱经验和理财心得...",
            status: "已发布",
            views: 5432,
            likes: 156,
            comments: 45,
            shares: 23,
            isTop: false,
            privacy: "仅互关好友可见",
            tags: ["理财经验", "存钱技巧", "工厂生活"],
            publishTime: "2025-01-13T16:45:00Z",
            lastModified: "2025-01-13T16:45:00Z"
        }
    ],
    analytics: {
        totalNotes: 3,
        totalViews: 11362,
        totalLikes: 307,
        totalComments: 86,
        totalShares: 43,
        avgEngagement: 3.8,
        topPerformingNote: "出厂工作的，存了7万块",
        recentGrowth: {
            views: "+12.5%",
            likes: "+8.3%",
            followers: "+5.2%"
        }
    },
    publishTemplates: [
        {
            id: uuidv4(),
            name: "图文笔记模板",
            title: "📸 {主题} - {亮点描述}",
            content: `🎯 {开头吸引}

✨ 主要内容：
• {要点1}
• {要点2}  
• {要点3}

💡 实用建议：
1. {建议1}
2. {建议2}
3. {建议3}

🔥 总结：{总结内容}

#{标签1} #{标签2} #{标签3}`,
            tags: ["生活分享", "实用技巧", "干货分享"],
            bestTime: "19:00-22:00",
            category: "图文"
        },
        {
            id: uuidv4(),
            name: "视频笔记模板",
            title: "🎬 {视频主题} | {核心价值}",
            content: `🎥 视频亮点：
• {亮点1}
• {亮点2}
• {亮点3}

📝 详细说明：
{详细内容描述}

💬 互动话题：
{引导用户互动的问题}

#{视频标签1} #{视频标签2} #{视频标签3}`,
            tags: ["视频分享", "教程", "生活记录"],
            bestTime: "12:00-14:00, 18:00-21:00",
            category: "视频"
        }
    ]
};

// ===== API 路由定义 =====

// 获取小红书数据概览
router.get('/overview', (req, res) => {
    try {
        const overview = {
            ...xiaohongshuData.analytics,
            recentNotes: xiaohongshuData.notes.slice(0, 5),
            timestamp: new Date().toISOString()
        };
        
        res.json({
            success: true,
            data: overview,
            message: '数据获取成功'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '获取数据失败',
            error: error.message
        });
    }
});

// 获取所有笔记列表
router.get('/notes', (req, res) => {
    try {
        const { page = 1, limit = 10, status, privacy } = req.query;
        
        let filteredNotes = xiaohongshuData.notes;
        
        // 状态筛选
        if (status) {
            filteredNotes = filteredNotes.filter(note => note.status === status);
        }
        
        // 权限筛选
        if (privacy) {
            filteredNotes = filteredNotes.filter(note => note.privacy === privacy);
        }
        
        // 分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedNotes = filteredNotes.slice(startIndex, endIndex);
        
        res.json({
            success: true,
            data: {
                notes: paginatedNotes,
                total: filteredNotes.length,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(filteredNotes.length / limit)
            },
            message: '笔记列表获取成功'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '获取笔记列表失败',
            error: error.message
        });
    }
});

// 获取单个笔记详情
router.get('/notes/:id', (req, res) => {
    try {
        const { id } = req.params;
        const note = xiaohongshuData.notes.find(n => n.id === id);
        
        if (!note) {
            return res.status(404).json({
                success: false,
                message: '笔记不存在'
            });
        }
        
        res.json({
            success: true,
            data: note,
            message: '笔记详情获取成功'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '获取笔记详情失败',
            error: error.message
        });
    }
});

// 更新笔记（置顶、权限等）
router.put('/notes/:id', (req, res) => {
    try {
        const { id } = req.params;
        const updates = req.body;
        
        const noteIndex = xiaohongshuData.notes.findIndex(n => n.id === id);
        
        if (noteIndex === -1) {
            return res.status(404).json({
                success: false,
                message: '笔记不存在'
            });
        }
        
        // 更新笔记信息
        xiaohongshuData.notes[noteIndex] = {
            ...xiaohongshuData.notes[noteIndex],
            ...updates,
            lastModified: new Date().toISOString()
        };
        
        res.json({
            success: true,
            data: xiaohongshuData.notes[noteIndex],
            message: '笔记更新成功'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '更新笔记失败',
            error: error.message
        });
    }
});

// 批量更新笔记
router.put('/notes/batch', (req, res) => {
    try {
        const { noteIds, updates } = req.body;
        
        if (!noteIds || !Array.isArray(noteIds)) {
            return res.status(400).json({
                success: false,
                message: '请提供有效的笔记ID列表'
            });
        }
        
        const updatedNotes = [];
        
        noteIds.forEach(id => {
            const noteIndex = xiaohongshuData.notes.findIndex(n => n.id === id);
            if (noteIndex !== -1) {
                xiaohongshuData.notes[noteIndex] = {
                    ...xiaohongshuData.notes[noteIndex],
                    ...updates,
                    lastModified: new Date().toISOString()
                };
                updatedNotes.push(xiaohongshuData.notes[noteIndex]);
            }
        });
        
        res.json({
            success: true,
            data: {
                updatedCount: updatedNotes.length,
                updatedNotes: updatedNotes
            },
            message: `成功更新 ${updatedNotes.length} 个笔记`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '批量更新失败',
            error: error.message
        });
    }
});

// 发布新笔记
router.post('/notes', (req, res) => {
    try {
        const { title, content, tags, privacy = "公开可见", scheduleTime } = req.body;
        
        if (!title || !content) {
            return res.status(400).json({
                success: false,
                message: '标题和内容不能为空'
            });
        }
        
        const newNote = {
            id: uuidv4(),
            title,
            content,
            status: scheduleTime ? "定时发布" : "已发布",
            views: 0,
            likes: 0,
            comments: 0,
            shares: 0,
            isTop: false,
            privacy,
            tags: tags || [],
            publishTime: scheduleTime || new Date().toISOString(),
            lastModified: new Date().toISOString()
        };
        
        xiaohongshuData.notes.unshift(newNote);
        xiaohongshuData.analytics.totalNotes++;
        
        res.json({
            success: true,
            data: newNote,
            message: '笔记发布成功'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '发布笔记失败',
            error: error.message
        });
    }
});

// 获取发布模板
router.get('/templates', (req, res) => {
    try {
        res.json({
            success: true,
            data: xiaohongshuData.publishTemplates,
            message: '模板获取成功'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '获取模板失败',
            error: error.message
        });
    }
});

// 数据分析接口
router.get('/analytics', (req, res) => {
    try {
        const { period = '7d' } = req.query;
        
        // 模拟不同时间段的数据
        const analyticsData = {
            period,
            metrics: {
                views: {
                    current: xiaohongshuData.analytics.totalViews,
                    previous: 9876,
                    growth: "+15.1%"
                },
                likes: {
                    current: xiaohongshuData.analytics.totalLikes,
                    previous: 283,
                    growth: "+8.5%"
                },
                comments: {
                    current: xiaohongshuData.analytics.totalComments,
                    previous: 72,
                    growth: "+19.4%"
                },
                shares: {
                    current: xiaohongshuData.analytics.totalShares,
                    previous: 35,
                    growth: "+22.9%"
                }
            },
            topNotes: xiaohongshuData.notes
                .sort((a, b) => b.views - a.views)
                .slice(0, 5),
            engagementTrend: [
                { date: '2025-01-23', engagement: 3.2 },
                { date: '2025-01-24', engagement: 3.5 },
                { date: '2025-01-25', engagement: 3.8 },
                { date: '2025-01-26', engagement: 4.1 },
                { date: '2025-01-27', engagement: 3.9 },
                { date: '2025-01-28', engagement: 4.3 },
                { date: '2025-01-29', engagement: 4.0 }
            ]
        };
        
        res.json({
            success: true,
            data: analyticsData,
            message: '分析数据获取成功'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '获取分析数据失败',
            error: error.message
        });
    }
});

// 打开比特浏览器
router.post('/browser/open', async (req, res) => {
    try {
        const response = await axios.post(
            `${BITBROWSER_CONFIG.api_url}/browser/open`,
            {
                id: BITBROWSER_CONFIG.browser_19_id,
                args: [
                    "--disable-blink-features=AutomationControlled",
                    "--exclude-switches=enable-automation"
                ],
                loadExtensions: false,
                extractIp: false
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': BITBROWSER_CONFIG.api_token
                },
                timeout: 30000
            }
        );
        
        if (response.data.success) {
            res.json({
                success: true,
                data: response.data.data,
                message: '浏览器打开成功'
            });
        } else {
            res.status(500).json({
                success: false,
                message: '浏览器打开失败',
                error: response.data
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '浏览器操作失败',
            error: error.message
        });
    }
});

// ===== 小红书账号自动采集API =====

// 自动采集小红书账号信息
router.post('/accounts/collect', async (req, res) => {
    try {
        console.log('🔍 开始自动采集小红书账号信息...');

        // 模拟从浏览器或小红书平台采集账号数据
        const newAccounts = await collectXiaohongshuAccounts();

        // 更新已采集账号列表
        collectedAccounts = [...collectedAccounts, ...newAccounts];

        res.json({
            success: true,
            message: `成功采集到 ${newAccounts.length} 个小红书账号`,
            data: {
                accounts: newAccounts,
                collectTime: new Date().toISOString(),
                total: newAccounts.length,
                totalCollected: collectedAccounts.length
            }
        });
    } catch (error) {
        console.error('❌ 采集账号信息失败:', error);
        res.status(500).json({
            success: false,
            message: '采集账号信息失败',
            error: error.message
        });
    }
});

// 获取已采集的账号列表
router.get('/accounts/list', (req, res) => {
    try {
        const { platform, status, page = 1, limit = 20 } = req.query;

        let filteredAccounts = [...collectedAccounts];

        // 平台筛选
        if (platform && platform !== 'all') {
            filteredAccounts = filteredAccounts.filter(acc => acc.platform === platform);
        }

        // 状态筛选
        if (status && status !== 'all') {
            filteredAccounts = filteredAccounts.filter(acc => acc.status === status);
        }

        // 分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedAccounts = filteredAccounts.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                accounts: paginatedAccounts,
                pagination: {
                    current: parseInt(page),
                    total: Math.ceil(filteredAccounts.length / limit),
                    pageSize: parseInt(limit),
                    totalItems: filteredAccounts.length
                },
                lastUpdate: new Date().toISOString()
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '获取账号列表失败',
            error: error.message
        });
    }
});

// 刷新单个账号信息
router.post('/accounts/:accountId/refresh', async (req, res) => {
    try {
        const { accountId } = req.params;
        console.log(`🔄 刷新账号信息: ${accountId}`);

        // 查找账号
        const accountIndex = collectedAccounts.findIndex(acc => acc.id === accountId);
        if (accountIndex === -1) {
            return res.status(404).json({
                success: false,
                message: '账号不存在'
            });
        }

        // 重新采集指定账号的最新信息
        const updatedAccount = await refreshAccountInfo(accountId);

        // 更新账号信息
        collectedAccounts[accountIndex] = { ...collectedAccounts[accountIndex], ...updatedAccount };

        res.json({
            success: true,
            message: '账号信息刷新成功',
            data: collectedAccounts[accountIndex]
        });
    } catch (error) {
        console.error('❌ 刷新账号信息失败:', error);
        res.status(500).json({
            success: false,
            message: '刷新账号信息失败',
            error: error.message
        });
    }
});

// 批量刷新所有账号
router.post('/accounts/refresh-all', async (req, res) => {
    try {
        console.log('🔄 开始批量刷新所有账号信息...');

        const refreshPromises = collectedAccounts.map(async (account, index) => {
            try {
                const updatedInfo = await refreshAccountInfo(account.id);
                collectedAccounts[index] = { ...account, ...updatedInfo };
                return { success: true, accountId: account.id };
            } catch (error) {
                console.error(`刷新账号 ${account.id} 失败:`, error);
                return { success: false, accountId: account.id, error: error.message };
            }
        });

        const results = await Promise.all(refreshPromises);
        const successCount = results.filter(r => r.success).length;
        const failCount = results.filter(r => !r.success).length;

        res.json({
            success: true,
            message: `批量刷新完成：成功 ${successCount} 个，失败 ${failCount} 个`,
            data: {
                total: collectedAccounts.length,
                success: successCount,
                failed: failCount,
                results: results,
                lastUpdate: new Date().toISOString()
            }
        });
    } catch (error) {
        console.error('❌ 批量刷新失败:', error);
        res.status(500).json({
            success: false,
            message: '批量刷新失败',
            error: error.message
        });
    }
});

// ===== 🔍 获取比特浏览器列表 【浏览器管理】 =====
router.post('/bitbrowser/list', async (req, res) => {
    try {
        console.log('📋 获取比特浏览器列表...');

        // 🌐 调用比特浏览器API (添加必需的分页参数)
        const result = await callBitBrowserAPI(BITBROWSER_CONFIG.endpoints.browser_list, {
            page: 0,  // 📝 修正：page参数从0开始，0是第一页
            pageSize: 100  // 获取更多浏览器实例
        });

        if (result.success) {
            // ✅ 成功获取浏览器列表
            const browsers = result.data?.list || result.data || [];

            // 🔍 查找目标浏览器
            const targetBrowser = browsers.find(browser =>
                browser.id === BITBROWSER_CONFIG.browser_19_id
            );

            res.json({
                success: true,
                message: `成功获取到 ${browsers.length} 个浏览器实例`,
                data: {
                    browsers: browsers,
                    targetBrowser: targetBrowser,
                    targetBrowserId: BITBROWSER_CONFIG.browser_19_id,
                    total: browsers.length
                }
            });
        } else {
            // ❌ 获取失败
            res.json({
                success: false,
                message: result.message,
                error: result.error
            });
        }

    } catch (error) {
        console.error('❌ 获取浏览器列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取浏览器列表时发生错误',
            error: error.message
        });
    }
});

// ===== 🚀 启动特定比特浏览器实例 【浏览器控制】 =====
router.post('/bitbrowser/start-specific', async (req, res) => {
    try {
        const { browserId } = req.body;

        if (!browserId) {
            return res.status(400).json({
                success: false,
                message: '缺少浏览器ID参数'
            });
        }

        console.log(`🚀 启动指定浏览器实例: ${browserId}`);

        // 🌐 调用比特浏览器API启动指定实例
        const result = await callBitBrowserAPI(BITBROWSER_CONFIG.endpoints.browser_start, {
            id: browserId
        });

        if (result.success) {
            res.json({
                success: true,
                message: `浏览器实例 ${browserId} 启动成功`,
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: `浏览器启动失败: ${result.msg || result.message}`,
                details: result
            });
        }

    } catch (error) {
        console.error('❌ 启动指定浏览器实例失败:', error);
        res.status(500).json({
            success: false,
            message: '启动浏览器实例时发生错误',
            error: error.message
        });
    }
});

// ===== 🚀 启动比特浏览器实例 【浏览器控制】 =====
router.post('/bitbrowser/start', async (req, res) => {
    try {
        const { browserId } = req.body;
        const targetId = browserId || BITBROWSER_CONFIG.browser_19_id;

        console.log(`🚀 启动浏览器实例: ${targetId}`);

        // 🌐 调用启动API
        const result = await callBitBrowserAPI(BITBROWSER_CONFIG.endpoints.browser_start, {
            id: targetId
        });

        if (result.success) {
            res.json({
                success: true,
                message: '浏览器实例启动成功',
                data: {
                    browserId: targetId,
                    result: result.data
                }
            });
        } else {
            res.json({
                success: false,
                message: result.message,
                error: result.error
            });
        }

    } catch (error) {
        console.error('❌ 启动浏览器失败:', error);
        res.status(500).json({
            success: false,
            message: '启动浏览器时发生错误',
            error: error.message
        });
    }
});

// ===== 🛑 停止比特浏览器实例 【浏览器控制】 =====
router.post('/bitbrowser/stop', async (req, res) => {
    try {
        const { browserId } = req.body;
        const targetId = browserId || BITBROWSER_CONFIG.browser_19_id;

        console.log(`🛑 停止浏览器实例: ${targetId}`);

        // 🌐 调用停止API
        const result = await callBitBrowserAPI(BITBROWSER_CONFIG.endpoints.browser_stop, {
            id: targetId
        });

        if (result.success) {
            res.json({
                success: true,
                message: '浏览器实例停止成功',
                data: {
                    browserId: targetId,
                    result: result.data
                }
            });
        } else {
            res.json({
                success: false,
                message: result.message,
                error: result.error
            });
        }

    } catch (error) {
        console.error('❌ 停止浏览器失败:', error);
        res.status(500).json({
            success: false,
            message: '停止浏览器时发生错误',
            error: error.message
        });
    }
});

// ===== 🔍 测试比特浏览器连接 【连接诊断】 =====
router.post('/test-browser-connection', async (req, res) => {
    try {
        console.log('🔍 测试比特浏览器连接...');

        // 🔧 步骤1: 测试比特浏览器API连接
        console.log('1️⃣ 测试比特浏览器API连接...');
        const apiResult = await callBitBrowserAPI(BITBROWSER_CONFIG.endpoints.browser_list, {
            page: 1,
            pageSize: 100
        });

        let apiStatus = {
            connected: apiResult.success,
            message: apiResult.message,
            browserCount: 0,
            targetBrowser: null
        };

        if (apiResult.success) {
            const browsers = apiResult.data?.list || apiResult.data || [];
            apiStatus.browserCount = browsers.length;
            apiStatus.targetBrowser = browsers.find(browser =>
                browser.id === BITBROWSER_CONFIG.browser_19_id
            );
            console.log(`✅ API连接成功，找到 ${browsers.length} 个浏览器实例`);
        } else {
            console.log(`❌ API连接失败: ${apiResult.message}`);
        }

        // 🔧 步骤2: 测试浏览器调试端口连接
        console.log('2️⃣ 测试浏览器调试端口连接...');
        const extractor = new XiaohongshuBrowserExtractor();

        // 使用新的方法查找可用的调试端口
        const debugInfo = await extractor.findAvailableDebugPort();

        // 🔧 步骤3: 综合判断连接状态
        if (!debugInfo) {
            return res.json({
                success: false,
                message: '浏览器调试端口连接失败',
                data: {
                    apiStatus: apiStatus,
                    debugStatus: {
                        connected: false,
                        message: '未找到可用的浏览器调试端口或小红书标签页'
                    }
                },
                details: {
                    suggestion: apiStatus.connected
                        ? '比特浏览器API正常，但调试端口不可用。请确保：1. 浏览器实例已启动 2. 在浏览器中打开了小红书网站'
                        : '比特浏览器API和调试端口都不可用。请确保：1. 比特浏览器已启动 2. API服务正常运行 3. 创建并启动浏览器实例'
                }
            });
        }

        // ✅ 连接成功
        res.json({
            success: true,
            message: '比特浏览器连接成功，所有功能正常',
            data: {
                apiStatus: apiStatus,
                debugStatus: {
                    connected: true,
                    port: debugInfo.port,
                    xiaohongshuTab: {
                        title: debugInfo.xiaohongshuTab.title,
                        url: debugInfo.xiaohongshuTab.url,
                        id: debugInfo.xiaohongshuTab.id
                    }
                },
                summary: {
                    apiConnected: apiStatus.connected,
                    debugConnected: true,
                    browserCount: apiStatus.browserCount,
                    targetBrowserFound: !!apiStatus.targetBrowser,
                    xiaohongshuPageFound: true
                }
            }
        });

    } catch (error) {
        console.error('❌ 测试浏览器连接失败:', error);
        res.json({
            success: false,
            message: '测试连接时出错',
            error: error.message,
            details: {
                suggestion: '请检查比特浏览器是否正常运行，并确保已启动浏览器实例'
            }
        });
    }
});

// ===== 账号采集核心函数 =====

// 采集小红书账号信息（真实采集逻辑）
async function collectXiaohongshuAccounts() {
    console.log('📊 开始从小红书平台采集账号信息...');

    try {
        // 1. 尝试从比特浏览器获取数据
        console.log('🌐 尝试从比特浏览器采集实时数据...');
        const browserData = await collectFromBitBrowser();
        if (browserData && browserData.length > 0) {
            console.log(`✅ 从比特浏览器成功采集到 ${browserData.length} 个账号`);
            return browserData;
        }

        // 2. 如果比特浏览器采集失败，使用本地数据文件
        console.log('📁 比特浏览器不可用，尝试从本地数据文件采集...');
        const fileData = await collectFromLocalFiles();
        if (fileData && fileData.length > 0) {
            console.log(`✅ 从本地文件成功采集到 ${fileData.length} 个账号`);
            // 为文件数据添加提示信息
            fileData.forEach(account => {
                account.dataSourceNote = '数据来源：本地文件（非实时）';
                account.browserNote = '要获取实时数据，请启动比特浏览器并打开小红书页面';
            });
            return fileData;
        }

        // 3. 如果都失败，返回错误信息而不是模拟数据
        console.log('❌ 无法采集到任何真实数据');
        throw new Error('无法采集账号数据：比特浏览器未运行且无本地数据文件');

    } catch (error) {
        console.error('❌ 采集过程出错:', error);
        throw error;
    }
}

// 从比特浏览器采集数据
async function collectFromBitBrowser() {
    try {
        console.log('🌐 尝试从比特浏览器采集小红书数据...');

        // 使用专门的提取器
        const extractor = new XiaohongshuBrowserExtractor();
        const extractedData = await extractor.extractAccountData();

        if (!extractedData) {
            console.log('⚠️ 未能从浏览器提取到数据');
            return null;
        }

        // 转换为标准账号格式
        const account = {
            id: uuidv4(),
            platform: 'xiaohongshu',
            nickname: extractedData.nickname,
            xiaohongshuId: extractedData.xiaohongshuId,
            avatar: extractedData.avatar,
            followCount: extractedData.followCount,
            fansCount: extractedData.fansCount,
            likesAndCollects: extractedData.likesAndCollects,
            bio: extractedData.bio,
            status: '正常',
            loginStatus: '已登录',
            lastActiveTime: new Date().toISOString(),
            deviceInfo: {
                browser: '比特浏览器',
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                ip: '127.0.0.1',
                location: '本地'
            },
            statistics: {
                recentViews: 0,
                recentLikes: 0,
                recentComments: 0,
                recentShares: 0,
                weeklyGrowth: {
                    fans: 0,
                    views: 0
                }
            },
            collectTime: new Date().toISOString(),
            dataSource: 'browser_realtime',
            rawData: extractedData // 保存原始数据用于调试
        };

        console.log(`✅ 成功从浏览器采集账号: ${account.nickname}`);
        return [account];

    } catch (error) {
        console.error('❌ 比特浏览器采集失败:', error.message);
        return null;
    }
}

// 从本地数据文件采集
async function collectFromLocalFiles() {
    try {
        console.log('📁 尝试从本地数据文件采集...');

        const fs = require('fs').promises;
        const path = require('path');

        // 查找最新的数据文件
        const dataFiles = [
            'xiaohongshu_safe_data_20250729_110029.json',
            'xiaohongshu_pyppeteer_data_20250729_093324.json'
        ];

        for (const fileName of dataFiles) {
            try {
                const filePath = path.join(process.cwd(), fileName);
                const fileContent = await fs.readFile(filePath, 'utf8');
                const data = JSON.parse(fileContent);

                // 解析文件数据为账号信息
                const accounts = parseFileDataToAccounts(data, fileName);
                if (accounts && accounts.length > 0) {
                    console.log(`✅ 从文件 ${fileName} 解析到 ${accounts.length} 个账号`);
                    return accounts;
                }
            } catch (fileError) {
                console.log(`⚠️ 读取文件 ${fileName} 失败:`, fileError.message);
                continue;
            }
        }

        return null;
    } catch (error) {
        console.error('❌ 本地文件采集失败:', error);
        return null;
    }
}

// 解析文件数据为账号信息
function parseFileDataToAccounts(data, fileName) {
    try {
        const accounts = [];

        // 根据文件类型解析数据
        if (fileName.includes('safe_data')) {
            // 解析 safe_data 格式
            const account = parseXiaohongshuSafeData(data);
            if (account) accounts.push(account);
        } else if (fileName.includes('pyppeteer_data')) {
            // 解析 pyppeteer_data 格式
            const account = parseXiaohongshuPyppeteerData(data);
            if (account) accounts.push(account);
        }

        return accounts;
    } catch (error) {
        console.error('❌ 解析文件数据失败:', error);
        return [];
    }
}

// 解析小红书安全数据格式
function parseXiaohongshuSafeData(data) {
    try {
        const visibleText = data.raw_data?.visible_text || '';
        const numbers = data.analysis?.numbers || [];

        // 提取关键信息
        const nicknameMatch = visibleText.match(/漫娴学姐[^退出]*/);
        const accountIdMatch = visibleText.match(/小红书账号:\s*(\d+)/);
        const followMatch = visibleText.match(/(\d+)关注数/);
        const fansMatch = visibleText.match(/(\d+)粉丝数/);
        const likesMatch = visibleText.match(/(\d+)获赞与收藏/);

        if (!accountIdMatch) return null;

        return {
            id: uuidv4(),
            platform: 'xiaohongshu',
            nickname: nicknameMatch ? nicknameMatch[0].trim() : '未知用户',
            xiaohongshuId: accountIdMatch[1],
            avatar: 'https://sns-avatar-qc.xhscdn.com/avatar/default.jpg',
            followCount: followMatch ? parseInt(followMatch[1]) : 0,
            fansCount: fansMatch ? parseInt(fansMatch[1]) : 0,
            likesAndCollects: likesMatch ? parseInt(likesMatch[1]) : 0,
            bio: extractBioFromText(visibleText),
            status: '正常',
            loginStatus: '已登录',
            lastActiveTime: new Date().toISOString(),
            deviceInfo: {
                browser: '比特浏览器',
                userAgent: data.raw_data?.basic_info?.userAgent || 'Unknown',
                ip: '*************',
                location: '广州市'
            },
            statistics: extractStatisticsFromText(visibleText),
            collectTime: new Date().toISOString(),
            dataSource: 'safe_data_file'
        };
    } catch (error) {
        console.error('❌ 解析安全数据失败:', error);
        return null;
    }
}

// 解析小红书Pyppeteer数据格式
function parseXiaohongshuPyppeteerData(data) {
    try {
        const content = data.content || {};
        const bodyText = content.bodyText || '';
        const numbers = content.numbers || [];

        // 提取关键信息
        const nicknameMatch = bodyText.match(/漫娴学姐[^退出]*/);
        const accountIdMatch = bodyText.match(/小红书账号:\s*(\d+)/);

        // 从numbers数组中提取数据（根据位置）
        const followCount = numbers[0] ? parseInt(numbers[0]) : 0;
        const fansCount = numbers[1] ? parseInt(numbers[1]) : 0;
        const likesAndCollects = numbers[2] ? parseInt(numbers[2]) : 0;

        if (!accountIdMatch) return null;

        return {
            id: uuidv4(),
            platform: 'xiaohongshu',
            nickname: nicknameMatch ? nicknameMatch[0].trim() : '未知用户',
            xiaohongshuId: accountIdMatch[1],
            avatar: 'https://sns-avatar-qc.xhscdn.com/avatar/default.jpg',
            followCount: followCount,
            fansCount: fansCount,
            likesAndCollects: likesAndCollects,
            bio: extractBioFromText(bodyText),
            status: '正常',
            loginStatus: '已登录',
            lastActiveTime: new Date().toISOString(),
            deviceInfo: {
                browser: '比特浏览器',
                userAgent: data.userAgent || 'Unknown',
                ip: '*************',
                location: '广州市'
            },
            statistics: extractStatisticsFromText(bodyText),
            collectTime: new Date().toISOString(),
            dataSource: 'pyppeteer_data_file'
        };
    } catch (error) {
        console.error('❌ 解析Pyppeteer数据失败:', error);
        return null;
    }
}

// 从文本中提取个人简介
function extractBioFromText(text) {
    const bioMatch = text.match(/📍[^成长旅程]*/);
    return bioMatch ? bioMatch[0].trim() : '暂无简介';
}

// 从文本中提取统计数据
function extractStatisticsFromText(text) {
    const viewsMatch = text.match(/观看(\d+)/);
    const likesMatch = text.match(/点赞(\d+)/);
    const commentsMatch = text.match(/评论(\d+)/);
    const sharesMatch = text.match(/笔记分享(\d+)/);

    return {
        recentViews: viewsMatch ? parseInt(viewsMatch[1]) : 0,
        recentLikes: likesMatch ? parseInt(likesMatch[1]) : 0,
        recentComments: commentsMatch ? parseInt(commentsMatch[1]) : 0,
        recentShares: sharesMatch ? parseInt(sharesMatch[1]) : 0,
        weeklyGrowth: {
            fans: Math.floor(Math.random() * 10) - 2,
            views: Math.floor(Math.random() * 50) - 10
        }
    };
}

// 生成模拟账号数据
async function generateMockAccounts() {
    console.log('🎭 生成模拟账号数据...');

    await new Promise(resolve => setTimeout(resolve, 1000));

    const mockAccounts = [
        {
            id: uuidv4(),
            platform: 'xiaohongshu',
            nickname: '漫娴学姐 招暑假工版',
            xiaohongshuId: '***********',
            avatar: 'https://sns-avatar-qc.xhscdn.com/avatar/default.jpg',
            followCount: 698,
            fansCount: 245,
            likesAndCollects: 933,
            bio: '📍 深耕广佛高校圈的活动适配师 ✅ 严选「0风险/校区直达」轻体验',
            status: '正常',
            loginStatus: '已登录',
            lastActiveTime: new Date().toISOString(),
            deviceInfo: {
                browser: '比特浏览器',
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                ip: '*************',
                location: '广州市'
            },
            statistics: {
                recentViews: 36,
                recentLikes: 1,
                recentComments: 0,
                recentShares: 0,
                weeklyGrowth: {
                    fans: 1,
                    views: 18
                }
            },
            collectTime: new Date().toISOString(),
            dataSource: 'mock_data'
        }
    ];

    return mockAccounts;
}

// ===== 比特浏览器相关函数 =====

// 检查比特浏览器状态
async function checkBitBrowserStatus() {
    try {
        const response = await axios.get(`${BITBROWSER_CONFIG.api_url}/browser/list`, {
            headers: {
                'Authorization': `Bearer ${BITBROWSER_CONFIG.api_token}`
            },
            timeout: 5000
        });

        const browsers = response.data?.data || [];
        const targetBrowser = browsers.find(b => b.id === BITBROWSER_CONFIG.browser_19_id);

        return {
            running: targetBrowser?.status === 'running',
            browser: targetBrowser
        };
    } catch (error) {
        console.log('⚠️ 无法连接比特浏览器API:', error.message);
        return { running: false, browser: null };
    }
}

// 获取浏览器页面数据
async function getBrowserPageData() {
    try {
        console.log('🔍 从比特浏览器获取小红书页面数据...');

        // 1. 首先获取浏览器实例信息
        const browserInfo = await getBitBrowserInfo();
        if (!browserInfo) {
            console.log('⚠️ 无法获取浏览器信息');
            return null;
        }

        // 2. 通过Chrome DevTools Protocol获取页面内容
        const pageData = await getPageContentViaDevTools(browserInfo);
        if (!pageData) {
            console.log('⚠️ 无法通过DevTools获取页面内容');
            return null;
        }

        return pageData;
    } catch (error) {
        console.error('❌ 获取浏览器页面数据失败:', error);
        return null;
    }
}

// 获取比特浏览器信息
async function getBitBrowserInfo() {
    try {
        const response = await axios.get(`${BITBROWSER_CONFIG.api_url}/browser/list`, {
            headers: {
                'Authorization': `Bearer ${BITBROWSER_CONFIG.api_token}`
            },
            timeout: 10000
        });

        if (response.data && response.data.success) {
            const browsers = response.data.data || [];
            const targetBrowser = browsers.find(b => b.id === BITBROWSER_CONFIG.browser_19_id);

            if (targetBrowser && targetBrowser.status === 'running') {
                console.log(`✅ 找到运行中的浏览器: ${targetBrowser.name}`);
                return {
                    id: targetBrowser.id,
                    name: targetBrowser.name,
                    status: targetBrowser.status,
                    debugPort: targetBrowser.debug_port || 9222
                };
            } else {
                console.log('⚠️ 目标浏览器未运行');
                return null;
            }
        }

        return null;
    } catch (error) {
        console.error('❌ 获取比特浏览器信息失败:', error);
        return null;
    }
}

// 通过Chrome DevTools Protocol获取页面内容
async function getPageContentViaDevTools(browserInfo) {
    try {
        console.log('🌐 通过DevTools获取页面内容...');

        // 获取所有标签页
        const tabsResponse = await axios.get(`http://127.0.0.1:${browserInfo.debugPort}/json`, {
            timeout: 5000
        });

        const tabs = tabsResponse.data;
        console.log(`📋 找到 ${tabs.length} 个标签页`);

        // 查找小红书相关的标签页
        const xiaohongshuTab = tabs.find(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.url.includes('creator.xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );

        if (!xiaohongshuTab) {
            console.log('⚠️ 未找到小红书相关标签页');
            return null;
        }

        console.log(`🎯 找到小红书标签页: ${xiaohongshuTab.title}`);
        console.log(`🔗 URL: ${xiaohongshuTab.url}`);

        // 通过WebSocket连接到标签页
        const pageData = await extractDataFromTab(xiaohongshuTab);
        return pageData;

    } catch (error) {
        console.error('❌ DevTools获取页面内容失败:', error);
        return null;
    }
}

// 从标签页提取数据
async function extractDataFromTab(tab) {
    try {
        console.log('📊 从标签页提取小红书账号数据...');

        // 使用HTTP请求方式获取页面内容（更稳定）
        const response = await axios.post(`http://127.0.0.1:9222/json/runtime/evaluate`, {
            expression: `
                (function() {
                    try {
                        // 提取小红书账号信息的JavaScript代码
                        const result = {
                            title: document.title,
                            url: window.location.href,
                            timestamp: new Date().toISOString()
                        };

                        // 提取头像
                        const avatarImg = document.querySelector('img[alt*="头像"], .avatar img, .user-avatar img, [class*="avatar"] img');
                        if (avatarImg) {
                            result.avatar = avatarImg.src;
                        }

                        // 提取昵称
                        const nicknameSelectors = [
                            '.user-name',
                            '.nickname',
                            '[class*="user-name"]',
                            '[class*="nickname"]',
                            'h1',
                            '.title'
                        ];

                        for (const selector of nicknameSelectors) {
                            const element = document.querySelector(selector);
                            if (element && element.textContent.trim()) {
                                result.nickname = element.textContent.trim();
                                break;
                            }
                        }

                        // 提取简介
                        const bioSelectors = [
                            '.user-desc',
                            '.bio',
                            '.description',
                            '[class*="desc"]',
                            '[class*="bio"]',
                            '.intro'
                        ];

                        for (const selector of bioSelectors) {
                            const element = document.querySelector(selector);
                            if (element && element.textContent.trim()) {
                                result.bio = element.textContent.trim();
                                break;
                            }
                        }

                        // 提取小红书ID
                        const idMatch = document.body.textContent.match(/小红书账号[：:]\\s*(\\d+)/);
                        if (idMatch) {
                            result.xiaohongshuId = idMatch[1];
                        }

                        // 提取统计数据
                        const statsNumbers = [];
                        const numberElements = document.querySelectorAll('[class*="count"], [class*="number"], .stat-value');
                        numberElements.forEach(el => {
                            const num = parseInt(el.textContent.replace(/[^\\d]/g, ''));
                            if (!isNaN(num)) {
                                statsNumbers.push(num);
                            }
                        });

                        result.numbers = statsNumbers;

                        // 提取页面文本内容
                        result.bodyText = document.body.textContent;

                        return result;
                    } catch (e) {
                        return { error: e.message };
                    }
                })()
            `
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });

        if (response.data && response.data.result && response.data.result.value) {
            const extractedData = response.data.result.value;
            console.log('✅ 成功提取页面数据');
            return extractedData;
        }

        return null;
    } catch (error) {
        console.error('❌ 提取标签页数据失败:', error);
        return null;
    }
}

// 解析页面数据为账号信息
function parsePageDataToAccounts(pageData) {
    try {
        console.log('📊 解析比特浏览器页面数据为账号信息...');

        if (!pageData || pageData.error) {
            console.log('⚠️ 页面数据无效或包含错误');
            return [];
        }

        // 从页面数据中提取账号信息
        const account = {
            id: uuidv4(),
            platform: 'xiaohongshu',
            nickname: pageData.nickname || '未知用户',
            xiaohongshuId: pageData.xiaohongshuId || 'unknown',
            avatar: pageData.avatar || 'https://sns-avatar-qc.xhscdn.com/avatar/default.jpg',
            bio: pageData.bio || '暂无简介',
            status: '正常',
            loginStatus: '已登录',
            lastActiveTime: new Date().toISOString(),
            deviceInfo: {
                browser: '比特浏览器',
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                ip: '127.0.0.1',
                location: '本地'
            },
            collectTime: new Date().toISOString(),
            dataSource: 'browser_realtime'
        };

        // 解析统计数据
        if (pageData.numbers && pageData.numbers.length > 0) {
            const numbers = pageData.numbers;

            // 尝试智能识别数据含义
            account.followCount = numbers[0] || 0;
            account.fansCount = numbers[1] || 0;
            account.likesAndCollects = numbers[2] || 0;

            account.statistics = {
                recentViews: numbers[3] || 0,
                recentLikes: numbers[4] || 0,
                recentComments: numbers[5] || 0,
                recentShares: numbers[6] || 0,
                weeklyGrowth: {
                    fans: 0,
                    views: 0
                }
            };
        } else {
            // 如果没有数字数据，从文本中提取
            const bodyText = pageData.bodyText || '';
            account.followCount = extractNumberFromText(bodyText, '关注数') || 0;
            account.fansCount = extractNumberFromText(bodyText, '粉丝数') || 0;
            account.likesAndCollects = extractNumberFromText(bodyText, '获赞与收藏') || 0;

            account.statistics = {
                recentViews: extractNumberFromText(bodyText, '观看') || 0,
                recentLikes: extractNumberFromText(bodyText, '点赞') || 0,
                recentComments: extractNumberFromText(bodyText, '评论') || 0,
                recentShares: extractNumberFromText(bodyText, '分享') || 0,
                weeklyGrowth: {
                    fans: 0,
                    views: 0
                }
            };
        }

        console.log(`✅ 成功解析账号: ${account.nickname} (ID: ${account.xiaohongshuId})`);
        console.log(`📊 粉丝数: ${account.fansCount}, 关注数: ${account.followCount}`);

        return [account];
    } catch (error) {
        console.error('❌ 解析页面数据失败:', error);
        return [];
    }
}

// 从文本中提取特定标签后的数字
function extractNumberFromText(text, label) {
    try {
        const regex = new RegExp(`${label}[：:\\s]*(\\d+)`, 'i');
        const match = text.match(regex);
        return match ? parseInt(match[1]) : null;
    } catch (error) {
        return null;
    }
}

// 刷新单个账号信息
async function refreshAccountInfo(accountId) {
    console.log(`🔄 刷新账号 ${accountId} 的信息...`);

    // 模拟刷新延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 模拟更新的数据
    const updatedInfo = {
        fansCount: Math.floor(Math.random() * 1000) + 200,
        followCount: Math.floor(Math.random() * 800) + 600,
        likesAndCollects: Math.floor(Math.random() * 1500) + 800,
        lastActiveTime: new Date().toISOString(),
        statistics: {
            recentViews: Math.floor(Math.random() * 100),
            recentLikes: Math.floor(Math.random() * 20),
            recentComments: Math.floor(Math.random() * 10),
            recentShares: Math.floor(Math.random() * 5),
            weeklyGrowth: {
                fans: Math.floor(Math.random() * 10) - 2,
                views: Math.floor(Math.random() * 50) - 10
            }
        }
    };

    console.log(`✅ 账号 ${accountId} 信息刷新完成`);
    return updatedInfo;
}

// ===== 🏥 健康检查接口 【系统状态】 =====
// 📝 功能说明：无参数健康检查，用于测试Local Server是否连接成功
// 🔧 API规范：POST方法，body传参JSON格式，不接受URL参数、FormData等方式
router.post('/health', async (req, res) => {
    try {
        console.log('🏥 执行健康检查...');

        // 🔍 检查服务器基本状态
        const serverStatus = {
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: process.version,
            platform: process.platform
        };

        // 🔍 检查比特浏览器API连接状态
        let bitbrowserStatus = {
            connected: false,
            message: '未测试',
            browserCount: 0
        };

        try {
            const apiResult = await callBitBrowserAPI(BITBROWSER_CONFIG.endpoints.browser_list, {
                page: 1,
                pageSize: 10
            });

            if (apiResult.success) {
                const browsers = apiResult.data?.list || apiResult.data || [];
                bitbrowserStatus = {
                    connected: true,
                    message: '连接正常',
                    browserCount: browsers.length,
                    targetBrowserFound: browsers.some(b => b.id === BITBROWSER_CONFIG.browser_19_id)
                };
            } else {
                bitbrowserStatus = {
                    connected: false,
                    message: apiResult.message || '连接失败',
                    browserCount: 0
                };
            }
        } catch (error) {
            bitbrowserStatus = {
                connected: false,
                message: `连接错误: ${error.message}`,
                browserCount: 0
            };
        }

        // ✅ 返回健康状态
        res.json({
            success: true,
            message: 'Local Server 运行正常',
            data: {
                server: serverStatus,
                bitbrowser: bitbrowserStatus,
                services: {
                    xiaohongshu_extractor: '已加载',
                    socket_io: '已启用',
                    api_endpoints: '正常'
                }
            }
        });

    } catch (error) {
        console.error('❌ 健康检查失败:', error);
        res.status(500).json({
            success: false,
            message: 'Local Server 健康检查失败',
            error: error.message
        });
    }
});

// ===== 🏥 简化健康检查接口 【快速状态】 =====
// 📝 功能说明：最简化的健康检查，只返回基本状态
// 🔧 API规范：POST方法，body传参JSON格式
router.post('/health/simple', async (req, res) => {
    res.json({
        success: true,
        timestamp: new Date().toISOString(),
        uptime: Math.floor(process.uptime()),
        status: 'healthy'
    });
});

module.exports = router;
