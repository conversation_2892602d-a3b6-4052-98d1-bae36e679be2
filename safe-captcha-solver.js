#!/usr/bin/env node

/**
 * 🛡️ 安全的小红书验证码解决方案
 * 使用改进版Puppeteer + 反检测措施
 */

const puppeteer = require('puppeteer');

class SafeCaptchaSolver {
    constructor() {
        this.debugPort = 55276;
        this.maxRetries = 3;
        this.humanDelayRange = [500, 2000];
    }

    /**
     * 生成人类化的随机延迟
     */
    randomDelay(min = 500, max = 2000) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * 生成贝塞尔曲线轨迹点
     */
    generateBezierPath(startX, startY, endX, endY, steps = 20) {
        const points = [];
        
        // 控制点，添加一些随机性
        const cp1x = startX + (endX - startX) * 0.25 + (Math.random() - 0.5) * 20;
        const cp1y = startY + (Math.random() - 0.5) * 10;
        const cp2x = startX + (endX - startX) * 0.75 + (Math.random() - 0.5) * 20;
        const cp2y = startY + (Math.random() - 0.5) * 10;

        for (let i = 0; i <= steps; i++) {
            const t = i / steps;
            const x = Math.pow(1 - t, 3) * startX + 
                     3 * Math.pow(1 - t, 2) * t * cp1x + 
                     3 * (1 - t) * Math.pow(t, 2) * cp2x + 
                     Math.pow(t, 3) * endX;
            const y = Math.pow(1 - t, 3) * startY + 
                     3 * Math.pow(1 - t, 2) * t * cp1y + 
                     3 * (1 - t) * Math.pow(t, 2) * cp2y + 
                     Math.pow(t, 3) * endY;
            
            points.push({ x, y });
        }
        
        return points;
    }

    /**
     * 隐藏自动化特征
     */
    async hideAutomationFeatures(page) {
        await page.evaluateOnNewDocument(() => {
            // 隐藏webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // 模拟chrome runtime
            window.chrome = {
                runtime: {},
            };

            // 隐藏自动化相关属性
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });

            // 模拟正常的用户代理
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });

            // 隐藏HeadlessChrome特征
            Object.defineProperty(navigator, 'platform', {
                get: () => 'Win32',
            });
        });
    }

    /**
     * 分析验证码图片，计算拖动距离
     */
    async analyzeCaptchaImage(page) {
        try {
            // 这里可以添加图像分析逻辑
            // 目前使用估算值
            const estimatedDistance = 150 + Math.random() * 50; // 150-200px
            console.log(`   📏 估算拖动距离: ${Math.round(estimatedDistance)}px`);
            return estimatedDistance;
        } catch (error) {
            console.log('   ⚠️ 图像分析失败，使用默认距离');
            return 180; // 默认距离
        }
    }

    /**
     * 执行人类化的鼠标拖动
     */
    async humanLikeDrag(page, startX, startY, endX, endY) {
        console.log('   🖱️ 开始人类化拖动...');
        
        // 移动到起始位置
        await page.mouse.move(startX, startY);
        await new Promise(resolve => setTimeout(resolve, this.randomDelay(100, 300)));

        // 按下鼠标
        await page.mouse.down();
        await new Promise(resolve => setTimeout(resolve, this.randomDelay(50, 150)));
        
        // 生成贝塞尔曲线路径
        const steps = 15 + Math.floor(Math.random() * 10); // 15-25步
        const path = this.generateBezierPath(startX, startY, endX, endY, steps);
        
        // 沿路径移动
        for (let i = 1; i < path.length; i++) {
            const point = path[i];
            
            // 添加微小的随机抖动
            const jitterX = (Math.random() - 0.5) * 2;
            const jitterY = (Math.random() - 0.5) * 2;
            
            await page.mouse.move(
                point.x + jitterX, 
                point.y + jitterY
            );
            
            // 随机延迟，模拟人类移动速度
            await new Promise(resolve => setTimeout(resolve, this.randomDelay(20, 80)));
        }
        
        // 在终点稍作停留
        await new Promise(resolve => setTimeout(resolve, this.randomDelay(100, 200)));
        
        // 释放鼠标
        await page.mouse.up();
        
        console.log('   ✅ 拖动完成');
    }

    /**
     * 主要的验证码解决函数
     */
    async solveCaptcha() {
        console.log('🛡️ 启动安全验证码解决方案...');
        console.log('');

        let browser = null;
        let success = false;
        let attempt = 0;

        while (!success && attempt < this.maxRetries) {
            attempt++;
            console.log(`🔄 第 ${attempt} 次尝试...`);

            try {
                // 连接到比特浏览器
                console.log('   🔗 连接到比特浏览器...');
                browser = await puppeteer.connect({
                    browserURL: `http://127.0.0.1:${this.debugPort}`,
                    defaultViewport: null
                });

                // 获取小红书页面
                const pages = await browser.pages();
                const xiaohongshuPage = pages.find(page => 
                    page.url().includes('xiaohongshu.com')
                );

                if (!xiaohongshuPage) {
                    throw new Error('未找到小红书页面');
                }

                console.log('   ✅ 找到小红书页面');
                console.log('   📄 页面URL:', xiaohongshuPage.url());

                // 隐藏自动化特征
                console.log('   🛡️ 应用反检测措施...');
                await this.hideAutomationFeatures(xiaohongshuPage);

                // 等待验证码元素加载
                console.log('   ⏳ 等待验证码加载...');
                
                // 使用正确的小红书验证码选择器
                const trackSelectors = [
                    '.red-captcha-track',
                    '.red-captcha-slider-bar',
                    '[class*="red-captcha-track"]',
                    '[class*="slider"]'
                ];

                let sliderTrack = null;
                for (const selector of trackSelectors) {
                    try {
                        await xiaohongshuPage.waitForSelector(selector, { timeout: 2000 });
                        sliderTrack = await xiaohongshuPage.$(selector);
                        if (sliderTrack) {
                            console.log(`   ✅ 找到滑动轨道: ${selector}`);
                            break;
                        }
                    } catch (e) {
                        continue;
                    }
                }

                if (!sliderTrack) {
                    throw new Error('未找到滑动轨道元素');
                }

                // 查找滑动按钮
                const buttonSelectors = [
                    '.red-captcha-slider',
                    '.red-captcha-slider-icon',
                    '[class*="red-captcha-slider"]',
                    '[class*="slider"]:not([class*="bar"]):not([class*="track"])'
                ];

                let sliderButton = null;
                for (const selector of buttonSelectors) {
                    try {
                        sliderButton = await xiaohongshuPage.$(selector);
                        if (sliderButton) {
                            console.log(`   ✅ 找到滑动按钮: ${selector}`);
                            break;
                        }
                    } catch (e) {
                        continue;
                    }
                }

                if (!sliderButton) {
                    throw new Error('未找到滑动按钮元素');
                }

                // 添加随机延迟，模拟用户思考时间
                const thinkingTime = this.randomDelay(1000, 3000);
                console.log(`   🤔 模拟用户思考时间: ${thinkingTime}ms`);
                await new Promise(resolve => setTimeout(resolve, thinkingTime));

                // 获取按钮位置
                const buttonBox = await sliderButton.boundingBox();
                if (!buttonBox) {
                    throw new Error('无法获取按钮位置');
                }

                console.log('   📍 按钮位置:', {
                    x: Math.round(buttonBox.x),
                    y: Math.round(buttonBox.y),
                    width: Math.round(buttonBox.width),
                    height: Math.round(buttonBox.height)
                });

                // 分析验证码并计算拖动距离
                console.log('   🔍 分析验证码图片...');
                const dragDistance = await this.analyzeCaptchaImage(xiaohongshuPage);

                // 计算起始和结束位置
                const startX = buttonBox.x + buttonBox.width / 2;
                const startY = buttonBox.y + buttonBox.height / 2;
                const endX = startX + dragDistance;
                const endY = startY;

                console.log('   🎯 拖动路径:', {
                    start: `(${Math.round(startX)}, ${Math.round(startY)})`,
                    end: `(${Math.round(endX)}, ${Math.round(endY)})`,
                    distance: `${Math.round(dragDistance)}px`
                });

                // 执行人类化拖动
                await this.humanLikeDrag(xiaohongshuPage, startX, startY, endX, endY);

                // 等待验证结果
                console.log('   ⏳ 等待验证结果...');
                await new Promise(resolve => setTimeout(resolve, 2000));

                // 检查是否验证成功
                const currentUrl = xiaohongshuPage.url();
                if (!currentUrl.includes('captcha')) {
                    console.log('   🎉 验证成功！页面已跳转');
                    success = true;
                } else {
                    console.log('   ❌ 验证失败，准备重试...');
                    
                    // 检查是否有刷新按钮
                    try {
                        const refreshButton = await xiaohongshuPage.$('.refresh-button, .reload-button, [class*="refresh"]');
                        if (refreshButton) {
                            console.log('   🔄 点击刷新按钮...');
                            await refreshButton.click();
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }
                    } catch (e) {
                        console.log('   ⚠️ 未找到刷新按钮，将重新尝试');
                    }
                }

            } catch (error) {
                console.error(`   ❌ 第 ${attempt} 次尝试失败:`, error.message);
                
                if (attempt < this.maxRetries) {
                    const retryDelay = this.randomDelay(2000, 5000);
                    console.log(`   ⏳ ${retryDelay}ms 后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }
        }

        if (browser) {
            await browser.disconnect();
        }

        if (success) {
            console.log('');
            console.log('🎉 验证码解决成功！');
            console.log('✅ 小红书页面已通过验证');
        } else {
            console.log('');
            console.log('❌ 验证码解决失败');
            console.log('💡 建议：');
            console.log('   1. 检查网络连接');
            console.log('   2. 尝试手动操作');
            console.log('   3. 刷新页面重新获取验证码');
        }

        return success;
    }
}

// 如果直接运行此文件
if (require.main === module) {
    const solver = new SafeCaptchaSolver();
    solver.solveCaptcha().catch(console.error);
}

module.exports = SafeCaptchaSolver;
