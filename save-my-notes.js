#!/usr/bin/env node

/**
 * 💾 保存我的小红书笔记数据
 * 将采集到的笔记信息保存为JSON和可读格式
 */

const fs = require('fs');
const path = require('path');
const { XiaohongshuNotesCollector } = require('./xiaohongshu-notes-collector.js');

async function saveMyNotes() {
    console.log('💾 开始采集并保存我的小红书笔记...\n');

    try {
        // 1. 采集笔记数据
        const collector = new XiaohongshuNotesCollector();
        const notesData = await collector.collectAllNotes();

        // 2. 生成文件名（包含时间戳）
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const jsonFileName = `my-xiaohongshu-notes-${timestamp}.json`;
        const txtFileName = `my-xiaohongshu-notes-${timestamp}.txt`;

        // 3. 保存JSON格式数据
        fs.writeFileSync(jsonFileName, JSON.stringify(notesData, null, 2), 'utf8');
        console.log(`✅ JSON数据已保存到: ${jsonFileName}`);

        // 4. 生成可读格式报告
        const report = generateReadableReport(notesData);
        fs.writeFileSync(txtFileName, report, 'utf8');
        console.log(`✅ 可读报告已保存到: ${txtFileName}`);

        // 5. 显示摘要
        console.log('\n📊 采集摘要:');
        console.log(`📝 总笔记数: ${notesData.summary.totalNotes}`);
        console.log(`🖼️  总图片数: ${notesData.summary.totalImages}`);
        console.log(`👍 总点赞数: ${notesData.summary.totalLikes}`);
        console.log(`💖 总收藏数: ${notesData.summary.totalCollects}`);
        console.log(`💬 总评论数: ${notesData.summary.totalComments}`);

        // 6. 显示热门笔记
        const topNotes = notesData.notes
            .sort((a, b) => (b.interactions.likes || 0) - (a.interactions.likes || 0))
            .slice(0, 5);

        console.log('\n🔥 最受欢迎的5篇笔记:');
        topNotes.forEach((note, index) => {
            console.log(`${index + 1}. ${note.title} (👍 ${note.interactions.likes || 0})`);
        });

        return {
            jsonFile: jsonFileName,
            txtFile: txtFileName,
            data: notesData
        };

    } catch (error) {
        console.error('❌ 保存失败:', error.message);
        throw error;
    }
}

// 生成可读格式报告
function generateReadableReport(data) {
    const lines = [];
    
    lines.push('📝 我的小红书笔记数据报告');
    lines.push('=' * 50);
    lines.push(`📅 采集时间: ${data.timestamp}`);
    lines.push(`🔗 页面URL: ${data.url}`);
    lines.push(`👤 页面标题: ${data.title}`);
    lines.push('');
    
    lines.push('📊 数据统计:');
    lines.push(`📝 总笔记数: ${data.summary.totalNotes}`);
    lines.push(`🖼️  总图片数: ${data.summary.totalImages}`);
    lines.push(`👍 总点赞数: ${data.summary.totalLikes}`);
    lines.push(`💖 总收藏数: ${data.summary.totalCollects}`);
    lines.push(`💬 总评论数: ${data.summary.totalComments}`);
    lines.push('');
    
    lines.push('📝 笔记详情:');
    lines.push('-' * 50);
    
    data.notes.forEach((note, index) => {
        lines.push(`\n📝 笔记 ${index + 1}:`);
        lines.push(`标题: ${note.title || '无标题'}`);
        lines.push(`图片数量: ${note.images.length}`);
        lines.push(`👍 点赞: ${note.interactions.likes || 0}`);
        lines.push(`💖 收藏: ${note.interactions.collects || 0}`);
        lines.push(`💬 评论: ${note.interactions.comments || 0}`);
        
        if (note.publishTime) {
            lines.push(`📅 发布时间: ${note.publishTime}`);
        }
        
        if (note.link) {
            lines.push(`🔗 链接: ${note.link}`);
        }
        
        if (note.images.length > 0) {
            lines.push(`🖼️  图片:`);
            note.images.forEach((img, imgIndex) => {
                lines.push(`   ${imgIndex + 1}. ${img.src}`);
            });
        }
    });
    
    lines.push('\n' + '=' * 50);
    lines.push('📊 数据来源: ' + data.dataSource);
    lines.push('🔧 提取方法: ' + data.extractionMethod);
    lines.push('📅 报告生成时间: ' + new Date().toLocaleString('zh-CN'));
    
    return lines.join('\n');
}

// 运行保存功能
if (require.main === module) {
    saveMyNotes().then(result => {
        console.log('\n🎉 笔记数据保存完成!');
        console.log(`📁 JSON文件: ${result.jsonFile}`);
        console.log(`📄 文本报告: ${result.txtFile}`);
    }).catch(console.error);
}

module.exports = { saveMyNotes };
