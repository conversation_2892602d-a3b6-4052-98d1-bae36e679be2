#!/usr/bin/env node

/**
 * 💾 保存滚动采集的笔记数据
 * 执行滚动采集并保存完整的笔记信息
 */

const fs = require('fs');
const { SimpleScrollCollector } = require('./simple-scroll-collector.js');

async function saveScrolledNotes() {
    console.log('💾 开始滚动采集并保存笔记数据...\n');

    try {
        // 1. 执行滚动采集
        const collector = new SimpleScrollCollector();
        const notesData = await collector.scrollAndCollect();

        // 2. 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const jsonFileName = `scrolled-notes-${timestamp}.json`;
        const txtFileName = `scrolled-notes-${timestamp}.txt`;

        // 3. 保存JSON数据
        fs.writeFileSync(jsonFileName, JSON.stringify(notesData, null, 2), 'utf8');
        console.log(`✅ JSON数据已保存: ${jsonFileName}`);

        // 4. 生成可读报告
        const report = generateScrollReport(notesData);
        fs.writeFileSync(txtFileName, report, 'utf8');
        console.log(`✅ 可读报告已保存: ${txtFileName}`);

        // 5. 显示详细结果
        console.log('\n📊 滚动采集详细结果:');
        console.log('=' * 60);
        console.log(`📝 总笔记数: ${notesData.summary.totalNotes}`);
        console.log(`🖼️  总图片数: ${notesData.summary.totalImages}`);
        console.log(`📏 页面高度: ${notesData.summary.pageHeight}px`);
        console.log(`🖥️  视窗高度: ${notesData.summary.viewportHeight}px`);
        console.log(`📅 采集时间: ${notesData.timestamp}`);
        console.log(`🔗 页面URL: ${notesData.url}\n`);

        // 6. 显示所有笔记
        console.log('📝 所有采集到的笔记:');
        console.log('-' * 60);
        notesData.notes.forEach((note, index) => {
            console.log(`\n📝 笔记 ${index + 1}:`);
            console.log(`   标题: ${note.title || '无标题'}`);
            console.log(`   图片数量: ${note.images.length}`);
            console.log(`   👍 点赞: ${note.interactions.likes || 0}`);
            console.log(`   💖 收藏: ${note.interactions.collects || 0}`);
            console.log(`   💬 评论: ${note.interactions.comments || 0}`);
            
            if (note.link) {
                console.log(`   🔗 链接: ${note.link}`);
            }
            
            if (note.images.length > 0) {
                console.log(`   🖼️  图片:`);
                note.images.forEach((img, imgIndex) => {
                    console.log(`      ${imgIndex + 1}. ${img.src}`);
                });
            }
        });

        return {
            jsonFile: jsonFileName,
            txtFile: txtFileName,
            data: notesData
        };

    } catch (error) {
        console.error('❌ 滚动采集保存失败:', error.message);
        throw error;
    }
}

// 生成滚动采集报告
function generateScrollReport(data) {
    const lines = [];
    
    lines.push('📝 滚动采集笔记数据报告');
    lines.push('=' * 60);
    lines.push(`📅 采集时间: ${data.timestamp}`);
    lines.push(`🔗 页面URL: ${data.url}`);
    lines.push(`👤 页面标题: ${data.title}`);
    lines.push(`📊 数据来源: ${data.dataSource}`);
    lines.push(`🔧 提取方法: ${data.extractionMethod}`);
    lines.push('');
    
    lines.push('📊 采集统计:');
    lines.push(`📝 总笔记数: ${data.summary.totalNotes}`);
    lines.push(`🖼️  总图片数: ${data.summary.totalImages}`);
    lines.push(`📏 页面高度: ${data.summary.pageHeight}px`);
    lines.push(`🖥️  视窗高度: ${data.summary.viewportHeight}px`);
    lines.push('');
    
    lines.push('📝 笔记详情:');
    lines.push('-' * 60);
    
    data.notes.forEach((note, index) => {
        lines.push(`\n📝 笔记 ${index + 1}:`);
        lines.push(`标题: ${note.title || '无标题'}`);
        lines.push(`ID: ${note.id}`);
        lines.push(`图片数量: ${note.images.length}`);
        lines.push(`👍 点赞: ${note.interactions.likes || 0}`);
        lines.push(`💖 收藏: ${note.interactions.collects || 0}`);
        lines.push(`💬 评论: ${note.interactions.comments || 0}`);
        
        if (note.link) {
            lines.push(`🔗 链接: ${note.link}`);
        }
        
        if (note.images.length > 0) {
            lines.push(`🖼️  图片列表:`);
            note.images.forEach((img, imgIndex) => {
                lines.push(`   ${imgIndex + 1}. ${img.src}`);
                if (img.alt) {
                    lines.push(`      描述: ${img.alt}`);
                }
            });
        }
    });
    
    lines.push('\n' + '=' * 60);
    lines.push('📊 技术信息:');
    lines.push(`🔧 提取方法: ${data.extractionMethod}`);
    lines.push(`📊 数据来源: ${data.dataSource}`);
    if (data.tabId) {
        lines.push(`🆔 标签页ID: ${data.tabId}`);
    }
    lines.push('📅 报告生成时间: ' + new Date().toLocaleString('zh-CN'));
    
    return lines.join('\n');
}

// 运行保存功能
if (require.main === module) {
    saveScrolledNotes().then(result => {
        console.log('\n🎉 滚动采集数据保存完成!');
        console.log(`📁 JSON文件: ${result.jsonFile}`);
        console.log(`📄 文本报告: ${result.txtFile}`);
        console.log('\n💡 提示: 如果需要采集更多笔记，可以在浏览器中继续滚动后重新运行此脚本');
    }).catch(console.error);
}

module.exports = { saveScrolledNotes };
