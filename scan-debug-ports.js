#!/usr/bin/env node

/**
 * 🔍 扫描当前可用的调试端口
 */

const axios = require('axios');

async function scanDebugPorts() {
    console.log('🔍 扫描当前可用的调试端口...\n');

    // 扩展端口范围，包含比特浏览器常用端口
    const portRanges = [
        // 比特浏览器动态端口范围
        { start: 53320, end: 53340, name: '比特浏览器动态端口' },
        { start: 63520, end: 63540, name: '比特浏览器常用端口' },
        { start: 51850, end: 51870, name: '动态分配端口' },
        { start: 58220, end: 58240, name: '备用端口' },
        { start: 9220, end: 9230, name: 'Chrome默认端口' }
    ];

    const availablePorts = [];

    for (const range of portRanges) {
        console.log(`📡 扫描 ${range.name} (${range.start}-${range.end})...`);
        
        for (let port = range.start; port <= range.end; port++) {
            try {
                const response = await axios.get(`http://127.0.0.1:${port}/json`, {
                    timeout: 1000
                });
                
                const tabs = response.data;
                console.log(`✅ 端口 ${port} 可用 - 找到 ${tabs.length} 个标签页`);
                
                availablePorts.push({
                    port: port,
                    tabCount: tabs.length,
                    tabs: tabs
                });
                
                // 检查是否有小红书标签页
                const xiaohongshuTabs = tabs.filter(tab => 
                    tab.url && (
                        tab.url.includes('xiaohongshu.com') || 
                        tab.title.includes('小红书')
                    )
                );
                
                if (xiaohongshuTabs.length > 0) {
                    console.log(`🎯 端口 ${port} 有 ${xiaohongshuTabs.length} 个小红书标签页！`);
                    xiaohongshuTabs.forEach((tab, index) => {
                        console.log(`   ${index + 1}. ${tab.title}`);
                    });
                }
                
            } catch (error) {
                // 端口不可用，继续扫描
            }
        }
    }

    console.log('\n📊 扫描结果总结:');
    if (availablePorts.length === 0) {
        console.log('❌ 未找到任何可用的调试端口');
        console.log('💡 请确保浏览器已启动并开启调试模式');
        return null;
    }

    console.log(`✅ 找到 ${availablePorts.length} 个可用端口:`);
    availablePorts.forEach((portInfo, index) => {
        console.log(`   ${index + 1}. 端口 ${portInfo.port} - ${portInfo.tabCount} 个标签页`);
        
        // 显示前3个标签页的标题
        portInfo.tabs.slice(0, 3).forEach((tab, tabIndex) => {
            const title = tab.title || '无标题';
            const url = (tab.url || '').substring(0, 50) + '...';
            console.log(`      ${tabIndex + 1}. ${title}`);
            console.log(`         ${url}`);
        });
        
        if (portInfo.tabs.length > 3) {
            console.log(`      ... 还有 ${portInfo.tabs.length - 3} 个标签页`);
        }
    });

    // 推荐最佳端口
    const bestPort = availablePorts.find(p => 
        p.tabs.some(tab => 
            tab.url && (
                tab.url.includes('xiaohongshu.com') || 
                tab.title.includes('小红书')
            )
        )
    ) || availablePorts[0];

    if (bestPort) {
        console.log(`\n🎯 推荐使用端口: ${bestPort.port}`);
        console.log('💡 可以开始数据采集了！');
        
        // 更新配置文件中的端口
        console.log('\n🔧 更新配置建议:');
        console.log(`   在 bitbrowser-config.js 中添加调试端口: ${bestPort.port}`);
        console.log(`   或在数据采集器中直接使用此端口`);
    }

    return availablePorts;
}

// 快速测试特定端口
async function testSpecificPort(port) {
    console.log(`🧪 测试端口 ${port}...`);
    
    try {
        const response = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 3000
        });
        
        const tabs = response.data;
        console.log(`✅ 端口 ${port} 连接成功`);
        console.log(`📋 标签页数量: ${tabs.length}`);
        
        if (tabs.length > 0) {
            console.log('📄 标签页列表:');
            tabs.forEach((tab, index) => {
                console.log(`   ${index + 1}. ${tab.title || '无标题'}`);
                console.log(`      URL: ${(tab.url || '').substring(0, 80)}...`);
            });
        }
        
        return true;
    } catch (error) {
        console.log(`❌ 端口 ${port} 连接失败: ${error.message}`);
        return false;
    }
}

// 命令行参数处理
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length > 0 && !isNaN(args[0])) {
        // 测试特定端口
        const port = parseInt(args[0]);
        testSpecificPort(port).then(success => {
            if (success) {
                console.log(`\n🎉 端口 ${port} 测试成功！`);
            } else {
                console.log(`\n❌ 端口 ${port} 测试失败！`);
            }
        });
    } else {
        // 扫描所有端口
        scanDebugPorts().then(ports => {
            if (ports && ports.length > 0) {
                console.log('\n🎉 端口扫描完成！');
            } else {
                console.log('\n❌ 端口扫描未找到可用端口！');
            }
        }).catch(error => {
            console.error('❌ 扫描过程出错:', error.message);
        });
    }
}

module.exports = { scanDebugPorts, testSpecificPort };
