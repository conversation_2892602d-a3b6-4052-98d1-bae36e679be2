#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 调试端口扫描器
扫描常见的Chrome调试端口，找到可用的端口
"""

import socket
import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

class DebugPortScanner:
    def __init__(self):
        # 常见的Chrome调试端口范围
        self.port_ranges = [
            range(9222, 9230),      # 标准Chrome调试端口
            range(64170, 64180),    # 你截图中显示的端口范围
            range(55270, 55280),    # 其他常见端口
            range(56900, 56910),    # 比特浏览器相关端口
            range(54340, 54350),    # 更多可能的端口
        ]
        
        # 单独的常见端口
        self.common_ports = [9222, 9223, 9224, 9225, 64170, 64217, 55276, 56906, 54345]
        
    def check_port_open(self, port, timeout=2):
        """检查端口是否开放"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result == 0
        except:
            return False
    
    def check_chrome_debug_port(self, port):
        """检查是否是Chrome调试端口"""
        try:
            # 尝试访问Chrome DevTools协议
            url = f"http://127.0.0.1:{port}/json"
            response = requests.get(url, timeout=3)
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list) and len(data) > 0:
                    # 检查是否有页面信息
                    for page in data:
                        if 'title' in page and 'url' in page:
                            return True, data
            return False, None
        except:
            return False, None
    
    def scan_port_range(self, port_range):
        """扫描端口范围"""
        open_ports = []
        
        for port in port_range:
            if self.check_port_open(port):
                open_ports.append(port)
        
        return open_ports
    
    def scan_all_ports(self):
        """扫描所有可能的端口"""
        print("🔍 扫描常见调试端口...")
        
        all_ports = set(self.common_ports)
        
        # 添加端口范围
        for port_range in self.port_ranges:
            all_ports.update(port_range)
        
        all_ports = sorted(list(all_ports))
        print(f"   总共扫描 {len(all_ports)} 个端口")
        
        open_ports = []
        
        # 使用线程池并发扫描
        with ThreadPoolExecutor(max_workers=20) as executor:
            future_to_port = {executor.submit(self.check_port_open, port): port for port in all_ports}
            
            for future in as_completed(future_to_port):
                port = future_to_port[future]
                try:
                    if future.result():
                        open_ports.append(port)
                        print(f"   ✅ 端口 {port} 开放")
                except Exception as e:
                    pass
        
        return sorted(open_ports)
    
    def identify_debug_ports(self, open_ports):
        """识别哪些是Chrome调试端口"""
        print(f"\n🔍 检查 {len(open_ports)} 个开放端口的调试协议...")
        
        debug_ports = []
        
        for port in open_ports:
            print(f"   检查端口 {port}...")
            is_debug, pages = self.check_chrome_debug_port(port)
            
            if is_debug:
                print(f"   ✅ 端口 {port} 是Chrome调试端口")
                
                # 分析页面信息
                xiaohongshu_pages = []
                for page in pages:
                    title = page.get('title', '')
                    url = page.get('url', '')
                    
                    if 'xiaohongshu.com' in url:
                        xiaohongshu_pages.append({
                            'title': title,
                            'url': url,
                            'id': page.get('id', '')
                        })
                
                debug_ports.append({
                    'port': port,
                    'total_pages': len(pages),
                    'xiaohongshu_pages': xiaohongshu_pages
                })
                
                if xiaohongshu_pages:
                    print(f"      🎉 发现 {len(xiaohongshu_pages)} 个小红书页面!")
                    for xhs_page in xiaohongshu_pages[:2]:  # 只显示前2个
                        print(f"         📄 {xhs_page['title'][:50]}...")
                else:
                    print(f"      📄 共 {len(pages)} 个页面，无小红书页面")
            else:
                print(f"   ❌ 端口 {port} 不是调试端口")
        
        return debug_ports
    
    def run(self):
        """运行扫描流程"""
        print("🎯 Chrome调试端口扫描器")
        print("=" * 50)
        
        start_time = time.time()
        
        # 1. 扫描开放端口
        open_ports = self.scan_all_ports()
        
        if not open_ports:
            print("❌ 未发现任何开放的端口")
            print("💡 请确保比特浏览器正在运行并且启用了调试模式")
            return []
        
        print(f"\n✅ 发现 {len(open_ports)} 个开放端口: {open_ports}")
        
        # 2. 识别调试端口
        debug_ports = self.identify_debug_ports(open_ports)
        
        # 3. 输出结果
        scan_time = time.time() - start_time
        print(f"\n" + "=" * 50)
        print(f"🎉 扫描完成! 耗时: {scan_time:.1f}秒")
        print(f"📊 开放端口: {len(open_ports)}")
        print(f"🎯 调试端口: {len(debug_ports)}")
        
        if debug_ports:
            print("\n🚀 可用的调试端口:")
            
            xiaohongshu_ports = []
            other_ports = []
            
            for debug_port in debug_ports:
                port = debug_port['port']
                xhs_count = len(debug_port['xiaohongshu_pages'])
                total_pages = debug_port['total_pages']
                
                if xhs_count > 0:
                    xiaohongshu_ports.append(port)
                    print(f"   🎉 端口 {port} - {xhs_count} 个小红书页面 (共{total_pages}页)")
                else:
                    other_ports.append(port)
                    print(f"   📄 端口 {port} - {total_pages} 个页面")
            
            # 4. 保存结果
            result = {
                'scan_time': scan_time,
                'open_ports': open_ports,
                'debug_ports': debug_ports,
                'xiaohongshu_ports': xiaohongshu_ports,
                'other_ports': other_ports,
                'timestamp': time.time()
            }
            
            with open('scan_results.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 扫描结果已保存到 scan_results.json")
            
            # 5. 推荐端口
            if xiaohongshu_ports:
                recommended_port = xiaohongshu_ports[0]
                print(f"🎯 推荐使用端口: {recommended_port} (有小红书页面)")
            elif other_ports:
                recommended_port = other_ports[0]
                print(f"🎯 推荐使用端口: {recommended_port}")
            else:
                recommended_port = debug_ports[0]['port']
                print(f"🎯 推荐使用端口: {recommended_port}")
            
            print(f"\n🚀 下一步测试命令:")
            print(f"   python test_debug_port.py {recommended_port}")
            
            return [dp['port'] for dp in debug_ports]
        else:
            print("❌ 未发现Chrome调试端口")
            print("💡 建议:")
            print("   1. 确保比特浏览器正在运行")
            print("   2. 确保浏览器启用了远程调试")
            print("   3. 尝试重启比特浏览器")
            return []

if __name__ == "__main__":
    scanner = DebugPortScanner()
    ports = scanner.run()
