/**
 * 🎯 爬取全部1472条评论
 * 超强版小红书评论爬虫，专门爬取所有1472条评论
 */

const puppeteer = require('puppeteer-core');
const axios = require('axios');
const fs = require('fs');

class SuperCommentsScraper {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.targetWindowSeq = 19;
        this.browser = null;
        this.page = null;
        this.comments = [];
        this.targetCommentCount = 1472; // 目标评论数量
    }

    /**
     * 获取调试信息并连接
     */
    async connectToBrowser() {
        console.log('🔗 连接到比特浏览器...');
        
        try {
            // 获取窗口信息
            const windowResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            const browsers = windowResponse.data.data.list;
            const window19 = browsers.find(browser => browser.seq === this.targetWindowSeq);
            
            if (!window19) {
                throw new Error('未找到19号窗口');
            }

            // 获取调试端口
            const portsResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            const ports = portsResponse.data.data;
            const debugPort = ports[window19.id];
            
            if (!debugPort) {
                throw new Error('19号窗口未获取到调试端口');
            }

            // 获取浏览器WebSocket端点
            const versionResponse = await axios.get(`http://localhost:${debugPort}/json/version`);
            const browserWSEndpoint = versionResponse.data.webSocketDebuggerUrl;
            
            this.browser = await puppeteer.connect({
                browserWSEndpoint,
                defaultViewport: null
            });
            
            // 获取小红书页面
            const browserPages = await this.browser.pages();
            this.page = browserPages.find(page => page.url().includes('xiaohongshu.com/explore/'));
            
            if (!this.page) {
                this.page = browserPages.find(page => page.url().includes('xiaohongshu.com'));
            }
            
            if (!this.page) {
                throw new Error('未找到小红书页面');
            }
            
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            console.log('✅ 连接成功!');
            console.log(`📄 页面: ${title}`);
            
            return true;
            
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    /**
     * 超级滚动 - 加载所有1472条评论
     */
    async superScrollToLoadAll() {
        console.log(`🚀 开始超级滚动，目标：加载全部 ${this.targetCommentCount} 条评论...`);
        
        let scrollCount = 0;
        const maxScrolls = 500; // 大幅增加滚动次数
        let lastHeight = 0;
        let noChangeCount = 0;
        let lastCommentCount = 0;
        
        while (scrollCount < maxScrolls && noChangeCount < 10) {
            // 滚动到页面底部
            await this.page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });
            
            // 等待更长时间让评论加载
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 检查页面高度变化
            const newHeight = await this.page.evaluate(() => document.body.scrollHeight);
            
            // 检查当前评论数量
            const currentCommentCount = await this.page.evaluate(() => {
                const commentElements = document.querySelectorAll('[class*="comment"], [class*="Comment"], [class*="reply"], [class*="Reply"]');
                return commentElements.length;
            });
            
            if (newHeight === lastHeight && currentCommentCount === lastCommentCount) {
                noChangeCount++;
                console.log(`📊 第 ${scrollCount + 1} 次滚动，无变化 (${noChangeCount}/10)，当前评论元素: ${currentCommentCount}`);
            } else {
                noChangeCount = 0;
                console.log(`📊 第 ${scrollCount + 1} 次滚动，高度: ${lastHeight} -> ${newHeight}，评论元素: ${lastCommentCount} -> ${currentCommentCount}`);
            }
            
            lastHeight = newHeight;
            lastCommentCount = currentCommentCount;
            scrollCount++;
            
            // 每20次滚动显示进度
            if (scrollCount % 20 === 0) {
                console.log(`🔄 已滚动 ${scrollCount} 次，当前评论元素: ${currentCommentCount}，目标: ${this.targetCommentCount}`);
                
                // 如果评论数量接近目标，可以提前结束
                if (currentCommentCount >= this.targetCommentCount * 0.9) {
                    console.log(`🎯 评论数量接近目标，当前: ${currentCommentCount}，目标: ${this.targetCommentCount}`);
                }
            }
            
            // 尝试点击"加载更多"按钮（如果存在）
            try {
                const loadMoreButton = await this.page.$('[class*="load"], [class*="more"], button:contains("更多"), button:contains("加载")');
                if (loadMoreButton) {
                    await loadMoreButton.click();
                    console.log('🔄 点击了"加载更多"按钮');
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }
            } catch (e) {
                // 忽略点击错误
            }
        }
        
        console.log(`📄 超级滚动完成！共滚动 ${scrollCount} 次`);
        
        // 最终评论数量统计
        const finalCommentCount = await this.page.evaluate(() => {
            const commentElements = document.querySelectorAll('[class*="comment"], [class*="Comment"], [class*="reply"], [class*="Reply"]');
            return commentElements.length;
        });
        
        console.log(`📊 最终页面评论元素数量: ${finalCommentCount}`);
        
        // 滚动回顶部
        await this.page.evaluate(() => window.scrollTo(0, 0));
        await new Promise(resolve => setTimeout(resolve, 3000));
    }

    /**
     * 超级提取 - 提取所有评论
     */
    async superExtractComments() {
        console.log('🔍 开始超级提取所有评论...');
        
        try {
            const comments = await this.page.evaluate(() => {
                const extractedComments = [];
                
                // 更全面的选择器列表
                const commentSelectors = [
                    '[class*="comment"]',
                    '[class*="Comment"]',
                    '[class*="reply"]',
                    '[class*="Reply"]',
                    '[class*="interaction"]',
                    '[class*="user-comment"]',
                    '[class*="note-comment"]',
                    '[class*="feed-comment"]',
                    '.note-item',
                    '.feed-item',
                    '[data-testid*="comment"]',
                    '[role="comment"]'
                ];
                
                // 方法1: 通过选择器提取
                for (const selector of commentSelectors) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        console.log(`选择器 ${selector} 找到 ${elements.length} 个元素`);
                        
                        elements.forEach((element, index) => {
                            const text = element.textContent?.trim();
                            if (text && text.length > 10) {
                                // 分析文本结构
                                const lines = text.split('\n').filter(line => line.trim());
                                
                                let username = '';
                                let content = '';
                                let time = '';
                                let likes = '';
                                
                                for (let i = 0; i < lines.length; i++) {
                                    const line = lines[i].trim();
                                    
                                    // 跳过太短的行
                                    if (line.length < 2) continue;
                                    
                                    // 检查是否是时间
                                    if (/\d{2}-\d{2}|\d+[分小天月年]前/.test(line)) {
                                        time = line;
                                        continue;
                                    }
                                    
                                    // 检查是否是操作按钮
                                    if (/^(赞|回复|展开|收起|点赞|分享|举报)$/.test(line)) {
                                        continue;
                                    }
                                    
                                    // 检查是否是点赞数
                                    if (/^\d+$/.test(line) && parseInt(line) > 0) {
                                        likes = line;
                                        continue;
                                    }
                                    
                                    // 用户名通常较短且在前面
                                    if (!username && line.length < 50 && line.length > 1 && 
                                        !line.includes('条评论') && !line.includes('回复') && 
                                        !line.includes('展开')) {
                                        username = line;
                                    } else if (line.length > 10 && line.length < 1000) {
                                        // 评论内容
                                        if (content) {
                                            content += ' ' + line;
                                        } else {
                                            content = line;
                                        }
                                    }
                                }
                                
                                // 清理内容
                                if (content) {
                                    // 移除常见的无用文本
                                    content = content.replace(/展开\s*\d+\s*条回复/g, '')
                                                   .replace(/\d+赞回复/g, '')
                                                   .replace(/赞回复$/g, '')
                                                   .replace(/回复$/g, '')
                                                   .trim();
                                    
                                    if (content.length > 5 && content.length < 2000) {
                                        extractedComments.push({
                                            username: username || '未知用户',
                                            content: content,
                                            time: time || '',
                                            likes: likes || '',
                                            method: `selector_${selector}`,
                                            element_index: index,
                                            timestamp: new Date().toISOString()
                                        });
                                    }
                                }
                            }
                        });
                    } catch (e) {
                        console.log(`选择器 ${selector} 处理失败:`, e.message);
                        continue;
                    }
                }
                
                // 方法2: 通过文本分析
                const bodyText = document.body.textContent || '';
                const allLines = bodyText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
                
                for (let i = 0; i < allLines.length; i++) {
                    const line = allLines[i];
                    
                    // 寻找时间标识
                    if (/\d{2}-\d{2}|\d+[分小天月年]前/.test(line)) {
                        // 向前查找可能的评论内容
                        for (let j = 1; j <= 5; j++) {
                            if (i - j >= 0) {
                                const contentLine = allLines[i - j];
                                if (contentLine.length > 15 && contentLine.length < 500 &&
                                    !/(赞|回复|展开|收起|点赞|分享|举报)$/.test(contentLine) &&
                                    !contentLine.includes('条评论')) {
                                    
                                    // 查找用户名
                                    let username = '未知用户';
                                    if (i - j - 1 >= 0) {
                                        const userLine = allLines[i - j - 1];
                                        if (userLine.length > 1 && userLine.length < 30) {
                                            username = userLine;
                                        }
                                    }
                                    
                                    extractedComments.push({
                                        username: username,
                                        content: contentLine,
                                        time: line,
                                        likes: '',
                                        method: 'text_analysis',
                                        timestamp: new Date().toISOString()
                                    });
                                    break;
                                }
                            }
                        }
                    }
                }
                
                // 去重处理
                const uniqueComments = [];
                const seenContents = new Set();
                
                for (const comment of extractedComments) {
                    // 使用内容的前50字符作为去重键
                    const contentKey = comment.content.substring(0, 50);
                    if (!seenContents.has(contentKey) && 
                        comment.content.length > 5 && 
                        comment.content.length < 2000) {
                        seenContents.add(contentKey);
                        uniqueComments.push(comment);
                    }
                }
                
                return uniqueComments;
            });
            
            this.comments = comments;
            console.log(`✅ 超级提取完成，共获得 ${this.comments.length} 条评论`);
            
            // 显示提取统计
            const methodStats = {};
            this.comments.forEach(comment => {
                methodStats[comment.method] = (methodStats[comment.method] || 0) + 1;
            });
            
            console.log('📊 提取方式统计:');
            Object.entries(methodStats).forEach(([method, count]) => {
                console.log(`   ${method}: ${count} 条`);
            });
            
            return this.comments.length > 0;
            
        } catch (error) {
            console.error('❌ 超级提取失败:', error.message);
            return false;
        }
    }

    /**
     * 保存为完美的TXT格式
     */
    async savePerfectTXT() {
        if (this.comments.length === 0) {
            console.log('⚠️ 没有评论数据可保存');
            return;
        }
        
        try {
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            // 生成完美的TXT内容
            let txtContent = '';
            txtContent += '='.repeat(80) + '\n';
            txtContent += '小红书评论完整数据\n';
            txtContent += '='.repeat(80) + '\n';
            txtContent += `页面标题: ${title}\n`;
            txtContent += `页面链接: ${currentUrl}\n`;
            txtContent += `爬取时间: ${new Date().toLocaleString('zh-CN')}\n`;
            txtContent += `评论总数: ${this.comments.length} 条\n`;
            txtContent += `目标数量: ${this.targetCommentCount} 条\n`;
            txtContent += `完成度: ${Math.round((this.comments.length / this.targetCommentCount) * 100)}%\n`;
            txtContent += '='.repeat(80) + '\n\n';
            
            // 按时间排序（如果有时间信息）
            const sortedComments = this.comments.sort((a, b) => {
                if (a.time && b.time) {
                    return a.time.localeCompare(b.time);
                }
                return 0;
            });
            
            // 添加评论内容
            sortedComments.forEach((comment, index) => {
                txtContent += `【评论 ${index + 1}】\n`;
                txtContent += `用户名: ${comment.username}\n`;
                if (comment.time) {
                    txtContent += `发布时间: ${comment.time}\n`;
                }
                if (comment.likes) {
                    txtContent += `点赞数: ${comment.likes}\n`;
                }
                txtContent += `评论内容: ${comment.content}\n`;
                txtContent += `提取方式: ${comment.method}\n`;
                txtContent += '-'.repeat(60) + '\n\n';
            });
            
            // 添加详细统计
            txtContent += '='.repeat(80) + '\n';
            txtContent += '详细统计信息\n';
            txtContent += '='.repeat(80) + '\n';
            
            // 按提取方式统计
            const methodStats = {};
            this.comments.forEach(comment => {
                methodStats[comment.method] = (methodStats[comment.method] || 0) + 1;
            });
            
            txtContent += '按提取方式统计:\n';
            Object.entries(methodStats).sort((a, b) => b[1] - a[1]).forEach(([method, count]) => {
                const percentage = Math.round((count / this.comments.length) * 100);
                txtContent += `  ${method}: ${count} 条 (${percentage}%)\n`;
            });
            
            // 按时间统计
            const timeStats = {};
            this.comments.forEach(comment => {
                if (comment.time) {
                    const timeKey = comment.time.includes('-') ? comment.time.substring(0, 5) : 
                                   comment.time.includes('前') ? comment.time : '其他';
                    timeStats[timeKey] = (timeStats[timeKey] || 0) + 1;
                }
            });
            
            if (Object.keys(timeStats).length > 0) {
                txtContent += '\n按时间统计:\n';
                Object.entries(timeStats).sort().forEach(([time, count]) => {
                    txtContent += `  ${time}: ${count} 条\n`;
                });
            }
            
            // 内容长度统计
            const lengthRanges = {
                '短评论(5-50字)': 0,
                '中等评论(51-150字)': 0,
                '长评论(151-500字)': 0,
                '超长评论(500字以上)': 0
            };
            
            this.comments.forEach(comment => {
                const len = comment.content.length;
                if (len <= 50) lengthRanges['短评论(5-50字)']++;
                else if (len <= 150) lengthRanges['中等评论(51-150字)']++;
                else if (len <= 500) lengthRanges['长评论(151-500字)']++;
                else lengthRanges['超长评论(500字以上)']++;
            });
            
            txtContent += '\n按内容长度统计:\n';
            Object.entries(lengthRanges).forEach(([range, count]) => {
                if (count > 0) {
                    const percentage = Math.round((count / this.comments.length) * 100);
                    txtContent += `  ${range}: ${count} 条 (${percentage}%)\n`;
                }
            });
            
            txtContent += '\n' + '='.repeat(80) + '\n';
            txtContent += '爬取完成！\n';
            txtContent += '='.repeat(80) + '\n';
            
            // 保存文件
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const txtFilename = `xiaohongshu_all_comments_${timestamp}.txt`;
            
            fs.writeFileSync(txtFilename, txtContent, 'utf8');
            
            console.log(`💾 完美TXT文件已保存: ${txtFilename}`);
            console.log(`📊 总计 ${this.comments.length} 条评论 (目标: ${this.targetCommentCount} 条)`);
            
            return txtFilename;
            
        } catch (error) {
            console.error('❌ 保存文件失败:', error.message);
        }
    }

    /**
     * 运行超级爬虫
     */
    async run() {
        console.log('🎯 超级小红书评论爬虫 - 目标1472条评论');
        console.log('='.repeat(80));
        
        try {
            // 1. 连接浏览器
            await this.connectToBrowser();
            
            // 2. 等待页面稳定
            console.log('⏳ 等待页面稳定...');
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 3. 超级滚动加载所有评论
            await this.superScrollToLoadAll();
            
            // 4. 超级提取所有评论
            const success = await this.superExtractComments();
            
            if (!success) {
                console.log('❌ 未能提取到评论数据');
                return false;
            }
            
            // 5. 保存为完美TXT格式
            const txtFile = await this.savePerfectTXT();
            
            console.log('\n🎉 超级爬取完成!');
            console.log(`📁 请查看生成的TXT文件: ${txtFile}`);
            console.log(`📊 成功获取 ${this.comments.length} / ${this.targetCommentCount} 条评论`);
            
            const completionRate = Math.round((this.comments.length / this.targetCommentCount) * 100);
            console.log(`🎯 完成度: ${completionRate}%`);
            
            return true;
            
        } catch (error) {
            console.error('❌ 超级爬虫运行失败:', error.message);
            return false;
        } finally {
            if (this.browser) {
                console.log('🔒 断开浏览器连接...');
                await this.browser.disconnect();
            }
        }
    }
}

// 主函数
async function main() {
    const scraper = new SuperCommentsScraper();
    await scraper.run();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = SuperCommentsScraper;
