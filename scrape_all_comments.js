/**
 * 🎯 爬取所有评论并保存为TXT格式
 * 增强版小红书评论爬虫，爬取更多评论并输出为可读的TXT文件
 */

const puppeteer = require('puppeteer-core');
const axios = require('axios');
const fs = require('fs');

class AllCommentsScraper {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.targetWindowSeq = 19;
        this.browser = null;
        this.page = null;
        this.comments = [];
    }

    /**
     * 获取调试信息
     */
    async getDebugInfo() {
        console.log('🔍 获取调试信息...');
        
        try {
            // 1. 获取窗口信息
            const windowResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            const browsers = windowResponse.data.data.list;
            const window19 = browsers.find(browser => browser.seq === this.targetWindowSeq);
            
            if (!window19) {
                throw new Error('未找到19号窗口');
            }

            // 2. 获取调试端口
            const portsResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            const ports = portsResponse.data.data;
            const debugPort = ports[window19.id];
            
            if (!debugPort) {
                throw new Error('19号窗口未获取到调试端口');
            }

            // 3. 获取页面列表
            const pagesResponse = await axios.get(`http://localhost:${debugPort}/json/list`);
            const pages = pagesResponse.data;

            console.log(`✅ 找到 ${pages.length} 个页面`);
            return { window19, debugPort, pages };

        } catch (error) {
            console.error('❌ 获取调试信息失败:', error.message);
            throw error;
        }
    }

    /**
     * 选择最佳的小红书页面
     */
    selectBestPage(pages) {
        const xiaohongshuPages = pages.filter(page => 
            page.url.includes('xiaohongshu.com') && 
            page.type === 'page'
        );

        if (xiaohongshuPages.length === 0) {
            throw new Error('未找到小红书页面');
        }

        // 优先选择包含 /explore/ 的页面（这些通常是内容详情页，有评论）
        const explorePage = xiaohongshuPages.find(page => page.url.includes('/explore/'));
        if (explorePage) {
            console.log(`🎯 选择评论页面: ${explorePage.title}`);
            return explorePage;
        }

        // 如果没有explore页面，选择第一个小红书页面
        console.log(`📄 选择页面: ${xiaohongshuPages[0].title}`);
        return xiaohongshuPages[0];
    }

    /**
     * 连接到比特浏览器
     */
    async connectToBrowser() {
        console.log('🔗 连接到比特浏览器...');
        
        try {
            const { window19, debugPort, pages } = await this.getDebugInfo();
            
            // 选择最佳页面
            const targetPage = this.selectBestPage(pages);
            
            // 获取浏览器WebSocket端点
            const versionResponse = await axios.get(`http://localhost:${debugPort}/json/version`);
            const browserWSEndpoint = versionResponse.data.webSocketDebuggerUrl;
            
            console.log('🔗 连接到浏览器...');
            this.browser = await puppeteer.connect({
                browserWSEndpoint,
                defaultViewport: null
            });
            
            // 获取所有页面
            const browserPages = await this.browser.pages();
            
            // 找到目标页面
            this.page = browserPages.find(page => page.url().includes(targetPage.url.split('?')[0]));
            
            if (!this.page) {
                // 如果找不到，使用第一个小红书页面
                this.page = browserPages.find(page => page.url().includes('xiaohongshu.com'));
            }
            
            if (!this.page) {
                throw new Error('未找到可用的小红书页面');
            }
            
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            console.log('✅ 连接成功!');
            console.log(`📄 当前页面: ${currentUrl}`);
            console.log(`📝 页面标题: ${title}`);
            
            return true;
            
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    /**
     * 深度滚动页面加载所有评论
     */
    async scrollAndLoadAllComments() {
        console.log('📜 开始深度滚动加载所有评论...');
        
        let scrollCount = 0;
        const maxScrolls = 100; // 增加到100次滚动，获取更多评论
        let lastHeight = 0;
        let noChangeCount = 0; // 连续无变化次数
        
        while (scrollCount < maxScrolls && noChangeCount < 5) {
            // 滚动到页面底部
            const currentHeight = await this.page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
                return document.body.scrollHeight;
            });
            
            // 等待新内容加载
            await new Promise(resolve => setTimeout(resolve, 3000)); // 增加等待时间
            
            // 检查是否有新内容
            const newHeight = await this.page.evaluate(() => document.body.scrollHeight);
            
            if (newHeight === lastHeight) {
                noChangeCount++;
                console.log(`📊 第 ${scrollCount + 1} 次滚动，页面高度无变化 (${noChangeCount}/5)`);
            } else {
                noChangeCount = 0;
                console.log(`📊 第 ${scrollCount + 1} 次滚动，页面高度: ${lastHeight} -> ${newHeight}`);
            }
            
            lastHeight = newHeight;
            scrollCount++;
            
            if (scrollCount % 10 === 0) {
                console.log(`📊 已滚动 ${scrollCount} 次，继续加载更多评论...`);
                
                // 每10次滚动检查一下当前评论数量
                const currentCommentCount = await this.page.evaluate(() => {
                    const commentElements = document.querySelectorAll('[class*="comment"], [class*="Comment"]');
                    return commentElements.length;
                });
                console.log(`   当前页面评论元素数量: ${currentCommentCount}`);
            }
        }
        
        console.log(`📄 滚动完成！共滚动 ${scrollCount} 次`);
        
        // 滚动回顶部
        await this.page.evaluate(() => window.scrollTo(0, 0));
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    /**
     * 提取所有评论数据
     */
    async extractAllComments() {
        console.log('📝 开始提取所有评论数据...');
        
        try {
            const comments = await this.page.evaluate(() => {
                const extractedComments = [];
                
                // 方法1: 通过常见的评论选择器
                const commentSelectors = [
                    '[class*="comment"]',
                    '[class*="Comment"]',
                    '[class*="reply"]',
                    '[class*="Reply"]',
                    '.note-item',
                    '.feed-item',
                    '[data-testid*="comment"]',
                    '[class*="interaction"]',
                    '[class*="user-comment"]'
                ];
                
                for (const selector of commentSelectors) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach((element, index) => {
                            const text = element.textContent?.trim();
                            if (text && text.length > 5) {
                                // 尝试提取用户名和评论内容
                                const lines = text.split('\n').filter(line => line.trim());
                                let username = '';
                                let content = '';
                                let time = '';
                                
                                // 简单的用户名和内容分离
                                for (let i = 0; i < lines.length; i++) {
                                    const line = lines[i].trim();
                                    
                                    // 检查是否是时间标识
                                    if (/[分小天月年]前|^\d{2}-\d{2}/.test(line)) {
                                        time = line;
                                        continue;
                                    }
                                    
                                    // 检查是否是操作按钮
                                    if (/(赞|回复|展开|收起|点赞|分享)$/.test(line)) {
                                        continue;
                                    }
                                    
                                    // 如果还没有用户名，且行长度合理，可能是用户名
                                    if (!username && line.length < 50 && line.length > 1) {
                                        username = line;
                                    } else if (line.length > 10) {
                                        // 较长的文本可能是评论内容
                                        if (content) {
                                            content += ' ' + line;
                                        } else {
                                            content = line;
                                        }
                                    }
                                }
                                
                                if (content && content.length > 5) {
                                    extractedComments.push({
                                        username: username || '未知用户',
                                        content: content,
                                        time: time || '',
                                        method: `selector_${selector}`,
                                        element_index: index,
                                        timestamp: new Date().toISOString()
                                    });
                                }
                            }
                        });
                    } catch (e) {
                        continue;
                    }
                }
                
                // 方法2: 通过文本模式匹配
                const bodyText = document.body.textContent || '';
                const lines = bodyText.split('\n');
                
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();
                    
                    // 检查是否像评论（包含时间标识）
                    if (/[分小天月年]前|^\d{2}-\d{2}/.test(line)) {
                        // 尝试获取前面的文本作为评论内容
                        for (let j = 1; j <= 3; j++) {
                            if (i - j >= 0) {
                                const prevLine = lines[i - j].trim();
                                if (prevLine.length > 10 && prevLine.length < 500 && 
                                    !/(点赞|关注|分享|收藏|笔记|作品|赞|回复)$/.test(prevLine)) {
                                    
                                    // 尝试找用户名
                                    let username = '未知用户';
                                    if (i - j - 1 >= 0) {
                                        const userLine = lines[i - j - 1].trim();
                                        if (userLine.length > 1 && userLine.length < 30) {
                                            username = userLine;
                                        }
                                    }
                                    
                                    extractedComments.push({
                                        username: username,
                                        content: prevLine,
                                        time: line,
                                        method: 'text_pattern',
                                        timestamp: new Date().toISOString()
                                    });
                                    break;
                                }
                            }
                        }
                    }
                }
                
                // 去重 - 基于内容去重
                const uniqueComments = [];
                const seenContents = new Set();
                
                for (const comment of extractedComments) {
                    const contentKey = comment.content.substring(0, 100); // 使用前100字符作为去重键
                    if (!seenContents.has(contentKey) && 
                        comment.content.length > 5 && 
                        comment.content.length < 1000) {
                        seenContents.add(contentKey);
                        uniqueComments.push(comment);
                    }
                }
                
                return uniqueComments;
            });
            
            this.comments = comments;
            console.log(`✅ 提取完成，共获得 ${this.comments.length} 条评论`);
            
            return this.comments.length > 0;
            
        } catch (error) {
            console.error('❌ 提取评论失败:', error.message);
            return false;
        }
    }

    /**
     * 保存为TXT格式
     */
    async saveAsTXT() {
        if (this.comments.length === 0) {
            console.log('⚠️ 没有评论数据可保存');
            return;
        }
        
        try {
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            // 生成TXT内容
            let txtContent = '';
            txtContent += '='.repeat(60) + '\n';
            txtContent += '小红书评论数据\n';
            txtContent += '='.repeat(60) + '\n';
            txtContent += `页面标题: ${title}\n`;
            txtContent += `页面链接: ${currentUrl}\n`;
            txtContent += `爬取时间: ${new Date().toLocaleString('zh-CN')}\n`;
            txtContent += `评论总数: ${this.comments.length} 条\n`;
            txtContent += '='.repeat(60) + '\n\n';
            
            // 添加评论内容
            this.comments.forEach((comment, index) => {
                txtContent += `【评论 ${index + 1}】\n`;
                txtContent += `用户: ${comment.username}\n`;
                txtContent += `时间: ${comment.time || '未知时间'}\n`;
                txtContent += `内容: ${comment.content}\n`;
                txtContent += `提取方式: ${comment.method}\n`;
                txtContent += '-'.repeat(40) + '\n\n';
            });
            
            // 添加统计信息
            txtContent += '='.repeat(60) + '\n';
            txtContent += '统计信息\n';
            txtContent += '='.repeat(60) + '\n';
            
            // 按提取方式统计
            const methodStats = {};
            this.comments.forEach(comment => {
                methodStats[comment.method] = (methodStats[comment.method] || 0) + 1;
            });
            
            txtContent += '按提取方式统计:\n';
            Object.entries(methodStats).forEach(([method, count]) => {
                txtContent += `  ${method}: ${count} 条\n`;
            });
            
            // 按时间统计（如果有时间信息）
            const timeStats = {};
            this.comments.forEach(comment => {
                if (comment.time) {
                    const timeKey = comment.time.includes('-') ? comment.time.substring(0, 5) : '其他';
                    timeStats[timeKey] = (timeStats[timeKey] || 0) + 1;
                }
            });
            
            if (Object.keys(timeStats).length > 0) {
                txtContent += '\n按时间统计:\n';
                Object.entries(timeStats).sort().forEach(([time, count]) => {
                    txtContent += `  ${time}: ${count} 条\n`;
                });
            }
            
            // 保存文件
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const txtFilename = `xiaohongshu_comments_${timestamp}.txt`;
            
            fs.writeFileSync(txtFilename, txtContent, 'utf8');
            
            console.log(`💾 TXT文件已保存: ${txtFilename}`);
            console.log(`📊 总计 ${this.comments.length} 条评论`);
            
            // 同时保存JSON格式作为备份
            const jsonData = {
                scrape_info: {
                    url: currentUrl,
                    title: title,
                    scrape_time: new Date().toISOString(),
                    total_comments: this.comments.length,
                    method: 'all_comments_scraper'
                },
                comments: this.comments
            };
            
            const jsonFilename = `xiaohongshu_comments_${timestamp}.json`;
            fs.writeFileSync(jsonFilename, JSON.stringify(jsonData, null, 2), 'utf8');
            console.log(`💾 JSON备份已保存: ${jsonFilename}`);
            
            return txtFilename;
            
        } catch (error) {
            console.error('❌ 保存文件失败:', error.message);
        }
    }

    /**
     * 运行爬虫
     */
    async run() {
        console.log('🎯 爬取所有小红书评论 (TXT格式输出)');
        console.log('='.repeat(60));
        
        try {
            // 1. 连接浏览器
            await this.connectToBrowser();
            
            // 2. 等待页面稳定
            console.log('⏳ 等待页面稳定...');
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 3. 深度滚动加载所有评论
            await this.scrollAndLoadAllComments();
            
            // 4. 提取所有评论
            const success = await this.extractAllComments();
            
            if (!success) {
                console.log('❌ 未能提取到评论数据');
                return false;
            }
            
            // 5. 保存为TXT格式
            const txtFile = await this.saveAsTXT();
            
            console.log('\n🎉 爬取完成!');
            console.log(`📁 请查看生成的TXT文件: ${txtFile}`);
            return true;
            
        } catch (error) {
            console.error('❌ 爬虫运行失败:', error.message);
            return false;
        } finally {
            if (this.browser) {
                console.log('🔒 断开浏览器连接...');
                await this.browser.disconnect();
            }
        }
    }
}

// 主函数
async function main() {
    console.log('🎯 小红书所有评论爬取器');
    console.log('='.repeat(60));
    
    console.log('📝 功能说明:');
    console.log('   1. 深度滚动加载所有评论 (最多100次滚动)');
    console.log('   2. 智能提取用户名、评论内容、时间');
    console.log('   3. 保存为易读的TXT格式');
    console.log('   4. 同时生成JSON备份文件');
    
    const scraper = new AllCommentsScraper();
    const success = await scraper.run();
    
    if (success) {
        console.log('\n🎉 所有评论爬取成功完成!');
        console.log('📁 请查看生成的TXT和JSON文件');
    } else {
        console.log('\n❌ 爬取失败');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = AllCommentsScraper;
