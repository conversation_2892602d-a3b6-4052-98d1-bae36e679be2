#!/usr/bin/env python3
"""
黑默科技 - Windows ICO图标生成器
生成适用于Electron应用的.ico格式图标文件
"""

from PIL import Image, ImageDraw
import os

def create_logo_image(size):
    """创建黑默科技logo图像"""
    # 创建透明背景图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 计算缩放比例
    scale = size / 100
    
    # 绘制深色背景圆角矩形
    margin = int(2 * scale)
    bg_rect = [margin, margin, size - margin, size - margin]
    draw.rounded_rectangle(bg_rect, radius=int(8 * scale), fill=(26, 26, 26, 255))
    
    # 线条宽度
    line_width = max(1, int(2 * scale))
    
    # 外层立方体框架 (六边形)
    outer_points = [
        (15 * scale, 25 * scale),  # 左上
        (50 * scale, 10 * scale),  # 顶部
        (85 * scale, 25 * scale),  # 右上
        (85 * scale, 75 * scale),  # 右下
        (50 * scale, 90 * scale),  # 底部
        (15 * scale, 75 * scale),  # 左下
    ]
    
    # 绘制外框
    for i in range(len(outer_points)):
        start = outer_points[i]
        end = outer_points[(i + 1) % len(outer_points)]
        draw.line([start, end], fill=(255, 255, 255, 255), width=line_width)
    
    # 内层立方体
    inner_points = [
        (25 * scale, 32 * scale),
        (50 * scale, 20 * scale),
        (75 * scale, 32 * scale),
        (75 * scale, 68 * scale),
        (50 * scale, 80 * scale),
        (25 * scale, 68 * scale),
    ]
    
    # 绘制内框
    for i in range(len(inner_points)):
        start = inner_points[i]
        end = inner_points[(i + 1) % len(inner_points)]
        draw.line([start, end], fill=(255, 255, 255, 255), width=max(1, int(1.5 * scale)))
    
    # 立体连接线
    connections = [
        # 顶部连接
        ((15 * scale, 25 * scale), (25 * scale, 32 * scale)),
        ((50 * scale, 10 * scale), (50 * scale, 20 * scale)),
        ((85 * scale, 25 * scale), (75 * scale, 32 * scale)),
        # 底部连接
        ((15 * scale, 75 * scale), (25 * scale, 68 * scale)),
        ((50 * scale, 90 * scale), (50 * scale, 80 * scale)),
        ((85 * scale, 75 * scale), (75 * scale, 68 * scale)),
    ]
    
    for start, end in connections:
        draw.line([start, end], fill=(255, 255, 255, 255), width=max(1, int(1.2 * scale)))
    
    # Y形结构
    y_lines = [
        ((50 * scale, 20 * scale), (50 * scale, 50 * scale)),  # 垂直线
        ((35 * scale, 38 * scale), (50 * scale, 50 * scale)),  # 左分支
        ((50 * scale, 50 * scale), (65 * scale, 38 * scale)),  # 右分支
    ]
    
    for start, end in y_lines:
        draw.line([start, end], fill=(255, 255, 255, 255), width=line_width)
    
    # 蓝色科技点
    dot_radius = max(1, int(2.5 * scale))
    dot_center = (50 * scale, 32 * scale)
    dot_bbox = [
        dot_center[0] - dot_radius,
        dot_center[1] - dot_radius,
        dot_center[0] + dot_radius,
        dot_center[1] + dot_radius
    ]
    draw.ellipse(dot_bbox, fill=(79, 70, 229, 255))
    
    return img

def generate_ico_file():
    """生成ICO文件"""
    # ICO文件需要的标准尺寸
    sizes = [16, 24, 32, 48, 64, 128, 256]

    # 创建不同尺寸的图像
    images = []
    for size in sizes:
        img = create_logo_image(size)
        images.append(img)

    # 确保输出目录存在
    output_dir = "assets"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 保存为ICO文件
    ico_path = os.path.join(output_dir, "icon.ico")
    images[0].save(ico_path, format='ICO', sizes=[(img.width, img.height) for img in images])

    # 同时保存PNG版本用于预览
    png_path = os.path.join(output_dir, "icon.png")
    images[-1].save(png_path, format='PNG')

    return ico_path, png_path

def main():
    """主函数"""
    try:
        # 直接生成图标，不输出太多信息
        generate_ico_file()

        # 创建成功标记文件
        with open("icon_generated.txt", "w", encoding="utf-8") as f:
            f.write("✅ 黑默科技图标生成成功！\n")
            f.write("📁 生成文件:\n")
            f.write("  - assets/icon.ico (Windows图标)\n")
            f.write("  - assets/icon.png (PNG预览)\n")
            f.write("\n🔧 使用说明:\n")
            f.write("1. 将 assets/icon.ico 用于Electron应用\n")
            f.write("2. 在 package.json 中配置图标路径\n")
            f.write("3. 重新构建应用即可看到新图标\n")

        return True

    except ImportError as e:
        with open("icon_error.txt", "w", encoding="utf-8") as f:
            f.write("❌ 错误: 需要安装 Pillow 库\n")
            f.write("请运行: pip install Pillow\n")
            f.write(f"详细错误: {e}\n")
        return False

    except Exception as e:
        with open("icon_error.txt", "w", encoding="utf-8") as f:
            f.write(f"❌ 生成图标时出错: {e}\n")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
