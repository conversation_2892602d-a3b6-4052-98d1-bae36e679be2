#!/usr/bin/env node

/**
 * 🔄 自动滚动笔记采集器
 * 自动滚动页面加载所有笔记，然后采集完整的笔记信息
 */

const axios = require('axios');
const WebSocket = require('ws');

class ScrollNotesCollector {
    constructor() {
        this.debugPort = null;
        this.currentTab = null;
        this.totalNotes = 0;
        this.scrollAttempts = 0;
        this.maxScrollAttempts = 20; // 最大滚动次数
    }

    // 🔄 主要采集方法 - 滚动获取所有笔记
    async collectAllNotesWithScroll() {
        console.log('🔄 开始滚动采集所有小红书笔记...\n');

        try {
            // 1. 获取当前调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 目标页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 执行滚动加载和数据采集
            const notesData = await this.scrollAndCollectNotes(tab);
            
            console.log('✅ 滚动采集完成!');
            return notesData;

        } catch (error) {
            console.error('❌ 滚动采集失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取当前调试端口
    async getCurrentDebugPort() {
        try {
            console.log('🔌 获取当前浏览器调试端口...');
            
            // 从本地服务器获取浏览器状态
            const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 10000
            });

            if (response.data.success && response.data.data && response.data.data.result) {
                const httpInfo = response.data.data.result.http;
                if (httpInfo) {
                    this.debugPort = httpInfo.split(':')[1];
                    console.log(`✅ 获取到调试端口: ${this.debugPort}`);
                    return this.debugPort;
                }
            }

            // 如果API获取失败，尝试常见端口
            console.log('⚠️ API获取端口失败，尝试常见端口...');
            const commonPorts = [63524, 51859, 58222, 9222, 9223, 9224];
            
            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到可用端口: ${port}`);
                    return port;
                } catch (error) {
                    // 继续尝试下一个端口
                }
            }

            throw new Error('未找到可用的调试端口');

        } catch (error) {
            console.error('❌ 获取调试端口失败:', error.message);
            throw error;
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        try {
            const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
                timeout: 5000
            });

            const tabs = response.data;
            return tabs.find(tab =>
                tab.url && (
                    tab.url.includes('xiaohongshu.com') ||
                    tab.url.includes('creator.xiaohongshu.com') ||
                    tab.title.includes('小红书')
                )
            );
        } catch (error) {
            console.error('❌ 无法获取标签页列表:', error.message);
            return null;
        }
    }

    // 🔄 滚动并采集笔记
    async scrollAndCollectNotes(tab) {
        return new Promise((resolve, reject) => {
            const wsUrl = tab.webSocketDebuggerUrl;
            console.log(`🔌 连接WebSocket: ${wsUrl}`);

            const ws = new WebSocket(wsUrl);
            let requestId = 1;
            let allNotes = [];
            let lastNotesCount = 0;
            let stableCount = 0;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');

                // 启用Runtime域
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                // 开始滚动和采集流程
                this.startScrollingProcess(ws, requestId);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.action === 'scroll') {
                            console.log(`📜 滚动完成，当前笔记数: ${result.notesCount}`);
                            
                            // 检查是否有新笔记加载
                            if (result.notesCount === lastNotesCount) {
                                stableCount++;
                                if (stableCount >= 3) {
                                    console.log('📋 没有更多笔记加载，开始最终数据采集...');
                                    this.collectFinalData(ws, requestId++);
                                    return;
                                }
                            } else {
                                stableCount = 0;
                                lastNotesCount = result.notesCount;
                            }
                            
                            // 继续滚动
                            if (this.scrollAttempts < this.maxScrollAttempts) {
                                setTimeout(() => {
                                    this.performScroll(ws, requestId++);
                                }, 2000);
                            } else {
                                console.log('📋 达到最大滚动次数，开始最终数据采集...');
                                this.collectFinalData(ws, requestId++);
                            }
                            
                        } else if (result.action === 'collect') {
                            console.log('📊 成功采集最终数据');
                            const processedData = this.processNotesData(result, tab);
                            ws.close();
                            resolve(processedData);
                        }
                    }
                } catch (error) {
                    console.error('❌ 解析WebSocket消息失败:', error.message);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket连接错误:', error.message);
                reject(error);
            });

            ws.on('close', () => {
                console.log('🔌 WebSocket连接已关闭');
            });

            // 超时处理
            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                    reject(new Error('WebSocket连接超时'));
                }
            }, 120000); // 2分钟超时
        });
    }

    // 🚀 开始滚动流程
    startScrollingProcess(ws, requestId) {
        console.log('🔄 开始自动滚动加载笔记...');
        
        // 等待页面加载完成后开始滚动
        setTimeout(() => {
            this.performScroll(ws, requestId);
        }, 3000);
    }

    // 📜 执行滚动操作
    performScroll(ws, requestId) {
        this.scrollAttempts++;
        console.log(`📜 执行第 ${this.scrollAttempts} 次滚动...`);

        const scrollScript = `
            (function() {
                try {
                    // 滚动到页面底部
                    window.scrollTo(0, document.body.scrollHeight);

                    // 统计当前笔记数量
                    const noteSelectors = [
                        '.note-item',
                        '.feed-item',
                        '[class*="note"]',
                        '[class*="feed"]',
                        '[class*="card"]'
                    ];

                    let notesCount = 0;
                    for (const selector of noteSelectors) {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > notesCount) {
                            notesCount = elements.length;
                        }
                    }

                    // 如果没找到特定选择器，使用通用方法
                    if (notesCount === 0) {
                        const allDivs = document.querySelectorAll('div');
                        notesCount = Array.from(allDivs).filter(el => {
                            const hasImage = el.querySelector('img');
                            const hasText = el.textContent.trim().length > 10;
                            return hasImage && hasText;
                        }).length;
                    }

                    return {
                        action: 'scroll',
                        notesCount: notesCount,
                        scrollHeight: document.body.scrollHeight,
                        scrollTop: window.pageYOffset
                    };

                } catch (error) {
                    return {
                        action: 'scroll',
                        error: error.message,
                        notesCount: 0
                    };
                }
            })();
        `;

        ws.send(JSON.stringify({
            id: requestId,
            method: 'Runtime.evaluate',
            params: {
                expression: scrollScript,
                returnByValue: true
            }
        }));
    }

    // 📊 采集最终数据
    collectFinalData(ws, requestId) {
        console.log('📊 开始采集最终笔记数据...');
        
        const collectScript = this.buildFinalCollectionScript();
        
        ws.send(JSON.stringify({
            id: requestId,
            method: 'Runtime.evaluate',
            params: {
                expression: collectScript,
                returnByValue: true
            }
        }));
    }

    // 📝 构建最终采集脚本
    buildFinalCollectionScript() {
        return `
            (function() {
                try {
                    const result = {
                        action: 'collect',
                        timestamp: new Date().toISOString(),
                        url: window.location.href,
                        title: document.title,
                        notes: []
                    };

                    // 查找笔记容器
                    const noteContainerSelectors = [
                        '.note-item',
                        '.feed-item',
                        '[class*="note"]',
                        '[class*="feed"]',
                        '[class*="card"]'
                    ];

                    let noteElements = [];
                    
                    for (const selector of noteContainerSelectors) {
                        noteElements = document.querySelectorAll(selector);
                        if (noteElements.length > 0) {
                            break;
                        }
                    }

                    // 如果没找到特定容器，使用通用方法
                    if (noteElements.length === 0) {
                        const allElements = document.querySelectorAll('div');
                        noteElements = Array.from(allElements).filter(el => {
                            const hasImage = el.querySelector('img');
                            const hasText = el.textContent.trim().length > 10;
                            return hasImage && hasText;
                        });
                    }

                    console.log('找到', noteElements.length, '个笔记元素');

                    // 提取每个笔记的信息
                    noteElements.forEach((noteElement, index) => {
                        try {
                            const noteData = {
                                index: index + 1,
                                id: noteElement.id || 'note_' + index
                            };

                            // 提取标题
                            const titleSelectors = ['.title', '.note-title', 'h1', 'h2', 'h3', '[class*="title"]'];
                            for (const selector of titleSelectors) {
                                const titleElement = noteElement.querySelector(selector);
                                if (titleElement && titleElement.textContent.trim()) {
                                    noteData.title = titleElement.textContent.trim();
                                    break;
                                }
                            }

                            // 如果没找到标题，使用文本内容
                            if (!noteData.title) {
                                const textContent = noteElement.textContent.trim();
                                if (textContent.length > 10) {
                                    noteData.title = textContent.substring(0, 50) + (textContent.length > 50 ? '...' : '');
                                }
                            }

                            // 提取图片
                            const images = noteElement.querySelectorAll('img');
                            noteData.images = [];
                            images.forEach(img => {
                                if (img.src && !img.src.includes('data:') && !img.src.includes('avatar')) {
                                    noteData.images.push({
                                        src: img.src,
                                        alt: img.alt || ''
                                    });
                                }
                            });

                            // 提取互动数据
                            noteData.interactions = { likes: 0, collects: 0, comments: 0 };
                            const interactionElements = noteElement.querySelectorAll('*');
                            interactionElements.forEach(el => {
                                const text = el.textContent.trim();
                                const number = parseInt(text);
                                if (!isNaN(number) && number >= 0) {
                                    const context = el.parentElement ? el.parentElement.textContent.toLowerCase() : '';
                                    if (context.includes('赞') || context.includes('like')) {
                                        noteData.interactions.likes = number;
                                    } else if (context.includes('收藏') || context.includes('collect')) {
                                        noteData.interactions.collects = number;
                                    } else if (context.includes('评论') || context.includes('comment')) {
                                        noteData.interactions.comments = number;
                                    }
                                }
                            });

                            // 提取链接
                            const linkElement = noteElement.querySelector('a[href*="/explore/"]') || 
                                              noteElement.querySelector('a[href*="/discovery/"]') ||
                                              noteElement.querySelector('a');
                            if (linkElement && linkElement.href) {
                                noteData.link = linkElement.href;
                            }

                            if (noteData.title || noteData.images.length > 0) {
                                result.notes.push(noteData);
                            }

                        } catch (error) {
                            console.error('处理笔记', index, '时出错:', error.message);
                        }
                    });

                    // 添加统计信息
                    result.summary = {
                        totalNotes: result.notes.length,
                        totalImages: result.notes.reduce((sum, note) => sum + note.images.length, 0),
                        totalLikes: result.notes.reduce((sum, note) => sum + (note.interactions.likes || 0), 0),
                        totalCollects: result.notes.reduce((sum, note) => sum + (note.interactions.collects || 0), 0),
                        totalComments: result.notes.reduce((sum, note) => sum + (note.interactions.comments || 0), 0),
                        scrollAttempts: ${this.scrollAttempts}
                    };

                    return result;
                    
                } catch (error) {
                    return {
                        action: 'collect',
                        error: error.message,
                        timestamp: new Date().toISOString()
                    };
                }
            })();
        `;
    }

    // 📊 处理笔记数据
    processNotesData(rawData, tab) {
        const processedData = {
            ...rawData,
            tabId: tab.id,
            webSocketUrl: tab.webSocketDebuggerUrl,
            dataSource: 'browser_websocket_scroll',
            extractionMethod: 'auto_scroll_collection'
        };

        return processedData;
    }
}

// 🧪 测试滚动采集
async function testScrollCollection() {
    const collector = new ScrollNotesCollector();
    
    try {
        const data = await collector.collectAllNotesWithScroll();
        
        console.log('\n📝 滚动采集结果:');
        console.log('=' * 60);
        console.log(`📊 总计: ${data.summary.totalNotes} 篇笔记`);
        console.log(`🖼️  总图片: ${data.summary.totalImages} 张`);
        console.log(`👍 总点赞: ${data.summary.totalLikes}`);
        console.log(`💖 总收藏: ${data.summary.totalCollects}`);
        console.log(`💬 总评论: ${data.summary.totalComments}`);
        console.log(`🔄 滚动次数: ${data.summary.scrollAttempts}\n`);
        
        // 显示前10篇笔记
        const displayNotes = data.notes.slice(0, 10);
        displayNotes.forEach((note, index) => {
            console.log(`📝 笔记 ${index + 1}:`);
            console.log(`   标题: ${note.title || '无标题'}`);
            console.log(`   图片数量: ${note.images.length}`);
            console.log(`   👍 点赞: ${note.interactions.likes || 0}`);
            console.log(`   💖 收藏: ${note.interactions.collects || 0}`);
            console.log(`   💬 评论: ${note.interactions.comments || 0}`);
            if (note.link) {
                console.log(`   🔗 链接: ${note.link}`);
            }
            console.log('');
        });
        
        if (data.notes.length > 10) {
            console.log(`... 还有 ${data.notes.length - 10} 篇笔记未显示`);
        }
        
        return data;
        
    } catch (error) {
        console.error('❌ 滚动采集失败:', error.message);
        console.log('💡 请确保19号浏览器已启动并打开了小红书页面');
    }
}

// 运行测试
if (require.main === module) {
    testScrollCollection().catch(console.error);
}

module.exports = { ScrollNotesCollector, testScrollCollection };
