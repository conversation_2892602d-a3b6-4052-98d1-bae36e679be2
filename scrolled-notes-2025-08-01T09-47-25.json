{"timestamp": "2025-08-01T09:47:25.265Z", "url": "https://www.xiaohongshu.com/user/profile/676964d20000000018014972", "title": "漫娴学姐 招暑假工版 - 小红书", "notes": [{"index": 1, "id": "note_0", "title": "有没有广州人可以给我说一下具体情况哇", "images": [{"src": "https://sns-webpic-qc.xhscdn.com/202508011742/97ae70780d194d4ac1c66339bbc5c5cf/spectrum/1040g0k031e0r9qq412005pr9cj962ibiaitk4g0!nc_n_webp_mw_1", "alt": ""}], "interactions": {"likes": 0, "collects": 0, "comments": 0}, "link": "https://www.xiaohongshu.com/explore/67b35cb40000000028034074"}, {"index": 2, "id": "note_1", "title": "有什么软件可以手机上兼织专米？ 求推荐！", "images": [{"src": "https://sns-webpic-qc.xhscdn.com/202508011742/58f7e591d59ed872f376a356191765dc/spectrum/1040g34o31dsvtjukgc0g5pr9cj962ibiqkq5670!nc_n_webp_mw_1", "alt": ""}], "interactions": {"likes": 0, "collects": 0, "comments": 0}, "link": "https://www.xiaohongshu.com/explore/67af69ee000000002a003a15"}, {"index": 3, "id": "note_2", "title": "现在还有兼织吗招学生的", "images": [{"src": "https://sns-webpic-qc.xhscdn.com/202508011742/d5c248c31dff8742b9bfb8a4d571100c/spectrum/1040g0k031drf17itg8005pr9cj962ibisfnfqk0!nc_n_webp_mw_1", "alt": ""}], "interactions": {"likes": 0, "collects": 0, "comments": 0}, "link": "https://www.xiaohongshu.com/explore/67add9c30000000029016cc2"}, {"index": 4, "id": "note_3", "title": "有没有宝子分享一下建议", "images": [{"src": "https://sns-webpic-qc.xhscdn.com/202508011742/00fa293395dc770bdc446de40c8816b2/spectrum/1040g34o31dragbj5080g5pr9cj962ibi95qqis8!nc_n_webp_mw_1", "alt": ""}], "interactions": {"likes": 0, "collects": 0, "comments": 0}, "link": "https://www.xiaohongshu.com/explore/67adb47000000000290080dc"}, {"index": 5, "id": "note_4", "title": "仅自己可见漫娴学姐 招暑假工版赞", "images": [{"src": "https://sns-webpic-qc.xhscdn.com/202508011742/07ee98722170e30d9024af8b23e91939/1040g2sg31dn50ns7gu6g5pr9cj962ibiog23ad8!nc_n_webp_mw_1", "alt": ""}], "interactions": {"likes": 0, "collects": 0, "comments": 0}, "link": "https://www.xiaohongshu.com/explore/67a96ed80000000018008f75"}], "summary": {"totalNotes": 5, "totalImages": 5, "pageHeight": 3726, "viewportHeight": 625}, "tabId": "FB0D0A8A7729718F666AC64225B41A5B", "dataSource": "scroll_collection", "extractionMethod": "simple_scroll_then_collect"}