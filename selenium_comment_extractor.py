#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Selenium评论提取器
使用Selenium直接控制比特浏览器提取所有评论
"""

import time
import json
import re
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class SeleniumCommentExtractor:
    def __init__(self):
        self.output_dir = './scraped_data'
        self.ensure_output_dir()
        
        self.config = {
            'target_comments': 1472,
            'max_scroll_attempts': 800,
            'scroll_delay': 0.8,
            'click_delay': 0.5,
            'wait_timeout': 10
        }
        
        self.driver = None
        
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def setup_driver(self):
        """设置Selenium WebDriver连接到比特浏览器"""
        print("🔗 设置Selenium连接到比特浏览器...")
        
        # 比特浏览器的Chrome选项
        chrome_options = Options()
        
        # 尝试连接到现有的比特浏览器实例
        # 比特浏览器通常在这些端口运行调试模式
        possible_ports = [9222, 9223, 9224, 9225, 55276, 54345]
        
        for port in possible_ports:
            try:
                print(f"   🔍 尝试连接端口 {port}...")
                chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
                
                # 创建WebDriver
                self.driver = webdriver.Chrome(options=chrome_options)
                print(f"   ✅ 成功连接到端口 {port}")
                return True
                
            except Exception as e:
                print(f"   ❌ 端口 {port} 连接失败: {str(e)}")
                continue
        
        # 如果无法连接到现有实例，尝试启动新的浏览器
        print("🚀 启动新的Chrome浏览器实例...")
        try:
            chrome_options = Options()
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ 成功启动新的浏览器实例")
            return True
            
        except Exception as e:
            print(f"❌ 启动浏览器失败: {str(e)}")
            return False
    
    def navigate_to_xiaohongshu(self):
        """导航到小红书页面"""
        print("🎯 检查当前页面...")
        
        current_url = self.driver.current_url
        print(f"当前URL: {current_url}")
        
        if 'xiaohongshu.com' in current_url or 'xhs' in current_url:
            print("✅ 已在小红书页面")
            return True
        
        # 如果不在小红书页面，提示用户手动导航
        print("💡 请手动导航到小红书评论页面")
        print("然后按回车继续...")
        input()
        
        return True
    
    def scroll_to_comments_section(self):
        """滚动到评论区域"""
        print("🎯 定位评论区域...")
        
        try:
            # 查找评论区域的多种方式
            comment_indicators = [
                "//span[contains(text(), '条评论')]",
                "//div[contains(text(), '评论')]",
                "//*[contains(@class, 'comment')]",
                "//*[contains(@class, 'Comment')]"
            ]
            
            for xpath in comment_indicators:
                try:
                    element = self.driver.find_element(By.XPATH, xpath)
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    print(f"✅ 找到评论区域: {xpath}")
                    time.sleep(2)
                    return True
                except NoSuchElementException:
                    continue
            
            # 如果没找到特定元素，滚动到页面中部
            print("💡 未找到特定评论区域，滚动到页面中部")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.6);")
            time.sleep(2)
            return True
            
        except Exception as e:
            print(f"❌ 定位评论区域失败: {str(e)}")
            return False
    
    def aggressive_scroll_load(self):
        """激进滚动加载所有评论"""
        print("🚀 开始激进滚动加载评论...")
        print(f"🎯 目标: {self.config['target_comments']} 条评论")
        
        current_comment_count = 0
        previous_comment_count = 0
        stable_count = 0
        total_scrolls = 0
        total_clicks = 0
        
        for i in range(self.config['max_scroll_attempts']):
            print(f"\n📜 激进滚动 {i + 1}/{self.config['max_scroll_attempts']}")
            
            # 统计当前评论数量
            current_comment_count = self.count_comments()
            print(f"   💬 当前评论数: {current_comment_count}")
            
            # 检查是否达到目标
            if current_comment_count >= self.config['target_comments'] * 0.95:
                print(f"🎉 接近目标！当前: {current_comment_count}/{self.config['target_comments']}")
                break
            
            # 检查进度
            if current_comment_count == previous_comment_count:
                stable_count += 1
                print(f"   ⏸️ 评论数稳定 {stable_count} 次")
            else:
                new_comments = current_comment_count - previous_comment_count
                print(f"   📈 新增评论: {new_comments} 条")
                stable_count = 0
                previous_comment_count = current_comment_count
            
            # 激进点击策略
            if stable_count >= 3:
                print("   💥 激进点击策略...")
                click_success = self.aggressive_click()
                if click_success:
                    total_clicks += 1
                    stable_count = 0
                    print(f"   ✅ 点击成功！总计: {total_clicks} 次")
            
            # 执行多样化滚动
            self.diversified_scroll(i)
            
            # 如果长时间无新内容，停止
            if stable_count >= 20:
                print("   ⏹️ 长时间无新内容，停止滚动")
                break
            
            # 动态等待时间
            wait_time = self.config['scroll_delay']
            if current_comment_count > previous_comment_count:
                wait_time *= 0.7  # 有新内容时加速
            elif stable_count >= 5:
                wait_time *= 1.3  # 无新内容时稍微减速
            
            time.sleep(wait_time)
            total_scrolls += 1
            
            # 每50次滚动输出进度
            if i % 50 == 0 and i > 0:
                progress = round((current_comment_count / self.config['target_comments']) * 100)
                print(f"\n📊 进度报告:")
                print(f"   📈 完成度: {progress}% ({current_comment_count}/{self.config['target_comments']})")
                print(f"   📜 总滚动次数: {total_scrolls}")
                print(f"   💥 总点击次数: {total_clicks}")
        
        print(f"\n✅ 激进滚动完成！")
        print(f"📊 最终统计:")
        print(f"   💬 最终评论数: {current_comment_count}")
        print(f"   📈 完成度: {round((current_comment_count / self.config['target_comments']) * 100)}%")
        print(f"   📜 总滚动次数: {total_scrolls}")
        print(f"   💥 总点击次数: {total_clicks}")
        
        return current_comment_count
    
    def diversified_scroll(self, scroll_index):
        """多样化滚动策略"""
        strategies = [
            # 策略1: 小步滚动
            lambda: self.driver.execute_script("window.scrollBy(0, 200 + Math.random() * 100);"),
            
            # 策略2: 中步滚动
            lambda: self.driver.execute_script("window.scrollBy(0, 400 + Math.random() * 200);"),
            
            # 策略3: 大步滚动
            lambda: self.driver.execute_script("window.scrollBy(0, 600 + Math.random() * 300);"),
            
            # 策略4: 滚动到底部
            lambda: self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);"),
            
            # 策略5: 滚动到最后评论
            lambda: self.driver.execute_script("""
                const comments = document.querySelectorAll('[class*="comment"], [class*="Comment"]');
                if (comments.length > 0) {
                    const lastComment = comments[comments.length - 1];
                    lastComment.scrollIntoView({behavior: 'smooth'});
                }
            """),
            
            # 策略6: 向上再向下
            lambda: self.driver.execute_script("""
                window.scrollBy(0, -300);
                setTimeout(() => window.scrollBy(0, 600), 200);
            """)
        ]
        
        # 根据滚动次数选择策略
        if scroll_index < 100:
            strategy_index = scroll_index % 3  # 前100次使用温和策略
        elif scroll_index < 300:
            strategy_index = scroll_index % 5  # 中期使用多样化策略
        else:
            strategy_index = scroll_index % len(strategies)  # 后期使用所有策略
        
        strategies[strategy_index]()
    
    def aggressive_click(self):
        """激进点击策略"""
        try:
            click_results = []
            
            # 激进文字点击
            aggressive_texts = [
                '加载更多', '展开更多', '查看更多', '显示更多', '更多评论',
                '展开', '更多', '加载', '查看全部', '展开全部'
            ]
            
            for text in aggressive_texts:
                try:
                    elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{text}')]")
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            try:
                                element.click()
                                click_results.append(f"文字点击: {text}")
                                time.sleep(0.2)
                            except:
                                # 如果普通点击失败，尝试JavaScript点击
                                try:
                                    self.driver.execute_script("arguments[0].click();", element)
                                    click_results.append(f"JS点击: {text}")
                                except:
                                    pass
                except:
                    continue
            
            # 激进按钮点击
            button_selectors = [
                "button", "[role='button']", ".btn", "[class*='button']",
                "[class*='load']", "[class*='more']", "[class*='expand']"
            ]
            
            for selector in button_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements[:5]:  # 限制数量避免过度点击
                        if element.is_displayed() and element.is_enabled():
                            element_text = element.text.lower()
                            if any(keyword in element_text for keyword in ['more', '更多', '展开', '加载']):
                                try:
                                    element.click()
                                    click_results.append(f"按钮点击: {element_text[:20]}")
                                    time.sleep(0.2)
                                except:
                                    pass
                except:
                    continue
            
            if click_results:
                print(f"   💥 激进点击成功: {', '.join(click_results[:3])}")
                return True
            
            return False
            
        except Exception as e:
            print(f"   ❌ 激进点击出错: {str(e)}")
            return False
    
    def count_comments(self):
        """统计评论数量"""
        try:
            page_text = self.driver.execute_script("return document.body.textContent;")
            
            # 多种统计方法
            methods = [
                # 方法1: 时间格式
                lambda: len(re.findall(r'\d{2}-\d{2}', page_text)),
                
                # 方法2: 回复数
                lambda: len(re.findall(r'\d+回复', page_text)),
                
                # 方法3: DOM元素
                lambda: len(self.driver.find_elements(By.CSS_SELECTOR, '[class*="comment"], [class*="Comment"]')),
                
                # 方法4: 关键词
                lambda: len(re.findall(r'求带|宝子|学姐|兼职|聊天员', page_text)) // 2
            ]
            
            counts = []
            for method in methods:
                try:
                    count = method()
                    counts.append(count)
                except:
                    counts.append(0)
            
            return max(counts)
            
        except Exception as e:
            print(f"❌ 统计评论数量失败: {str(e)}")
            return 0

    def extract_all_comments(self):
        """提取所有评论"""
        print("🧠 开始提取所有评论...")

        # 获取页面文本
        page_text = self.driver.execute_script("return document.body.textContent;")
        print(f"📄 页面文本长度: {len(page_text)}")

        # 提取基本信息
        note_info = self.extract_note_info(page_text)

        # 解析评论
        comments = self.parse_comments(page_text)

        return {
            'noteInfo': note_info,
            'comments': comments,
            'extractStats': {
                'totalTextLength': len(page_text),
                'successfulExtractions': len(comments),
                'extractionMethods': ['selenium']
            },
            'extractTime': datetime.now().isoformat()
        }

    def extract_note_info(self, page_text):
        """提取笔记基本信息"""
        info = {
            'id': '',
            'title': '',
            'author': '',
            'totalCommentCount': 0
        }

        try:
            # 提取笔记ID
            current_url = self.driver.current_url
            url_match = re.search(r'explore/([a-f0-9]+)', current_url)
            if url_match:
                info['id'] = url_match.group(1)

            # 提取标题
            try:
                title = self.driver.title.replace(' - 小红书', '')
                info['title'] = title
            except:
                pass

            # 提取评论总数
            comment_count_match = re.search(r'(\d+)\s*条评论', page_text)
            if comment_count_match:
                info['totalCommentCount'] = int(comment_count_match.group(1))

            # 提取作者
            if '漫娴学姐' in page_text:
                info['author'] = '漫娴学姐 招暑假工版'

        except Exception as e:
            print(f"❌ 提取笔记信息失败: {str(e)}")

        return info

    def parse_comments(self, page_text):
        """解析评论"""
        print("🔍 开始解析评论...")

        all_comments = []

        # 使用多种解析策略
        strategies = [
            ('时间模式', self.parse_by_time),
            ('关键词模式', self.parse_by_keywords),
            ('用户模式', self.parse_by_users),
            ('结构模式', self.parse_by_structure)
        ]

        for strategy_name, strategy_method in strategies:
            try:
                print(f"🔍 执行{strategy_name}解析...")
                strategy_comments = strategy_method(page_text)
                print(f"   📝 {strategy_name}提取到 {len(strategy_comments)} 条评论")
                all_comments.extend(strategy_comments)
            except Exception as e:
                print(f"   ❌ {strategy_name}解析失败: {str(e)}")

        # 去重和清理
        unique_comments = self.deduplicate_comments(all_comments)

        print(f"✅ 解析完成，最终提取到 {len(unique_comments)} 条评论")
        return unique_comments

    def parse_by_time(self, page_text):
        """按时间解析"""
        comments = []

        # 时间模式
        time_pattern = r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)'
        time_matches = list(re.finditer(time_pattern, page_text))

        for i, match in enumerate(time_matches):
            time_str = match.group(1)
            time_index = match.start()

            # 提取时间前后的内容
            start_index = max(0, time_index - 200)
            end_index = time_matches[i + 1].start() if i + 1 < len(time_matches) else time_index + 1000
            end_index = min(len(page_text), end_index)

            comment_text = page_text[start_index:end_index].strip()

            if 20 < len(comment_text) < 2000:
                comment = self.create_comment(comment_text, len(comments) + 1, 'time')
                if comment:
                    comments.append(comment)

        return comments

    def parse_by_keywords(self, page_text):
        """按关键词解析"""
        comments = []

        keywords = [
            '求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯',
            'Carina', '广州', '工作', '赚钱', '日入', '月入'
        ]

        for keyword in keywords:
            for match in re.finditer(keyword, page_text):
                start_index = max(0, match.start() - 150)
                end_index = min(len(page_text), match.start() + 800)

                comment_text = page_text[start_index:end_index].strip()

                if 15 < len(comment_text) < 1500:
                    comment = self.create_comment(comment_text, len(comments) + 1, 'keyword')
                    if comment:
                        comments.append(comment)

        return comments

    def parse_by_users(self, page_text):
        """按用户解析"""
        comments = []

        user_patterns = [
            r'([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(\d{2}-\d{2})',
            r'小红薯([A-F0-9]{8,})',
            r'([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带'
        ]

        for pattern in user_patterns:
            for match in re.finditer(pattern, page_text):
                start_index = max(0, match.start() - 100)
                end_index = min(len(page_text), match.start() + 600)

                comment_text = page_text[start_index:end_index].strip()

                if 10 < len(comment_text) < 1200:
                    comment = self.create_comment(comment_text, len(comments) + 1, 'user')
                    if comment:
                        comments.append(comment)

        return comments

    def parse_by_structure(self, page_text):
        """按结构解析"""
        comments = []
        lines = [line.strip() for line in page_text.split('\n') if len(line.strip()) > 3]

        current_comment = None
        comment_index = 0

        for line in lines:
            if self.should_skip_line(line):
                continue

            if self.is_comment_start(line):
                if current_comment and self.is_valid_comment(current_comment['content']):
                    comment = self.create_comment(current_comment['content'], comment_index + 1, 'structure')
                    if comment:
                        comments.append(comment)
                        comment_index += 1

                current_comment = {'content': line}
            elif current_comment and len(line) > 2:
                current_comment['content'] += ' ' + line

                if len(current_comment['content']) > 1800:
                    comment = self.create_comment(current_comment['content'], comment_index + 1, 'structure')
                    if comment:
                        comments.append(comment)
                        comment_index += 1
                    current_comment = None

        if current_comment and self.is_valid_comment(current_comment['content']):
            comment = self.create_comment(current_comment['content'], comment_index + 1, 'structure')
            if comment:
                comments.append(comment)

        return comments

    def create_comment(self, text, comment_id, source):
        """创建评论对象"""
        comment = {
            'id': comment_id,
            'userId': '',
            'username': '',
            'content': '',
            'time': '',
            'likes': 0,
            'replyCount': 0,
            'isAuthor': '作者' in text,
            'isPinned': '置顶' in text,
            'extractedInfo': {
                'source': source,
                'originalLength': len(text)
            }
        }

        # 提取时间
        time_match = re.search(r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)', text)
        if time_match:
            comment['time'] = time_match.group(1)

        # 提取用户名
        user_patterns = [
            r'^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}',
            r'([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带',
            r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})'
        ]

        for pattern in user_patterns:
            match = re.search(pattern, text)
            if match:
                comment['username'] = match.group(1).strip()
                break

        # 清理内容
        content = text
        content = re.sub(r'\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前', '', content)
        content = re.sub(r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+', '', content)
        content = re.sub(r'作者|置顶评论|回复|赞|仅自己可见', '', content)
        if comment['username']:
            content = content.replace(comment['username'], '')
        content = re.sub(r'\s+', ' ', content).strip()

        comment['content'] = content

        # 提取数字信息
        like_match = re.search(r'(\d+)\s*赞', text)
        if like_match:
            comment['likes'] = int(like_match.group(1))

        reply_match = re.search(r'(\d+)\s*回复', text)
        if reply_match:
            comment['replyCount'] = int(reply_match.group(1))

        # 提取用户ID
        user_id_match = re.search(r'小红薯([A-F0-9]{8,})', text)
        if user_id_match:
            comment['userId'] = user_id_match.group(1)

        return comment if len(comment['content']) >= 5 else None

    def should_skip_line(self, line):
        """判断是否应该跳过该行"""
        skip_patterns = [
            '加载中', '你还没有', '温馨提示', '广告屏蔽', '我知道了',
            '沪ICP备', '营业执照', '公网安备', '增值电信', '医疗器械',
            'window.', 'function', 'console.', 'document.'
        ]

        return (any(pattern in line for pattern in skip_patterns) or
                len(line) < 3 or
                re.match(r'^[0-9\s\-:\.]+$', line))

    def is_comment_start(self, line):
        """判断是否是评论开始"""
        return ('作者' in line or
                '置顶' in line or
                re.search(r'\d{2}-\d{2}', line) or
                re.search(r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]', line) or
                any(keyword in line for keyword in ['求带', '宝子', '学姐', '兼职']))

    def is_valid_comment(self, content):
        """判断是否是有效评论"""
        return content and 10 <= len(content) <= 2000

    def deduplicate_comments(self, comments):
        """去重评论"""
        unique = []
        seen = set()

        for comment in comments:
            key = comment['content'][:30]
            if key not in seen:
                seen.add(key)
                unique.append(comment)

        # 重新分配ID
        for i, comment in enumerate(unique):
            comment['id'] = i + 1

        return unique

    def save_to_file(self, data, filename):
        """保存数据到文件"""
        filepath = os.path.join(self.output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"💾 数据已保存到: {filepath}")
        return filepath

    def run(self):
        """主运行方法"""
        try:
            print("🎯 启动Selenium评论提取器...")
            print(f"🎯 目标: 获取所有 {self.config['target_comments']} 条评论")
            print("🛡️ 策略: Selenium控制 + 激进滚动 + 多重解析")

            # 设置WebDriver
            if not self.setup_driver():
                print("❌ 无法设置WebDriver")
                return

            # 导航到小红书页面
            if not self.navigate_to_xiaohongshu():
                print("❌ 无法导航到小红书页面")
                return

            # 滚动到评论区域
            self.scroll_to_comments_section()

            # 激进滚动加载
            loaded_comment_count = self.aggressive_scroll_load()

            # 提取所有评论
            result = self.extract_all_comments()

            # 保存数据
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"selenium_comments_{result['noteInfo']['id']}_{timestamp}.json"
            self.save_to_file(result, filename)

            # 输出结果
            print("\n🎉 Selenium提取完成！")
            print("📊 最终统计:")
            print(f"   📝 笔记ID: {result['noteInfo']['id']}")
            print(f"   📝 笔记标题: {result['noteInfo']['title'] or '未知'}")
            print(f"   👤 笔记作者: {result['noteInfo']['author'] or '未知'}")
            print(f"   🎯 目标评论数: {result['noteInfo']['totalCommentCount']}")
            print(f"   💬 实际提取数: {len(result['comments'])}")

            if result['noteInfo']['totalCommentCount'] > 0:
                completion_rate = round((len(result['comments']) / result['noteInfo']['totalCommentCount']) * 100)
                print(f"   📈 完成度: {completion_rate}%")

            print(f"   📄 页面文本长度: {result['extractStats']['totalTextLength']}")
            print(f"   📁 保存文件: {filename}")

            if result['comments']:
                print("\n👥 评论详细预览:")
                for i, comment in enumerate(result['comments'][:15]):
                    print(f"\n   {i + 1}. 评论ID: {comment['id']}")
                    print(f"      👤 用户: {comment['username'] or '未知'}")
                    print(f"      🆔 用户ID: {comment['userId'] or '未提取到'}")
                    print(f"      ⏰ 时间: {comment['time'] or '未知'}")
                    content_preview = comment['content'][:100]
                    if len(comment['content']) > 100:
                        content_preview += '...'
                    print(f"      💬 内容: {content_preview}")
                    print(f"      👍 点赞: {comment['likes']} | 💬 回复: {comment['replyCount']}")
                    print(f"      📊 来源: {comment['extractedInfo']['source']}")

                # 质量统计
                with_username = len([c for c in result['comments'] if c['username']])
                with_user_id = len([c for c in result['comments'] if c['userId']])
                with_time = len([c for c in result['comments'] if c['time']])
                with_likes = len([c for c in result['comments'] if c['likes'] > 0])
                with_replies = len([c for c in result['comments'] if c['replyCount'] > 0])

                total_comments = len(result['comments'])
                print("\n📈 数据质量统计:")
                print(f"   👤 有用户名: {with_username}/{total_comments} ({round(with_username/total_comments*100)}%)")
                print(f"   🆔 有用户ID: {with_user_id}/{total_comments} ({round(with_user_id/total_comments*100)}%)")
                print(f"   ⏰ 有时间: {with_time}/{total_comments} ({round(with_time/total_comments*100)}%)")
                print(f"   👍 有点赞数: {with_likes}/{total_comments} ({round(with_likes/total_comments*100)}%)")
                print(f"   💬 有回复数: {with_replies}/{total_comments} ({round(with_replies/total_comments*100)}%)")

                # 兼职信息分析
                job_keywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱', '月入']
                job_comments = [c for c in result['comments']
                              if any(keyword in c['content'] for keyword in job_keywords)]

                if job_comments:
                    job_percentage = round((len(job_comments) / total_comments) * 100)
                    print(f"\n💼 兼职相关评论: {len(job_comments)}/{total_comments} ({job_percentage}%)")

                    print("\n💼 兼职评论示例:")
                    for i, comment in enumerate(job_comments[:10]):
                        username = comment['username'] or '匿名'
                        time_str = comment['time'] or '未知时间'
                        content_preview = comment['content'][:80]
                        if len(comment['content']) > 80:
                            content_preview += '...'
                        print(f"   {i + 1}. {username} ({time_str}): {content_preview}")

                # 成功度评估
                if result['noteInfo']['totalCommentCount'] > 0:
                    completion_rate = len(result['comments']) / result['noteInfo']['totalCommentCount']
                    if completion_rate >= 0.8:
                        print("\n🎉 优秀！Selenium已提取到大部分评论数据！")
                    elif completion_rate >= 0.5:
                        print("\n👍 良好！Selenium已提取到一半以上的评论数据！")
                    elif completion_rate >= 0.3:
                        print("\n📈 进步！Selenium提取效果显著！")
                    else:
                        print("\n💡 提示：可能需要更长时间的滚动或手动干预")

            print("\n✅ Selenium提取任务完成！")
            print(f"📁 数据文件: {filename}")

        except Exception as e:
            print(f"❌ Selenium提取失败: {str(e)}")
        finally:
            if self.driver:
                print("🔄 关闭浏览器...")
                self.driver.quit()

def main():
    """主函数"""
    extractor = SeleniumCommentExtractor()
    extractor.run()

if __name__ == "__main__":
    main()
