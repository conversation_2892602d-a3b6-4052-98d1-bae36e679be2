// ===== 🚀 黑默科技桌面端后端服务器 【主服务器】 =====
// 📝 功能说明：这个文件是应用的核心后端服务器，为桌面端提供 API 服务和静态文件服务
// 🎯 主要职责：处理HTTP请求、WebSocket通信、路由分发、静态文件服务
// 🔧 技术栈：Express.js + Socket.IO + CORS + Body-Parser

// ===== 📦 导入必要的 Node.js 模块 =====
const express = require('express');        // 🌐 Web应用框架 - 处理HTTP请求和路由
const http = require('http');              // 🔗 HTTP服务器 - 创建基础HTTP服务
const socketIo = require('socket.io');     // ⚡ WebSocket实时通信 - 双向数据传输
const cors = require('cors');              // 🔓 跨域资源共享中间件 - 允许前端访问
const bodyParser = require('body-parser'); // 📋 请求体解析中间件 - 解析POST数据
const path = require('path');              // 📁 路径处理模块 - 文件路径操作

// ===== 🏗️ 服务器初始化配置 【核心实例创建】 =====
const app = express();                    // 🎯 创建Express应用实例 - 主要的Web应用对象
const server = http.createServer(app);    // 🌐 创建HTTP服务器 - 基于Express应用的HTTP服务
const io = socketIo(server, {             // ⚡ 创建Socket.IO实例 - 实时双向通信服务
    cors: {
        origin: "*",                      // 🔓 允许所有来源访问（桌面端应用跨域）
        methods: ["GET", "POST"]          // 📝 允许的HTTP方法 - 读取和提交数据
    }
});

// ===== 🔧 中间件配置 【请求处理管道】 =====
// 📝 说明：中间件是处理HTTP请求的函数，按照注册顺序依次执行，形成处理管道
app.use(cors());                                     // 🔓 启用跨域资源共享 - 允许桌面端和Web端访问API
app.use(bodyParser.json());                         // 📋 解析JSON格式请求体 - 处理application/json数据
app.use(bodyParser.urlencoded({ extended: true })); // 📝 解析URL编码请求体 - 处理表单提交数据
app.use(express.static('public'));                  // 📁 提供静态文件服务 - 托管HTML、CSS、JS、图片等资源

// ===== 健康检查端点 =====
// 用于检测服务器是否正常运行
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy', // 服务器状态
        timestamp: new Date().toISOString(), // 当前时间戳
        uptime: process.uptime(), // 服务器运行时间（秒）
        version: process.env.npm_package_version || '1.0.0' // 应用版本号
    });
});

// ===== 桌面端页面路由 =====
// 定义桌面端应用的页面访问路径
app.get('/', (req, res) => {
    // 根路径：返回桌面端主页面
    res.sendFile(path.join(__dirname, 'public', 'premium-index.html'));
});

app.get('/premium', (req, res) => {
    // Premium 路径：也返回桌面端主页面（兼容性）
    res.sendFile(path.join(__dirname, 'public', 'premium-index.html'));
});

// ===== 桌面端 API 路由 =====
// 将不同功能的 API 请求分发到对应的路由文件处理
app.use('/api/overview', require('./routes/overview')); // 数据总览 API
app.use('/api/monitor', require('./routes/monitor')); // 监控相关 API
app.use('/api/chat', require('./routes/chat')); // 聊天功能 API
app.use('/api/accounts', require('./routes/accounts')); // 账号管理 API
app.use('/api/messages', require('./routes/messages')); // 消息管理 API
app.use('/api/records', require('./routes/records')); // 记录管理 API
app.use('/api/xiaohongshu', require('./routes/xiaohongshu')); // 小红书管理 API

// Socket.IO 连接处理
io.on('connection', (socket) => {
    console.log('用户连接:', socket.id);
    
    // 加入聊天室
    socket.on('join-chat', (data) => {
        socket.join(data.room);
        console.log(`用户 ${socket.id} 加入聊天室: ${data.room}`);
    });
    
    // 处理聊天消息
    socket.on('chat-message', (data) => {
        io.to(data.room).emit('chat-message', {
            id: Date.now(),
            user: data.user,
            message: data.message,
            timestamp: new Date().toISOString()
        });
    });
    
    // 处理断开连接
    socket.on('disconnect', () => {
        console.log('用户断开连接:', socket.id);
    });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`🚀 黑默科技平台启动成功！`);
    console.log(`📡 服务器运行在: http://localhost:${PORT}`);
    console.log(`⚡ Socket.IO 已启用`);
});
