#!/usr/bin/env node

/**
 * 🖱️ 简化版点击采集器
 * 点击单篇笔记并采集详细数据
 */

const axios = require('axios');
const WebSocket = require('ws');

class SimpleClickCollector {
    constructor() {
        this.debugPort = null;
    }

    // 🖱️ 点击并采集单篇笔记
    async clickAndCollectSingle(noteIndex = 0) {
        console.log(`🖱️ 开始点击第 ${noteIndex + 1} 篇笔记并采集详细数据...\n`);

        try {
            // 1. 获取调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 目标页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 点击笔记并采集
            const noteData = await this.performClickAndCollect(tab, noteIndex);
            
            return noteData;

        } catch (error) {
            console.error('❌ 点击采集失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取调试端口
    async getCurrentDebugPort() {
        try {
            const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 10000
            });

            if (response.data.success && response.data.data && response.data.data.result) {
                const httpInfo = response.data.data.result.http;
                if (httpInfo) {
                    this.debugPort = httpInfo.split(':')[1];
                    console.log(`✅ 调试端口: ${this.debugPort}`);
                    return;
                }
            }

            const commonPorts = [63524, 51859, 58222, 9222];
            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到端口: ${port}`);
                    return;
                } catch (error) {
                    // 继续尝试
                }
            }

            throw new Error('未找到可用端口');
        } catch (error) {
            throw new Error(`获取端口失败: ${error.message}`);
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
            timeout: 5000
        });

        return response.data.find(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.url.includes('creator.xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );
    }

    // 🖱️ 执行点击和采集
    async performClickAndCollect(tab, noteIndex) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;
            let originalUrl = '';

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    // 先记录原始URL，然后点击
                    const clickScript = `
                        (function() {
                            try {
                                const originalUrl = window.location.href;
                                const noteLinks = document.querySelectorAll('a[href*="/explore/"]');
                                
                                if (noteLinks[${noteIndex}]) {
                                    console.log('找到笔记链接:', noteLinks[${noteIndex}].href);
                                    noteLinks[${noteIndex}].click();
                                    
                                    return { 
                                        clicked: true, 
                                        href: noteLinks[${noteIndex}].href,
                                        originalUrl: originalUrl
                                    };
                                } else {
                                    return { 
                                        clicked: false, 
                                        error: '未找到指定笔记',
                                        totalLinks: noteLinks.length
                                    };
                                }
                            } catch (error) {
                                return { clicked: false, error: error.message };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: clickScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.clicked) {
                            console.log('🖱️ 点击成功，等待页面加载...');
                            originalUrl = result.originalUrl;
                            
                            // 等待页面加载后采集数据
                            setTimeout(() => {
                                this.collectDetailedData(ws, requestId++, originalUrl, resolve, reject);
                            }, 5000);
                            
                        } else {
                            console.log('❌ 点击失败:', result.error);
                            console.log(`📊 找到 ${result.totalLinks || 0} 个笔记链接`);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理消息失败:', error.message);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('点击采集超时'));
            }, 60000);
        });
    }

    // 📊 采集详细数据
    collectDetailedData(ws, requestId, originalUrl, resolve, reject) {
        console.log('📊 开始采集详细数据...');
        
        const collectScript = `
            (function() {
                try {
                    const noteData = {
                        url: window.location.href,
                        originalUrl: '${originalUrl}',
                        title: '',
                        content: '',
                        author: '',
                        publishTime: '',
                        likes: 0,
                        collects: 0,
                        comments: 0,
                        views: 0,
                        shares: 0,
                        tags: [],
                        images: [],
                        timestamp: new Date().toISOString()
                    };

                    // 提取标题 - 多种策略
                    const titleSelectors = [
                        'h1', 'h2', '.note-title', '[class*="title"]', '.content-title',
                        '[data-testid="note-title"]', '.note-content h1', '.note-content h2'
                    ];
                    
                    for (const selector of titleSelectors) {
                        const titleEl = document.querySelector(selector);
                        if (titleEl && titleEl.textContent.trim()) {
                            noteData.title = titleEl.textContent.trim();
                            break;
                        }
                    }

                    // 如果没找到标题，从页面标题中提取
                    if (!noteData.title && document.title) {
                        const pageTitle = document.title.replace(' - 小红书', '').trim();
                        if (pageTitle.length > 0) {
                            noteData.title = pageTitle;
                        }
                    }

                    // 提取内容
                    const contentSelectors = [
                        '.note-content', '.content', '[class*="content"]', '.desc',
                        '[data-testid="note-content"]', '.note-text', '.post-content'
                    ];
                    
                    for (const selector of contentSelectors) {
                        const contentEl = document.querySelector(selector);
                        if (contentEl && contentEl.textContent.trim()) {
                            noteData.content = contentEl.textContent.trim();
                            break;
                        }
                    }

                    // 提取作者
                    const authorSelectors = [
                        '.author', '.username', '[class*="author"]', '[class*="user"]',
                        '.user-name', '.nickname', '[data-testid="author"]'
                    ];
                    
                    for (const selector of authorSelectors) {
                        const authorEl = document.querySelector(selector);
                        if (authorEl && authorEl.textContent.trim()) {
                            noteData.author = authorEl.textContent.trim();
                            break;
                        }
                    }

                    // 精确提取互动数据
                    const interactionElements = document.querySelectorAll('*');
                    interactionElements.forEach(el => {
                        const text = el.textContent.trim();
                        
                        // 匹配纯数字或带单位的数字
                        const numberMatch = text.match(/^(\\d+(?:\\.\\d+)?)(万|k|K)?$/);
                        if (numberMatch) {
                            let number = parseFloat(numberMatch[1]);
                            const unit = numberMatch[2];
                            
                            if (unit === '万') {
                                number *= 10000;
                            } else if (unit === 'k' || unit === 'K') {
                                number *= 1000;
                            }
                            
                            const parent = el.parentElement;
                            const context = parent ? parent.textContent.toLowerCase() : '';
                            const className = el.className.toLowerCase();
                            
                            // 根据上下文判断数据类型
                            if (context.includes('赞') || context.includes('❤️') || context.includes('👍') || 
                                className.includes('like') || className.includes('heart')) {
                                noteData.likes = Math.max(noteData.likes, Math.round(number));
                            } else if (context.includes('收藏') || context.includes('💖') || 
                                      className.includes('collect') || className.includes('favorite')) {
                                noteData.collects = Math.max(noteData.collects, Math.round(number));
                            } else if (context.includes('评论') || context.includes('💬') || 
                                      className.includes('comment')) {
                                noteData.comments = Math.max(noteData.comments, Math.round(number));
                            } else if (context.includes('浏览') || context.includes('👀') || 
                                      className.includes('view')) {
                                noteData.views = Math.max(noteData.views, Math.round(number));
                            } else if (context.includes('分享') || context.includes('转发') || 
                                      className.includes('share')) {
                                noteData.shares = Math.max(noteData.shares, Math.round(number));
                            }
                        }
                    });

                    // 提取图片
                    const images = document.querySelectorAll('img');
                    images.forEach(img => {
                        if (img.src && img.src.includes('xhscdn.com') && 
                            !img.src.includes('avatar') && !img.src.includes('icon') &&
                            img.naturalWidth > 100) { // 过滤小图标
                            noteData.images.push({
                                src: img.src,
                                alt: img.alt || '',
                                width: img.naturalWidth || img.width || 0,
                                height: img.naturalHeight || img.height || 0
                            });
                        }
                    });

                    // 提取标签
                    const tagSelectors = [
                        '[class*="tag"]', '.hashtag', '[class*="topic"]', '.tag',
                        '[data-testid="tag"]', '.note-tag'
                    ];
                    
                    tagSelectors.forEach(selector => {
                        const tagElements = document.querySelectorAll(selector);
                        tagElements.forEach(tagEl => {
                            const tagText = tagEl.textContent.trim();
                            if (tagText && tagText.length > 0 && tagText.length < 50 && 
                                !noteData.tags.includes(tagText)) {
                                noteData.tags.push(tagText);
                            }
                        });
                    });

                    // 提取发布时间
                    const timeElements = document.querySelectorAll('*');
                    timeElements.forEach(el => {
                        const text = el.textContent.trim();
                        if (text.match(/\\d{4}-\\d{2}-\\d{2}|\\d{4}\\/\\d{2}\\/\\d{2}|\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天/)) {
                            if (!noteData.publishTime || text.length > noteData.publishTime.length) {
                                noteData.publishTime = text;
                            }
                        }
                    });

                    return { success: true, noteData: noteData };
                    
                } catch (error) {
                    return { 
                        success: false,
                        error: error.message,
                        url: window.location.href 
                    };
                }
            })();
        `;

        ws.send(JSON.stringify({
            id: requestId,
            method: 'Runtime.evaluate',
            params: {
                expression: collectScript,
                returnByValue: true
            }
        }));

        // 监听采集结果
        const messageHandler = (data) => {
            try {
                const message = JSON.parse(data);
                if (message.result && message.result.result && message.result.result.value) {
                    const result = message.result.result.value;
                    
                    if (result.success) {
                        console.log('✅ 详细数据采集成功');
                        
                        // 返回原页面
                        setTimeout(() => {
                            ws.send(JSON.stringify({
                                id: requestId + 1,
                                method: 'Runtime.evaluate',
                                params: {
                                    expression: 'window.history.back(); return {goBack: true};',
                                    returnByValue: true
                                }
                            }));
                            
                            setTimeout(() => {
                                ws.close();
                                resolve(result.noteData);
                            }, 2000);
                        }, 1000);
                        
                    } else {
                        console.log('❌ 数据采集失败:', result.error);
                        ws.close();
                        reject(new Error(result.error));
                    }
                }
            } catch (error) {
                console.error('❌ 处理采集结果失败:', error.message);
            }
        };

        ws.on('message', messageHandler);
    }
}

// 🧪 测试单篇笔记点击采集
async function testSingleClickCollection(noteIndex = 0) {
    const collector = new SimpleClickCollector();
    
    try {
        const noteData = await collector.clickAndCollectSingle(noteIndex);
        
        console.log('\n🖱️ 点击采集结果:');
        console.log('=' * 60);
        console.log(`📝 标题: ${noteData.title || '无标题'}`);
        console.log(`👤 作者: ${noteData.author || '未知'}`);
        console.log(`📄 内容: ${noteData.content ? noteData.content.substring(0, 200) + '...' : '无内容'}`);
        console.log(`👍 点赞: ${noteData.likes}`);
        console.log(`💖 收藏: ${noteData.collects}`);
        console.log(`💬 评论: ${noteData.comments}`);
        console.log(`👀 浏览: ${noteData.views}`);
        console.log(`📤 分享: ${noteData.shares}`);
        console.log(`🖼️  图片数量: ${noteData.images.length}`);
        console.log(`🏷️  标签数量: ${noteData.tags.length}`);
        if (noteData.publishTime) {
            console.log(`📅 发布时间: ${noteData.publishTime}`);
        }
        console.log(`🔗 笔记链接: ${noteData.url}`);
        
        if (noteData.tags.length > 0) {
            console.log(`🏷️  标签: ${noteData.tags.join(', ')}`);
        }
        
        return noteData;
        
    } catch (error) {
        console.error('❌ 单篇点击采集失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    const noteIndex = process.argv[2] ? parseInt(process.argv[2]) : 0;
    console.log(`🎯 准备采集第 ${noteIndex + 1} 篇笔记...`);
    testSingleClickCollection(noteIndex).catch(console.error);
}

module.exports = { SimpleClickCollector, testSingleClickCollection };
