#!/usr/bin/env node

/**
 * 🔍 简单评论提取器
 * 基于页面结构分析结果的直接提取
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class SimpleCommentsExtractor {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    async connectToBrowser() {
        try {
            console.log('🔗 连接到比特浏览器...');
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${this.debugPort}`,
                defaultViewport: null
            });
            
            const pages = await browser.pages();
            const page = pages.find(p => p.url().includes('xiaohongshu.com/explore/'));
            
            if (!page) {
                throw new Error('未找到小红书笔记页面');
            }
            
            console.log('✅ 找到小红书笔记页面');
            console.log(`📄 当前URL: ${page.url()}`);
            
            return { browser, page };
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    async extractComments(page) {
        console.log('💬 开始提取评论...');
        
        const commentsData = await page.evaluate(() => {
            const results = {
                noteTitle: '',
                noteId: '',
                totalCommentCount: 0,
                comments: [],
                extractTime: new Date().toISOString()
            };
            
            try {
                // 提取笔记标题
                const titleElement = document.querySelector('h1, .title, [class*="title"]');
                if (titleElement) {
                    results.noteTitle = titleElement.textContent.trim();
                }
                
                // 从URL提取笔记ID
                const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
                if (urlMatch) {
                    results.noteId = urlMatch[1];
                }
                
                // 提取评论总数
                const commentCountText = document.body.textContent;
                const countMatch = commentCountText.match(/共\s*(\d+)\s*条评论/);
                if (countMatch) {
                    results.totalCommentCount = parseInt(countMatch[1]);
                }
                
                // 直接从页面文本中提取评论内容
                const pageText = document.body.textContent;
                
                // 查找评论区域的文本
                const commentSectionMatch = pageText.match(/共\s*\d+\s*条评论([\s\S]*?)(?:回到顶部|发现|发布|$)/);
                if (commentSectionMatch) {
                    const commentText = commentSectionMatch[1];
                    
                    // 简单的评论分割 - 基于常见的评论模式
                    const lines = commentText.split('\n').filter(line => line.trim().length > 0);
                    
                    let currentComment = null;
                    let commentIndex = 0;
                    
                    for (let i = 0; i < lines.length; i++) {
                        const line = lines[i].trim();
                        
                        // 跳过明显的非评论内容
                        if (line.includes('加载中') || 
                            line.includes('你还没有') || 
                            line.includes('温馨提示') ||
                            line.includes('广告屏蔽') ||
                            line.length < 3) {
                            continue;
                        }
                        
                        // 检查是否是新评论的开始（包含用户名模式）
                        if (line.includes('作者') || 
                            line.match(/^\d{2}-\d{2}/) || 
                            line.includes('回复') ||
                            line.includes('置顶评论')) {
                            
                            // 保存前一个评论
                            if (currentComment && currentComment.content.length > 5) {
                                results.comments.push(currentComment);
                            }
                            
                            // 开始新评论
                            currentComment = {
                                id: ++commentIndex,
                                user: '',
                                content: line,
                                time: '',
                                likes: '',
                                replies: [],
                                rawText: line
                            };
                            
                            // 尝试提取时间
                            const timeMatch = line.match(/(\d{2}-\d{2})/);
                            if (timeMatch) {
                                currentComment.time = timeMatch[1];
                            }
                            
                        } else if (currentComment && line.length > 3) {
                            // 继续当前评论的内容
                            currentComment.content += ' ' + line;
                            currentComment.rawText += ' ' + line;
                        }
                    }
                    
                    // 保存最后一个评论
                    if (currentComment && currentComment.content.length > 5) {
                        results.comments.push(currentComment);
                    }
                }
                
                console.log(`提取到 ${results.comments.length} 条评论`);
                
            } catch (error) {
                console.error('提取评论时出错:', error);
                results.error = error.message;
            }
            
            return results;
        });
        
        return commentsData;
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        try {
            console.log('🚀 启动简单评论提取器...');
            
            const { browser, page } = await this.connectToBrowser();
            
            const commentsData = await this.extractComments(page);
            
            // 生成文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `simple_comments_${commentsData.noteId}_${timestamp}.json`;
            
            // 保存数据
            const filepath = this.saveToFile(commentsData, filename);
            
            // 输出统计信息
            console.log('\n🎉 评论提取完成！');
            console.log('📊 统计信息:');
            console.log(`   📝 笔记标题: ${commentsData.noteTitle}`);
            console.log(`   🆔 笔记ID: ${commentsData.noteId}`);
            console.log(`   📊 目标评论数: ${commentsData.totalCommentCount}`);
            console.log(`   💬 实际提取数: ${commentsData.comments.length}`);
            console.log(`   📁 保存文件: ${filename}`);
            
            if (commentsData.comments.length > 0) {
                console.log('\n📝 前3条评论预览:');
                commentsData.comments.slice(0, 3).forEach((comment, index) => {
                    console.log(`   ${index + 1}. ${comment.content.substring(0, 50)}...`);
                });
            }
            
            await browser.disconnect();
            
        } catch (error) {
            console.error('❌ 提取失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行提取器
if (require.main === module) {
    const extractor = new SimpleCommentsExtractor();
    extractor.run();
}

module.exports = SimpleCommentsExtractor;
