#!/usr/bin/env node

/**
 * 🔄 简化版滚动笔记采集器
 * 分步骤执行：先滚动，再采集
 */

const axios = require('axios');
const WebSocket = require('ws');

class SimpleScrollCollector {
    constructor() {
        this.debugPort = null;
    }

    // 🔄 主要方法：滚动并采集
    async scrollAndCollect() {
        console.log('🔄 开始滚动并采集笔记...\n');

        try {
            // 1. 获取调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 目标页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 执行滚动
            console.log('📜 开始滚动页面加载更多内容...');
            await this.performScrolling(tab);
            
            // 4. 等待加载完成
            console.log('⏱️ 等待内容加载完成...');
            await this.sleep(5000);
            
            // 5. 采集数据
            console.log('📊 开始采集笔记数据...');
            const notesData = await this.collectNotesData(tab);
            
            return notesData;

        } catch (error) {
            console.error('❌ 操作失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取调试端口
    async getCurrentDebugPort() {
        try {
            console.log('🔌 获取调试端口...');
            
            const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 10000
            });

            if (response.data.success && response.data.data && response.data.data.result) {
                const httpInfo = response.data.data.result.http;
                if (httpInfo) {
                    this.debugPort = httpInfo.split(':')[1];
                    console.log(`✅ 调试端口: ${this.debugPort}`);
                    return;
                }
            }

            // 尝试常见端口
            const commonPorts = [63524, 51859, 58222, 9222];
            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到端口: ${port}`);
                    return;
                } catch (error) {
                    // 继续尝试
                }
            }

            throw new Error('未找到可用端口');

        } catch (error) {
            throw new Error(`获取端口失败: ${error.message}`);
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
            timeout: 5000
        });

        return response.data.find(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.url.includes('creator.xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );
    }

    // 📜 执行滚动操作
    async performScrolling(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;
            let scrollCount = 0;
            const maxScrolls = 10;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                // 启用Runtime
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                // 开始滚动
                this.doScroll(ws, requestId++, scrollCount, maxScrolls, resolve, reject);
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('滚动超时'));
            }, 60000);
        });
    }

    // 🔄 执行单次滚动
    doScroll(ws, requestId, scrollCount, maxScrolls, resolve, reject) {
        if (scrollCount >= maxScrolls) {
            console.log(`✅ 完成 ${scrollCount} 次滚动`);
            ws.close();
            resolve();
            return;
        }

        scrollCount++;
        console.log(`📜 第 ${scrollCount} 次滚动...`);

        const scrollScript = `
            window.scrollTo(0, document.body.scrollHeight);
            setTimeout(() => {
                return {
                    scrolled: true,
                    scrollHeight: document.body.scrollHeight,
                    scrollTop: window.pageYOffset
                };
            }, 1000);
        `;

        ws.send(JSON.stringify({
            id: requestId,
            method: 'Runtime.evaluate',
            params: {
                expression: scrollScript,
                returnByValue: true
            }
        }));

        // 等待后继续下一次滚动
        setTimeout(() => {
            this.doScroll(ws, requestId + 1, scrollCount, maxScrolls, resolve, reject);
        }, 3000);
    }

    // 📊 采集笔记数据
    async collectNotesData(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ 数据采集WebSocket连接成功');
                
                // 启用Runtime
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                // 执行数据采集
                setTimeout(() => {
                    const collectScript = this.buildCollectionScript();
                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: collectScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        console.log('📊 数据采集成功');
                        
                        const processedData = {
                            ...result,
                            tabId: tab.id,
                            dataSource: 'scroll_collection',
                            extractionMethod: 'simple_scroll_then_collect'
                        };
                        
                        ws.close();
                        resolve(processedData);
                    }
                } catch (error) {
                    console.error('❌ 解析数据失败:', error.message);
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ 数据采集WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('数据采集超时'));
            }, 30000);
        });
    }

    // 📝 构建采集脚本
    buildCollectionScript() {
        return `
            (function() {
                try {
                    const result = {
                        timestamp: new Date().toISOString(),
                        url: window.location.href,
                        title: document.title,
                        notes: []
                    };

                    // 查找笔记元素
                    const noteSelectors = [
                        '.note-item',
                        '.feed-item',
                        '[class*="note"]',
                        '[class*="feed"]',
                        '[class*="card"]'
                    ];

                    let noteElements = [];
                    for (const selector of noteSelectors) {
                        noteElements = document.querySelectorAll(selector);
                        if (noteElements.length > 0) {
                            console.log('使用选择器:', selector, '找到', noteElements.length, '个元素');
                            break;
                        }
                    }

                    // 通用方法
                    if (noteElements.length === 0) {
                        const allDivs = document.querySelectorAll('div');
                        noteElements = Array.from(allDivs).filter(el => {
                            const hasImage = el.querySelector('img');
                            const hasText = el.textContent.trim().length > 10;
                            return hasImage && hasText;
                        });
                        console.log('通用方法找到', noteElements.length, '个元素');
                    }

                    // 提取笔记信息
                    noteElements.forEach((element, index) => {
                        try {
                            const noteData = {
                                index: index + 1,
                                id: element.id || 'note_' + index
                            };

                            // 提取标题
                            const titleSelectors = ['.title', '.note-title', 'h1', 'h2', 'h3', '[class*="title"]'];
                            for (const selector of titleSelectors) {
                                const titleEl = element.querySelector(selector);
                                if (titleEl && titleEl.textContent.trim()) {
                                    noteData.title = titleEl.textContent.trim();
                                    break;
                                }
                            }

                            if (!noteData.title) {
                                const text = element.textContent.trim();
                                if (text.length > 10) {
                                    noteData.title = text.substring(0, 50) + (text.length > 50 ? '...' : '');
                                }
                            }

                            // 提取图片
                            const images = element.querySelectorAll('img');
                            noteData.images = [];
                            images.forEach(img => {
                                if (img.src && !img.src.includes('data:') && !img.src.includes('avatar')) {
                                    noteData.images.push({
                                        src: img.src,
                                        alt: img.alt || ''
                                    });
                                }
                            });

                            // 提取互动数据
                            noteData.interactions = { likes: 0, collects: 0, comments: 0 };
                            
                            // 提取链接
                            const linkEl = element.querySelector('a[href*="/explore/"]') || 
                                          element.querySelector('a[href*="/discovery/"]') ||
                                          element.querySelector('a');
                            if (linkEl && linkEl.href) {
                                noteData.link = linkEl.href;
                            }

                            if (noteData.title || noteData.images.length > 0) {
                                result.notes.push(noteData);
                            }

                        } catch (error) {
                            console.error('处理笔记', index, '出错:', error.message);
                        }
                    });

                    // 统计信息
                    result.summary = {
                        totalNotes: result.notes.length,
                        totalImages: result.notes.reduce((sum, note) => sum + note.images.length, 0),
                        pageHeight: document.body.scrollHeight,
                        viewportHeight: window.innerHeight
                    };

                    return result;
                    
                } catch (error) {
                    return {
                        error: error.message,
                        timestamp: new Date().toISOString()
                    };
                }
            })();
        `;
    }

    // 💤 等待函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 🧪 测试函数
async function testSimpleScrollCollection() {
    const collector = new SimpleScrollCollector();
    
    try {
        const data = await collector.scrollAndCollect();
        
        console.log('\n📝 滚动采集结果:');
        console.log('=' * 50);
        console.log(`📊 总笔记数: ${data.summary.totalNotes}`);
        console.log(`🖼️  总图片数: ${data.summary.totalImages}`);
        console.log(`📏 页面高度: ${data.summary.pageHeight}px`);
        console.log(`🖥️  视窗高度: ${data.summary.viewportHeight}px\n`);
        
        // 显示前5篇笔记
        const displayNotes = data.notes.slice(0, 5);
        displayNotes.forEach((note, index) => {
            console.log(`📝 笔记 ${index + 1}:`);
            console.log(`   标题: ${note.title || '无标题'}`);
            console.log(`   图片数量: ${note.images.length}`);
            if (note.link) {
                console.log(`   🔗 链接: ${note.link}`);
            }
            console.log('');
        });
        
        if (data.notes.length > 5) {
            console.log(`... 还有 ${data.notes.length - 5} 篇笔记`);
        }
        
        return data;
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    testSimpleScrollCollection().catch(console.error);
}

module.exports = { SimpleScrollCollector, testSimpleScrollCollection };
