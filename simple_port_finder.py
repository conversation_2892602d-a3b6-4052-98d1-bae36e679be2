#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 简单端口检测器 - 不依赖额外库
"""

import requests
import json

def test_debug_port(port):
    """测试调试端口是否可用"""
    try:
        response = requests.get(f"http://localhost:{port}/json/version", timeout=2)
        if response.status_code == 200:
            data = response.json()
            return True, data
    except:
        pass
    return False, None

def get_browser_tabs(port):
    """获取浏览器标签页"""
    try:
        response = requests.get(f"http://localhost:{port}/json", timeout=2)
        if response.status_code == 200:
            return response.json()
    except:
        pass
    return []

def find_xiaohongshu_tabs(port):
    """查找小红书标签页"""
    tabs = get_browser_tabs(port)
    xiaohongshu_tabs = []
    
    for tab in tabs:
        url = tab.get('url', '')
        title = tab.get('title', '')
        
        if 'xiaohongshu.com' in url or 'xhs' in url.lower():
            xiaohongshu_tabs.append({
                'id': tab.get('id'),
                'title': title,
                'url': url,
                'type': tab.get('type', ''),
                'webSocketDebuggerUrl': tab.get('webSocketDebuggerUrl', '')
            })
    
    return xiaohongshu_tabs

def main():
    """主函数"""
    print("🎯 简单端口检测器")
    print("=" * 30)
    
    # 扩展端口范围，包含更多可能的端口
    ports = (
        list(range(9222, 9235)) +  # Chrome默认端口
        list(range(55270, 55290)) +  # 比特浏览器常用端口
        list(range(54340, 54360)) +  # 比特浏览器常用端口
        [56906, 56907, 56908, 56909, 56910] +  # API端口附近
        list(range(63000, 63100)) +  # 高端口范围
        list(range(64000, 64100))   # 高端口范围
    )
    
    print(f"🔍 扫描 {len(ports)} 个端口...")
    
    active_ports = []
    
    for i, port in enumerate(ports):
        if i % 20 == 0:
            print(f"   进度: {i}/{len(ports)}")
        
        is_active, data = test_debug_port(port)
        if is_active:
            print(f"   ✅ 端口 {port} 活跃!")
            active_ports.append({
                'port': port,
                'browser': data.get('Browser', '未知'),
                'version': data.get('Protocol-Version', '未知'),
                'data': data
            })
    
    if active_ports:
        print(f"\n📊 找到 {len(active_ports)} 个活跃端口:")
        
        best_port = None
        
        for port_info in active_ports:
            port = port_info['port']
            print(f"\n🔗 端口 {port}:")
            print(f"   浏览器: {port_info['browser']}")
            print(f"   版本: {port_info['version']}")
            
            # 查找小红书标签页
            xiaohongshu_tabs = find_xiaohongshu_tabs(port)
            
            if xiaohongshu_tabs:
                print(f"   🎯 找到 {len(xiaohongshu_tabs)} 个小红书标签页:")
                for tab in xiaohongshu_tabs:
                    print(f"      - {tab['title'][:50]}...")
                    print(f"        URL: {tab['url'][:80]}...")
                
                if not best_port:
                    best_port = port
            else:
                # 获取所有标签页
                all_tabs = get_browser_tabs(port)
                print(f"   📄 总共 {len(all_tabs)} 个标签页")
                
                if all_tabs:
                    print("   标签页列表:")
                    for i, tab in enumerate(all_tabs[:3]):  # 只显示前3个
                        title = tab.get('title', '无标题')[:30]
                        url = tab.get('url', '')[:50]
                        print(f"      {i+1}. {title} - {url}...")
        
        # 推荐最佳端口
        if best_port:
            print(f"\n🎉 推荐使用端口: {best_port} (有小红书页面)")
            return best_port
        elif active_ports:
            recommended_port = active_ports[0]['port']
            print(f"\n💡 推荐使用端口: {recommended_port} (第一个活跃端口)")
            return recommended_port
    
    else:
        print("\n❌ 未找到任何活跃的调试端口")
        print("\n💡 解决方案:")
        print("1. 确保比特浏览器正在运行")
        print("2. 在比特浏览器中启用调试模式")
        print("3. 或者手动启动Chrome时添加参数: --remote-debugging-port=9222")
    
    return None

if __name__ == "__main__":
    port = main()
    if port:
        print(f"\n✅ 检测完成，推荐端口: {port}")
        
        # 自动输入到等待的脚本中
        print(f"\n🚀 现在可以在另一个终端中输入端口号: {port}")
    else:
        print("\n❌ 未找到可用端口")
