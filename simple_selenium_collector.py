#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 简化版Selenium小红书评论采集器
连接到现有的Chrome浏览器实例
"""

import json
import time
import random
import re
from datetime import datetime
from typing import List, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service


class SimpleSeleniumCollector:
    """简化版Selenium评论采集器"""
    
    def __init__(self):
        self.driver = None
        self.comments_data = {
            'note_url': '',
            'note_title': '',
            'timestamp': '',
            'comments': [],
            'summary': {
                'total_comments': 0,
                'total_replies': 0,
                'total_likes': 0,
                'strategy': 'simple-selenium'
            }
        }
    
    def connect_to_existing_browser(self, debug_port: int = 63524):
        """连接到现有的Chrome浏览器实例"""
        print(f"🔌 连接到现有Chrome浏览器 (端口: {debug_port})...")
        
        try:
            # 配置Chrome选项连接到现有实例
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{debug_port}")
            
            # 创建WebDriver连接
            self.driver = webdriver.Chrome(options=chrome_options)
            
            print("✅ 成功连接到现有浏览器")
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
            return False
    
    def get_current_page_info(self):
        """获取当前页面信息"""
        print("📋 获取当前页面信息...")
        
        self.comments_data['note_url'] = self.driver.current_url
        self.comments_data['note_title'] = self.driver.title.replace(' - 小红书', '').strip()
        self.comments_data['timestamp'] = datetime.now().isoformat()
        
        print(f"📝 页面标题: {self.comments_data['note_title']}")
        print(f"🔗 页面URL: {self.comments_data['note_url'][:80]}...")
    
    def count_current_elements(self):
        """统计当前页面元素"""
        print("📊 统计当前页面元素...")
        
        avatars = self.driver.find_elements(By.CSS_SELECTOR, 'img[src*="avatar"]')
        comment_divs = self.driver.find_elements(By.CSS_SELECTOR, '[class*="comment"]')
        
        print(f"   👤 头像数量: {len(avatars)}")
        print(f"   💬 评论div: {len(comment_divs)}")
        
        return len(avatars), len(comment_divs)
    
    def click_more_buttons_once(self):
        """点击一轮"更多"按钮"""
        print("🔘 点击一轮'更多'按钮...")
        
        clicked_count = 0
        button_texts = ['更多', '展开', '查看']
        
        for button_text in button_texts:
            try:
                # 查找包含特定文本的元素
                xpath = f"//*[contains(text(), '{button_text}')]"
                elements = self.driver.find_elements(By.XPATH, xpath)
                
                print(f"   找到 {len(elements)} 个'{button_text}'按钮")
                
                # 点击前3个按钮
                for i, element in enumerate(elements[:3]):
                    try:
                        if element.is_displayed() and element.is_enabled():
                            # 滚动到元素可见
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                            time.sleep(0.5)
                            
                            # 点击元素
                            element.click()
                            clicked_count += 1
                            
                            print(f"     ✅ 点击: {element.text[:20]}...")
                            time.sleep(1)
                            
                    except Exception as e:
                        print(f"     ❌ 点击失败: {str(e)[:30]}...")
                        continue
                        
            except Exception as e:
                continue
        
        # 特别处理"条回复"按钮
        try:
            reply_xpath = "//*[contains(text(), '条回复')]"
            reply_elements = self.driver.find_elements(By.XPATH, reply_xpath)
            
            print(f"   找到 {len(reply_elements)} 个'条回复'按钮")
            
            for element in reply_elements[:2]:
                try:
                    if element.is_displayed() and element.is_enabled():
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                        time.sleep(0.5)
                        element.click()
                        clicked_count += 1
                        print(f"     ✅ 点击回复: {element.text[:20]}...")
                        time.sleep(1)
                except:
                    continue
        except:
            pass
        
        print(f"   🎯 本轮总共点击了 {clicked_count} 个按钮")
        return clicked_count
    
    def scroll_to_bottom(self):
        """滚动到页面底部"""
        print("📜 滚动到页面底部...")
        
        before_height = self.driver.execute_script("return document.body.scrollHeight")
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)
        after_height = self.driver.execute_script("return document.body.scrollHeight")
        
        print(f"   页面高度: {before_height} → {after_height}")
        return after_height > before_height
    
    def extract_comments_simple(self):
        """简单提取评论"""
        print("🔍 简单提取评论...")
        
        comments = []
        
        # 查找所有头像元素
        avatar_elements = self.driver.find_elements(By.CSS_SELECTOR, 'img[src*="avatar"]')
        print(f"   找到 {len(avatar_elements)} 个头像")
        
        for index, avatar in enumerate(avatar_elements):
            try:
                # 获取父容器
                container = avatar
                for _ in range(3):  # 向上查找3层
                    container = container.find_element(By.XPATH, '..')
                
                container_text = container.text
                
                # 基本信息提取
                comment_data = {
                    'id': index + 1,
                    'avatar': avatar.get_attribute('src') or '',
                    'container_text': container_text[:200],  # 前200个字符
                    'has_time': bool(re.search(r'\d+天前|\d+小时前|\d+分钟前', container_text)),
                    'has_like': bool(re.search(r'\d+\s*赞', container_text)),
                    'text_length': len(container_text)
                }
                
                # 只保留有意义的评论
                if comment_data['text_length'] > 10 and comment_data['text_length'] < 2000:
                    comments.append(comment_data)
                
            except Exception as e:
                continue
        
        print(f"   提取到 {len(comments)} 条有效评论")
        return comments
    
    def run_simple_test(self):
        """运行简单测试"""
        print("🧪 运行简单测试...\n")
        
        try:
            # 1. 连接到现有浏览器
            if not self.connect_to_existing_browser():
                return False
            
            # 2. 获取页面信息
            self.get_current_page_info()
            
            # 3. 统计初始元素
            print("\n📊 初始状态:")
            initial_avatars, initial_divs = self.count_current_elements()
            
            # 4. 滚动到底部
            print("\n📜 滚动测试:")
            scroll_result = self.scroll_to_bottom()
            
            # 5. 点击按钮
            print("\n🔘 按钮点击测试:")
            clicked_count = self.click_more_buttons_once()
            
            # 6. 等待加载
            print("\n⏳ 等待内容加载...")
            time.sleep(3)
            
            # 7. 统计最终元素
            print("\n📊 最终状态:")
            final_avatars, final_divs = self.count_current_elements()
            
            # 8. 简单提取评论
            print("\n🔍 评论提取测试:")
            comments = self.extract_comments_simple()
            
            # 9. 显示结果
            print("\n🎉 测试结果:")
            print(f"   👤 头像变化: {initial_avatars} → {final_avatars} (+{final_avatars - initial_avatars})")
            print(f"   💬 评论div变化: {initial_divs} → {final_divs} (+{final_divs - initial_divs})")
            print(f"   🖱️ 点击按钮: {clicked_count}个")
            print(f"   📝 提取评论: {len(comments)}条")
            
            # 10. 保存简单结果
            self.comments_data['comments'] = comments
            self.save_simple_results()
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            return False
        finally:
            # 不关闭浏览器，保持连接
            pass
    
    def save_simple_results(self):
        """保存简单结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"simple_test_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.comments_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 简单结果已保存: {filename}")


def main():
    """主函数"""
    collector = SimpleSeleniumCollector()
    
    print("🎯 简化版Selenium评论采集器")
    print("=" * 50)
    print("📋 这个工具会:")
    print("   1. 连接到现有的Chrome浏览器")
    print("   2. 分析当前小红书页面")
    print("   3. 测试滚动和按钮点击")
    print("   4. 简单提取评论信息")
    print("   5. 保存测试结果")
    print()
    
    success = collector.run_simple_test()
    
    if success:
        print("\n🎉 测试完成! 请查看生成的JSON文件。")
    else:
        print("\n❌ 测试失败! 请检查Chrome浏览器是否正确启动。")


if __name__ == "__main__":
    main()
