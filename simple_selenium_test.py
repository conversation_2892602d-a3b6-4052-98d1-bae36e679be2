#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 简化版Selenium测试
使用最基本的配置尝试连接调试端口
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time

def test_simple_connection(port):
    """最简单的连接测试"""
    print(f"🔍 简单连接测试端口 {port}...")
    
    try:
        # 最基本的Chrome选项
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
        
        print(f"   🔗 连接到 localhost:{port}...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 获取基本信息
        current_url = driver.current_url
        title = driver.title
        
        print(f"   ✅ 连接成功!")
        print(f"   📄 页面: {current_url}")
        print(f"   📝 标题: {title}")
        
        # 如果是小红书页面，做简单测试
        if 'xiaohongshu.com' in current_url:
            print("   🎉 小红书页面检测成功!")
            
            # 简单的页面操作测试
            try:
                # 获取页面文本
                body = driver.find_element(By.TAG_NAME, "body")
                page_text = body.text
                
                # 检查评论相关内容
                comment_count = page_text.count('条评论')
                reply_count = page_text.count('回复')
                
                print(f"   📊 页面分析:")
                print(f"      评论标识: {comment_count}")
                print(f"      回复标识: {reply_count}")
                
                if comment_count > 0 or reply_count > 0:
                    print("   ✅ 页面包含评论内容，可以进行爬取!")
                else:
                    print("   ⚠️ 页面可能不在评论区")
                    
            except Exception as e:
                print(f"   ⚠️ 页面分析失败: {e}")
        
        driver.quit()
        return True
        
    except Exception as e:
        error_msg = str(e)
        print(f"   ❌ 连接失败: {error_msg}")
        
        # 错误分析
        if "version" in error_msg.lower():
            print("   💡 版本不匹配问题")
        elif "cannot connect" in error_msg.lower():
            print("   💡 端口连接问题")
        elif "session not created" in error_msg.lower():
            print("   💡 会话创建问题")
        
        return False

def test_different_ports():
    """测试不同的端口"""
    print("🔍 测试多个可能的端口...")
    
    # 我们知道的端口 + 一些常见端口
    ports_to_test = [
        60811,  # 我们通过API获取的端口
        9222,   # Chrome默认调试端口
        9223,   # 备用端口
        9224,   # 备用端口
    ]
    
    for port in ports_to_test:
        print(f"\n🔍 测试端口 {port}...")
        if test_simple_connection(port):
            print(f"🎉 端口 {port} 连接成功!")
            return port
        else:
            print(f"❌ 端口 {port} 连接失败")
    
    return None

def check_chrome_processes():
    """检查Chrome进程"""
    print("🔍 检查Chrome进程...")
    
    try:
        import subprocess
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq chrome.exe'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            chrome_processes = [line for line in result.stdout.split('\n') if 'chrome.exe' in line]
            print(f"   找到 {len(chrome_processes)} 个Chrome进程")
            
            for i, process in enumerate(chrome_processes[:5], 1):  # 只显示前5个
                print(f"   {i}. {process.strip()}")
            
            return len(chrome_processes) > 0
        else:
            print("   ❌ 无法检查进程")
            return False
            
    except Exception as e:
        print(f"   ❌ 进程检查失败: {e}")
        return False

def main():
    print("🎯 简化版Selenium调试端口测试")
    print("=" * 50)
    
    # 1. 检查Chrome进程
    if not check_chrome_processes():
        print("⚠️ 未检测到Chrome进程，请确保比特浏览器正在运行")
    
    # 2. 测试我们获取的端口
    print(f"\n🎯 测试API获取的端口 60811...")
    if test_simple_connection(60811):
        print("\n🎉 成功连接到比特浏览器!")
        print("🚀 现在可以运行完整的爬虫脚本")
        return
    
    # 3. 测试其他可能的端口
    print(f"\n🔍 尝试其他可能的端口...")
    working_port = test_different_ports()
    
    if working_port:
        print(f"\n🎉 找到可用端口: {working_port}")
        print("🚀 现在可以运行完整的爬虫脚本")
    else:
        print("\n❌ 所有端口测试都失败了")
        print("💡 可能的原因:")
        print("   1. 比特浏览器未启用调试模式")
        print("   2. ChromeDriver版本不兼容")
        print("   3. 防火墙阻止连接")
        print("   4. 比特浏览器配置问题")
        
        print("\n🎯 建议的解决方案:")
        print("   1. 重启比特浏览器")
        print("   2. 确保19号窗口正在运行")
        print("   3. 使用浏览器控制台方案（最可靠）")

if __name__ == "__main__":
    main()
