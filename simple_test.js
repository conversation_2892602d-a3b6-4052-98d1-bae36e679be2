#!/usr/bin/env node

/**
 * 🧪 简单测试脚本
 * 测试基本的评论采集功能
 */

const axios = require('axios');
const WebSocket = require('ws');

async function simpleTest() {
    console.log('🧪 开始简单测试...\n');

    try {
        // 1. 获取调试端口
        const debugPort = 63524;
        console.log(`✅ 使用端口: ${debugPort}`);

        // 2. 获取标签页
        const response = await axios.get(`http://127.0.0.1:${debugPort}/json`, {
            timeout: 5000
        });

        const tab = response.data.find(tab =>
            tab.url && tab.url.includes('xiaohongshu.com')
        );

        if (!tab) {
            throw new Error('未找到小红书标签页');
        }

        console.log(`🎯 找到标签页: ${tab.title}`);

        // 3. 执行简单的评论统计
        const ws = new WebSocket(tab.webSocketDebuggerUrl);

        ws.on('open', () => {
            console.log('✅ WebSocket连接成功');
            
            ws.send(JSON.stringify({
                id: 1,
                method: 'Runtime.enable'
            }));

            setTimeout(() => {
                const testScript = `
                    (function() {
                        try {
                            // 统计页面上的评论相关元素
                            const stats = {
                                avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                commentDivs: document.querySelectorAll('[class*="comment"]').length,
                                replyDivs: document.querySelectorAll('[class*="reply"]').length,
                                timeElements: Array.from(document.querySelectorAll('*')).filter(el => 
                                    /\\d+天前|\\d+小时前|\\d+分钟前/.test(el.textContent)
                                ).length,
                                moreButtons: Array.from(document.querySelectorAll('*')).filter(el => 
                                    el.textContent.includes('更多') || el.textContent.includes('展开')
                                ).length,
                                pageHeight: document.body.scrollHeight,
                                visibleHeight: window.innerHeight
                            };

                            // 尝试点击一些"更多"按钮
                            let clicked = 0;
                            const moreElements = Array.from(document.querySelectorAll('*')).filter(el => 
                                (el.textContent.includes('更多') || el.textContent.includes('展开')) &&
                                el.offsetParent !== null
                            );

                            for (let i = 0; i < Math.min(5, moreElements.length); i++) {
                                try {
                                    moreElements[i].click();
                                    clicked++;
                                } catch (e) {
                                    // 忽略点击错误
                                }
                            }

                            stats.buttonsClicked = clicked;

                            return { success: true, data: stats };
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    })();
                `;

                ws.send(JSON.stringify({
                    id: 2,
                    method: 'Runtime.evaluate',
                    params: {
                        expression: testScript,
                        returnByValue: true
                    }
                }));
            }, 1000);
        });

        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                if (message.id === 2 && message.result && message.result.result && message.result.result.value) {
                    const result = message.result.result.value;
                    
                    if (result.success) {
                        const stats = result.data;
                        console.log('\n📊 页面统计结果:');
                        console.log(`👤 头像数量: ${stats.avatars}`);
                        console.log(`💬 评论div: ${stats.commentDivs}`);
                        console.log(`↩️ 回复div: ${stats.replyDivs}`);
                        console.log(`⏰ 时间元素: ${stats.timeElements}`);
                        console.log(`🔘 更多按钮: ${stats.moreButtons}`);
                        console.log(`📏 页面高度: ${stats.pageHeight}px`);
                        console.log(`👀 可视高度: ${stats.visibleHeight}px`);
                        console.log(`🖱️ 点击按钮: ${stats.buttonsClicked}个`);
                        
                        console.log('\n🎉 简单测试完成!');
                    } else {
                        console.log('❌ 测试失败:', result.error);
                    }
                    
                    ws.close();
                }
            } catch (error) {
                console.error('❌ 处理结果失败:', error.message);
                ws.close();
            }
        });

        ws.on('error', (error) => {
            console.error('❌ WebSocket错误:', error.message);
        });

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

if (require.main === module) {
    simpleTest();
}

module.exports = simpleTest;
