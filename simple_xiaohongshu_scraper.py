#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 简单小红书评论爬取器
启动新的Chrome实例，手动导航到小红书页面后自动爬取所有评论
"""

import time
import json
import re
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class SimpleXiaohongshuScraper:
    def __init__(self):
        self.output_dir = './scraped_data'
        self.ensure_output_dir()
        
        self.config = {
            'target_comments': 1472,
            'max_scroll_attempts': 1000,
            'scroll_delay': 0.8,
            'click_delay': 0.5
        }
        
        self.driver = None
        
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def setup_chrome(self):
        """设置Chrome浏览器"""
        print("🚀 启动Chrome浏览器...")
        
        try:
            chrome_options = Options()
            
            # 反检测设置
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 其他设置
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            # 用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ Chrome浏览器启动成功")
            return True
            
        except Exception as e:
            print(f"❌ Chrome浏览器启动失败: {str(e)}")
            return False
    
    def navigate_to_xiaohongshu(self):
        """导航到小红书页面"""
        print("🎯 请手动导航到小红书评论页面...")
        
        # 打开小红书首页
        self.driver.get("https://www.xiaohongshu.com")
        
        print("📋 操作步骤：")
        print("1. 在打开的浏览器中登录小红书账号")
        print("2. 导航到目标笔记页面（漫娴学姐的兼职帖子）")
        print("3. 确保可以看到评论区域")
        print("4. 完成后在此处按回车继续...")
        
        input()
        
        # 检查当前页面
        current_url = self.driver.current_url
        print(f"📄 当前页面: {current_url}")
        
        if 'xiaohongshu.com' not in current_url:
            print("⚠️ 警告：当前不在小红书页面")
            print("请确保已导航到正确的页面，然后按回车继续...")
            input()
        
        return True
    
    def scroll_and_load_all_comments(self):
        """滚动并加载所有评论"""
        print("🚀 开始滚动加载所有评论...")
        print(f"🎯 目标: {self.config['target_comments']} 条评论")
        
        current_count = 0
        previous_count = 0
        stable_count = 0
        total_scrolls = 0
        total_clicks = 0
        max_count_seen = 0
        
        # 先滚动到评论区域
        self.scroll_to_comments()
        
        for i in range(self.config['max_scroll_attempts']):
            print(f"\n📜 滚动 {i + 1}/{self.config['max_scroll_attempts']}")
            
            # 统计评论数量
            current_count = self.count_comments()
            print(f"   💬 当前评论数: {current_count}")
            
            # 更新最大值
            if current_count > max_count_seen:
                max_count_seen = current_count
                print(f"   🎯 新记录: {max_count_seen}")
            
            # 检查是否达到目标
            if current_count >= self.config['target_comments'] * 0.95:
                print(f"🎉 接近目标！{current_count}/{self.config['target_comments']}")
                break
            
            # 检查进度
            if current_count == previous_count:
                stable_count += 1
                print(f"   ⏸️ 稳定 {stable_count} 次")
            else:
                new_comments = current_count - previous_count
                print(f"   📈 新增: {new_comments} 条")
                stable_count = 0
                previous_count = current_count
            
            # 点击加载更多
            if stable_count >= 3:
                print("   🔄 尝试点击加载更多...")
                if self.click_load_more():
                    total_clicks += 1
                    stable_count = 0
                    print(f"   ✅ 点击成功，总计: {total_clicks} 次")
            
            # 执行滚动
            self.perform_scroll(i)
            
            # 如果长时间稳定，停止
            if stable_count >= 20:
                print("   ⏹️ 长时间无新内容，停止滚动")
                break
            
            time.sleep(self.config['scroll_delay'])
            total_scrolls += 1
            
            # 每50次输出进度
            if i % 50 == 0 and i > 0:
                progress = round((current_count / self.config['target_comments']) * 100)
                print(f"\n📊 进度报告:")
                print(f"   📈 完成度: {progress}% ({current_count}/{self.config['target_comments']})")
                print(f"   📜 总滚动: {total_scrolls} 次")
                print(f"   🔄 总点击: {total_clicks} 次")
                print(f"   🏆 最高记录: {max_count_seen}")
        
        print(f"\n✅ 滚动完成！")
        print(f"📊 最终统计:")
        print(f"   💬 最终评论数: {current_count}")
        print(f"   📈 完成度: {round((current_count / self.config['target_comments']) * 100)}%")
        print(f"   📜 总滚动: {total_scrolls} 次")
        print(f"   🔄 总点击: {total_clicks} 次")
        print(f"   🏆 最高记录: {max_count_seen}")
        
        return current_count
    
    def scroll_to_comments(self):
        """滚动到评论区域"""
        print("🎯 定位评论区域...")
        
        try:
            # 查找评论区域的多种方式
            comment_selectors = [
                "//span[contains(text(), '条评论')]",
                "//div[contains(text(), '评论')]",
                "//*[contains(@class, 'comment')]",
                "//*[contains(@class, 'Comment')]",
                "//span[contains(text(), '评论')]"
            ]
            
            for selector in comment_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    print(f"✅ 找到评论区域: {selector}")
                    time.sleep(2)
                    return True
                except:
                    continue
            
            # 如果没找到特定元素，滚动到页面中部
            print("💡 未找到特定评论区域，滚动到页面中部")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.6);")
            time.sleep(2)
            return True
            
        except Exception as e:
            print(f"❌ 定位评论区域失败: {str(e)}")
            return False
    
    def perform_scroll(self, index):
        """执行滚动"""
        strategies = [
            # 小步滚动
            lambda: self.driver.execute_script("window.scrollBy(0, 300 + Math.random() * 100);"),
            
            # 中步滚动
            lambda: self.driver.execute_script("window.scrollBy(0, 500 + Math.random() * 200);"),
            
            # 大步滚动
            lambda: self.driver.execute_script("window.scrollBy(0, 800 + Math.random() * 300);"),
            
            # 滚动到底部
            lambda: self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);"),
            
            # 滚动到最后评论
            lambda: self.driver.execute_script("""
                const comments = document.querySelectorAll('[class*="comment"], [class*="Comment"]');
                if (comments.length > 0) {
                    const lastComment = comments[comments.length - 1];
                    lastComment.scrollIntoView({behavior: 'smooth'});
                }
            """),
            
            # 向上再向下
            lambda: self.driver.execute_script("""
                window.scrollBy(0, -200);
                setTimeout(() => window.scrollBy(0, 600), 200);
            """)
        ]
        
        # 根据滚动次数选择策略
        if index < 100:
            strategy_index = index % 3  # 前100次使用温和策略
        elif index < 300:
            strategy_index = index % 5  # 中期使用多样化策略
        else:
            strategy_index = index % len(strategies)  # 后期使用所有策略
        
        strategies[strategy_index]()
    
    def click_load_more(self):
        """点击加载更多按钮"""
        try:
            # 要查找的文字
            load_more_texts = [
                '加载更多', '展开更多', '查看更多', '显示更多', '更多评论',
                '展开', '更多', '加载', '查看全部', '展开全部', '点击加载'
            ]
            
            for text in load_more_texts:
                try:
                    # 查找包含该文字的元素
                    elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{text}')]")
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            try:
                                element.click()
                                time.sleep(self.config['click_delay'])
                                return True
                            except:
                                # 如果普通点击失败，尝试JavaScript点击
                                try:
                                    self.driver.execute_script("arguments[0].click();", element)
                                    time.sleep(self.config['click_delay'])
                                    return True
                                except:
                                    continue
                except:
                    continue
            
            # 尝试点击按钮元素
            button_selectors = [
                "button[class*='load']",
                "button[class*='more']",
                "div[class*='load']",
                "div[class*='more']",
                "span[class*='load']",
                "span[class*='more']"
            ]
            
            for selector in button_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element_text = element.text.lower()
                            if any(keyword in element_text for keyword in ['more', '更多', '展开', '加载']):
                                try:
                                    element.click()
                                    time.sleep(self.config['click_delay'])
                                    return True
                                except:
                                    pass
                except:
                    continue
            
            return False
            
        except Exception as e:
            print(f"   ❌ 点击失败: {str(e)}")
            return False
    
    def count_comments(self):
        """统计评论数量"""
        try:
            page_text = self.driver.execute_script("return document.body.textContent;")
            
            # 多种统计方法
            methods = [
                # 方法1: 时间格式统计
                lambda: len(re.findall(r'\d{2}-\d{2}', page_text)),
                
                # 方法2: 回复数统计
                lambda: len(re.findall(r'\d+回复', page_text)),
                
                # 方法3: DOM元素统计
                lambda: len(self.driver.find_elements(By.CSS_SELECTOR, '[class*="comment"], [class*="Comment"]')),
                
                # 方法4: 关键词统计
                lambda: len(re.findall(r'求带|宝子|学姐|兼职|聊天员', page_text)) // 2,
                
                # 方法5: 点赞数统计
                lambda: len(re.findall(r'\d+赞', page_text)),
                
                # 方法6: 用户ID统计
                lambda: len(re.findall(r'小红薯[A-F0-9]{8}', page_text))
            ]
            
            counts = []
            for method in methods:
                try:
                    count = method()
                    counts.append(count)
                except:
                    counts.append(0)
            
            return max(counts) if counts else 0
            
        except Exception as e:
            print(f"❌ 统计评论失败: {str(e)}")
            return 0

    def extract_all_comments(self):
        """提取所有评论"""
        print("🧠 开始提取所有评论...")

        # 获取页面文本
        page_text = self.driver.execute_script("return document.body.textContent;")
        print(f"📄 页面文本长度: {len(page_text)}")

        # 提取基本信息
        note_info = {
            'id': '',
            'title': '',
            'author': '',
            'totalCommentCount': 0
        }

        try:
            # 提取笔记ID
            current_url = self.driver.current_url
            url_match = re.search(r'explore/([a-f0-9]+)', current_url)
            if url_match:
                note_info['id'] = url_match.group(1)

            # 提取标题
            note_info['title'] = self.driver.title.replace(' - 小红书', '')

            # 提取评论总数
            comment_count_match = re.search(r'(\d+)\s*条评论', page_text)
            if comment_count_match:
                note_info['totalCommentCount'] = int(comment_count_match.group(1))

            # 提取作者
            if '漫娴学姐' in page_text:
                note_info['author'] = '漫娴学姐 招暑假工版'

        except Exception as e:
            print(f"❌ 提取基本信息失败: {str(e)}")

        # 解析评论
        comments = self.parse_comments(page_text)

        return {
            'noteInfo': note_info,
            'comments': comments,
            'extractStats': {
                'totalTextLength': len(page_text),
                'successfulExtractions': len(comments),
                'extractionMethods': ['selenium-chrome']
            },
            'extractTime': datetime.now().isoformat()
        }

    def parse_comments(self, page_text):
        """解析评论"""
        print("🔍 开始解析评论...")

        all_comments = []

        # 策略1: 时间模式解析
        print("   🔍 执行时间模式解析...")
        time_pattern = r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)'
        time_matches = list(re.finditer(time_pattern, page_text))

        for i, match in enumerate(time_matches):
            time_str = match.group(1)
            time_index = match.start()

            start_index = max(0, time_index - 200)
            end_index = time_matches[i + 1].start() if i + 1 < len(time_matches) else time_index + 1000
            end_index = min(len(page_text), end_index)

            comment_text = page_text[start_index:end_index].strip()

            if 20 < len(comment_text) < 2000:
                comment = self.create_comment(comment_text, len(all_comments) + 1, 'time')
                if comment:
                    all_comments.append(comment)

        print(f"   📝 时间模式提取到 {len([c for c in all_comments if c['extractedInfo']['source'] == 'time'])} 条评论")

        # 策略2: 关键词模式解析
        print("   🔍 执行关键词模式解析...")
        keywords = [
            '求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯',
            'Carina', '广州', '工作', '赚钱', '日入', '月入', '陪聊'
        ]

        for keyword in keywords:
            for match in re.finditer(keyword, page_text):
                start_index = max(0, match.start() - 150)
                end_index = min(len(page_text), match.start() + 800)

                comment_text = page_text[start_index:end_index].strip()

                if 15 < len(comment_text) < 1500:
                    comment = self.create_comment(comment_text, len(all_comments) + 1, 'keyword')
                    if comment:
                        all_comments.append(comment)

        print(f"   📝 关键词模式提取到 {len([c for c in all_comments if c['extractedInfo']['source'] == 'keyword'])} 条评论")

        # 策略3: 用户模式解析
        print("   🔍 执行用户模式解析...")
        user_patterns = [
            r'([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(\d{2}-\d{2})',
            r'小红薯([A-F0-9]{8,})',
            r'([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带'
        ]

        for pattern in user_patterns:
            for match in re.finditer(pattern, page_text):
                start_index = max(0, match.start() - 100)
                end_index = min(len(page_text), match.start() + 600)

                comment_text = page_text[start_index:end_index].strip()

                if 10 < len(comment_text) < 1200:
                    comment = self.create_comment(comment_text, len(all_comments) + 1, 'user')
                    if comment:
                        all_comments.append(comment)

        print(f"   📝 用户模式提取到 {len([c for c in all_comments if c['extractedInfo']['source'] == 'user'])} 条评论")

        print(f"📊 总计收集到 {len(all_comments)} 条原始评论")

        # 去重
        unique_comments = self.deduplicate_comments(all_comments)

        print(f"✅ 解析完成，最终提取到 {len(unique_comments)} 条评论")
        return unique_comments

    def create_comment(self, text, comment_id, source):
        """创建评论对象"""
        comment = {
            'id': comment_id,
            'userId': '',
            'username': '',
            'content': '',
            'time': '',
            'likes': 0,
            'replyCount': 0,
            'isAuthor': '作者' in text,
            'isPinned': '置顶' in text,
            'extractedInfo': {
                'source': source,
                'originalLength': len(text)
            }
        }

        # 提取时间
        time_match = re.search(r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)', text)
        if time_match:
            comment['time'] = time_match.group(1)

        # 提取用户名
        user_patterns = [
            r'^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}',
            r'([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带',
            r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})'
        ]

        for pattern in user_patterns:
            match = re.search(pattern, text)
            if match:
                comment['username'] = match.group(1).strip()
                break

        # 清理内容
        content = text
        content = re.sub(r'\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前', '', content)
        content = re.sub(r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+', '', content)
        content = re.sub(r'作者|置顶评论|回复|赞|仅自己可见', '', content)
        if comment['username']:
            content = content.replace(comment['username'], '')
        content = re.sub(r'\s+', ' ', content).strip()

        comment['content'] = content

        # 提取数字信息
        like_match = re.search(r'(\d+)\s*赞', text)
        if like_match:
            comment['likes'] = int(like_match.group(1))

        reply_match = re.search(r'(\d+)\s*回复', text)
        if reply_match:
            comment['replyCount'] = int(reply_match.group(1))

        # 提取用户ID
        user_id_match = re.search(r'小红薯([A-F0-9]{8,})', text)
        if user_id_match:
            comment['userId'] = user_id_match.group(1)

        return comment if len(comment['content']) >= 5 else None

    def deduplicate_comments(self, comments):
        """去重评论"""
        unique = []
        seen = set()

        for comment in comments:
            key = comment['content'][:30]
            if key not in seen:
                seen.add(key)
                unique.append(comment)

        # 重新分配ID
        for i, comment in enumerate(unique):
            comment['id'] = i + 1

        return unique

    def save_to_file(self, data, filename):
        """保存数据到文件"""
        filepath = os.path.join(self.output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"💾 数据已保存到: {filepath}")
        return filepath

    def run(self):
        """主运行方法"""
        try:
            print("🎯 启动简单小红书评论爬取器...")
            print(f"🎯 目标: 获取所有 {self.config['target_comments']} 条评论")
            print("🛡️ 策略: Chrome浏览器 + 手动导航 + 自动滚动爬取")

            # 设置Chrome浏览器
            if not self.setup_chrome():
                print("❌ 无法启动Chrome浏览器")
                return

            # 导航到小红书页面
            if not self.navigate_to_xiaohongshu():
                print("❌ 无法导航到小红书页面")
                return

            # 滚动加载所有评论
            loaded_count = self.scroll_and_load_all_comments()

            # 提取所有评论
            result = self.extract_all_comments()

            # 保存数据
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"xiaohongshu_comments_{result['noteInfo']['id']}_{timestamp}.json"
            self.save_to_file(result, filename)

            # 输出结果
            print("\n🎉 小红书评论爬取完成！")
            print("📊 最终统计:")
            print(f"   📝 笔记ID: {result['noteInfo']['id']}")
            print(f"   📝 笔记标题: {result['noteInfo']['title']}")
            print(f"   👤 笔记作者: {result['noteInfo']['author']}")
            print(f"   🎯 目标评论数: {result['noteInfo']['totalCommentCount']}")
            print(f"   💬 实际提取数: {len(result['comments'])}")

            if result['noteInfo']['totalCommentCount'] > 0:
                completion_rate = round((len(result['comments']) / result['noteInfo']['totalCommentCount']) * 100)
                print(f"   📈 完成度: {completion_rate}%")

            print(f"   📄 页面文本长度: {result['extractStats']['totalTextLength']}")
            print(f"   📁 保存文件: {filename}")

            if result['comments']:
                print("\n👥 评论详细预览:")
                for i, comment in enumerate(result['comments'][:15]):
                    print(f"\n   {i + 1}. 评论ID: {comment['id']}")
                    print(f"      👤 用户: {comment['username'] or '未知'}")
                    print(f"      🆔 用户ID: {comment['userId'] or '未提取到'}")
                    print(f"      ⏰ 时间: {comment['time'] or '未知'}")
                    content_preview = comment['content'][:100]
                    if len(comment['content']) > 100:
                        content_preview += '...'
                    print(f"      💬 内容: {content_preview}")
                    print(f"      👍 点赞: {comment['likes']} | 💬 回复: {comment['replyCount']}")
                    print(f"      📊 来源: {comment['extractedInfo']['source']}")

                # 质量统计
                with_username = len([c for c in result['comments'] if c['username']])
                with_user_id = len([c for c in result['comments'] if c['userId']])
                with_time = len([c for c in result['comments'] if c['time']])
                with_likes = len([c for c in result['comments'] if c['likes'] > 0])
                with_replies = len([c for c in result['comments'] if c['replyCount'] > 0])

                total_comments = len(result['comments'])
                print("\n📈 数据质量统计:")
                print(f"   👤 有用户名: {with_username}/{total_comments} ({round(with_username/total_comments*100)}%)")
                print(f"   🆔 有用户ID: {with_user_id}/{total_comments} ({round(with_user_id/total_comments*100)}%)")
                print(f"   ⏰ 有时间: {with_time}/{total_comments} ({round(with_time/total_comments*100)}%)")
                print(f"   👍 有点赞数: {with_likes}/{total_comments} ({round(with_likes/total_comments*100)}%)")
                print(f"   💬 有回复数: {with_replies}/{total_comments} ({round(with_replies/total_comments*100)}%)")

                # 来源统计
                source_stats = {}
                for comment in result['comments']:
                    source = comment['extractedInfo']['source']
                    source_stats[source] = source_stats.get(source, 0) + 1

                print("\n📊 提取来源统计:")
                for source, count in source_stats.items():
                    percentage = round((count / total_comments) * 100)
                    print(f"   {source}: {count}条 ({percentage}%)")

                # 兼职信息分析
                job_keywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱', '月入']
                job_comments = [c for c in result['comments']
                              if any(keyword in c['content'] for keyword in job_keywords)]

                if job_comments:
                    job_percentage = round((len(job_comments) / total_comments) * 100)
                    print(f"\n💼 兼职相关评论: {len(job_comments)}/{total_comments} ({job_percentage}%)")

                    # 兼职类型分析
                    job_types = {
                        '聊天员': len([c for c in job_comments if '聊天员' in c['content']]),
                        '陪玩': len([c for c in job_comments if '陪玩' in c['content']]),
                        '直播': len([c for c in job_comments if '直播' in c['content']]),
                        '客服': len([c for c in job_comments if '客服' in c['content']]),
                        '求带': len([c for c in job_comments if '求带' in c['content']])
                    }

                    print("\n💼 兼职类型分布:")
                    for job_type, count in job_types.items():
                        if count > 0:
                            percentage = round((count / len(job_comments)) * 100)
                            print(f"   {job_type}: {count}条 ({percentage}%)")

                    print("\n💼 兼职评论示例:")
                    for i, comment in enumerate(job_comments[:10]):
                        username = comment['username'] or '匿名'
                        time_str = comment['time'] or '未知时间'
                        content_preview = comment['content'][:80]
                        if len(comment['content']) > 80:
                            content_preview += '...'
                        print(f"   {i + 1}. {username} ({time_str}): {content_preview}")

                # 成功度评估
                if result['noteInfo']['totalCommentCount'] > 0:
                    completion_rate = len(result['comments']) / result['noteInfo']['totalCommentCount']
                    if completion_rate >= 0.8:
                        print("\n🎉 优秀！已提取到大部分评论数据！")
                    elif completion_rate >= 0.5:
                        print("\n👍 良好！已提取到一半以上的评论数据！")
                    elif completion_rate >= 0.3:
                        print("\n📈 进步！提取效果显著！")
                    else:
                        print("\n💡 提示：可能需要更长时间的滚动或手动干预")

            print(f"\n✅ 爬取任务完成！数据已保存到: {filename}")
            print("🔄 浏览器将保持打开状态，您可以继续查看页面...")

        except Exception as e:
            print(f"❌ 爬取失败: {str(e)}")
        finally:
            # 不自动关闭浏览器，让用户手动关闭
            print("\n💡 请手动关闭浏览器窗口")

def main():
    """主函数"""
    scraper = SimpleXiaohongshuScraper()
    scraper.run()

if __name__ == "__main__":
    main()
