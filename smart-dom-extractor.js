#!/usr/bin/env node

/**
 * 🎯 智能DOM评论提取器
 * 自动检测端口 + 基于DOM结构的精确提取
 */

const puppeteer = require('puppeteer');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

class SmartDOMExtractor {
    constructor() {
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        // 可能的调试端口
        this.possiblePorts = [55276, 9222, 9223, 9224, 9225, 54345, 54346];
        this.workingPort = null;
        
        this.config = {
            maxScrollAttempts: 100,
            scrollDelay: 1500,
            waitForContent: 2000,
            targetComments: 1472
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    // 自动检测可用的调试端口
    async detectWorkingPort() {
        console.log('🔍 自动检测可用的调试端口...');
        
        for (const port of this.possiblePorts) {
            try {
                console.log(`   🔍 尝试端口 ${port}...`);
                
                // 检查端口是否可用
                const response = await axios.get(`http://localhost:${port}/json/version`, {
                    timeout: 3000
                });
                
                if (response.status === 200) {
                    console.log(`   ✅ 端口 ${port} 可用`);
                    this.workingPort = port;
                    return port;
                }
            } catch (error) {
                console.log(`   ❌ 端口 ${port} 不可用`);
            }
        }
        
        throw new Error('未找到可用的调试端口，请确保比特浏览器正在运行');
    }

    async connectToBrowser() {
        try {
            // 自动检测端口
            const port = await this.detectWorkingPort();
            
            console.log(`🔗 连接到比特浏览器 (端口: ${port})...`);
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${port}`,
                defaultViewport: null
            });
            
            const pages = await browser.pages();
            const page = pages.find(p => p.url().includes('xiaohongshu.com/explore/'));
            
            if (!page) {
                throw new Error('未找到小红书笔记页面');
            }
            
            console.log('✅ 找到小红书笔记页面');
            console.log(`📄 当前URL: ${page.url()}`);
            
            return { browser, page };
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    // 智能滚动加载评论
    async smartScrollLoad(page) {
        console.log('🚀 开始智能滚动加载评论...');
        
        let currentCommentCount = 0;
        let previousCommentCount = 0;
        let stableCount = 0;
        let totalScrolls = 0;
        
        // 首先滚动到评论区域
        await this.scrollToCommentSection(page);
        
        for (let i = 0; i < this.config.maxScrollAttempts; i++) {
            console.log(`\n📜 滚动 ${i + 1}/${this.config.maxScrollAttempts}`);
            
            // 统计当前评论数量
            currentCommentCount = await this.countComments(page);
            console.log(`   💬 当前评论数: ${currentCommentCount}`);
            
            // 检查是否达到目标
            if (currentCommentCount >= this.config.targetComments * 0.9) {
                console.log(`🎉 接近目标评论数！当前: ${currentCommentCount}`);
                break;
            }
            
            // 检查进度
            if (currentCommentCount === previousCommentCount) {
                stableCount++;
                console.log(`   ⏸️ 评论数稳定 ${stableCount} 次`);
            } else {
                const newComments = currentCommentCount - previousCommentCount;
                console.log(`   📈 新增评论: ${newComments}`);
                stableCount = 0;
                previousCommentCount = currentCommentCount;
            }
            
            // 如果稳定太久，尝试点击加载更多
            if (stableCount >= 3) {
                console.log('   🔄 尝试点击加载更多...');
                const clickSuccess = await this.clickLoadMore(page);
                if (clickSuccess) {
                    stableCount = 0;
                }
            }
            
            // 执行滚动
            await this.performScroll(page, i);
            
            // 如果长时间无新内容，停止
            if (stableCount >= 10) {
                console.log('   ⏹️ 长时间无新内容，停止滚动');
                break;
            }
            
            await new Promise(resolve => setTimeout(resolve, this.config.scrollDelay));
            totalScrolls++;
        }
        
        console.log(`✅ 滚动完成，总计滚动 ${totalScrolls} 次，最终评论数: ${currentCommentCount}`);
        return currentCommentCount;
    }

    // 滚动到评论区域
    async scrollToCommentSection(page) {
        await page.evaluate(() => {
            // 查找评论区域的多种方式
            const commentIndicators = [
                '条评论',
                '评论',
                'comment'
            ];
            
            for (const indicator of commentIndicators) {
                const elements = Array.from(document.querySelectorAll('*')).filter(el => 
                    el.textContent.includes(indicator) && el.offsetHeight > 0
                );
                
                if (elements.length > 0) {
                    elements[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                    console.log('找到评论区域:', indicator);
                    return;
                }
            }
            
            // 如果没找到，滚动到页面中部
            window.scrollTo(0, document.body.scrollHeight * 0.6);
        });
        
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 执行滚动
    async performScroll(page, scrollIndex) {
        await page.evaluate((index) => {
            const strategies = [
                // 策略1: 小幅滚动
                () => window.scrollBy(0, 300),
                
                // 策略2: 中等滚动
                () => window.scrollBy(0, 500),
                
                // 策略3: 滚动到底部
                () => window.scrollTo(0, document.body.scrollHeight),
                
                // 策略4: 滚动到评论区域
                () => {
                    const comments = document.querySelectorAll('[class*="comment"], [class*="Comment"]');
                    if (comments.length > 0) {
                        const lastComment = comments[comments.length - 1];
                        lastComment.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            ];
            
            // 根据滚动次数选择策略
            const strategyIndex = index % strategies.length;
            strategies[strategyIndex]();
        }, scrollIndex);
    }

    // 点击加载更多
    async clickLoadMore(page) {
        try {
            const clickResults = await page.evaluate(() => {
                const results = [];
                const loadMoreTexts = [
                    '加载更多', '展开更多', '查看更多', '显示更多', '更多评论',
                    '展开', '更多', '加载', '查看全部', '展开全部'
                ];
                
                loadMoreTexts.forEach(text => {
                    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                        const elementText = el.textContent.trim();
                        return elementText.includes(text) && 
                               el.offsetHeight > 0 && 
                               el.offsetWidth > 0 &&
                               elementText.length < 100;
                    });
                    
                    elements.forEach(el => {
                        try {
                            const rect = el.getBoundingClientRect();
                            if (rect.top >= 0 && rect.top <= window.innerHeight) {
                                el.click();
                                results.push(`点击: ${text}`);
                            }
                        } catch (e) {
                            // 忽略点击失败
                        }
                    });
                });
                
                return results;
            });
            
            if (clickResults.length > 0) {
                console.log(`   ✅ 点击结果: ${clickResults.join(', ')}`);
                return true;
            }
            return false;
        } catch (error) {
            console.log(`   ❌ 点击出错: ${error.message}`);
            return false;
        }
    }

    // 统计评论数量
    async countComments(page) {
        return await page.evaluate(() => {
            const pageText = document.body.textContent;
            
            // 多种统计方法
            const methods = [
                // 方法1: 时间格式
                () => (pageText.match(/\d{2}-\d{2}/g) || []).length,
                
                // 方法2: 回复数
                () => (pageText.match(/\d+回复/g) || []).length,
                
                // 方法3: DOM元素
                () => {
                    const selectors = [
                        '[class*="comment"]',
                        '[class*="Comment"]',
                        '[data-testid*="comment"]'
                    ];
                    
                    let maxCount = 0;
                    selectors.forEach(selector => {
                        try {
                            const count = document.querySelectorAll(selector).length;
                            maxCount = Math.max(maxCount, count);
                        } catch (e) {
                            // 忽略选择器错误
                        }
                    });
                    
                    return maxCount;
                },
                
                // 方法4: 关键词
                () => {
                    const keywords = ['求带', '宝子', '学姐', '兼职', '聊天员'];
                    let total = 0;
                    keywords.forEach(keyword => {
                        const matches = (pageText.match(new RegExp(keyword, 'g')) || []).length;
                        total += matches;
                    });
                    return Math.floor(total / 2);
                }
            ];
            
            const counts = methods.map(method => {
                try {
                    return method();
                } catch (e) {
                    return 0;
                }
            });
            
            return Math.max(...counts);
        });
    }

    // 智能评论提取
    async smartExtractComments(page) {
        console.log('🧠 开始智能评论提取...');

        const result = await page.evaluate(() => {
            const data = {
                noteInfo: {
                    id: '',
                    title: '',
                    author: '',
                    totalCommentCount: 0
                },
                comments: [],
                extractStats: {
                    totalTextLength: 0,
                    extractionMethods: [],
                    successfulExtractions: 0
                },
                extractTime: new Date().toISOString()
            };

            try {
                // 提取基本信息
                const pageText = document.body.textContent;
                data.extractStats.totalTextLength = pageText.length;

                // 提取笔记ID
                const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
                if (urlMatch) {
                    data.noteInfo.id = urlMatch[1];
                }

                // 提取标题
                const titleElement = document.querySelector('title');
                if (titleElement) {
                    data.noteInfo.title = titleElement.textContent.replace(' - 小红书', '');
                }

                // 提取评论总数
                const commentCountMatch = pageText.match(/(\d+)\s*条评论/);
                if (commentCountMatch) {
                    data.noteInfo.totalCommentCount = parseInt(commentCountMatch[1]);
                }

                // 提取作者
                if (pageText.includes('漫娴学姐')) {
                    data.noteInfo.author = '漫娴学姐 招暑假工版';
                }

                return data;
            } catch (error) {
                console.error('提取基本信息时出错:', error);
                data.error = error.message;
                return data;
            }
        });

        // 在Node.js中进行评论解析
        const pageText = await page.evaluate(() => document.body.textContent);
        const comments = this.parseComments(pageText);
        result.comments = comments;
        result.extractStats.successfulExtractions = comments.length;

        return result;
    }

    // 解析评论
    parseComments(pageText) {
        console.log('🔍 开始解析评论...');
        console.log(`📄 页面文本长度: ${pageText.length}`);

        const allComments = [];

        // 使用多种解析策略
        const strategies = [
            { name: '时间模式', method: () => this.parseByTime(pageText) },
            { name: '关键词模式', method: () => this.parseByKeywords(pageText) },
            { name: '用户模式', method: () => this.parseByUsers(pageText) },
            { name: '结构模式', method: () => this.parseByStructure(pageText) }
        ];

        strategies.forEach(strategy => {
            try {
                console.log(`🔍 执行${strategy.name}解析...`);
                const strategyComments = strategy.method();
                console.log(`   📝 ${strategy.name}提取到 ${strategyComments.length} 条评论`);
                allComments.push(...strategyComments);
            } catch (error) {
                console.log(`   ❌ ${strategy.name}解析失败: ${error.message}`);
            }
        });

        // 去重和清理
        const uniqueComments = this.deduplicateComments(allComments);

        console.log(`✅ 解析完成，最终提取到 ${uniqueComments.length} 条评论`);
        return uniqueComments;
    }

    // 按时间解析
    parseByTime(pageText) {
        const comments = [];
        const timePattern = /(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/g;
        const timeMatches = [];
        let match;

        while ((match = timePattern.exec(pageText)) !== null) {
            timeMatches.push({
                time: match[1],
                index: match.index
            });
        }

        for (let i = 0; i < timeMatches.length; i++) {
            const currentTime = timeMatches[i];
            const nextTime = timeMatches[i + 1];

            const startIndex = Math.max(0, currentTime.index - 150);
            const endIndex = nextTime ? nextTime.index : currentTime.index + 800;

            const commentText = pageText.substring(startIndex, endIndex).trim();

            if (commentText.length > 20 && commentText.length < 1500) {
                const comment = this.createComment(commentText, comments.length + 1, 'time');
                if (comment) {
                    comments.push(comment);
                }
            }
        }

        return comments;
    }

    // 按关键词解析
    parseByKeywords(pageText) {
        const comments = [];
        const keywords = [
            '求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯',
            'Carina', '广州', '工作', '赚钱', '日入', '月入'
        ];

        keywords.forEach(keyword => {
            const regex = new RegExp(keyword, 'g');
            let match;

            while ((match = regex.exec(pageText)) !== null) {
                const startIndex = Math.max(0, match.index - 100);
                const endIndex = Math.min(pageText.length, match.index + 600);

                const commentText = pageText.substring(startIndex, endIndex).trim();

                if (commentText.length > 15 && commentText.length < 1200) {
                    const comment = this.createComment(commentText, comments.length + 1, 'keyword');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });

        return comments;
    }

    // 按用户解析
    parseByUsers(pageText) {
        const comments = [];
        const userPatterns = [
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(\d{2}-\d{2})/g,
            /小红薯([A-F0-9]{8,})/g,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带/g
        ];

        userPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                const startIndex = Math.max(0, match.index - 80);
                const endIndex = Math.min(pageText.length, match.index + 500);

                const commentText = pageText.substring(startIndex, endIndex).trim();

                if (commentText.length > 10 && commentText.length < 1000) {
                    const comment = this.createComment(commentText, comments.length + 1, 'user');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });

        return comments;
    }

    // 按结构解析
    parseByStructure(pageText) {
        const comments = [];
        const lines = pageText.split('\n').map(line => line.trim()).filter(line => line.length > 3);

        let currentComment = null;
        let commentIndex = 0;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            if (this.shouldSkipLine(line)) continue;

            if (this.isCommentStart(line)) {
                if (currentComment && this.isValidComment(currentComment.content)) {
                    const comment = this.createComment(currentComment.content, ++commentIndex, 'structure');
                    if (comment) {
                        comments.push(comment);
                    }
                }

                currentComment = {
                    content: line
                };
            } else if (currentComment && line.length > 2) {
                currentComment.content += ' ' + line;

                if (currentComment.content.length > 1500) {
                    const comment = this.createComment(currentComment.content, ++commentIndex, 'structure');
                    if (comment) {
                        comments.push(comment);
                    }
                    currentComment = null;
                }
            }
        }

        if (currentComment && this.isValidComment(currentComment.content)) {
            const comment = this.createComment(currentComment.content, ++commentIndex, 'structure');
            if (comment) {
                comments.push(comment);
            }
        }

        return comments;
    }

    // 创建评论对象
    createComment(text, id, source) {
        const comment = {
            id: id,
            userId: '',
            username: '',
            content: '',
            time: '',
            likes: 0,
            replyCount: 0,
            isAuthor: text.includes('作者'),
            isPinned: text.includes('置顶'),
            extractedInfo: {
                source: source,
                originalLength: text.length
            }
        };

        // 提取时间
        const timeMatch = text.match(/(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/);
        if (timeMatch) {
            comment.time = timeMatch[1];
        }

        // 提取用户名
        const userPatterns = [
            /^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带/,
            /^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})/
        ];

        for (const pattern of userPatterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                comment.username = match[1].trim();
                break;
            }
        }

        // 清理内容
        let content = text;
        content = content.replace(/\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前/g, '');
        content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');
        content = content.replace(/作者|置顶评论|回复|赞|仅自己可见/g, '');
        if (comment.username) {
            content = content.replace(comment.username, '');
        }
        content = content.replace(/\s+/g, ' ').trim();

        comment.content = content;

        // 提取数字信息
        const likeMatch = text.match(/(\d+)\s*赞/);
        if (likeMatch) {
            comment.likes = parseInt(likeMatch[1]);
        }

        const replyMatch = text.match(/(\d+)\s*回复/);
        if (replyMatch) {
            comment.replyCount = parseInt(replyMatch[1]);
        }

        // 提取用户ID
        const userIdMatch = text.match(/小红薯([A-F0-9]{8,})/);
        if (userIdMatch) {
            comment.userId = userIdMatch[1];
        }

        return comment.content.length >= 5 ? comment : null;
    }

    // 辅助方法
    shouldSkipLine(line) {
        const skipPatterns = [
            '加载中', '你还没有', '温馨提示', '广告屏蔽', '我知道了',
            '沪ICP备', '营业执照', '公网安备', '增值电信', '医疗器械',
            'window.', 'function', 'console.', 'document.'
        ];

        return skipPatterns.some(pattern => line.includes(pattern)) ||
               line.length < 3 ||
               /^[0-9\s\-:\.]+$/.test(line);
    }

    isCommentStart(line) {
        return line.includes('作者') ||
               line.includes('置顶') ||
               line.match(/\d{2}-\d{2}/) ||
               line.match(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/) ||
               ['求带', '宝子', '学姐', '兼职'].some(keyword => line.includes(keyword));
    }

    isValidComment(content) {
        return content && content.length >= 10 && content.length <= 1500;
    }

    // 去重
    deduplicateComments(comments) {
        const unique = [];
        const seen = new Set();

        comments.forEach(comment => {
            const key = comment.content.substring(0, 30);
            if (!seen.has(key)) {
                seen.add(key);
                unique.push(comment);
            }
        });

        // 重新分配ID
        unique.forEach((comment, index) => {
            comment.id = index + 1;
        });

        return unique;
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        try {
            console.log('🎯 启动智能DOM评论提取器...');
            console.log(`🎯 目标: 获取所有 ${this.config.targetComments} 条评论`);
            console.log('🛡️ 策略: 自动端口检测 + 智能滚动 + 多重解析');

            const { browser, page } = await this.connectToBrowser();

            // 智能滚动加载
            const loadedCommentCount = await this.smartScrollLoad(page);

            // 智能评论提取
            const result = await this.smartExtractComments(page);

            // 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `smart_comments_${result.noteInfo.id}_${timestamp}.json`;
            this.saveToFile(result, filename);

            // 输出结果
            console.log('\n🎉 智能提取完成！');
            console.log('📊 最终统计:');
            console.log(`   🆔 笔记ID: ${result.noteInfo.id}`);
            console.log(`   📝 笔记标题: ${result.noteInfo.title || '未知'}`);
            console.log(`   👤 笔记作者: ${result.noteInfo.author || '未知'}`);
            console.log(`   🎯 目标评论数: ${result.noteInfo.totalCommentCount}`);
            console.log(`   💬 实际提取数: ${result.comments.length}`);
            console.log(`   📈 完成度: ${Math.round(result.comments.length / result.noteInfo.totalCommentCount * 100)}%`);
            console.log(`   📄 页面文本长度: ${result.extractStats.totalTextLength}`);
            console.log(`   📁 保存文件: ${filename}`);

            if (result.comments.length > 0) {
                console.log('\n👥 评论详细预览:');
                result.comments.slice(0, 15).forEach((comment, index) => {
                    console.log(`\n   ${index + 1}. 评论ID: ${comment.id}`);
                    console.log(`      👤 用户: ${comment.username || '未知'}`);
                    console.log(`      🆔 用户ID: ${comment.userId || '未提取到'}`);
                    console.log(`      ⏰ 时间: ${comment.time || '未知'}`);
                    console.log(`      💬 内容: ${comment.content.substring(0, 100)}${comment.content.length > 100 ? '...' : ''}`);
                    console.log(`      👍 点赞: ${comment.likes} | 💬 回复: ${comment.replyCount}`);
                    console.log(`      🏷️ 标识: ${comment.isAuthor ? '作者' : ''}${comment.isPinned ? '置顶' : ''}${!comment.isAuthor && !comment.isPinned ? '普通' : ''}`);
                    console.log(`      📊 来源: ${comment.extractedInfo?.source || '未知'}`);
                });

                // 质量统计
                const withUsername = result.comments.filter(c => c.username).length;
                const withUserId = result.comments.filter(c => c.userId).length;
                const withTime = result.comments.filter(c => c.time).length;
                const withLikes = result.comments.filter(c => c.likes > 0).length;
                const withReplies = result.comments.filter(c => c.replyCount > 0).length;

                console.log('\n📈 数据质量统计:');
                console.log(`   👤 有用户名: ${withUsername}/${result.comments.length} (${Math.round(withUsername/result.comments.length*100)}%)`);
                console.log(`   🆔 有用户ID: ${withUserId}/${result.comments.length} (${Math.round(withUserId/result.comments.length*100)}%)`);
                console.log(`   ⏰ 有时间: ${withTime}/${result.comments.length} (${Math.round(withTime/result.comments.length*100)}%)`);
                console.log(`   👍 有点赞数: ${withLikes}/${result.comments.length} (${Math.round(withLikes/result.comments.length*100)}%)`);
                console.log(`   💬 有回复数: ${withReplies}/${result.comments.length} (${Math.round(withReplies/result.comments.length*100)}%)`);

                // 来源统计
                const sourceStats = {};
                result.comments.forEach(comment => {
                    const source = comment.extractedInfo?.source || 'unknown';
                    sourceStats[source] = (sourceStats[source] || 0) + 1;
                });

                console.log('\n📊 提取来源统计:');
                Object.entries(sourceStats).forEach(([source, count]) => {
                    const percentage = Math.round((count / result.comments.length) * 100);
                    console.log(`   ${source}: ${count}条 (${percentage}%)`);
                });

                // 成功度评估
                const completionRate = result.comments.length / result.noteInfo.totalCommentCount;
                if (completionRate >= 0.8) {
                    console.log('\n🎉 优秀！已提取到大部分评论数据！');
                } else if (completionRate >= 0.5) {
                    console.log('\n👍 良好！已提取到一半以上的评论数据！');
                } else if (completionRate >= 0.3) {
                    console.log('\n📈 进步！智能提取效果显著！');
                } else {
                    console.log('\n💡 提示：可能需要更长时间的滚动或手动干预');
                }

                // 兼职信息分析
                const jobKeywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱', '月入'];
                const jobComments = result.comments.filter(c =>
                    jobKeywords.some(keyword => c.content.includes(keyword))
                );

                if (jobComments.length > 0) {
                    console.log(`\n💼 兼职相关评论: ${jobComments.length}/${result.comments.length} (${Math.round(jobComments.length/result.comments.length*100)}%)`);

                    // 兼职类型分析
                    const jobTypes = {
                        '聊天员': jobComments.filter(c => c.content.includes('聊天员')).length,
                        '陪玩': jobComments.filter(c => c.content.includes('陪玩')).length,
                        '直播': jobComments.filter(c => c.content.includes('直播')).length,
                        '客服': jobComments.filter(c => c.content.includes('客服')).length,
                        '代理': jobComments.filter(c => c.content.includes('代理')).length,
                        '抢票': jobComments.filter(c => c.content.includes('演唱会') || c.content.includes('抢票')).length,
                        '求带': jobComments.filter(c => c.content.includes('求带')).length
                    };

                    console.log('\n💼 兼职类型分布:');
                    Object.entries(jobTypes).forEach(([type, count]) => {
                        if (count > 0) {
                            const percentage = Math.round((count / jobComments.length) * 100);
                            console.log(`   ${type}: ${count}条 (${percentage}%)`);
                        }
                    });

                    // 显示兼职评论示例
                    console.log('\n💼 兼职评论示例:');
                    jobComments.slice(0, 8).forEach((comment, index) => {
                        console.log(`   ${index + 1}. ${comment.username || '匿名'} (${comment.time || '未知时间'}): ${comment.content.substring(0, 80)}...`);
                    });

                    // 收入信息分析
                    const incomeComments = jobComments.filter(c =>
                        c.content.match(/\d+元|\d+块|日入\d+|月入\d+|\d+一天/)
                    );

                    if (incomeComments.length > 0) {
                        console.log(`\n💰 包含收入信息的评论: ${incomeComments.length}条`);

                        incomeComments.slice(0, 3).forEach((comment, index) => {
                            console.log(`   ${index + 1}. ${comment.content.substring(0, 100)}...`);
                        });
                    }
                }

                // 用户活跃度分析
                const userStats = {};
                result.comments.forEach(comment => {
                    if (comment.username) {
                        userStats[comment.username] = (userStats[comment.username] || 0) + 1;
                    }
                });

                const activeUsers = Object.entries(userStats)
                    .filter(([username, count]) => count > 1)
                    .sort((a, b) => b[1] - a[1]);

                if (activeUsers.length > 0) {
                    console.log(`\n👥 活跃用户: ${activeUsers.length}个用户有多条评论`);
                    console.log('👥 最活跃用户:');
                    activeUsers.slice(0, 5).forEach(([username, count], index) => {
                        console.log(`   ${index + 1}. ${username}: ${count}条评论`);
                    });
                }
            }

            await browser.disconnect();

            console.log('\n✅ 智能提取任务完成！');
            console.log(`📁 数据文件: ${filename}`);
            console.log(`🔗 使用端口: ${this.workingPort}`);

        } catch (error) {
            console.error('❌ 智能提取失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行智能提取器
if (require.main === module) {
    const extractor = new SmartDOMExtractor();
    extractor.run();
}

module.exports = SmartDOMExtractor;
