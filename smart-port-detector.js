#!/usr/bin/env node

/**
 * 🔍 智能端口检测器
 * 自动检测比特浏览器的调试端口并提取评论
 */

const axios = require('axios');
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class SmartPortDetector {
    constructor() {
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        // 扩展端口范围
        this.possiblePorts = [
            // 常见Chrome调试端口
            9222, 9223, 9224, 9225, 9226, 9227, 9228, 9229,
            // 比特浏览器可能的端口
            55276, 55277, 55278, 55279, 55280,
            54345, 54346, 54347, 54348, 54349,
            // 其他可能的端口
            8080, 8081, 8082, 8083, 8084,
            3000, 3001, 3002, 3003, 3004,
            // 高端口范围
            56900, 56901, 56902, 56903, 56904, 56905, 56906, 56907, 56908, 56909,
            57000, 57001, 57002, 57003, 57004, 57005
        ];
        
        this.config = {
            targetComments: 1472,
            maxScrollAttempts: 600,
            scrollDelay: 700,
            aggressiveMode: true
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    // 智能检测调试端口
    async detectDebugPort() {
        console.log('🔍 开始智能检测调试端口...');
        console.log(`🎯 将检测 ${this.possiblePorts.length} 个可能的端口`);
        
        const workingPorts = [];
        
        for (const port of this.possiblePorts) {
            try {
                console.log(`   🔍 检测端口 ${port}...`);
                
                // 检查是否是Chrome DevTools端口
                const response = await axios.get(`http://localhost:${port}/json/version`, {
                    timeout: 2000
                });
                
                if (response.status === 200 && response.data) {
                    const data = response.data;
                    console.log(`   ✅ 端口 ${port} 响应成功`);
                    console.log(`      浏览器: ${data.Browser || '未知'}`);
                    console.log(`      版本: ${data['Protocol-Version'] || '未知'}`);
                    console.log(`      WebSocket: ${data.webSocketDebuggerUrl ? '支持' : '不支持'}`);
                    
                    // 检查是否有页面
                    try {
                        const pagesResponse = await axios.get(`http://localhost:${port}/json`, {
                            timeout: 2000
                        });
                        
                        if (pagesResponse.status === 200 && Array.isArray(pagesResponse.data)) {
                            const pages = pagesResponse.data;
                            console.log(`      页面数: ${pages.length}`);
                            
                            // 查找小红书页面
                            const xhsPages = pages.filter(page => 
                                page.url && (
                                    page.url.includes('xiaohongshu.com') ||
                                    page.url.includes('xhs') ||
                                    page.url.includes('redbook')
                                )
                            );
                            
                            if (xhsPages.length > 0) {
                                console.log(`      🎯 找到 ${xhsPages.length} 个小红书页面！`);
                                xhsPages.forEach((page, index) => {
                                    console.log(`         ${index + 1}. ${page.title || '无标题'} - ${page.url.substring(0, 60)}...`);
                                });
                            }
                            
                            workingPorts.push({
                                port: port,
                                browser: data.Browser || '未知',
                                version: data['Protocol-Version'] || '未知',
                                pageCount: pages.length,
                                xhsPageCount: xhsPages.length,
                                pages: pages
                            });
                        }
                    } catch (pageError) {
                        console.log(`      ⚠️ 无法获取页面列表: ${pageError.message}`);
                    }
                }
            } catch (error) {
                // 静默失败，只在详细模式下显示
                if (error.code !== 'ECONNREFUSED' && error.code !== 'ETIMEDOUT') {
                    console.log(`   ❌ 端口 ${port} 错误: ${error.message}`);
                }
            }
        }
        
        console.log(`\n📊 检测完成，找到 ${workingPorts.length} 个可用端口`);
        
        if (workingPorts.length === 0) {
            throw new Error('未找到任何可用的调试端口，请确保比特浏览器正在运行并启用了调试模式');
        }
        
        // 选择最佳端口（优先选择有小红书页面的）
        const bestPort = workingPorts.find(p => p.xhsPageCount > 0) || workingPorts[0];
        
        console.log(`\n🎯 选择最佳端口: ${bestPort.port}`);
        console.log(`   浏览器: ${bestPort.browser}`);
        console.log(`   页面数: ${bestPort.pageCount}`);
        console.log(`   小红书页面数: ${bestPort.xhsPageCount}`);
        
        return bestPort;
    }

    // 连接到最佳端口
    async connectToBestPort() {
        try {
            const bestPortInfo = await this.detectDebugPort();
            
            console.log(`🔗 连接到端口 ${bestPortInfo.port}...`);
            
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${bestPortInfo.port}`,
                defaultViewport: null
            });
            
            console.log('✅ 成功连接到浏览器');
            
            const pages = await browser.pages();
            console.log(`📄 当前有 ${pages.length} 个页面`);
            
            // 查找小红书页面
            let targetPage = null;
            for (let i = 0; i < pages.length; i++) {
                const page = pages[i];
                const url = page.url();
                const title = await page.title().catch(() => '无标题');
                
                console.log(`   页面 ${i + 1}: ${title} - ${url.substring(0, 80)}...`);
                
                if (url.includes('xiaohongshu.com/explore/') || 
                    url.includes('xhs') || 
                    url.includes('redbook')) {
                    targetPage = page;
                    console.log(`✅ 选择小红书页面: 页面 ${i + 1}`);
                    break;
                }
            }
            
            // 如果没找到小红书页面，使用第一个页面
            if (!targetPage && pages.length > 0) {
                targetPage = pages[0];
                console.log(`💡 使用第一个页面作为目标`);
            }
            
            if (!targetPage) {
                throw new Error('没有找到可用的页面');
            }
            
            console.log(`🎯 目标页面: ${await targetPage.title()}`);
            console.log(`🔗 页面URL: ${targetPage.url()}`);
            
            return { browser, page: targetPage, port: bestPortInfo.port };
            
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    // 智能滚动加载
    async smartScrollLoad(page) {
        console.log('🚀 开始智能滚动加载...');
        console.log(`🎯 目标: ${this.config.targetComments} 条评论`);
        
        let currentCommentCount = 0;
        let previousCommentCount = 0;
        let stableCount = 0;
        let totalScrolls = 0;
        let totalClicks = 0;
        
        // 导航到评论区域
        await this.navigateToComments(page);
        
        for (let i = 0; i < this.config.maxScrollAttempts; i++) {
            console.log(`\n📜 智能滚动 ${i + 1}/${this.config.maxScrollAttempts}`);
            
            // 统计评论数量
            currentCommentCount = await this.countComments(page);
            console.log(`   💬 当前评论数: ${currentCommentCount}`);
            
            // 检查是否达到目标
            if (currentCommentCount >= this.config.targetComments * 0.95) {
                console.log(`🎉 接近目标！当前: ${currentCommentCount}/${this.config.targetComments}`);
                break;
            }
            
            // 检查进度
            if (currentCommentCount === previousCommentCount) {
                stableCount++;
                console.log(`   ⏸️ 评论数稳定 ${stableCount} 次`);
            } else {
                const newComments = currentCommentCount - previousCommentCount;
                console.log(`   📈 新增评论: ${newComments} 条`);
                stableCount = 0;
                previousCommentCount = currentCommentCount;
            }
            
            // 智能点击策略
            if (stableCount >= 3) {
                console.log('   🔄 智能点击...');
                const clickSuccess = await this.smartClick(page);
                if (clickSuccess) {
                    totalClicks++;
                    stableCount = 0;
                    console.log(`   ✅ 点击成功！总计: ${totalClicks} 次`);
                }
            }
            
            // 执行智能滚动
            await this.executeSmartScroll(page, i);
            
            // 如果长时间无新内容，停止
            if (stableCount >= 15) {
                console.log('   ⏹️ 长时间无新内容，停止滚动');
                break;
            }
            
            await new Promise(resolve => setTimeout(resolve, this.config.scrollDelay));
            totalScrolls++;
            
            // 每50次滚动输出进度
            if (i % 50 === 0 && i > 0) {
                const progress = Math.round((currentCommentCount / this.config.targetComments) * 100);
                console.log(`\n📊 进度报告:`);
                console.log(`   📈 完成度: ${progress}% (${currentCommentCount}/${this.config.targetComments})`);
                console.log(`   📜 总滚动次数: ${totalScrolls}`);
                console.log(`   🔄 总点击次数: ${totalClicks}`);
            }
        }
        
        console.log(`\n✅ 智能滚动完成！`);
        console.log(`📊 最终统计:`);
        console.log(`   💬 最终评论数: ${currentCommentCount}`);
        console.log(`   📈 完成度: ${Math.round((currentCommentCount / this.config.targetComments) * 100)}%`);
        console.log(`   📜 总滚动次数: ${totalScrolls}`);
        console.log(`   🔄 总点击次数: ${totalClicks}`);
        
        return currentCommentCount;
    }

    // 导航到评论区域
    async navigateToComments(page) {
        await page.evaluate(() => {
            // 查找评论区域
            const commentIndicators = ['条评论', '评论', 'comment'];
            
            for (const indicator of commentIndicators) {
                const elements = Array.from(document.querySelectorAll('*')).filter(el => 
                    el.textContent.includes(indicator) && el.offsetHeight > 0
                );
                
                if (elements.length > 0) {
                    elements[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                    console.log('导航到评论区域:', indicator);
                    return;
                }
            }
            
            // 如果没找到，滚动到页面中部
            window.scrollTo(0, document.body.scrollHeight * 0.6);
        });
        
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 执行智能滚动
    async executeSmartScroll(page, scrollIndex) {
        const strategies = [
            () => page.evaluate(() => window.scrollBy(0, 200 + Math.random() * 100)),
            () => page.evaluate(() => window.scrollBy(0, 400 + Math.random() * 200)),
            () => page.evaluate(() => window.scrollBy(0, 600 + Math.random() * 300)),
            () => page.evaluate(() => window.scrollTo(0, document.body.scrollHeight)),
            () => page.evaluate(() => {
                const comments = document.querySelectorAll('[class*="comment"], [class*="Comment"]');
                if (comments.length > 0) {
                    const lastComment = comments[comments.length - 1];
                    lastComment.scrollIntoView({ behavior: 'smooth' });
                }
            })
        ];
        
        const strategyIndex = scrollIndex % strategies.length;
        await strategies[strategyIndex]();
    }

    // 智能点击
    async smartClick(page) {
        try {
            const clickResults = await page.evaluate(() => {
                const results = [];
                const loadMoreTexts = [
                    '加载更多', '展开更多', '查看更多', '显示更多', '更多评论',
                    '展开', '更多', '加载', '查看全部', '展开全部'
                ];
                
                loadMoreTexts.forEach(text => {
                    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                        const elementText = el.textContent.trim();
                        return elementText.includes(text) && 
                               el.offsetHeight > 0 && 
                               el.offsetWidth > 0 &&
                               elementText.length < 200;
                    });
                    
                    elements.forEach(el => {
                        try {
                            el.click();
                            results.push(`点击: ${text}`);
                        } catch (e) {
                            // 忽略点击失败
                        }
                    });
                });
                
                return results;
            });
            
            if (clickResults.length > 0) {
                console.log(`   ✅ 智能点击结果: ${clickResults.join(', ')}`);
                return true;
            }
            return false;
        } catch (error) {
            console.log(`   ❌ 智能点击出错: ${error.message}`);
            return false;
        }
    }

    // 统计评论数量
    async countComments(page) {
        return await page.evaluate(() => {
            const pageText = document.body.textContent;
            
            const methods = [
                () => (pageText.match(/\d{2}-\d{2}/g) || []).length,
                () => (pageText.match(/\d+回复/g) || []).length,
                () => {
                    const selectors = ['[class*="comment"]', '[class*="Comment"]'];
                    let maxCount = 0;
                    selectors.forEach(selector => {
                        try {
                            const count = document.querySelectorAll(selector).length;
                            maxCount = Math.max(maxCount, count);
                        } catch (e) {
                            // 忽略选择器错误
                        }
                    });
                    return maxCount;
                },
                () => {
                    const keywords = ['求带', '宝子', '学姐', '兼职', '聊天员'];
                    let total = 0;
                    keywords.forEach(keyword => {
                        const matches = (pageText.match(new RegExp(keyword, 'g')) || []).length;
                        total += matches;
                    });
                    return Math.floor(total / 2);
                }
            ];
            
            const counts = methods.map(method => {
                try {
                    return method();
                } catch (e) {
                    return 0;
                }
            });
            
            return Math.max(...counts);
        });
    }

    // 提取评论
    async extractComments(page) {
        console.log('🧠 开始提取评论...');

        // 获取页面文本
        const pageText = await page.evaluate(() => document.body.textContent);
        console.log(`📄 页面文本长度: ${pageText.length}`);

        // 提取基本信息
        const noteInfo = await page.evaluate(() => {
            const info = {
                id: '',
                title: '',
                author: '',
                totalCommentCount: 0
            };

            // 提取笔记ID
            const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
            if (urlMatch) {
                info.id = urlMatch[1];
            }

            // 提取标题
            const titleElement = document.querySelector('title');
            if (titleElement) {
                info.title = titleElement.textContent.replace(' - 小红书', '');
            }

            // 提取评论总数
            const pageText = document.body.textContent;
            const commentCountMatch = pageText.match(/(\d+)\s*条评论/);
            if (commentCountMatch) {
                info.totalCommentCount = parseInt(commentCountMatch[1]);
            }

            // 提取作者
            if (pageText.includes('漫娴学姐')) {
                info.author = '漫娴学姐 招暑假工版';
            }

            return info;
        });

        // 解析评论
        const comments = this.parseComments(pageText);

        return {
            noteInfo,
            comments,
            extractStats: {
                totalTextLength: pageText.length,
                successfulExtractions: comments.length,
                extractionMethods: ['smart-port-detector']
            },
            extractTime: new Date().toISOString()
        };
    }

    // 解析评论
    parseComments(pageText) {
        console.log('🔍 开始解析评论...');

        const allComments = [];

        // 时间模式解析
        const timePattern = /(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/g;
        const timeMatches = [];
        let match;

        while ((match = timePattern.exec(pageText)) !== null) {
            timeMatches.push({
                time: match[1],
                index: match.index
            });
        }

        for (let i = 0; i < timeMatches.length; i++) {
            const currentTime = timeMatches[i];
            const nextTime = timeMatches[i + 1];

            const startIndex = Math.max(0, currentTime.index - 150);
            const endIndex = nextTime ? nextTime.index : currentTime.index + 800;

            const commentText = pageText.substring(startIndex, endIndex).trim();

            if (commentText.length > 20 && commentText.length < 1500) {
                const comment = this.createComment(commentText, allComments.length + 1, 'time');
                if (comment) {
                    allComments.push(comment);
                }
            }
        }

        // 关键词模式解析
        const keywords = ['求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯'];

        keywords.forEach(keyword => {
            const regex = new RegExp(keyword, 'g');
            let match;

            while ((match = regex.exec(pageText)) !== null) {
                const startIndex = Math.max(0, match.index - 100);
                const endIndex = Math.min(pageText.length, match.index + 600);

                const commentText = pageText.substring(startIndex, endIndex).trim();

                if (commentText.length > 15 && commentText.length < 1200) {
                    const comment = this.createComment(commentText, allComments.length + 1, 'keyword');
                    if (comment) {
                        allComments.push(comment);
                    }
                }
            }
        });

        // 去重
        const uniqueComments = this.deduplicateComments(allComments);

        console.log(`✅ 解析完成，提取到 ${uniqueComments.length} 条评论`);
        return uniqueComments;
    }

    // 创建评论对象
    createComment(text, id, source) {
        const comment = {
            id: id,
            userId: '',
            username: '',
            content: '',
            time: '',
            likes: 0,
            replyCount: 0,
            isAuthor: text.includes('作者'),
            isPinned: text.includes('置顶'),
            extractedInfo: {
                source: source,
                originalLength: text.length
            }
        };

        // 提取时间
        const timeMatch = text.match(/(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)/);
        if (timeMatch) {
            comment.time = timeMatch[1];
        }

        // 提取用户名
        const userPatterns = [
            /^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带/,
            /^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})/
        ];

        for (const pattern of userPatterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                comment.username = match[1].trim();
                break;
            }
        }

        // 清理内容
        let content = text;
        content = content.replace(/\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前/g, '');
        content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');
        content = content.replace(/作者|置顶评论|回复|赞|仅自己可见/g, '');
        if (comment.username) {
            content = content.replace(comment.username, '');
        }
        content = content.replace(/\s+/g, ' ').trim();

        comment.content = content;

        // 提取数字信息
        const likeMatch = text.match(/(\d+)\s*赞/);
        if (likeMatch) {
            comment.likes = parseInt(likeMatch[1]);
        }

        const replyMatch = text.match(/(\d+)\s*回复/);
        if (replyMatch) {
            comment.replyCount = parseInt(replyMatch[1]);
        }

        // 提取用户ID
        const userIdMatch = text.match(/小红薯([A-F0-9]{8,})/);
        if (userIdMatch) {
            comment.userId = userIdMatch[1];
        }

        return comment.content.length >= 5 ? comment : null;
    }

    // 去重
    deduplicateComments(comments) {
        const unique = [];
        const seen = new Set();

        comments.forEach(comment => {
            const key = comment.content.substring(0, 30);
            if (!seen.has(key)) {
                seen.add(key);
                unique.push(comment);
            }
        });

        // 重新分配ID
        unique.forEach((comment, index) => {
            comment.id = index + 1;
        });

        return unique;
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        try {
            console.log('🎯 启动智能端口检测评论提取器...');
            console.log(`🎯 目标: 获取所有 ${this.config.targetComments} 条评论`);
            console.log('🛡️ 策略: 智能端口检测 + 智能滚动 + 精确解析');

            const { browser, page, port } = await this.connectToBestPort();

            // 智能滚动加载
            const loadedCommentCount = await this.smartScrollLoad(page);

            // 提取评论
            const result = await this.extractComments(page);

            // 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `smart_comments_${result.noteInfo.id}_${timestamp}.json`;
            this.saveToFile(result, filename);

            // 输出结果
            console.log('\n🎉 智能提取完成！');
            console.log('📊 最终统计:');
            console.log(`   🔗 使用端口: ${port}`);
            console.log(`   📝 笔记ID: ${result.noteInfo.id}`);
            console.log(`   📝 笔记标题: ${result.noteInfo.title || '未知'}`);
            console.log(`   👤 笔记作者: ${result.noteInfo.author || '未知'}`);
            console.log(`   🎯 目标评论数: ${result.noteInfo.totalCommentCount}`);
            console.log(`   💬 实际提取数: ${result.comments.length}`);
            console.log(`   📈 完成度: ${Math.round(result.comments.length / result.noteInfo.totalCommentCount * 100)}%`);
            console.log(`   📄 页面文本长度: ${result.extractStats.totalTextLength}`);
            console.log(`   📁 保存文件: ${filename}`);

            if (result.comments.length > 0) {
                console.log('\n👥 评论详细预览:');
                result.comments.slice(0, 15).forEach((comment, index) => {
                    console.log(`\n   ${index + 1}. 评论ID: ${comment.id}`);
                    console.log(`      👤 用户: ${comment.username || '未知'}`);
                    console.log(`      🆔 用户ID: ${comment.userId || '未提取到'}`);
                    console.log(`      ⏰ 时间: ${comment.time || '未知'}`);
                    console.log(`      💬 内容: ${comment.content.substring(0, 100)}${comment.content.length > 100 ? '...' : ''}`);
                    console.log(`      👍 点赞: ${comment.likes} | 💬 回复: ${comment.replyCount}`);
                    console.log(`      📊 来源: ${comment.extractedInfo?.source || '未知'}`);
                });

                // 质量统计
                const withUsername = result.comments.filter(c => c.username).length;
                const withUserId = result.comments.filter(c => c.userId).length;
                const withTime = result.comments.filter(c => c.time).length;

                console.log('\n📈 数据质量统计:');
                console.log(`   👤 有用户名: ${withUsername}/${result.comments.length} (${Math.round(withUsername/result.comments.length*100)}%)`);
                console.log(`   🆔 有用户ID: ${withUserId}/${result.comments.length} (${Math.round(withUserId/result.comments.length*100)}%)`);
                console.log(`   ⏰ 有时间: ${withTime}/${result.comments.length} (${Math.round(withTime/result.comments.length*100)}%)`);

                // 兼职信息分析
                const jobKeywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱', '月入'];
                const jobComments = result.comments.filter(c =>
                    jobKeywords.some(keyword => c.content.includes(keyword))
                );

                if (jobComments.length > 0) {
                    console.log(`\n💼 兼职相关评论: ${jobComments.length}/${result.comments.length} (${Math.round(jobComments.length/result.comments.length*100)}%)`);

                    console.log('\n💼 兼职评论示例:');
                    jobComments.slice(0, 8).forEach((comment, index) => {
                        console.log(`   ${index + 1}. ${comment.username || '匿名'} (${comment.time || '未知时间'}): ${comment.content.substring(0, 80)}...`);
                    });
                }

                // 成功度评估
                const completionRate = result.comments.length / result.noteInfo.totalCommentCount;
                if (completionRate >= 0.8) {
                    console.log('\n🎉 优秀！智能检测已提取到大部分评论数据！');
                } else if (completionRate >= 0.5) {
                    console.log('\n👍 良好！智能检测已提取到一半以上的评论数据！');
                } else if (completionRate >= 0.3) {
                    console.log('\n📈 进步！智能检测效果显著！');
                } else {
                    console.log('\n💡 提示：可能需要更长时间的滚动或手动干预');
                }
            }

            await browser.disconnect();

            console.log('\n✅ 智能检测提取任务完成！');
            console.log(`📁 数据文件: ${filename}`);
            console.log(`🔗 使用端口: ${port}`);

        } catch (error) {
            console.error('❌ 智能检测提取失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行智能端口检测器
if (require.main === module) {
    const extractor = new SmartPortDetector();
    extractor.run();
}

module.exports = SmartPortDetector;
