#!/usr/bin/env node

/**
 * 🧠 智能评论采集器
 * 基于研究结果的简化版高效采集器
 */

const axios = require('axios');
const WebSocket = require('ws');

class SmartCommentsCollector {
    constructor() {
        this.debugPort = null;
    }

    // 🧠 主要采集方法
    async collectComments() {
        console.log('🧠 启动智能评论采集器...\n');

        try {
            // 1. 获取调试端口
            await this.getCurrentDebugPort();
            
            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 当前页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 3. 执行智能采集
            console.log('🔄 执行智能评论采集...');
            const commentsData = await this.executeSmartCollection(tab);
            
            return commentsData;

        } catch (error) {
            console.error('❌ 智能采集失败:', error.message);
            throw error;
        }
    }

    // 🔌 获取调试端口
    async getCurrentDebugPort() {
        try {
            const commonPorts = [63524, 51859, 58222, 9222];
            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到端口: ${port}`);
                    return;
                } catch (error) {
                    // 继续尝试
                }
            }
            throw new Error('未找到可用端口');
        } catch (error) {
            throw new Error(`获取端口失败: ${error.message}`);
        }
    }

    // 🔍 查找小红书标签页
    async findXiaohongshuTab() {
        const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
            timeout: 5000
        });

        return response.data.find(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );
    }

    // 🔄 执行智能采集
    async executeSmartCollection(tab) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(tab.webSocketDebuggerUrl);
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ 智能采集WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    const smartCollectionScript = `
                        (async function() {
                            try {
                                console.log('🧠 开始智能评论采集...');
                                
                                const commentsData = {
                                    noteUrl: window.location.href,
                                    noteTitle: document.title.replace(' - 小红书', '').trim(),
                                    timestamp: new Date().toISOString(),
                                    comments: [],
                                    summary: {
                                        totalComments: 0,
                                        totalReplies: 0,
                                        totalLikes: 0
                                    }
                                };

                                // 第一步：强力展开所有"更多"按钮
                                console.log('🔘 第一步：展开所有"更多"按钮...');
                                let totalClicked = 0;
                                
                                for (let round = 0; round < 30; round++) {
                                    let clickedInRound = 0;
                                    
                                    // 查找所有可能的"更多"按钮
                                    const moreTexts = ['更多', '展开', '查看', '条回复', 'more'];
                                    
                                    for (const text of moreTexts) {
                                        const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                                            const elText = el.textContent.trim();
                                            const isVisible = el.offsetParent !== null;
                                            const isClickable = el.tagName === 'BUTTON' || 
                                                              el.tagName === 'A' ||
                                                              el.onclick || 
                                                              el.className.includes('btn') ||
                                                              getComputedStyle(el).cursor === 'pointer';
                                            
                                            return isVisible && isClickable && (
                                                elText.includes(text) ||
                                                (text === '条回复' && /\\d+条回复/.test(elText))
                                            );
                                        });
                                        
                                        for (const el of elements) {
                                            try {
                                                el.scrollIntoView({ behavior: 'instant', block: 'center' });
                                                el.click();
                                                clickedInRound++;
                                                totalClicked++;
                                                await new Promise(resolve => setTimeout(resolve, 200));
                                            } catch (e) {
                                                // 忽略点击错误
                                            }
                                        }
                                    }
                                    
                                    if (clickedInRound === 0) {
                                        console.log('✅ 所有按钮已展开');
                                        break;
                                    }
                                    
                                    await new Promise(resolve => setTimeout(resolve, 500));
                                }
                                
                                console.log(\`🎉 总共点击了 \${totalClicked} 个按钮\`);

                                // 第二步：深度滚动
                                console.log('📜 第二步：深度滚动...');
                                for (let i = 0; i < 20; i++) {
                                    window.scrollTo(0, document.body.scrollHeight);
                                    await new Promise(resolve => setTimeout(resolve, 500));
                                }

                                // 第三步：智能提取评论
                                console.log('🔍 第三步：智能提取评论...');
                                
                                // 使用多种策略查找评论元素
                                const commentElements = new Set();
                                
                                // 策略1: 查找有头像的元素
                                const avatars = document.querySelectorAll('img[src*="avatar"]');
                                avatars.forEach(img => {
                                    const commentDiv = img.closest('div');
                                    if (commentDiv && commentDiv.textContent.trim().length > 20) {
                                        commentElements.add(commentDiv);
                                    }
                                });
                                
                                // 策略2: 查找有时间信息的元素
                                const timeElements = Array.from(document.querySelectorAll('*')).filter(el => {
                                    const text = el.textContent.trim();
                                    return /\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}-\\d{2}/.test(text);
                                });
                                
                                timeElements.forEach(timeEl => {
                                    const commentDiv = timeEl.closest('div');
                                    if (commentDiv && commentDiv.textContent.trim().length > 20) {
                                        commentElements.add(commentDiv);
                                    }
                                });
                                
                                // 策略3: 查找包含评论关键词的元素
                                const allDivs = document.querySelectorAll('div');
                                allDivs.forEach(div => {
                                    const text = div.textContent.trim();
                                    if (text.length > 20 && text.length < 1000 && 
                                        (text.includes('回复') || text.includes('点赞') || 
                                         /\\d+天前|\\d+小时前/.test(text))) {
                                        commentElements.add(div);
                                    }
                                });

                                console.log(\`🔍 找到 \${commentElements.size} 个潜在评论元素\`);

                                // 提取评论详细信息
                                Array.from(commentElements).forEach((element, index) => {
                                    try {
                                        const commentData = {
                                            id: index + 1,
                                            username: '',
                                            avatar: '',
                                            content: '',
                                            publishTime: '',
                                            likes: 0
                                        };

                                        // 提取用户名
                                        const textNodes = Array.from(element.querySelectorAll('*')).filter(el => 
                                            el.children.length === 0 && 
                                            el.textContent.trim().length > 1 && 
                                            el.textContent.trim().length < 30 &&
                                            !el.textContent.includes('天前') &&
                                            !el.textContent.includes('小时前')
                                        );
                                        if (textNodes.length > 0) {
                                            commentData.username = textNodes[0].textContent.trim();
                                        }

                                        // 提取头像
                                        const avatarImg = element.querySelector('img[src*="avatar"]');
                                        if (avatarImg) {
                                            commentData.avatar = avatarImg.src;
                                        }

                                        // 提取评论内容
                                        const allTexts = Array.from(element.querySelectorAll('*')).map(el => 
                                            el.textContent.trim()
                                        ).filter(text => 
                                            text.length > 10 && text.length < 1000 &&
                                            !text.includes('天前') && !text.includes('小时前') &&
                                            !text.includes('点赞') && !text.includes('回复')
                                        );
                                        
                                        if (allTexts.length > 0) {
                                            commentData.content = allTexts.reduce((a, b) => a.length > b.length ? a : b);
                                        }

                                        // 提取发布时间
                                        const timeMatch = element.textContent.match(/\\d+天前|\\d+小时前|\\d+分钟前|昨天|前天|\\d{2}-\\d{2}/);
                                        if (timeMatch) {
                                            commentData.publishTime = timeMatch[0];
                                        }

                                        // 提取点赞数
                                        const likeMatch = element.textContent.match(/\\d+(?=\\s*赞|\\s*❤️)/);
                                        if (likeMatch) {
                                            commentData.likes = parseInt(likeMatch[0]) || 0;
                                        }

                                        // 只添加有内容的评论
                                        if (commentData.content && commentData.content.length > 5) {
                                            commentsData.comments.push(commentData);
                                        }

                                    } catch (e) {
                                        // 忽略提取错误
                                    }
                                });

                                // 去重
                                const uniqueComments = [];
                                const seenContents = new Set();
                                
                                commentsData.comments.forEach(comment => {
                                    const key = comment.content.substring(0, 50);
                                    if (!seenContents.has(key)) {
                                        seenContents.add(key);
                                        uniqueComments.push(comment);
                                    }
                                });
                                
                                commentsData.comments = uniqueComments;

                                // 计算统计信息
                                commentsData.summary.totalComments = commentsData.comments.length;
                                commentsData.summary.totalLikes = commentsData.comments.reduce((sum, c) => sum + c.likes, 0);

                                console.log(\`🎉 智能采集完成! 采集到 \${commentsData.summary.totalComments} 条独特评论\`);

                                return { success: true, data: commentsData };
                                
                            } catch (error) {
                                console.error('采集过程出错:', error);
                                return { 
                                    success: false, 
                                    error: error.message || '未知错误'
                                };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: smartCollectionScript,
                            returnByValue: true
                        }
                    }));
                }, 2000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            console.log('✅ 智能采集成功');
                            console.log(`📊 采集结果: ${result.data.summary.totalComments} 条评论`);
                            ws.close();
                            resolve(result.data);
                        } else {
                            console.log('❌ 智能采集失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理采集结果失败:', error.message);
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ 智能采集WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('智能采集超时'));
            }, 180000); // 3分钟超时
        });
    }
}

// 💾 保存智能采集数据
async function saveSmartCommentsData() {
    const fs = require('fs');
    const collector = new SmartCommentsCollector();

    try {
        console.log('💾 开始智能采集并保存评论数据...\n');

        const commentsData = await collector.collectComments();

        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const jsonFileName = `smart-comments-${timestamp}.json`;
        const txtFileName = `smart-comments-${timestamp}.txt`;

        // 保存JSON数据
        fs.writeFileSync(jsonFileName, JSON.stringify(commentsData, null, 2), 'utf8');
        console.log(`✅ JSON数据已保存: ${jsonFileName}`);

        // 生成可读报告
        const report = generateSmartCommentsReport(commentsData);
        fs.writeFileSync(txtFileName, report, 'utf8');
        console.log(`✅ 可读报告已保存: ${txtFileName}`);

        return { jsonFile: jsonFileName, txtFile: txtFileName, data: commentsData };

    } catch (error) {
        console.error('❌ 保存智能采集数据失败:', error.message);
        throw error;
    }
}

// 📝 生成智能评论报告
function generateSmartCommentsReport(data) {
    const lines = [];

    lines.push('🧠 小红书笔记智能评论采集报告');
    lines.push('=' * 60);
    lines.push(`📅 采集时间: ${data.timestamp}`);
    lines.push(`📝 笔记标题: ${data.noteTitle}`);
    lines.push(`🔗 笔记链接: ${data.noteUrl}`);
    lines.push('');

    lines.push('📊 采集统计:');
    lines.push(`💬 总评论数: ${data.summary.totalComments}`);
    lines.push(`👍 总点赞数: ${data.summary.totalLikes}`);
    lines.push(`📈 采集进度: ${Math.round(data.summary.totalComments/1472*100)}% (目标1472条)`);
    lines.push('');

    lines.push('💬 评论详情:');
    lines.push('-' * 60);

    data.comments.forEach((comment, index) => {
        lines.push(`\n💬 评论 ${index + 1}:`);
        lines.push(`👤 用户: ${comment.username || '匿名'}`);
        lines.push(`📄 内容: ${comment.content || '无内容'}`);
        lines.push(`👍 点赞: ${comment.likes}`);
        lines.push(`📅 时间: ${comment.publishTime || '未知'}`);
        lines.push(`🔗 头像: ${comment.avatar || '无'}`);
    });

    lines.push('\n' + '=' * 60);
    lines.push('📅 报告生成时间: ' + new Date().toLocaleString('zh-CN'));

    return lines.join('\n');
}

// 运行测试
if (require.main === module) {
    const args = process.argv.slice(2);
    if (args.includes('--save')) {
        saveSmartCommentsData().then(result => {
            console.log('\n🎉 智能采集数据保存完成!');
            console.log(`📁 JSON文件: ${result.jsonFile}`);
            console.log(`📄 文本报告: ${result.txtFile}`);
        }).catch(console.error);
    } else {
        const collector = new SmartCommentsCollector();
        collector.collectComments().then(data => {
            console.log('\n🎉 智能采集完成!');
            console.log(`📊 采集到 ${data.summary.totalComments} 条评论`);
        }).catch(console.error);
    }
}

module.exports = { SmartCommentsCollector, saveSmartCommentsData };
