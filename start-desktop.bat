@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🖥️ BTX科技 - 桌面应用启动器
echo ========================================
echo.

echo 📂 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 📥 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过

echo 📦 检查依赖包...
if not exist "node_modules" (
    echo 📥 首次运行，正在安装依赖包...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包已存在
)

echo 🔍 检查Electron...
if not exist "node_modules\electron" (
    echo 📥 安装Electron...
    npm install
    if errorlevel 1 (
        echo ❌ Electron安装失败
        pause
        exit /b 1
    )
)

echo ✅ Electron已准备就绪

echo.
echo 🚀 启动BTX科技桌面应用...
echo 📱 正在打开桌面窗口...
echo.

npm run electron

if errorlevel 1 (
    echo.
    echo ❌ 桌面应用启动失败
    echo 💡 请检查错误信息并重试
    echo.
    echo 🔧 故障排除建议:
    echo    1. 确保端口3000未被占用
    echo    2. 以管理员权限运行
    echo    3. 检查防火墙设置
    echo    4. 重启计算机后再试
    echo.
    pause
    exit /b 1
)

echo.
echo 👋 桌面应用已关闭
pause
