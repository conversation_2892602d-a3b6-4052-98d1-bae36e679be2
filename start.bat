@echo off
chcp 65001 >nul
echo.
echo ========================================
echo ========================================
echo.

echo 📂 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 📥 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过

echo 📦 检查依赖包...
if not exist "node_modules" (
    echo 📥 首次运行，正在安装依赖包...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包已存在
)

echo.
echo 
echo 📡 服务器地址: http://localhost:3000
echo.

npm start

if errorlevel 1 (
    echo.
    echo ❌ 应用启动失败
    echo 💡 请检查错误信息并重试
    pause
    exit /b 1
)

echo.
echo 👋 应用已关闭
pause
