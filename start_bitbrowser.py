#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 比特浏览器启动和19号窗口管理器
帮助启动比特浏览器并打开19号窗口
"""

import requests
import time
import json
import subprocess
import os
from datetime import datetime


class BitBrowserManager:
    """比特浏览器管理器"""
    
    def __init__(self):
        self.api_url = "http://127.0.0.1:56906"
        self.api_token = "ca28ee5ca6de4d209182a83aa16a2044"
        self.browser_19_id = "e3afefd184384c3f90c78b6b19309ca0"
        self.headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
    
    def check_bitbrowser_running(self):
        """检查比特浏览器是否运行"""
        print("🔍 检查比特浏览器状态...")
        
        try:
            response = requests.get(f"{self.api_url}/browser/list", 
                                  headers=self.headers, 
                                  timeout=5)
            
            if response.status_code == 200:
                print("✅ 比特浏览器API连接成功")
                return True
            else:
                print(f"❌ API响应错误: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ 比特浏览器未启动或API端口不可用")
            return False
        except Exception as e:
            print(f"❌ 检查失败: {str(e)}")
            return False
    
    def start_bitbrowser_process(self):
        """启动比特浏览器进程"""
        print("🚀 尝试启动比特浏览器...")
        
        # 常见的比特浏览器安装路径
        possible_paths = [
            r"C:\Program Files\BitBrowser\BitBrowser.exe",
            r"C:\Program Files (x86)\BitBrowser\BitBrowser.exe",
            r"D:\BitBrowser\BitBrowser.exe",
            r"E:\BitBrowser\BitBrowser.exe",
            r"C:\Users\<USER>\AppData\Local\BitBrowser\BitBrowser.exe".format(os.getenv('USERNAME')),
            r"C:\BitBrowser\BitBrowser.exe"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                print(f"✅ 找到比特浏览器: {path}")
                try:
                    subprocess.Popen([path], shell=True)
                    print("🚀 比特浏览器启动中...")
                    
                    # 等待启动
                    for i in range(30):
                        print(f"⏳ 等待启动... ({i+1}/30)")
                        time.sleep(2)
                        if self.check_bitbrowser_running():
                            print("✅ 比特浏览器启动成功!")
                            return True
                    
                    print("❌ 比特浏览器启动超时")
                    return False
                    
                except Exception as e:
                    print(f"❌ 启动失败: {str(e)}")
                    continue
        
        print("❌ 未找到比特浏览器安装路径")
        print("💡 请手动启动比特浏览器，或检查安装路径")
        return False
    
    def get_browser_list(self):
        """获取浏览器列表"""
        print("📋 获取浏览器列表...")
        
        try:
            response = requests.get(f"{self.api_url}/browser/list", 
                                  headers=self.headers, 
                                  timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    browsers = data.get('data', [])
                    print(f"✅ 找到 {len(browsers)} 个浏览器配置")
                    return browsers
                else:
                    print(f"❌ API返回错误: {data.get('msg', '未知错误')}")
                    return []
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 获取浏览器列表失败: {str(e)}")
            return []
    
    def find_browser_19(self, browsers):
        """查找19号浏览器"""
        print("🔍 查找19号浏览器...")
        
        # 方法1: 通过ID查找
        target_browser = None
        for browser in browsers:
            if browser.get('id') == self.browser_19_id:
                target_browser = browser
                print(f"✅ 通过ID找到19号浏览器: {browser.get('name', '未命名')}")
                break
        
        # 方法2: 通过名称查找
        if not target_browser:
            for browser in browsers:
                name = browser.get('name', '').lower()
                if '19' in name or 'nineteen' in name:
                    target_browser = browser
                    print(f"✅ 通过名称找到19号浏览器: {browser.get('name', '未命名')}")
                    break
        
        # 方法3: 使用第一个可用浏览器
        if not target_browser and browsers:
            target_browser = browsers[0]
            print(f"⚠️ 未找到专门的19号浏览器，使用第一个: {target_browser.get('name', '未命名')}")
        
        if target_browser:
            print(f"   ID: {target_browser.get('id')}")
            print(f"   名称: {target_browser.get('name')}")
            print(f"   状态: {target_browser.get('status', '未知')}")
            return target_browser
        else:
            print("❌ 没有找到可用的浏览器")
            return None
    
    def start_browser_window(self, browser_id):
        """启动浏览器窗口"""
        print(f"🚀 启动浏览器窗口: {browser_id}")
        
        try:
            response = requests.post(f"{self.api_url}/browser/start",
                                   headers=self.headers,
                                   json={"id": browser_id},
                                   timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data.get('data', {})
                    debug_port = result.get('debug_port') or result.get('selenium_port')
                    
                    print("✅ 浏览器窗口启动成功!")
                    print(f"   调试端口: {debug_port}")
                    print(f"   WebSocket: {result.get('ws_endpoint', '未提供')}")
                    
                    return {
                        'success': True,
                        'debug_port': debug_port,
                        'ws_endpoint': result.get('ws_endpoint'),
                        'data': result
                    }
                else:
                    print(f"❌ 启动失败: {data.get('msg', '未知错误')}")
                    return {'success': False, 'error': data.get('msg')}
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"❌ 启动浏览器窗口失败: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def navigate_to_xiaohongshu(self, debug_port):
        """导航到小红书页面"""
        print("🌐 导航到小红书页面...")
        
        try:
            # 获取标签页列表
            response = requests.get(f"http://127.0.0.1:{debug_port}/json", timeout=10)
            
            if response.status_code == 200:
                tabs = response.json()
                
                if tabs:
                    # 使用第一个标签页
                    tab = tabs[0]
                    tab_id = tab['id']
                    
                    # 导航到小红书
                    navigate_url = f"http://127.0.0.1:{debug_port}/json/runtime/evaluate"
                    navigate_script = {
                        "expression": "window.location.href = 'https://www.xiaohongshu.com/explore';"
                    }
                    
                    # 这里简化处理，实际可以使用WebSocket
                    print("✅ 请手动在浏览器中导航到小红书页面")
                    print("   推荐URL: https://www.xiaohongshu.com/explore")
                    return True
                else:
                    print("❌ 没有找到可用的标签页")
                    return False
            else:
                print(f"❌ 获取标签页失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 导航失败: {str(e)}")
            return False
    
    def setup_browser_19(self):
        """设置19号浏览器的完整流程"""
        print("🎯 开始设置19号浏览器...\n")
        
        # 1. 检查比特浏览器是否运行
        if not self.check_bitbrowser_running():
            print("\n🚀 比特浏览器未运行，尝试启动...")
            if not self.start_bitbrowser_process():
                print("\n❌ 无法自动启动比特浏览器")
                print("💡 请手动启动比特浏览器，然后重新运行此脚本")
                return None
        
        # 2. 获取浏览器列表
        browsers = self.get_browser_list()
        if not browsers:
            print("\n❌ 无法获取浏览器列表")
            return None
        
        # 3. 查找19号浏览器
        target_browser = self.find_browser_19(browsers)
        if not target_browser:
            print("\n❌ 没有找到19号浏览器")
            print("💡 请在比特浏览器中创建一个浏览器配置")
            return None
        
        # 4. 启动浏览器窗口
        browser_id = target_browser['id']
        result = self.start_browser_window(browser_id)
        
        if result['success']:
            debug_port = result['debug_port']
            
            # 5. 导航到小红书
            self.navigate_to_xiaohongshu(debug_port)
            
            print(f"\n🎉 19号浏览器设置完成!")
            print(f"📊 浏览器信息:")
            print(f"   ID: {browser_id}")
            print(f"   名称: {target_browser.get('name')}")
            print(f"   调试端口: {debug_port}")
            print(f"\n📋 下一步:")
            print(f"   1. 在浏览器中导航到要采集的小红书笔记页面")
            print(f"   2. 等待页面完全加载")
            print(f"   3. 运行评论采集脚本")
            
            return {
                'browser_id': browser_id,
                'browser_name': target_browser.get('name'),
                'debug_port': debug_port,
                'ws_endpoint': result.get('ws_endpoint')
            }
        else:
            print(f"\n❌ 启动19号浏览器失败: {result.get('error')}")
            return None


def main():
    """主函数"""
    print("🎯 比特浏览器19号窗口管理器")
    print("=" * 50)
    print("📋 功能:")
    print("   1. 检查比特浏览器状态")
    print("   2. 自动启动比特浏览器(如果需要)")
    print("   3. 查找并启动19号浏览器窗口")
    print("   4. 准备评论采集环境")
    print()
    
    manager = BitBrowserManager()
    
    try:
        result = manager.setup_browser_19()
        
        if result:
            print("\n✅ 设置完成! 现在可以进行评论采集了。")
            
            # 保存配置信息
            config = {
                'timestamp': datetime.now().isoformat(),
                'browser_info': result
            }
            
            with open('browser_19_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print("📁 配置信息已保存到: browser_19_config.json")
        else:
            print("\n❌ 设置失败!")
            
    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")


if __name__ == "__main__":
    main()
