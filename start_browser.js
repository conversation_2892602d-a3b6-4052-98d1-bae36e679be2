#!/usr/bin/env node

/**
 * 🚀 启动比特浏览器实例
 */

const axios = require('axios');

// 比特浏览器配置
const BITBROWSER_CONFIG = {
    api_url: "http://127.0.0.1:54345",
    api_token: "ca28ee5ca6de4d209182a83aa16a2044",
    browser_19_id: "0d094596cb404282be3f814b98139c74"
};

async function startBrowser() {
    console.log('🚀 尝试启动比特浏览器实例...\n');

    try {
        // 1. 检查API连接
        console.log('1️⃣ 检查API连接...');
        const listResponse = await axios.get(`${BITBROWSER_CONFIG.api_url}/browser/list`, {
            headers: {
                'Authorization': `Bearer ${BITBROWSER_CONFIG.api_token}`
            },
            timeout: 5000
        });

        if (listResponse.data && listResponse.data.success) {
            console.log('✅ API连接成功');
            const browsers = listResponse.data.data || [];
            console.log(`📋 找到 ${browsers.length} 个浏览器实例`);

            // 2. 查找目标浏览器
            const targetBrowser = browsers.find(b => b.id === BITBROWSER_CONFIG.browser_19_id);
            
            if (targetBrowser) {
                console.log(`✅ 找到目标浏览器: ${targetBrowser.name}`);
                console.log(`   状态: ${targetBrowser.status}`);
                
                if (targetBrowser.status === 'running') {
                    console.log('✅ 浏览器已在运行');
                    console.log(`   调试端口: ${targetBrowser.debug_port || '未设置'}`);
                    
                    if (targetBrowser.debug_port) {
                        // 测试调试端口
                        await testDebugPort(targetBrowser.debug_port);
                    }
                } else {
                    console.log('🚀 启动浏览器实例...');
                    await startBrowserInstance();
                }
            } else {
                console.log('❌ 未找到目标浏览器实例');
                console.log('💡 可用的浏览器实例：');
                browsers.forEach(browser => {
                    console.log(`   - ${browser.name} (ID: ${browser.id}, 状态: ${browser.status})`);
                });
            }
        } else {
            console.log('❌ API响应格式错误');
        }

    } catch (error) {
        console.log('❌ API连接失败:', error.message);
        console.log('\n💡 请检查：');
        console.log('1. 比特浏览器是否已启动');
        console.log('2. API端口是否为54345');
        console.log('3. API Token是否正确');
        console.log('4. 防火墙是否阻止连接');
        
        // 尝试其他常见端口
        await tryAlternativePorts();
    }
}

async function startBrowserInstance() {
    try {
        const startResponse = await axios.post(
            `${BITBROWSER_CONFIG.api_url}/browser/start`,
            {
                id: BITBROWSER_CONFIG.browser_19_id,
                args: ["--remote-debugging-port=0"] // 让系统自动分配端口
            },
            {
                headers: {
                    'Authorization': `Bearer ${BITBROWSER_CONFIG.api_token}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            }
        );

        if (startResponse.data && startResponse.data.success) {
            console.log('✅ 浏览器启动成功');
            const result = startResponse.data.data;
            
            if (result && result.debug_port) {
                console.log(`🔌 调试端口: ${result.debug_port}`);
                await testDebugPort(result.debug_port);
            }
        } else {
            console.log('❌ 浏览器启动失败:', startResponse.data.message);
        }
    } catch (error) {
        console.log('❌ 启动浏览器失败:', error.message);
    }
}

async function testDebugPort(port) {
    console.log(`\n🧪 测试调试端口 ${port}...`);
    
    try {
        const response = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });

        console.log(`✅ 调试端口 ${port} 连接成功`);
        console.log(`📋 找到 ${response.data.length} 个标签页`);

        // 查找小红书标签页
        const xiaohongshuTabs = response.data.filter(tab =>
            tab.url && (
                tab.url.includes('xiaohongshu.com') ||
                tab.title.includes('小红书')
            )
        );

        if (xiaohongshuTabs.length > 0) {
            console.log(`🎯 找到 ${xiaohongshuTabs.length} 个小红书标签页:`);
            xiaohongshuTabs.forEach((tab, index) => {
                console.log(`   ${index + 1}. ${tab.title}`);
            });
            
            console.log('\n🎉 可以开始评论采集了!');
            console.log(`🚀 运行命令: node comments-collector.js --port=${port} --save`);
        } else {
            console.log('⚠️ 未找到小红书标签页');
            console.log('💡 请在浏览器中打开小红书笔记页面');
        }

    } catch (error) {
        console.log(`❌ 调试端口 ${port} 连接失败:`, error.message);
    }
}

async function tryAlternativePorts() {
    console.log('\n🔍 尝试其他常见的比特浏览器API端口...');
    
    const alternativePorts = [54346, 54347, 54348, 54349, 54350];
    
    for (const port of alternativePorts) {
        try {
            console.log(`🔍 测试端口 ${port}...`);
            const response = await axios.get(`http://127.0.0.1:${port}/browser/list`, {
                headers: {
                    'Authorization': `Bearer ${BITBROWSER_CONFIG.api_token}`
                },
                timeout: 2000
            });
            
            if (response.data) {
                console.log(`✅ 找到比特浏览器API在端口 ${port}`);
                BITBROWSER_CONFIG.api_url = `http://127.0.0.1:${port}`;
                await startBrowser();
                return;
            }
        } catch (error) {
            console.log(`❌ 端口 ${port} 不可用`);
        }
    }
    
    console.log('❌ 未找到可用的比特浏览器API端口');
}

if (require.main === module) {
    startBrowser();
}

module.exports = { startBrowser, testDebugPort };
