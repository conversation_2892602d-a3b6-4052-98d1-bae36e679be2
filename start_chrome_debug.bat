@echo off
echo 🚀 启动Chrome调试模式...
echo.

REM 关闭所有Chrome进程
echo 📝 关闭现有Chrome进程...
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 2 >nul

REM 创建临时用户数据目录
set TEMP_DIR=%TEMP%\chrome_debug_%RANDOM%
echo 📁 创建临时目录: %TEMP_DIR%

REM 启动Chrome调试模式
echo 🔧 启动Chrome调试模式 (端口: 9222)...
start "" "C:\Program Files\Google\Chrome\Application\chrome.exe" ^
  --remote-debugging-port=9222 ^
  --user-data-dir="%TEMP_DIR%" ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor ^
  --no-first-run ^
  --no-default-browser-check ^
  "https://www.xiaohongshu.com/explore"

echo.
echo ✅ Chrome已启动，请等待3秒...
timeout /t 3 >nul

echo.
echo 📋 接下来请:
echo    1. 在Chrome中导航到要采集的小红书笔记页面
echo    2. 等待页面完全加载
echo    3. 运行采集脚本: python direct_tab19_collector.py
echo.
pause
