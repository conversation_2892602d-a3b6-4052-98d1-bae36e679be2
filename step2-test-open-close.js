#!/usr/bin/env node

/**
 * 🎯 第2步：对19号窗口进行打开关闭功能测试
 * 使用POST /browser/open和POST /browser/close接口
 * 目标窗口ID: e3afefd184384c3f90c78b6b19309ca0
 */

const axios = require('axios');
const { BITBROWSER_CONFIG, ConfigUtils } = require('./bitbrowser-config');

// 19号窗口ID
const WINDOW_19_ID = BITBROWSER_CONFIG.browser_id;

async function testWindow19OpenClose() {
    console.log('🎯 第2步：19号窗口打开关闭功能测试');
    console.log('📋 测试配置:');
    console.log(`   窗口名称: ${BITBROWSER_CONFIG.browser_name}`);
    console.log(`   窗口ID: ${WINDOW_19_ID}`);
    console.log(`   API地址: ${BITBROWSER_CONFIG.api_url}`);
    console.log('');

    try {
        // 1. 检查初始状态
        console.log('1️⃣ 检查19号窗口初始状态...');
        const initialStatus = await checkWindowStatus();
        console.log(`   初始状态: ${getStatusText(initialStatus.status)}`);
        console.log('');

        // 2. 尝试重置窗口状态（如果需要）
        if (initialStatus.status === 1 || initialStatus.status === 3) {
            console.log('2️⃣ 检测到窗口状态异常，尝试重置...');
            await resetWindowStatus();
            await sleep(2000);
        }

        // 3. 测试打开窗口
        console.log('3️⃣ 测试打开19号窗口...');
        const openResult = await openWindow19();
        
        if (openResult.success) {
            console.log('   ✅ 窗口打开成功！');
            console.log('   📊 返回信息:');
            console.log(`      WebSocket: ${openResult.data.ws || '未提供'}`);
            console.log(`      HTTP端点: ${openResult.data.http || '未提供'}`);
            console.log(`      内核版本: ${openResult.data.coreVersion || '未提供'}`);
            console.log(`      进程ID: ${openResult.data.pid || '未提供'}`);
            console.log(`      序号: ${openResult.data.seq || '未提供'}`);

            // 等待窗口完全启动
            console.log('   ⏳ 等待窗口完全启动...');
            await sleep(5000);

            // 4. 验证窗口状态
            console.log('\n4️⃣ 验证窗口打开状态...');
            const openStatus = await checkWindowStatus();
            console.log(`   当前状态: ${getStatusText(openStatus.status)}`);
            
            if (openStatus.status === 0) {
                console.log('   ✅ 窗口状态验证成功 - 已运行');
                
                // 4. 测试调试端口连接
                if (openResult.data.http) {
                    console.log('\n4️⃣ 测试调试端口连接...');
                    await testDebugPort(openResult.data.http);
                }
                
                // 5. 等待一段时间后关闭
                console.log('\n5️⃣ 等待5秒后测试关闭窗口...');
                await sleep(5000);
                
                // 6. 测试关闭窗口
                console.log('6️⃣ 测试关闭19号窗口...');
                const closeResult = await closeWindow19();
                
                if (closeResult.success) {
                    console.log('   ✅ 窗口关闭请求成功！');
                    
                    // 等待窗口完全关闭
                    console.log('   ⏳ 等待窗口完全关闭...');
                    await sleep(5000);
                    
                    // 7. 验证关闭状态
                    console.log('\n7️⃣ 验证窗口关闭状态...');
                    const closeStatus = await checkWindowStatus();
                    console.log(`   最终状态: ${getStatusText(closeStatus.status)}`);
                    
                    if (closeStatus.status === 2) {
                        console.log('   ✅ 窗口状态验证成功 - 已关闭');
                        console.log('\n🎉 第2步测试完全成功！');
                        return true;
                    } else {
                        console.log('   ⚠️ 窗口可能未完全关闭，状态异常');
                        return false;
                    }
                } else {
                    console.log('   ❌ 窗口关闭失败:', closeResult.message);
                    return false;
                }
                
            } else {
                console.log('   ❌ 窗口状态异常，可能未正确启动');
                return false;
            }
            
        } else {
            console.log('   ❌ 窗口打开失败:', openResult.message);

            // 分析失败原因并尝试解决
            if (openResult.message && openResult.message.includes('限制')) {
                console.log('   💡 失败原因: 窗口被限制打开');
                console.log('   🔄 尝试其他方法...');

                // 尝试方法1: 使用不同的启动参数
                console.log('\n🔧 尝试方法1: 使用不同的启动参数...');
                const altOpenResult = await openWindow19WithArgs();

                if (altOpenResult.success) {
                    console.log('   ✅ 使用替代方法打开成功！');
                    return await continueTestAfterOpen(altOpenResult);
                }

                // 尝试方法2: 检查是否有其他运行中的窗口可以测试关闭功能
                console.log('\n🔧 尝试方法2: 测试其他运行中窗口的关闭功能...');
                const testResult = await testCloseRunningWindows();

                if (testResult) {
                    console.log('   ✅ 关闭功能测试完成（使用其他窗口）');
                    console.log('   ⚠️ 注意: 19号窗口仍然被限制打开');
                    return true;
                }

                console.log('   🛠️ 最终解决方案: 请在比特浏览器界面中手动启动窗口');

            } else if (openResult.message && openResult.message.includes('正在')) {
                console.log('   💡 失败原因: 窗口正在启动或关闭中');
                console.log('   🛠️ 解决方案: 等待当前操作完成后重试');
            }

            return false;
        }

    } catch (error) {
        console.error('❌ 第2步测试过程出错:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 请确保比特浏览器已启动并开启Local API功能');
        } else if (error.response) {
            console.log('📊 错误响应:', JSON.stringify(error.response.data, null, 2));
        }
        
        return false;
    }
}

async function checkWindowStatus() {
    try {
        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/detail'),
            { id: WINDOW_19_ID },
            ConfigUtils.getRequestConfig()
        );

        if (response.data.success) {
            return response.data.data;
        } else {
            throw new Error(`获取窗口状态失败: ${response.data.msg}`);
        }
    } catch (error) {
        throw new Error(`检查窗口状态失败: ${error.message}`);
    }
}

async function resetWindowStatus() {
    try {
        console.log('   🔄 尝试重置窗口状态...');

        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/closing/reset'),
            { id: WINDOW_19_ID },
            ConfigUtils.getRequestConfig()
        );

        if (response.data.success) {
            console.log('   ✅ 窗口状态重置成功');
        } else {
            console.log('   ⚠️ 窗口状态重置失败:', response.data.msg);
        }

        return response.data.success;

    } catch (error) {
        console.log('   ❌ 重置窗口状态失败:', error.message);
        return false;
    }
}

async function openWindow19() {
    try {
        console.log('   🚀 发送打开请求...');
        
        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/open'),
            {
                id: WINDOW_19_ID,
                args: [],
                queue: true  // 使用队列方式防止并发错误
            },
            {
                ...ConfigUtils.getRequestConfig(),
                timeout: 60000  // 增加超时时间
            }
        );

        console.log('   📊 API响应:', JSON.stringify(response.data, null, 2));

        return {
            success: response.data.success,
            data: response.data.data || {},
            message: response.data.msg || ''
        };

    } catch (error) {
        return {
            success: false,
            data: {},
            message: error.message
        };
    }
}

async function closeWindow19() {
    try {
        console.log('   🔒 发送关闭请求...');
        
        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/close'),
            { id: WINDOW_19_ID },
            {
                ...ConfigUtils.getRequestConfig(),
                timeout: 30000
            }
        );

        console.log('   📊 API响应:', JSON.stringify(response.data, null, 2));

        return {
            success: response.data.success,
            message: response.data.msg || ''
        };

    } catch (error) {
        return {
            success: false,
            message: error.message
        };
    }
}

async function testDebugPort(httpEndpoint) {
    try {
        // 从HTTP端点提取端口号
        const portMatch = httpEndpoint.match(/:(\d+)/);
        if (!portMatch) {
            console.log('   ⚠️ 无法从HTTP端点提取端口号');
            return;
        }
        
        const port = parseInt(portMatch[1]);
        console.log(`   🔍 测试调试端口 ${port}...`);
        
        const response = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });
        
        const tabs = response.data;
        console.log(`   ✅ 调试端口连接成功 - 找到 ${tabs.length} 个标签页`);
        
        if (tabs.length > 0) {
            console.log('   📄 标签页列表:');
            tabs.slice(0, 3).forEach((tab, index) => {
                const title = tab.title || '无标题';
                const url = (tab.url || '').substring(0, 50) + '...';
                console.log(`      ${index + 1}. ${title}`);
                console.log(`         ${url}`);
            });
            
            if (tabs.length > 3) {
                console.log(`      ... 还有 ${tabs.length - 3} 个标签页`);
            }
        }
        
    } catch (error) {
        console.log(`   ❌ 调试端口连接失败: ${error.message}`);
    }
}

function getStatusText(statusCode) {
    switch (statusCode) {
        case 0: return '运行中';
        case 1: return '启动中';
        case 2: return '未运行';
        case 3: return '关闭中';
        default: return `未知状态(${statusCode})`;
    }
}

async function openWindow19WithArgs() {
    try {
        console.log('   🚀 使用特殊参数尝试打开...');

        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/open'),
            {
                id: WINDOW_19_ID,
                args: ["--no-sandbox", "--disable-web-security"],
                queue: true,
                ignoreDefaultUrls: true
            },
            {
                ...ConfigUtils.getRequestConfig(),
                timeout: 60000
            }
        );

        console.log('   📊 特殊参数API响应:', JSON.stringify(response.data, null, 2));

        return {
            success: response.data.success,
            data: response.data.data || {},
            message: response.data.msg || ''
        };

    } catch (error) {
        return {
            success: false,
            data: {},
            message: error.message
        };
    }
}

async function testCloseRunningWindows() {
    try {
        console.log('   🔍 查找运行中的窗口...');

        // 获取所有窗口列表
        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/list'),
            { page: 0, pageSize: 50 },
            ConfigUtils.getRequestConfig()
        );

        if (!response.data.success) {
            return false;
        }

        const windows = response.data.data.list || [];
        const runningWindows = windows.filter(w => w.status === 0); // 运行中的窗口

        console.log(`   📊 找到 ${runningWindows.length} 个运行中的窗口`);

        if (runningWindows.length === 0) {
            console.log('   ⚠️ 没有运行中的窗口可以测试');
            return false;
        }

        // 选择第一个运行中的窗口进行关闭测试
        const testWindow = runningWindows[0];
        console.log(`   🎯 选择窗口进行关闭测试: ${testWindow.name || '未命名'} (${testWindow.id})`);

        // 测试关闭
        const closeResponse = await axios.post(
            ConfigUtils.getApiUrl('/browser/close'),
            { id: testWindow.id },
            ConfigUtils.getRequestConfig()
        );

        console.log('   📊 关闭测试响应:', JSON.stringify(closeResponse.data, null, 2));

        if (closeResponse.data.success) {
            console.log('   ✅ 关闭功能测试成功');

            // 等待一段时间后重新打开该窗口
            await sleep(3000);

            console.log('   🔄 尝试重新打开测试窗口...');
            const reopenResponse = await axios.post(
                ConfigUtils.getApiUrl('/browser/open'),
                { id: testWindow.id, queue: true },
                ConfigUtils.getRequestConfig()
            );

            if (reopenResponse.data.success) {
                console.log('   ✅ 重新打开成功，关闭/打开功能正常');
                return true;
            } else {
                console.log('   ⚠️ 重新打开失败，但关闭功能正常');
                return true;
            }
        } else {
            console.log('   ❌ 关闭功能测试失败');
            return false;
        }

    } catch (error) {
        console.log('   ❌ 测试运行中窗口失败:', error.message);
        return false;
    }
}

async function continueTestAfterOpen(openResult) {
    // 继续执行打开后的测试流程
    console.log('   📊 返回信息:');
    console.log(`      WebSocket: ${openResult.data.ws || '未提供'}`);
    console.log(`      HTTP端点: ${openResult.data.http || '未提供'}`);
    console.log(`      内核版本: ${openResult.data.coreVersion || '未提供'}`);
    console.log(`      进程ID: ${openResult.data.pid || '未提供'}`);

    await sleep(5000);

    // 验证状态并测试关闭
    const openStatus = await checkWindowStatus();
    console.log(`\n   当前状态: ${getStatusText(openStatus.status)}`);

    if (openStatus.status === 0) {
        console.log('\n   🔒 测试关闭窗口...');
        const closeResult = await closeWindow19();

        if (closeResult.success) {
            await sleep(5000);
            const closeStatus = await checkWindowStatus();
            return closeStatus.status === 2;
        }
    }

    return false;
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

if (require.main === module) {
    testWindow19OpenClose().then(success => {
        if (success) {
            console.log('\n🎉 第2步测试完全成功！');
            console.log('✅ 19号窗口打开关闭功能正常');
            console.log('\n💡 下一步: 运行第3步测试');
            console.log('   命令: node step3-test-cookie-extract.js');
        } else {
            console.log('\n❌ 第2步测试失败！');
            console.log('💡 请检查错误信息并解决问题后重试');
        }
    }).catch(error => {
        console.error('❌ 第2步执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { testWindow19OpenClose };
