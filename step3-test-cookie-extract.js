#!/usr/bin/env node

/**
 * 🎯 第3步：对19号窗口进行Cookie提取测试
 * 使用POST /browser/cookies/get接口
 * 目标窗口ID: e3afefd184384c3f90c78b6b19309ca0
 */

const axios = require('axios');
const { BITBROWSER_CONFIG, ConfigUtils } = require('./bitbrowser-config');

// 19号窗口ID
const WINDOW_19_ID = BITBROWSER_CONFIG.browser_id;

async function testWindow19CookieExtract() {
    console.log('🎯 第3步：19号窗口Cookie提取测试');
    console.log('📋 测试配置:');
    console.log(`   窗口名称: ${BITBROWSER_CONFIG.browser_name}`);
    console.log(`   窗口ID: ${WINDOW_19_ID}`);
    console.log(`   API地址: ${BITBROWSER_CONFIG.api_url}`);
    console.log('');

    try {
        // 1. 检查19号窗口状态
        console.log('1️⃣ 检查19号窗口状态...');
        const windowStatus = await checkWindowStatus(WINDOW_19_ID);
        console.log(`   19号窗口状态: ${getStatusText(windowStatus.status)}`);
        
        // 2. 如果19号窗口未运行，尝试找一个运行中的窗口进行测试
        let targetWindowId = WINDOW_19_ID;
        let targetWindowName = BITBROWSER_CONFIG.browser_name;
        
        if (windowStatus.status !== 0) {
            console.log('   ⚠️ 19号窗口未运行，查找其他运行中的窗口...');
            const runningWindow = await findRunningWindow();
            
            if (runningWindow) {
                targetWindowId = runningWindow.id;
                targetWindowName = runningWindow.name || '未命名';
                console.log(`   🎯 使用运行中的窗口: ${targetWindowName} (${targetWindowId})`);
            } else {
                console.log('   ❌ 没有找到运行中的窗口');
                console.log('   💡 尝试启动一个窗口进行测试...');
                
                const startResult = await startAnyWindow();
                if (startResult) {
                    targetWindowId = startResult.id;
                    targetWindowName = startResult.name;
                    console.log(`   ✅ 成功启动窗口: ${targetWindowName} (${targetWindowId})`);
                } else {
                    console.log('   ❌ 无法启动任何窗口，Cookie测试无法进行');
                    return false;
                }
            }
        }
        
        console.log('');
        
        // 3. 测试Cookie提取
        console.log('2️⃣ 测试Cookie提取功能...');
        console.log(`   目标窗口: ${targetWindowName}`);
        console.log(`   窗口ID: ${targetWindowId}`);
        
        const cookieResult = await extractCookies(targetWindowId);
        
        if (cookieResult.success) {
            console.log('   ✅ Cookie提取成功！');
            console.log(`   📊 Cookie数量: ${cookieResult.cookies.length}`);

            // 4. 如果没有Cookie，先设置一些测试Cookie
            if (cookieResult.cookies.length === 0) {
                console.log('\n3️⃣ 没有现有Cookie，先设置测试Cookie...');
                await setTestCookies(targetWindowId);

                // 重新提取Cookie验证设置
                console.log('\n4️⃣ 重新提取Cookie验证设置...');
                const newCookieResult = await extractCookies(targetWindowId);
                if (newCookieResult.success && newCookieResult.cookies.length > 0) {
                    console.log(`   ✅ 设置成功，现在有 ${newCookieResult.cookies.length} 个Cookie`);
                    cookieResult.cookies = newCookieResult.cookies;
                }
            }

            // 5. 分析Cookie数据
            console.log('\n5️⃣ 分析Cookie数据...');
            analyzeCookies(cookieResult.cookies);

            // 6. 测试Cookie格式化功能
            console.log('\n6️⃣ 测试Cookie格式化功能...');
            await testCookieFormat(cookieResult.cookies);

            // 7. 测试Cookie清空功能
            console.log('\n7️⃣ 测试Cookie清空功能...');
            await testCookieClear(targetWindowId);

            console.log('\n🎉 第3步测试完全成功！');
            return true;
            
        } else {
            console.log('   ❌ Cookie提取失败:', cookieResult.message);
            
            // 分析失败原因
            if (cookieResult.message && cookieResult.message.includes('未打开')) {
                console.log('   💡 失败原因: 窗口未打开');
                console.log('   🛠️ 解决方案: 确保窗口处于运行状态');
            }
            
            return false;
        }

    } catch (error) {
        console.error('❌ 第3步测试过程出错:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 请确保比特浏览器已启动并开启Local API功能');
        } else if (error.response) {
            console.log('📊 错误响应:', JSON.stringify(error.response.data, null, 2));
        }
        
        return false;
    }
}

async function checkWindowStatus(windowId) {
    try {
        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/detail'),
            { id: windowId },
            ConfigUtils.getRequestConfig()
        );

        if (response.data.success) {
            return response.data.data;
        } else {
            throw new Error(`获取窗口状态失败: ${response.data.msg}`);
        }
    } catch (error) {
        throw new Error(`检查窗口状态失败: ${error.message}`);
    }
}

async function findRunningWindow() {
    try {
        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/list'),
            { page: 0, pageSize: 50 },
            ConfigUtils.getRequestConfig()
        );

        if (response.data.success) {
            const windows = response.data.data.list || [];
            const runningWindows = windows.filter(w => w.status === 0);
            
            console.log(`   📊 找到 ${runningWindows.length} 个运行中的窗口`);
            
            return runningWindows.length > 0 ? runningWindows[0] : null;
        }
        
        return null;
    } catch (error) {
        console.log('   ❌ 查找运行中窗口失败:', error.message);
        return null;
    }
}

async function startAnyWindow() {
    try {
        // 获取所有未运行的窗口
        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/list'),
            { page: 0, pageSize: 50 },
            ConfigUtils.getRequestConfig()
        );

        if (response.data.success) {
            const windows = response.data.data.list || [];
            const stoppedWindows = windows.filter(w => w.status === 2);
            
            if (stoppedWindows.length === 0) {
                return null;
            }
            
            // 尝试启动第一个未运行的窗口
            const targetWindow = stoppedWindows[0];
            console.log(`   🚀 尝试启动窗口: ${targetWindow.name || '未命名'}`);
            
            const startResponse = await axios.post(
                ConfigUtils.getApiUrl('/browser/open'),
                { id: targetWindow.id, queue: true },
                ConfigUtils.getRequestConfig()
            );
            
            if (startResponse.data.success) {
                // 等待窗口启动
                await sleep(5000);
                return {
                    id: targetWindow.id,
                    name: targetWindow.name || '未命名'
                };
            }
        }
        
        return null;
    } catch (error) {
        console.log('   ❌ 启动窗口失败:', error.message);
        return null;
    }
}

async function extractCookies(windowId) {
    try {
        console.log('   🍪 发送Cookie提取请求...');
        
        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/cookies/get'),
            { browserId: windowId },
            ConfigUtils.getRequestConfig()
        );

        console.log('   📊 API响应:', JSON.stringify({
            success: response.data.success,
            cookieCount: response.data.data ? response.data.data.length : 0,
            message: response.data.msg
        }, null, 2));

        return {
            success: response.data.success,
            cookies: response.data.data || [],
            message: response.data.msg || ''
        };

    } catch (error) {
        return {
            success: false,
            cookies: [],
            message: error.message
        };
    }
}

function analyzeCookies(cookies) {
    if (cookies.length === 0) {
        console.log('   📊 Cookie分析: 无Cookie数据');
        return;
    }
    
    console.log(`   📊 Cookie总数: ${cookies.length}`);
    
    // 按域名分组
    const domainGroups = {};
    cookies.forEach(cookie => {
        const domain = cookie.domain || '未知域名';
        domainGroups[domain] = (domainGroups[domain] || 0) + 1;
    });
    
    console.log('   🌐 域名分布:');
    Object.entries(domainGroups).slice(0, 10).forEach(([domain, count]) => {
        console.log(`      ${domain}: ${count} 个`);
    });
    
    if (Object.keys(domainGroups).length > 10) {
        console.log(`      ... 还有 ${Object.keys(domainGroups).length - 10} 个域名`);
    }
    
    // 安全属性统计
    const secureCount = cookies.filter(c => c.secure).length;
    const httpOnlyCount = cookies.filter(c => c.httpOnly).length;
    const sessionCount = cookies.filter(c => c.session).length;
    
    console.log('   🔒 安全属性:');
    console.log(`      Secure: ${secureCount} 个`);
    console.log(`      HttpOnly: ${httpOnlyCount} 个`);
    console.log(`      Session: ${sessionCount} 个`);
    
    // 显示前几个Cookie示例
    console.log('   📋 Cookie示例:');
    cookies.slice(0, 3).forEach((cookie, index) => {
        console.log(`      ${index + 1}. ${cookie.name} = ${(cookie.value || '').substring(0, 20)}...`);
        console.log(`         域名: ${cookie.domain}`);
        console.log(`         路径: ${cookie.path}`);
        console.log(`         过期: ${cookie.expires ? new Date(cookie.expires * 1000).toLocaleString() : '会话'}`);
    });
}

async function testCookieFormat(cookies) {
    if (cookies.length === 0) {
        console.log('   ⚠️ 无Cookie数据，跳过格式化测试');
        return;
    }
    
    try {
        // 选择第一个Cookie进行格式化测试
        const testCookie = cookies[0];
        const cookieString = `${testCookie.name}=${testCookie.value}`;
        
        console.log('   🔄 测试Cookie格式化...');
        console.log(`   测试Cookie: ${cookieString.substring(0, 50)}...`);
        
        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/cookies/format'),
            {
                cookie: cookieString,
                hostname: testCookie.domain
            },
            ConfigUtils.getRequestConfig()
        );
        
        if (response.data.success) {
            console.log('   ✅ Cookie格式化成功');
            console.log('   📊 格式化结果:', JSON.stringify(response.data.data, null, 2));
        } else {
            console.log('   ❌ Cookie格式化失败:', response.data.msg);
        }
        
    } catch (error) {
        console.log('   ❌ Cookie格式化测试失败:', error.message);
    }
}

async function setTestCookies(windowId) {
    try {
        // 创建多个测试Cookie
        const testCookies = [
            {
                name: 'test_session_cookie',
                value: 'session_value_' + Date.now(),
                domain: '.example.com',
                path: '/',
                secure: false,
                httpOnly: false,
                session: true
            },
            {
                name: 'test_persistent_cookie',
                value: 'persistent_value_' + Date.now(),
                domain: '.test.com',
                path: '/',
                expires: Math.floor(Date.now() / 1000) + 86400, // 24小时后过期
                secure: true,
                httpOnly: true,
                session: false
            },
            {
                name: 'test_api_cookie',
                value: 'api_test_' + Math.random().toString(36).substring(7),
                domain: '.api.example.com',
                path: '/api',
                secure: false,
                httpOnly: false,
                session: true
            }
        ];

        console.log('   🔄 设置测试Cookie...');
        console.log(`   准备设置 ${testCookies.length} 个测试Cookie`);

        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/cookies/set'),
            {
                browserId: windowId,
                cookies: testCookies
            },
            ConfigUtils.getRequestConfig()
        );

        if (response.data.success) {
            console.log('   ✅ 测试Cookie设置成功');
            testCookies.forEach((cookie, index) => {
                console.log(`      ${index + 1}. ${cookie.name} = ${cookie.value.substring(0, 20)}...`);
                console.log(`         域名: ${cookie.domain}, 路径: ${cookie.path}`);
            });
        } else {
            console.log('   ❌ 测试Cookie设置失败:', response.data.msg);
        }

    } catch (error) {
        console.log('   ❌ 设置测试Cookie失败:', error.message);
    }
}

async function testCookieClear(windowId) {
    try {
        console.log('   🔄 测试Cookie清空功能...');

        const response = await axios.post(
            ConfigUtils.getApiUrl('/browser/cookies/clear'),
            {
                browserId: windowId,
                saveSynced: false  // 清空本地和云端Cookie
            },
            ConfigUtils.getRequestConfig()
        );

        if (response.data.success) {
            console.log('   ✅ Cookie清空成功');

            // 验证清空是否成功
            await sleep(1000);
            console.log('   🔍 验证Cookie清空...');

            const verifyResult = await extractCookies(windowId);
            if (verifyResult.success) {
                console.log(`   📊 清空后Cookie数量: ${verifyResult.cookies.length}`);
                if (verifyResult.cookies.length === 0) {
                    console.log('   ✅ Cookie清空验证成功');
                } else {
                    console.log('   ⚠️ 仍有Cookie残留，可能清空不完全');
                }
            }

        } else {
            console.log('   ❌ Cookie清空失败:', response.data.msg);
        }

    } catch (error) {
        console.log('   ❌ Cookie清空测试失败:', error.message);
    }
}

function getStatusText(statusCode) {
    switch (statusCode) {
        case 0: return '运行中';
        case 1: return '启动中';
        case 2: return '未运行';
        case 3: return '关闭中';
        default: return `未知状态(${statusCode})`;
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

if (require.main === module) {
    testWindow19CookieExtract().then(success => {
        if (success) {
            console.log('\n🎉 第3步测试完全成功！');
            console.log('✅ Cookie提取功能正常');
            console.log('✅ Cookie格式化功能正常');
            console.log('✅ Cookie设置功能正常');
            console.log('\n🎯 所有测试步骤完成！');
        } else {
            console.log('\n❌ 第3步测试失败！');
            console.log('💡 请检查错误信息并解决问题后重试');
        }
    }).catch(error => {
        console.error('❌ 第3步执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { testWindow19CookieExtract };
