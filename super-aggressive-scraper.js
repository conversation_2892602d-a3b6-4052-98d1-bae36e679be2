#!/usr/bin/env node

/**
 * 🔥 超级激进评论获取器
 * 不逃避问题，直接获取所有1472条评论！
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class SuperAggressiveScraper {
    constructor() {
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        
        // 超级激进配置
        this.config = {
            targetComments: 1472,
            maxScrollAttempts: 2000,    // 增加到2000次
            scrollDelay: 500,           // 减少到500ms
            clickDelay: 300,            // 减少到300ms
            waitForContent: 1000,       // 减少到1秒
            superAggressiveMode: true,
            noGiveUp: true              // 永不放弃模式
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    async connectToBrowser() {
        console.log('🔥 超级激进模式启动！');
        console.log('💪 目标：获取所有1472条评论，绝不放弃！');
        
        // 尝试所有可能的端口
        const ports = [55276, 9222, 9223, 9224, 9225, 54345, 54346, 54347];
        
        for (const port of ports) {
            try {
                console.log(`🔍 尝试端口 ${port}...`);
                const browser = await puppeteer.connect({
                    browserURL: `http://localhost:${port}`,
                    defaultViewport: null
                });
                
                const pages = await browser.pages();
                console.log(`📄 找到 ${pages.length} 个页面`);
                
                // 查找小红书页面
                for (let i = 0; i < pages.length; i++) {
                    const page = pages[i];
                    const url = page.url();
                    console.log(`   页面 ${i + 1}: ${url.substring(0, 80)}...`);
                    
                    if (url.includes('xiaohongshu.com') || url.includes('xhs')) {
                        console.log(`✅ 找到小红书页面！端口: ${port}, 页面: ${i + 1}`);
                        return { browser, page, port };
                    }
                }
                
                // 如果没找到小红书页面，使用第一个页面
                if (pages.length > 0) {
                    console.log(`💡 使用第一个页面作为目标`);
                    return { browser, page: pages[0], port };
                }
                
            } catch (error) {
                console.log(`❌ 端口 ${port} 失败: ${error.message}`);
                continue;
            }
        }
        
        throw new Error('🚨 无法连接到任何浏览器端口！请确保比特浏览器正在运行！');
    }

    // 超级激进滚动策略
    async superAggressiveScroll(page) {
        console.log('🚀 启动超级激进滚动策略！');
        console.log(`🎯 目标: ${this.config.targetComments} 条评论`);
        console.log(`💪 最大滚动次数: ${this.config.maxScrollAttempts}`);
        console.log(`⚡ 滚动间隔: ${this.config.scrollDelay}ms`);
        
        let currentCommentCount = 0;
        let previousCommentCount = 0;
        let stableCount = 0;
        let totalScrolls = 0;
        let totalClicks = 0;
        let maxCommentsSeen = 0;
        
        // 预热：快速滚动到评论区域
        console.log('🔥 预热阶段：快速定位评论区域...');
        await this.fastScrollToComments(page);
        
        // 主要滚动阶段
        console.log('💥 主要滚动阶段：超级激进模式！');
        
        for (let i = 0; i < this.config.maxScrollAttempts; i++) {
            const progress = Math.round((i / this.config.maxScrollAttempts) * 100);
            console.log(`\n🔥 超级滚动 ${i + 1}/${this.config.maxScrollAttempts} (${progress}%)`);
            
            // 统计当前评论数量
            currentCommentCount = await this.countCommentsAggressively(page);
            console.log(`   💬 当前评论数: ${currentCommentCount}`);
            
            // 更新最大值
            if (currentCommentCount > maxCommentsSeen) {
                maxCommentsSeen = currentCommentCount;
                console.log(`   🎯 新记录: ${maxCommentsSeen} 条评论！`);
            }
            
            // 检查是否达到目标
            if (currentCommentCount >= this.config.targetComments * 0.98) {
                console.log(`🎉 成功！接近目标评论数: ${currentCommentCount}/${this.config.targetComments}`);
                break;
            }
            
            // 检查进度
            if (currentCommentCount === previousCommentCount) {
                stableCount++;
                console.log(`   ⏸️ 评论数稳定 ${stableCount} 次`);
            } else {
                const newComments = currentCommentCount - previousCommentCount;
                console.log(`   📈 新增评论: ${newComments} 条`);
                stableCount = 0;
                previousCommentCount = currentCommentCount;
            }
            
            // 超级激进点击策略
            if (stableCount >= 1) { // 只要稳定1次就点击
                console.log('   💥 超级激进点击...');
                const clickSuccess = await this.superAggressiveClick(page);
                if (clickSuccess) {
                    totalClicks++;
                    stableCount = 0;
                    console.log(`   ✅ 点击成功！总计: ${totalClicks} 次`);
                }
            }
            
            // 执行超级滚动
            await this.executeSuperScroll(page, i);
            
            // 动态调整等待时间
            let waitTime = this.config.scrollDelay;
            if (currentCommentCount > previousCommentCount) {
                waitTime = Math.max(200, waitTime * 0.5); // 有新内容时加速
            } else if (stableCount >= 5) {
                waitTime = Math.min(2000, waitTime * 1.5); // 无新内容时稍微减速
            }
            
            await new Promise(resolve => setTimeout(resolve, waitTime));
            totalScrolls++;
            
            // 每100次滚动输出详细进度
            if (i % 100 === 0 && i > 0) {
                const completionRate = Math.round((currentCommentCount / this.config.targetComments) * 100);
                console.log(`\n📊 超级进度报告:`);
                console.log(`   🎯 目标完成度: ${completionRate}% (${currentCommentCount}/${this.config.targetComments})`);
                console.log(`   📜 总滚动次数: ${totalScrolls}`);
                console.log(`   💥 总点击次数: ${totalClicks}`);
                console.log(`   🏆 历史最高: ${maxCommentsSeen} 条评论`);
                console.log(`   ⏸️ 当前稳定次数: ${stableCount}`);
                
                if (completionRate >= 80) {
                    console.log(`   🎉 已完成80%以上，继续冲刺！`);
                } else if (completionRate >= 50) {
                    console.log(`   💪 已完成一半，加油！`);
                } else if (completionRate >= 20) {
                    console.log(`   📈 有进展，继续努力！`);
                } else {
                    console.log(`   🔥 刚开始，全力以赴！`);
                }
            }
            
            // 永不放弃模式：即使长时间稳定也继续
            if (stableCount >= 50) {
                console.log('   🔥 永不放弃模式：尝试更激进的策略...');
                await this.desperateMode(page);
                stableCount = 0; // 重置计数器
            }
        }
        
        console.log(`\n🏁 超级激进滚动完成！`);
        console.log(`📊 最终战果:`);
        console.log(`   💬 最终评论数: ${currentCommentCount}`);
        console.log(`   🎯 完成度: ${Math.round((currentCommentCount / this.config.targetComments) * 100)}%`);
        console.log(`   📜 总滚动次数: ${totalScrolls}`);
        console.log(`   💥 总点击次数: ${totalClicks}`);
        console.log(`   🏆 历史最高: ${maxCommentsSeen}`);
        
        return currentCommentCount;
    }

    // 快速滚动到评论区域
    async fastScrollToComments(page) {
        await page.evaluate(() => {
            // 快速滚动策略
            for (let i = 0; i < 10; i++) {
                window.scrollBy(0, 500);
            }
            
            // 查找评论区域
            const commentIndicators = ['条评论', '评论', 'comment', '💬'];
            
            for (const indicator of commentIndicators) {
                const elements = Array.from(document.querySelectorAll('*')).filter(el => 
                    el.textContent.includes(indicator) && el.offsetHeight > 0
                );
                
                if (elements.length > 0) {
                    elements[0].scrollIntoView({ behavior: 'auto', block: 'center' });
                    console.log('快速定位到评论区域:', indicator);
                    return;
                }
            }
            
            // 如果没找到，滚动到页面70%位置
            window.scrollTo(0, document.body.scrollHeight * 0.7);
        });
        
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 执行超级滚动
    async executeSuperScroll(page, scrollIndex) {
        const strategies = [
            // 策略1: 快速小步滚动
            () => page.evaluate(() => window.scrollBy(0, 150 + Math.random() * 50)),
            
            // 策略2: 中等滚动
            () => page.evaluate(() => window.scrollBy(0, 300 + Math.random() * 100)),
            
            // 策略3: 大步滚动
            () => page.evaluate(() => window.scrollBy(0, 500 + Math.random() * 200)),
            
            // 策略4: 直接滚动到底部
            () => page.evaluate(() => window.scrollTo(0, document.body.scrollHeight)),
            
            // 策略5: 滚动到最后一个评论
            () => page.evaluate(() => {
                const comments = document.querySelectorAll('[class*="comment"], [class*="Comment"], [data-testid*="comment"]');
                if (comments.length > 0) {
                    const lastComment = comments[comments.length - 1];
                    lastComment.scrollIntoView({ behavior: 'auto' });
                }
            }),
            
            // 策略6: 随机位置滚动
            () => page.evaluate(() => {
                const randomY = document.body.scrollHeight * (0.6 + Math.random() * 0.4);
                window.scrollTo(0, randomY);
            }),
            
            // 策略7: 向上再向下（刺激加载）
            () => page.evaluate(() => {
                window.scrollBy(0, -200);
                setTimeout(() => window.scrollBy(0, 400), 100);
            }),
            
            // 策略8: 连续快速滚动
            () => page.evaluate(() => {
                for (let i = 0; i < 5; i++) {
                    setTimeout(() => window.scrollBy(0, 100), i * 50);
                }
            })
        ];
        
        // 根据滚动次数和当前情况选择策略
        let strategyIndex;
        if (scrollIndex < 50) {
            // 前50次使用温和策略
            strategyIndex = scrollIndex % 3;
        } else if (scrollIndex < 200) {
            // 中期使用多样化策略
            strategyIndex = scrollIndex % 6;
        } else {
            // 后期使用所有策略
            strategyIndex = scrollIndex % strategies.length;
        }
        
        await strategies[strategyIndex]();
    }

    // 超级激进点击
    async superAggressiveClick(page) {
        try {
            const clickResults = await page.evaluate(() => {
                const results = [];

                // 超级激进文字点击
                const aggressiveTexts = [
                    '加载更多', '展开更多', '查看更多', '显示更多', '更多评论', '更多内容',
                    '展开', '更多', '加载', '查看全部', '展开全部', '点击加载', '点击展开',
                    '显示', '查看', '展开回复', '更多回复', '全部回复', '查看回复',
                    'Load more', 'Show more', 'View more', 'Expand', 'More', 'Load', 'Show'
                ];

                aggressiveTexts.forEach(text => {
                    // 查找所有包含这些文字的元素
                    const allElements = document.querySelectorAll('*');
                    Array.from(allElements).forEach(el => {
                        const elementText = el.textContent.trim();
                        if (elementText.includes(text) &&
                            el.offsetHeight > 0 &&
                            el.offsetWidth > 0 &&
                            elementText.length < 300) {

                            try {
                                // 强制点击，不管位置
                                el.click();
                                results.push(`强制文字点击: ${text}`);
                            } catch (e) {
                                // 如果普通点击失败，尝试事件触发
                                try {
                                    el.dispatchEvent(new MouseEvent('click', {
                                        view: window,
                                        bubbles: true,
                                        cancelable: true
                                    }));
                                    results.push(`事件触发: ${text}`);
                                } catch (e2) {
                                    // 忽略失败
                                }
                            }
                        }
                    });
                });

                // 超级激进按钮点击
                const buttonSelectors = [
                    'button', '[role="button"]', '.btn', '[class*="button"]',
                    '[class*="load"]', '[class*="more"]', '[class*="expand"]',
                    '[class*="show"]', '[class*="view"]', 'a[href*="more"]',
                    'div[onclick]', 'span[onclick]', '[data-click]'
                ];

                buttonSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        Array.from(elements).forEach(el => {
                            if (el.offsetHeight > 0 && el.offsetWidth > 0) {
                                const text = el.textContent.trim().toLowerCase();
                                const hasTargetText = [
                                    'more', '更多', '展开', '加载', 'load', 'expand',
                                    'show', 'view', '显示', '查看', '全部', '评论'
                                ].some(keyword => text.includes(keyword));

                                if (hasTargetText || el.className.includes('load') || el.className.includes('more')) {
                                    try {
                                        el.click();
                                        results.push(`按钮点击: ${text.substring(0, 20)}`);
                                    } catch (e) {
                                        try {
                                            el.dispatchEvent(new MouseEvent('click', {
                                                view: window,
                                                bubbles: true,
                                                cancelable: true
                                            }));
                                            results.push(`按钮事件: ${text.substring(0, 20)}`);
                                        } catch (e2) {
                                            // 忽略失败
                                        }
                                    }
                                }
                            }
                        });
                    } catch (e) {
                        // 忽略选择器错误
                    }
                });

                // 超级激进：点击所有可能的交互元素
                const interactiveSelectors = [
                    '[tabindex]', '[onclick]', '[data-testid]', '[data-action]',
                    '.clickable', '.interactive', '.expandable', '.collapsible'
                ];

                interactiveSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        Array.from(elements).slice(0, 10).forEach(el => { // 限制数量避免过度点击
                            if (el.offsetHeight > 0 && el.offsetWidth > 0) {
                                try {
                                    el.click();
                                    results.push(`交互元素: ${selector}`);
                                } catch (e) {
                                    // 忽略失败
                                }
                            }
                        });
                    } catch (e) {
                        // 忽略选择器错误
                    }
                });

                return results;
            });

            if (clickResults.length > 0) {
                console.log(`   💥 超级点击成功: ${clickResults.slice(0, 5).join(', ')}${clickResults.length > 5 ? '...' : ''}`);
                return true;
            } else {
                console.log(`   ⚠️ 超级点击未找到目标`);
                return false;
            }
        } catch (error) {
            console.log(`   ❌ 超级点击出错: ${error.message}`);
            return false;
        }
    }

    // 绝望模式：最后的尝试
    async desperateMode(page) {
        console.log('   🆘 启动绝望模式：最后的尝试...');

        await page.evaluate(() => {
            // 绝望策略1: 疯狂滚动
            for (let i = 0; i < 20; i++) {
                window.scrollBy(0, 100);
                window.scrollBy(0, -50);
            }

            // 绝望策略2: 点击所有可见元素
            const allElements = document.querySelectorAll('*');
            let clickCount = 0;
            Array.from(allElements).forEach(el => {
                if (clickCount < 50 && el.offsetHeight > 0 && el.offsetWidth > 0) {
                    const text = el.textContent.trim();
                    if (text.length > 0 && text.length < 100 &&
                        (text.includes('更多') || text.includes('展开') || text.includes('加载') ||
                         text.includes('评论') || text.includes('回复'))) {
                        try {
                            el.click();
                            clickCount++;
                        } catch (e) {
                            // 忽略失败
                        }
                    }
                }
            });

            // 绝望策略3: 触发所有可能的事件
            ['scroll', 'resize', 'focus', 'blur'].forEach(eventType => {
                try {
                    window.dispatchEvent(new Event(eventType));
                } catch (e) {
                    // 忽略失败
                }
            });

            // 绝望策略4: 滚动到各个位置
            const positions = [0, 0.25, 0.5, 0.75, 1.0];
            positions.forEach((pos, index) => {
                setTimeout(() => {
                    window.scrollTo(0, document.body.scrollHeight * pos);
                }, index * 200);
            });
        });

        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 激进统计评论数量
    async countCommentsAggressively(page) {
        return await page.evaluate(() => {
            const pageText = document.body.textContent;

            // 超级激进统计方法
            const methods = [
                // 方法1: 时间格式统计（最可靠）
                () => {
                    const timePatterns = [
                        /\d{2}-\d{2}/g,
                        /\d+小时前/g,
                        /\d+分钟前/g,
                        /\d+天前/g,
                        /\d{4}-\d{2}-\d{2}/g
                    ];

                    let totalMatches = 0;
                    timePatterns.forEach(pattern => {
                        const matches = pageText.match(pattern) || [];
                        totalMatches += matches.length;
                    });

                    return totalMatches;
                },

                // 方法2: 回复数统计
                () => {
                    const replyMatches = pageText.match(/\d+回复/g) || [];
                    return replyMatches.length * 2; // 假设每个回复对应2条相关内容
                },

                // 方法3: DOM元素统计
                () => {
                    const selectors = [
                        '[class*="comment"]',
                        '[class*="Comment"]',
                        '[data-testid*="comment"]',
                        '[class*="reply"]',
                        '[class*="Reply"]',
                        '[class*="user"]',
                        '[class*="User"]'
                    ];

                    let maxCount = 0;
                    selectors.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            maxCount = Math.max(maxCount, elements.length);
                        } catch (e) {
                            // 忽略选择器错误
                        }
                    });

                    return maxCount;
                },

                // 方法4: 关键词统计
                () => {
                    const keywords = ['求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯', 'Carina'];
                    let totalMatches = 0;

                    keywords.forEach(keyword => {
                        const regex = new RegExp(keyword, 'g');
                        const matches = pageText.match(regex) || [];
                        totalMatches += matches.length;
                    });

                    return Math.floor(totalMatches / 3); // 假设每个评论平均包含3个关键词
                },

                // 方法5: 用户ID统计
                () => {
                    const userIdMatches = pageText.match(/小红薯[A-F0-9]{8}/g) || [];
                    return userIdMatches.length * 3; // 假设每个用户ID对应3条相关内容
                },

                // 方法6: 点赞数统计
                () => {
                    const likeMatches = pageText.match(/\d+赞/g) || [];
                    return likeMatches.length;
                },

                // 方法7: 表情符号统计
                () => {
                    const emojiMatches = pageText.match(/[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/g) || [];
                    return Math.floor(emojiMatches.length / 8); // 假设每个评论平均包含8个表情
                },

                // 方法8: 行数统计
                () => {
                    const lines = pageText.split('\n').filter(line => {
                        const trimmed = line.trim();
                        return trimmed.length > 5 &&
                               (trimmed.includes('求带') || trimmed.includes('宝子') ||
                                trimmed.includes('学姐') || trimmed.includes('兼职') ||
                                /\d{2}-\d{2}/.test(trimmed));
                    });
                    return lines.length;
                }
            ];

            // 计算所有方法的结果
            const counts = methods.map((method, index) => {
                try {
                    const result = method();
                    return { method: index + 1, count: result };
                } catch (e) {
                    return { method: index + 1, count: 0 };
                }
            });

            // 取最大值，但也考虑一致性
            const maxCount = Math.max(...counts.map(c => c.count));
            const avgCount = counts.reduce((sum, c) => sum + c.count, 0) / counts.length;

            // 如果最大值远大于平均值，可能是异常，使用第二大的值
            if (maxCount > avgCount * 4) {
                const sortedCounts = counts.map(c => c.count).sort((a, b) => b - a);
                return sortedCounts[1] || maxCount;
            }

            return maxCount;
        });
    }

    // 超级激进评论提取
    async superExtractComments(page) {
        console.log('🧠 开始超级激进评论提取...');

        // 获取页面文本
        const pageText = await page.evaluate(() => document.body.textContent);
        console.log(`📄 页面文本长度: ${pageText.length}`);

        // 提取基本信息
        const noteInfo = await page.evaluate(() => {
            const info = {
                id: '',
                title: '',
                author: '',
                totalCommentCount: 0
            };

            // 提取笔记ID
            const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
            if (urlMatch) {
                info.id = urlMatch[1];
            }

            // 提取标题
            const titleElement = document.querySelector('title');
            if (titleElement) {
                info.title = titleElement.textContent.replace(' - 小红书', '');
            }

            // 提取评论总数
            const pageText = document.body.textContent;
            const commentCountMatch = pageText.match(/(\d+)\s*条评论/);
            if (commentCountMatch) {
                info.totalCommentCount = parseInt(commentCountMatch[1]);
            }

            // 提取作者
            if (pageText.includes('漫娴学姐')) {
                info.author = '漫娴学姐 招暑假工版';
            }

            return info;
        });

        // 超级解析评论
        const comments = this.superParseComments(pageText);

        return {
            noteInfo,
            comments,
            extractStats: {
                totalTextLength: pageText.length,
                successfulExtractions: comments.length,
                extractionMethods: ['super-aggressive']
            },
            extractTime: new Date().toISOString()
        };
    }

    // 超级解析评论
    superParseComments(pageText) {
        console.log('🔥 开始超级解析评论...');

        const allComments = [];

        // 超级解析策略
        const strategies = [
            { name: '时间戳超级解析', method: () => this.superParseByTime(pageText) },
            { name: '关键词超级解析', method: () => this.superParseByKeywords(pageText) },
            { name: '用户名超级解析', method: () => this.superParseByUsers(pageText) },
            { name: '表情符号超级解析', method: () => this.superParseByEmoji(pageText) },
            { name: '数字模式超级解析', method: () => this.superParseByNumbers(pageText) },
            { name: '结构化超级解析', method: () => this.superParseByStructure(pageText) }
        ];

        strategies.forEach(strategy => {
            try {
                console.log(`🔥 执行${strategy.name}...`);
                const strategyComments = strategy.method();
                console.log(`   💥 ${strategy.name}提取到 ${strategyComments.length} 条评论`);
                allComments.push(...strategyComments);
            } catch (error) {
                console.log(`   ❌ ${strategy.name}失败: ${error.message}`);
            }
        });

        console.log(`📊 总计收集到 ${allComments.length} 条原始评论`);

        // 超级去重
        const uniqueComments = this.superDeduplication(allComments);

        console.log(`✅ 超级解析完成，最终提取到 ${uniqueComments.length} 条评论`);
        return uniqueComments;
    }

    // 超级时间解析
    superParseByTime(pageText) {
        const comments = [];
        const timePatterns = [
            /(\d{2}-\d{2})/g,
            /(\d+小时前)/g,
            /(\d+分钟前)/g,
            /(\d+天前)/g
        ];

        timePatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                const timeStr = match[1];
                const timeIndex = match.index;

                // 提取更大范围的上下文
                const startIndex = Math.max(0, timeIndex - 300);
                const endIndex = Math.min(pageText.length, timeIndex + 1200);
                const contextText = pageText.substring(startIndex, endIndex);

                if (contextText.length > 30 && this.looksLikeComment(contextText)) {
                    const comment = this.createSuperComment(contextText, comments.length + 1, 'time');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });

        return comments;
    }

    // 超级关键词解析
    superParseByKeywords(pageText) {
        const comments = [];
        const keywords = [
            '求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯',
            'Carina', '广州', '工作', '赚钱', '日入', '月入', '陪聊',
            '直播', '客服', '代理', '抢票', '演唱会', '游戏', '试玩',
            '大学生', '暑假工', '兼织', '专米', '推荐', '软件', '平台'
        ];

        keywords.forEach(keyword => {
            const regex = new RegExp(keyword, 'g');
            let match;

            while ((match = regex.exec(pageText)) !== null) {
                const keywordIndex = match.index;

                // 提取更大范围的上下文
                const startIndex = Math.max(0, keywordIndex - 200);
                const endIndex = Math.min(pageText.length, keywordIndex + 1000);
                const contextText = pageText.substring(startIndex, endIndex);

                if (contextText.length > 25 && this.looksLikeComment(contextText)) {
                    const comment = this.createSuperComment(contextText, comments.length + 1, 'keyword');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });

        return comments;
    }

    // 其他超级解析方法（简化版）
    superParseByUsers(pageText) {
        const comments = [];
        const userPatterns = [
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(\d{2}-\d{2})/g,
            /小红薯([A-F0-9]{8,})/g,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*(求带|宝子|学姐)/g
        ];

        userPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                const startIndex = Math.max(0, match.index - 150);
                const endIndex = Math.min(pageText.length, match.index + 800);
                const contextText = pageText.substring(startIndex, endIndex);

                if (contextText.length > 20 && this.looksLikeComment(contextText)) {
                    const comment = this.createSuperComment(contextText, comments.length + 1, 'user');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });

        return comments;
    }

    superParseByEmoji(pageText) {
        const comments = [];
        const emojiPattern = /[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/g;
        let match;

        while ((match = emojiPattern.exec(pageText)) !== null) {
            const startIndex = Math.max(0, match.index - 100);
            const endIndex = Math.min(pageText.length, match.index + 600);
            const contextText = pageText.substring(startIndex, endIndex);

            if (contextText.length > 20 && this.looksLikeComment(contextText)) {
                const comment = this.createSuperComment(contextText, comments.length + 1, 'emoji');
                if (comment) {
                    comments.push(comment);
                }
            }
        }

        return comments;
    }

    superParseByNumbers(pageText) {
        const comments = [];
        const numberPatterns = [
            /(\d+)\s*赞/g,
            /(\d+)\s*回复/g,
            /日入\s*(\d+)/g,
            /月入\s*(\d+)/g,
            /(\d+)\s*元/g
        ];

        numberPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(pageText)) !== null) {
                const startIndex = Math.max(0, match.index - 150);
                const endIndex = Math.min(pageText.length, match.index + 500);
                const contextText = pageText.substring(startIndex, endIndex);

                if (contextText.length > 15 && this.looksLikeComment(contextText)) {
                    const comment = this.createSuperComment(contextText, comments.length + 1, 'number');
                    if (comment) {
                        comments.push(comment);
                    }
                }
            }
        });

        return comments;
    }

    superParseByStructure(pageText) {
        const comments = [];
        const lines = pageText.split('\n').map(line => line.trim()).filter(line => line.length > 3);

        let currentComment = null;
        let commentIndex = 0;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            if (this.shouldSkipLine(line)) continue;

            if (this.isCommentStart(line)) {
                if (currentComment && this.isValidComment(currentComment.content)) {
                    const comment = this.createSuperComment(currentComment.content, ++commentIndex, 'structure');
                    if (comment) {
                        comments.push(comment);
                    }
                }

                currentComment = { content: line };
            } else if (currentComment && line.length > 2) {
                currentComment.content += ' ' + line;

                if (currentComment.content.length > 2000) {
                    const comment = this.createSuperComment(currentComment.content, ++commentIndex, 'structure');
                    if (comment) {
                        comments.push(comment);
                    }
                    currentComment = null;
                }
            }
        }

        if (currentComment && this.isValidComment(currentComment.content)) {
            const comment = this.createSuperComment(currentComment.content, ++commentIndex, 'structure');
            if (comment) {
                comments.push(comment);
            }
        }

        return comments;
    }

    // 判断是否看起来像评论
    looksLikeComment(text) {
        const commentFeatures = [
            /\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前/,
            /求带|宝子|学姐|兼职|聊天员|陪玩/,
            /[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/,
            /\d+赞|\d+回复/,
            /[A-Za-z0-9_\u4e00-\u9fff]{2,20}\s*\d{2}-\d{2}/
        ];

        const hasFeature = commentFeatures.some(pattern => pattern.test(text));

        const excludePatterns = [
            /沪ICP备|营业执照|公网安备|增值电信|医疗器械/,
            /window\.|function|console\.|document\./,
            /创作服务|直播管理|专业号|商家入驻/
        ];

        const isExcluded = excludePatterns.some(pattern => pattern.test(text));

        return hasFeature && !isExcluded && text.length >= 10 && text.length <= 2500;
    }

    // 创建超级评论对象
    createSuperComment(text, id, source) {
        const comment = {
            id: id,
            userId: '',
            username: '',
            content: '',
            time: '',
            likes: 0,
            replyCount: 0,
            isAuthor: text.includes('作者'),
            isPinned: text.includes('置顶'),
            extractedInfo: {
                source: source,
                originalLength: text.length
            }
        };

        // 提取时间
        const timePatterns = [
            /(\d{2}-\d{2})/,
            /(\d+小时前)/,
            /(\d+分钟前)/,
            /(\d+天前)/
        ];

        for (const pattern of timePatterns) {
            const match = text.match(pattern);
            if (match) {
                comment.time = match[1];
                break;
            }
        }

        // 提取用户名
        const userPatterns = [
            /^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}/,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带/,
            /^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})/,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*学姐/,
            /([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*宝子/
        ];

        for (const pattern of userPatterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                let username = match[1].trim();
                username = username.replace(/[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/g, '');
                if (username.length >= 2 && username.length <= 20) {
                    comment.username = username;
                    break;
                }
            }
        }

        // 清理内容
        let content = text;
        content = content.replace(/\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前/g, '');
        content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');
        content = content.replace(/作者|置顶评论|回复|赞|仅自己可见/g, '');
        if (comment.username) {
            content = content.replace(new RegExp(comment.username, 'g'), '');
        }
        content = content.replace(/\s+/g, ' ').trim();
        content = content.replace(/^[^\w\u4e00-\u9fff]+/, '').trim();

        comment.content = content;

        // 提取数字信息
        const likeMatch = text.match(/(\d+)\s*赞/);
        if (likeMatch) {
            const num = parseInt(likeMatch[1]);
            if (num > 0 && num < 50000) {
                comment.likes = num;
            }
        }

        const replyMatch = text.match(/(\d+)\s*回复/);
        if (replyMatch) {
            const num = parseInt(replyMatch[1]);
            if (num > 0 && num < 10000) {
                comment.replyCount = num;
            }
        }

        // 提取用户ID
        const userIdMatch = text.match(/小红薯([A-F0-9]{8,})/);
        if (userIdMatch) {
            comment.userId = userIdMatch[1];
        }

        return comment.content.length >= 5 && comment.content.length <= 2500 ? comment : null;
    }

    // 超级去重
    superDeduplication(comments) {
        console.log('🔥 开始超级去重...');

        const unique = [];
        const seen = new Map();

        // 按来源分组并排序
        const bySource = {};
        comments.forEach(comment => {
            const source = comment.extractedInfo?.source || 'unknown';
            if (!bySource[source]) {
                bySource[source] = [];
            }
            bySource[source].push(comment);
        });

        console.log('📊 按来源分组:');
        Object.entries(bySource).forEach(([source, sourceComments]) => {
            console.log(`   ${source}: ${sourceComments.length}条`);
        });

        // 优先级排序
        const sourcePriority = ['time', 'user', 'keyword', 'structure', 'emoji', 'number'];

        sourcePriority.forEach(source => {
            if (bySource[source]) {
                bySource[source].forEach(comment => {
                    const contentKey = this.generateContentKey(comment.content);

                    if (!seen.has(contentKey)) {
                        seen.set(contentKey, comment);
                        unique.push(comment);
                    } else {
                        const existing = seen.get(contentKey);
                        if (this.isCommentBetter(comment, existing)) {
                            const index = unique.indexOf(existing);
                            if (index !== -1) {
                                unique[index] = comment;
                                seen.set(contentKey, comment);
                            }
                        }
                    }
                });
            }
        });

        // 重新分配ID
        unique.forEach((comment, index) => {
            comment.id = index + 1;
        });

        console.log(`✅ 超级去重完成: ${comments.length} → ${unique.length}`);
        return unique;
    }

    // 辅助方法
    generateContentKey(content) {
        let normalized = content.toLowerCase();
        normalized = normalized.replace(/\s+/g, ' ');
        normalized = normalized.replace(/[^\w\u4e00-\u9fff\s]/g, '');
        return normalized.substring(0, 50);
    }

    isCommentBetter(comment1, comment2) {
        let score1 = 0;
        let score2 = 0;

        if (comment1.username) score1 += 10;
        if (comment2.username) score2 += 10;

        if (comment1.time) score1 += 8;
        if (comment2.time) score2 += 8;

        if (comment1.userId) score1 += 15;
        if (comment2.userId) score2 += 15;

        if (comment1.likes > 0) score1 += 5;
        if (comment2.likes > 0) score2 += 5;

        if (comment1.replyCount > 0) score1 += 5;
        if (comment2.replyCount > 0) score2 += 5;

        const len1 = comment1.content.length;
        const len2 = comment2.content.length;

        if (len1 >= 20 && len1 <= 500) score1 += 3;
        if (len2 >= 20 && len2 <= 500) score2 += 3;

        return score1 > score2;
    }

    shouldSkipLine(line) {
        const skipPatterns = [
            '加载中', '你还没有', '温馨提示', '广告屏蔽', '我知道了',
            '沪ICP备', '营业执照', '公网安备', '增值电信', '医疗器械',
            'window.', 'function', 'console.', 'document.'
        ];

        return skipPatterns.some(pattern => line.includes(pattern)) ||
               line.length < 3 ||
               /^[0-9\s\-:\.]+$/.test(line);
    }

    isCommentStart(line) {
        return line.includes('作者') ||
               line.includes('置顶') ||
               line.match(/\d{2}-\d{2}/) ||
               line.match(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/) ||
               ['求带', '宝子', '学姐', '兼职', '聊天员', '陪玩'].some(keyword => line.includes(keyword));
    }

    isValidComment(content) {
        return content && content.length >= 10 && content.length <= 2500;
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }
