#!/usr/bin/env node

/**
 * 🔄 配置同步脚本
 * 确保所有文件都使用统一的比特浏览器配置
 */

const fs = require('fs');
const path = require('path');
const { BITBROWSER_CONFIG } = require('./bitbrowser-config');

// 需要更新的文件列表
const FILES_TO_UPDATE = [
    'click-notes-collector.js',
    'accurate-notes-collector.js',
    'simple-click-collector.js',
    'scroll-notes-collector.js',
    'xiaohongshu_browser_extractor.js',
    'start_bitbrowser.py',
    'bitbrowser_tab19_collector.py'
];

// 配置映射
const CONFIG_MAPPINGS = {
    // JavaScript文件的配置更新
    js: {
        'http://127.0.0.1:54345': BITBROWSER_CONFIG.api_url,
        'http://127.0.0.1:56906': BITBROWSER_CONFIG.api_url,
        '0d094596cb404282be3f814b98139c74': BITBROWSER_CONFIG.browser_id,
        '95362955272': BITBROWSER_CONFIG.browser_id.substring(0, 11),
        'ca28ee5ca6de4d209182a83aa16a2044': BITBROWSER_CONFIG.api_token
    },
    // Python文件的配置更新
    py: {
        'http://127.0.0.1:54345': BITBROWSER_CONFIG.api_url,
        '0d094596cb404282be3f814b98139c74': BITBROWSER_CONFIG.browser_id,
        'ca28ee5ca6de4d209182a83aa16a2044': BITBROWSER_CONFIG.api_token
    }
};

function syncConfig() {
    console.log('🔄 开始同步配置...\n');
    
    let updatedFiles = 0;
    let totalReplacements = 0;

    FILES_TO_UPDATE.forEach(filename => {
        const filePath = path.join(__dirname, filename);
        
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️ 文件不存在: ${filename}`);
            return;
        }

        try {
            let content = fs.readFileSync(filePath, 'utf8');
            const originalContent = content;
            const fileExt = path.extname(filename).substring(1);
            const mappings = CONFIG_MAPPINGS[fileExt] || CONFIG_MAPPINGS.js;
            
            let fileReplacements = 0;

            // 执行替换
            Object.entries(mappings).forEach(([oldValue, newValue]) => {
                const regex = new RegExp(oldValue.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
                const matches = content.match(regex);
                if (matches) {
                    content = content.replace(regex, newValue);
                    fileReplacements += matches.length;
                    console.log(`   ✅ ${oldValue} → ${newValue} (${matches.length}次)`);
                }
            });

            // 如果有更改，写入文件
            if (content !== originalContent) {
                fs.writeFileSync(filePath, content, 'utf8');
                console.log(`📝 已更新: ${filename} (${fileReplacements}处更改)`);
                updatedFiles++;
                totalReplacements += fileReplacements;
            } else {
                console.log(`✅ 无需更新: ${filename}`);
            }

        } catch (error) {
            console.log(`❌ 更新失败: ${filename} - ${error.message}`);
        }

        console.log('');
    });

    // 显示总结
    console.log('📊 同步总结:');
    console.log(`   更新文件数: ${updatedFiles}`);
    console.log(`   总替换次数: ${totalReplacements}`);
    console.log(`   当前配置:`);
    console.log(`     API地址: ${BITBROWSER_CONFIG.api_url}`);
    console.log(`     浏览器ID: ${BITBROWSER_CONFIG.browser_id}`);
    console.log(`     API Token: ${BITBROWSER_CONFIG.api_token}`);

    if (updatedFiles > 0) {
        console.log('\n🎉 配置同步完成！建议运行测试验证配置是否正确。');
        console.log('💡 运行测试: node test-unified-config.js');
    } else {
        console.log('\n✅ 所有文件配置已是最新！');
    }
}

// 验证配置一致性
function verifyConfig() {
    console.log('🔍 验证配置一致性...\n');
    
    const issues = [];

    FILES_TO_UPDATE.forEach(filename => {
        const filePath = path.join(__dirname, filename);
        
        if (!fs.existsSync(filePath)) {
            return;
        }

        try {
            const content = fs.readFileSync(filePath, 'utf8');
            
            // 检查是否包含旧配置
            const oldConfigs = [
                'http://127.0.0.1:54345',
                '0d094596cb404282be3f814b98139c74'
            ];

            oldConfigs.forEach(oldConfig => {
                if (content.includes(oldConfig)) {
                    issues.push(`${filename}: 包含旧配置 ${oldConfig}`);
                }
            });

        } catch (error) {
            issues.push(`${filename}: 读取失败 - ${error.message}`);
        }
    });

    if (issues.length === 0) {
        console.log('✅ 所有文件配置一致！');
    } else {
        console.log('❌ 发现配置不一致:');
        issues.forEach(issue => console.log(`   ${issue}`));
        console.log('\n💡 运行同步: node sync-config.js');
    }

    return issues.length === 0;
}

// 命令行参数处理
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--verify')) {
        verifyConfig();
    } else {
        syncConfig();
    }
}

module.exports = { syncConfig, verifyConfig };
