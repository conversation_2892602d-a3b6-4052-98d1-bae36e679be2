#!/usr/bin/env node

/**
 * 🧪 测试所有浏览器实例的启动状态
 * 找到可以成功启动的浏览器实例
 */

const axios = require('axios');

const CONFIG = {
    localServer: 'http://localhost:3000'
};

async function testAllBrowsers() {
    console.log('🧪 测试所有浏览器实例的启动状态...\n');

    try {
        // 1. 获取浏览器列表
        console.log('1️⃣ 获取浏览器列表...');
        const listResponse = await axios.post(`${CONFIG.localServer}/api/xiaohongshu/bitbrowser/list`, {
            page: 0,
            pageSize: 50
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 15000
        });

        if (!listResponse.data.success) {
            console.log('❌ 获取浏览器列表失败');
            return;
        }

        const browsers = listResponse.data.data.browsers || [];
        console.log(`✅ 找到 ${browsers.length} 个浏览器实例\n`);

        // 2. 测试每个浏览器的启动状态
        console.log('2️⃣ 测试浏览器启动状态...\n');
        
        const testResults = [];
        
        for (let i = 0; i < Math.min(browsers.length, 5); i++) { // 只测试前5个
            const browser = browsers[i];
            console.log(`🔍 测试浏览器 ${i + 1}: ${browser.name || browser.id}`);
            console.log(`   ID: ${browser.id}`);
            console.log(`   当前状态: ${browser.status || '未知'}`);
            
            try {
                // 尝试启动浏览器
                const startResponse = await axios.post(`${CONFIG.localServer}/api/xiaohongshu/bitbrowser/start-specific`, {
                    browserId: browser.id
                }, {
                    headers: { 'Content-Type': 'application/json' },
                    timeout: 20000
                });
                
                if (startResponse.data.success) {
                    console.log(`   ✅ 启动成功!`);
                    testResults.push({
                        browser: browser,
                        status: 'success',
                        message: '启动成功'
                    });
                } else {
                    console.log(`   ❌ 启动失败: ${startResponse.data.message}`);
                    testResults.push({
                        browser: browser,
                        status: 'failed',
                        message: startResponse.data.message
                    });
                }
                
            } catch (error) {
                const errorMsg = error.response?.data?.message || error.message;
                console.log(`   ❌ 启动出错: ${errorMsg}`);
                testResults.push({
                    browser: browser,
                    status: 'error',
                    message: errorMsg
                });
            }
            
            console.log(''); // 空行分隔
            
            // 等待一秒避免请求过快
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // 3. 显示测试总结
        console.log('📊 测试总结:');
        console.log('=' * 50);
        
        const successBrowsers = testResults.filter(r => r.status === 'success');
        const failedBrowsers = testResults.filter(r => r.status === 'failed');
        const errorBrowsers = testResults.filter(r => r.status === 'error');
        
        console.log(`✅ 成功启动: ${successBrowsers.length} 个`);
        console.log(`❌ 启动失败: ${failedBrowsers.length} 个`);
        console.log(`⚠️  启动出错: ${errorBrowsers.length} 个`);
        
        if (successBrowsers.length > 0) {
            console.log('\n🎉 可用的浏览器实例:');
            successBrowsers.forEach((result, index) => {
                console.log(`   ${index + 1}. ${result.browser.name || result.browser.id}`);
                console.log(`      ID: ${result.browser.id}`);
            });
        }
        
        if (failedBrowsers.length > 0) {
            console.log('\n⚠️  启动失败的浏览器:');
            failedBrowsers.forEach((result, index) => {
                console.log(`   ${index + 1}. ${result.browser.name || result.browser.id}`);
                console.log(`      原因: ${result.message}`);
            });
        }
        
    } catch (error) {
        console.error('❌ 测试过程出错:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    testAllBrowsers().catch(console.error);
}

module.exports = { testAllBrowsers };
