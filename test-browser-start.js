#!/usr/bin/env node

/**
 * 🚀 测试启动比特浏览器实例
 */

const axios = require('axios');

// 比特浏览器配置
const BITBROWSER_CONFIG = {
    api_url: "http://127.0.0.1:56906",
    api_token: "ca28ee5ca6de4d209182a83aa16a2044",
    browser_id: "e3afefd184384c3f90c78b6b19309ca0" // ace1浏览器
};

async function testBrowserStart() {
    console.log('🚀 测试启动比特浏览器实例...\n');

    try {
        // 1. 启动浏览器
        console.log('1️⃣ 启动浏览器实例...');
        const startResponse = await axios.post(`${BITBROWSER_CONFIG.api_url}/browser/open`, {
            id: BITBROWSER_CONFIG.browser_id
        }, {
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': BITBROWSER_CONFIG.api_token
            },
            timeout: 30000
        });

        console.log('📊 启动响应:', JSON.stringify(startResponse.data, null, 2));

        if (startResponse.data && startResponse.data.success) {
            console.log('✅ 浏览器启动成功');
            const result = startResponse.data.data;
            
            if (result) {
                console.log(`🔌 调试端口: ${result.debug_port || result.selenium_port || '未提供'}`);
                console.log(`🌐 WebSocket: ${result.ws_endpoint || '未提供'}`);
                console.log(`🔗 HTTP端点: ${result.http || '未提供'}`);
                
                // 2. 测试调试端口连接
                const debugPort = result.debug_port || result.selenium_port;
                if (debugPort) {
                    await testDebugPort(debugPort);
                }
                
                // 3. 等待用户操作
                console.log('\n📋 下一步操作:');
                console.log('1. 在打开的浏览器中访问小红书网站');
                console.log('2. 登录您的小红书账号');
                console.log('3. 导航到要采集的页面');
                console.log('4. 运行数据采集脚本');
                
                return result;
            }
        } else {
            console.log('❌ 浏览器启动失败:', startResponse.data);
            return null;
        }
    } catch (error) {
        console.log('❌ 启动浏览器失败:', error.message);
        if (error.response) {
            console.log('📋 错误详情:', error.response.data);
        }
        return null;
    }
}

async function testDebugPort(port) {
    console.log(`\n2️⃣ 测试调试端口 ${port}...`);
    
    try {
        const response = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });
        
        const tabs = response.data;
        console.log(`✅ 调试端口连接成功，找到 ${tabs.length} 个标签页`);
        
        // 查找小红书标签页
        const xiaohongshuTabs = tabs.filter(tab => 
            tab.url && (
                tab.url.includes('xiaohongshu.com') || 
                tab.title.includes('小红书')
            )
        );
        
        if (xiaohongshuTabs.length > 0) {
            console.log(`🎯 找到 ${xiaohongshuTabs.length} 个小红书标签页:`);
            xiaohongshuTabs.forEach((tab, index) => {
                console.log(`   ${index + 1}. ${tab.title}`);
                console.log(`      URL: ${tab.url.substring(0, 80)}...`);
            });
        } else {
            console.log('⚠️ 未找到小红书标签页，请手动打开小红书网站');
        }
        
        return true;
    } catch (error) {
        console.log(`❌ 调试端口 ${port} 连接失败: ${error.message}`);
        return false;
    }
}

if (require.main === module) {
    testBrowserStart().then(result => {
        if (result) {
            console.log('\n🎉 浏览器启动测试完成！');
        } else {
            console.log('\n❌ 浏览器启动测试失败！');
        }
    }).catch(error => {
        console.error('❌ 测试过程出错:', error.message);
    });
}

module.exports = { testBrowserStart, testDebugPort };
