#!/usr/bin/env node

/**
 * 🧪 比特浏览器实例测试脚本
 * 专门用于测试和获取浏览器实例列表
 */

const axios = require('axios');

const CONFIG = {
    localServer: 'http://localhost:3000',
    bitbrowserAPI: 'http://127.0.0.1:54345',
    apiToken: 'ca28ee5ca6de4d209182a83aa16a2044'
};

async function testBrowserList() {
    console.log('🧪 开始测试比特浏览器实例列表...\n');

    // 测试1: 通过本地服务器获取
    console.log('1️⃣ 通过本地服务器获取浏览器列表:');
    try {
        const response = await axios.post(`${CONFIG.localServer}/api/xiaohongshu/bitbrowser/list`, {
            page: 0,  // 修正：page从0开始
            pageSize: 50
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 15000
        });

        console.log('✅ 响应成功');
        console.log('📊 总数:', response.data.data?.total || 0);
        console.log('📋 当前页浏览器数:', response.data.data?.browsers?.length || 0);
        
        if (response.data.data?.browsers?.length > 0) {
            console.log('🌐 浏览器实例列表:');
            response.data.data.browsers.forEach((browser, index) => {
                console.log(`   ${index + 1}. ${browser.name || browser.id}`);
                console.log(`      ID: ${browser.id}`);
                console.log(`      状态: ${browser.status || '未知'}`);
                console.log(`      调试端口: ${browser.debug_port || '未分配'}`);
                console.log('');
            });
        }
        
    } catch (error) {
        console.log('❌ 失败:', error.message);
    }

    // 测试2: 直接调用比特浏览器API
    console.log('\n2️⃣ 直接调用比特浏览器API:');
    try {
        const response = await axios.post(`${CONFIG.bitbrowserAPI}/browser/list`, {
            page: 0,  // 修正：page从0开始
            pageSize: 50
        }, {
            headers: { 
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${CONFIG.apiToken}`
            },
            timeout: 15000
        });

        console.log('✅ 直接API调用成功');
        console.log('📊 API响应:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.log('❌ 直接API调用失败:', error.message);
        if (error.response) {
            console.log('📋 错误详情:', error.response.data);
        }
    }

    // 测试3: 尝试不同的分页参数
    console.log('\n3️⃣ 尝试不同的分页参数:');
    const pageTests = [
        { page: 0, pageSize: 20 },
        { page: 1, pageSize: 10 },
        { page: 1, pageSize: 20 },
        { page: 1, pageSize: 100 }
    ];

    for (const params of pageTests) {
        try {
            console.log(`   测试参数: page=${params.page}, pageSize=${params.pageSize}`);
            const response = await axios.post(`${CONFIG.bitbrowserAPI}/browser/list`, params, {
                headers: { 
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${CONFIG.apiToken}`
                },
                timeout: 10000
            });

            const data = response.data.data || response.data;
            console.log(`   ✅ 总数: ${data.totalNum || 0}, 列表长度: ${data.list?.length || 0}`);
            
            if (data.list && data.list.length > 0) {
                console.log('   🎯 找到浏览器实例！');
                data.list.forEach((browser, index) => {
                    console.log(`      ${index + 1}. ${browser.name || browser.id} (${browser.status || '未知'})`);
                });
                break; // 找到数据就停止
            }
            
        } catch (error) {
            console.log(`   ❌ 失败: ${error.message}`);
        }
    }

    // 测试4: 测试连接状态
    console.log('\n4️⃣ 测试完整连接状态:');
    try {
        const response = await axios.post(`${CONFIG.localServer}/api/xiaohongshu/test-browser-connection`, {}, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 20000
        });

        console.log('📊 连接测试结果:');
        if (response.data.success) {
            const summary = response.data.data.summary;
            console.log(`   API连接: ${summary.apiConnected ? '✅' : '❌'}`);
            console.log(`   调试端口: ${summary.debugConnected ? '✅' : '❌'}`);
            console.log(`   目标浏览器: ${summary.targetBrowserFound ? '✅' : '❌'}`);
            console.log(`   小红书页面: ${summary.xiaohongshuPageFound ? '✅' : '❌'}`);
        } else {
            console.log(`   ❌ ${response.data.message}`);
        }
        
    } catch (error) {
        console.log('❌ 连接测试失败:', error.message);
    }

    console.log('\n🏁 测试完成！');
}

// 运行测试
if (require.main === module) {
    testBrowserList().catch(console.error);
}

module.exports = { testBrowserList };
