#!/usr/bin/env node

/**
 * 🧪 测试评论采集功能
 */

const axios = require('axios');
const WebSocket = require('ws');

async function testCommentsExtraction() {
    console.log('🧪 测试评论采集功能...\n');

    try {
        // 1. 获取调试端口
        console.log('1️⃣ 获取调试端口...');
        const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });

        const port = response.data.data.result.http.split(':')[1];
        console.log(`✅ 调试端口: ${port}`);

        // 2. 获取标签页
        console.log('2️⃣ 获取小红书标签页...');
        const tabsResponse = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });

        const tab = tabsResponse.data.find(t => 
            t.url && t.url.includes('xiaohongshu.com')
        );

        if (!tab) {
            throw new Error('未找到小红书标签页');
        }

        console.log(`✅ 找到页面: ${tab.title}`);
        console.log(`🔗 URL: ${tab.url}`);

        // 3. 检查页面类型
        if (tab.url.includes('/explore/')) {
            console.log('✅ 当前在笔记详情页，可以采集评论');
        } else {
            console.log('⚠️ 当前不在笔记详情页，评论可能较少');
        }

        // 4. 快速测试评论提取
        console.log('3️⃣ 快速测试评论提取...');
        const commentsData = await quickExtractComments(tab);
        
        console.log('\n📊 快速测试结果:');
        console.log(`💬 找到评论元素: ${commentsData.totalElements}`);
        console.log(`📝 提取的评论: ${commentsData.extractedComments}`);
        console.log(`🔍 使用的选择器: ${commentsData.usedSelector}`);
        
        if (commentsData.sampleComments.length > 0) {
            console.log('\n📝 示例评论:');
            commentsData.sampleComments.forEach((comment, index) => {
                console.log(`${index + 1}. ${comment.username}: ${comment.content}`);
            });
        }

        return commentsData;

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        throw error;
    }
}

// 快速提取评论测试
function quickExtractComments(tab) {
    return new Promise((resolve, reject) => {
        const ws = new WebSocket(tab.webSocketDebuggerUrl);
        let requestId = 1;

        ws.on('open', () => {
            console.log('✅ WebSocket连接成功');
            
            ws.send(JSON.stringify({
                id: requestId++,
                method: 'Runtime.enable'
            }));

            setTimeout(() => {
                const quickTestScript = `
                    (function() {
                        try {
                            const result = {
                                totalElements: 0,
                                extractedComments: 0,
                                usedSelector: '',
                                sampleComments: [],
                                pageInfo: {
                                    title: document.title,
                                    url: window.location.href,
                                    totalDivs: document.querySelectorAll('div').length,
                                    totalSpans: document.querySelectorAll('span').length
                                }
                            };

                            // 测试不同的评论选择器
                            const commentSelectors = [
                                '[class*="comment"]',
                                '[class*="reply"]',
                                '.comment-item',
                                '.comment-list > *',
                                '[data-testid*="comment"]'
                            ];

                            let bestSelector = '';
                            let maxElements = 0;

                            for (const selector of commentSelectors) {
                                const elements = document.querySelectorAll(selector);
                                console.log('测试选择器:', selector, '找到', elements.length, '个元素');
                                
                                if (elements.length > maxElements) {
                                    maxElements = elements.length;
                                    bestSelector = selector;
                                }
                            }

                            result.totalElements = maxElements;
                            result.usedSelector = bestSelector;

                            // 如果没找到专门的评论元素，使用通用方法
                            if (maxElements === 0) {
                                console.log('使用通用方法查找评论...');
                                const allDivs = document.querySelectorAll('div');
                                const commentLikeElements = Array.from(allDivs).filter(div => {
                                    const text = div.textContent.trim();
                                    const hasTimeInfo = text.includes('天前') || text.includes('小时前') || 
                                                      text.includes('分钟前') || text.includes('昨天');
                                    const hasUserContent = text.length > 10 && text.length < 500;
                                    const hasAvatar = div.querySelector('img');
                                    
                                    return hasTimeInfo && hasUserContent && hasAvatar;
                                });
                                
                                result.totalElements = commentLikeElements.length;
                                result.usedSelector = '通用方法';
                                
                                // 提取前3个作为示例
                                commentLikeElements.slice(0, 3).forEach((element, index) => {
                                    const username = element.querySelector('*')?.textContent?.trim()?.split(' ')[0] || '用户' + (index + 1);
                                    const content = element.textContent.trim().substring(0, 100) + '...';
                                    
                                    result.sampleComments.push({
                                        username: username,
                                        content: content
                                    });
                                });
                            } else {
                                // 从找到的元素中提取示例
                                const elements = document.querySelectorAll(bestSelector);
                                Array.from(elements).slice(0, 3).forEach((element, index) => {
                                    const text = element.textContent.trim();
                                    const username = text.split(' ')[0] || '用户' + (index + 1);
                                    const content = text.length > 50 ? text.substring(0, 50) + '...' : text;
                                    
                                    result.sampleComments.push({
                                        username: username,
                                        content: content
                                    });
                                });
                            }

                            result.extractedComments = result.sampleComments.length;

                            // 查找评论相关的文本
                            const commentTexts = [];
                            const allText = document.body.textContent;
                            const commentKeywords = ['评论', '回复', '天前', '小时前', '分钟前'];
                            
                            commentKeywords.forEach(keyword => {
                                const count = (allText.match(new RegExp(keyword, 'g')) || []).length;
                                if (count > 0) {
                                    commentTexts.push(keyword + ': ' + count + '次');
                                }
                            });
                            
                            result.commentKeywords = commentTexts;

                            return result;
                            
                        } catch (error) {
                            return {
                                error: error.message,
                                stack: error.stack
                            };
                        }
                    })();
                `;

                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.evaluate',
                    params: {
                        expression: quickTestScript,
                        returnByValue: true
                    }
                }));
            }, 1000);
        });

        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                if (message.result && message.result.result && message.result.result.value) {
                    const result = message.result.result.value;
                    
                    if (result.error) {
                        console.log('❌ 脚本执行失败:', result.error);
                        reject(new Error(result.error));
                    } else {
                        console.log('✅ 快速测试完成');
                        ws.close();
                        resolve(result);
                    }
                }
            } catch (error) {
                console.error('❌ 处理结果失败:', error.message);
                reject(error);
            }
        });

        ws.on('error', (error) => {
            console.error('❌ WebSocket错误:', error.message);
            reject(error);
        });

        setTimeout(() => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
            reject(new Error('快速测试超时'));
        }, 15000);
    });
}

// 运行测试
if (require.main === module) {
    testCommentsExtraction().then(result => {
        console.log('\n🎉 评论采集功能测试完成!');
        
        if (result.totalElements > 0) {
            console.log('✅ 评论采集功能可用');
            console.log('💡 可以运行完整的评论采集: node comments-collector.js');
        } else {
            console.log('⚠️ 当前页面可能没有评论或需要调整选择器');
            console.log('💡 建议先导航到有评论的笔记详情页');
        }
    }).catch(console.error);
}

module.exports = { testCommentsExtraction };
