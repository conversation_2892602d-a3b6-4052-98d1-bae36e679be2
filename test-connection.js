#!/usr/bin/env node

/**
 * 🔧 测试连接状态
 */

const axios = require('axios');

async function testConnection() {
    console.log('🔧 测试连接状态...\n');

    try {
        // 1. 测试服务器
        console.log('1️⃣ 测试本地服务器...');
        try {
            const serverResponse = await axios.get('http://localhost:3000', { timeout: 5000 });
            console.log('✅ 本地服务器正常');
        } catch (error) {
            console.log('❌ 本地服务器连接失败:', error.message);
            return;
        }

        // 2. 测试浏览器启动
        console.log('2️⃣ 测试浏览器启动...');
        try {
            const browserResponse = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 15000
            });
            
            if (browserResponse.data.success) {
                console.log('✅ 浏览器启动成功');
                console.log('📊 响应数据:', JSON.stringify(browserResponse.data, null, 2));
                
                const httpInfo = browserResponse.data.data?.result?.http;
                if (httpInfo) {
                    const port = httpInfo.split(':')[1];
                    console.log(`🔌 调试端口: ${port}`);
                    
                    // 3. 测试调试端口
                    console.log('3️⃣ 测试调试端口...');
                    try {
                        const tabsResponse = await axios.get(`http://127.0.0.1:${port}/json`, {
                            timeout: 5000
                        });
                        
                        console.log(`✅ 调试端口连接成功，找到 ${tabsResponse.data.length} 个标签页`);
                        
                        // 4. 查找小红书页面
                        console.log('4️⃣ 查找小红书页面...');
                        const xhsTab = tabsResponse.data.find(tab =>
                            tab.url && (
                                tab.url.includes('xiaohongshu.com') ||
                                tab.url.includes('creator.xiaohongshu.com') ||
                                tab.title.includes('小红书')
                            )
                        );
                        
                        if (xhsTab) {
                            console.log('✅ 找到小红书页面');
                            console.log(`   标题: ${xhsTab.title}`);
                            console.log(`   URL: ${xhsTab.url}`);
                            console.log(`   WebSocket: ${xhsTab.webSocketDebuggerUrl}`);
                            
                            return {
                                success: true,
                                port: port,
                                tab: xhsTab
                            };
                        } else {
                            console.log('❌ 未找到小红书页面');
                            console.log('📋 所有标签页:');
                            tabsResponse.data.forEach((tab, index) => {
                                console.log(`   ${index + 1}. ${tab.title} - ${tab.url}`);
                            });
                        }
                        
                    } catch (error) {
                        console.log('❌ 调试端口连接失败:', error.message);
                    }
                } else {
                    console.log('❌ 未获取到调试端口信息');
                }
            } else {
                console.log('❌ 浏览器启动失败:', browserResponse.data.message);
            }
        } catch (error) {
            console.log('❌ 浏览器启动请求失败:', error.message);
        }

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    testConnection().catch(console.error);
}

module.exports = { testConnection };
