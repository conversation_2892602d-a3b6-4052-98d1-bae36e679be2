#!/usr/bin/env node

/**
 * 🔍 小红书数据采集测试脚本
 * 专门测试从19号浏览器采集头像、简介、小红书号等数据
 */

const axios = require('axios');

const CONFIG = {
    localServer: 'http://localhost:3000',
    debugPort: 51859  // 从之前的日志中获取的端口
};

async function testDataCollection() {
    console.log('🔍 开始测试小红书数据采集功能...\n');

    try {
        // 1. 检查浏览器状态
        console.log('1️⃣ 检查浏览器状态...');
        try {
            const tabsResponse = await axios.get(`http://127.0.0.1:${CONFIG.debugPort}/json`, {
                timeout: 5000
            });
            
            const tabs = tabsResponse.data;
            console.log(`✅ 找到 ${tabs.length} 个标签页`);
            
            // 查找小红书标签页
            const xiaohongshuTab = tabs.find(tab =>
                tab.url && (
                    tab.url.includes('xiaohongshu.com') ||
                    tab.url.includes('creator.xiaohongshu.com') ||
                    tab.title.includes('小红书')
                )
            );
            
            if (xiaohongshuTab) {
                console.log(`🎉 找到小红书标签页: ${xiaohongshuTab.title}`);
                console.log(`🔗 URL: ${xiaohongshuTab.url}`);
                
                // 从标签页信息提取基本数据
                const basicData = extractBasicData(xiaohongshuTab);
                console.log('\n📊 从标签页提取的基本数据:');
                console.log(JSON.stringify(basicData, null, 2));
                
            } else {
                console.log('❌ 未找到小红书标签页');
                console.log('💡 请在浏览器中打开小红书网站');
                return;
            }
            
        } catch (error) {
            console.log(`❌ 无法连接到调试端口 ${CONFIG.debugPort}:`, error.message);
            console.log('💡 请确保19号浏览器已启动');
            return;
        }

        // 2. 通过API测试数据采集
        console.log('\n2️⃣ 通过API测试数据采集...');
        try {
            const collectResponse = await axios.post(`${CONFIG.localServer}/api/xiaohongshu/accounts/collect`, {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 30000
            });
            
            if (collectResponse.data.success) {
                console.log('✅ API数据采集成功!');
                console.log(`📊 采集到 ${collectResponse.data.data?.total || 0} 个账号`);
                
                if (collectResponse.data.data?.accounts) {
                    collectResponse.data.data.accounts.forEach((account, index) => {
                        console.log(`\n账号 ${index + 1}:`);
                        console.log(`  🏷️  昵称: ${account.nickname || '未获取'}`);
                        console.log(`  🆔 小红书号: ${account.xiaohongshuId || '未获取'}`);
                        console.log(`  🖼️  头像: ${account.avatar ? '已获取' : '未获取'}`);
                        console.log(`  📝 简介: ${account.bio || '未获取'}`);
                        console.log(`  👥 粉丝数: ${account.fansCount || '未获取'}`);
                        console.log(`  👥 关注数: ${account.followCount || '未获取'}`);
                        console.log(`  ❤️  获赞数: ${account.likesAndCollects || '未获取'}`);
                        console.log(`  📊 数据来源: ${account.dataSource || '未知'}`);
                    });
                }
            } else {
                console.log('❌ API数据采集失败:', collectResponse.data.message);
            }
            
        } catch (error) {
            console.log('❌ API调用失败:', error.response?.data || error.message);
        }

        // 3. 测试浏览器连接状态
        console.log('\n3️⃣ 测试浏览器连接状态...');
        try {
            const connectionResponse = await axios.post(`${CONFIG.localServer}/api/xiaohongshu/test-browser-connection`, {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 20000
            });
            
            if (connectionResponse.data.success) {
                console.log('✅ 浏览器连接测试成功!');
                const summary = connectionResponse.data.data?.summary;
                if (summary) {
                    console.log('📊 连接状态总结:');
                    console.log(`   API连接: ${summary.apiConnected ? '✅' : '❌'}`);
                    console.log(`   调试端口: ${summary.debugConnected ? '✅' : '❌'}`);
                    console.log(`   目标浏览器: ${summary.targetBrowserFound ? '✅' : '❌'}`);
                    console.log(`   小红书页面: ${summary.xiaohongshuPageFound ? '✅' : '❌'}`);
                }
            } else {
                console.log('❌ 浏览器连接测试失败:', connectionResponse.data.message);
            }
            
        } catch (error) {
            console.log('❌ 连接测试失败:', error.response?.data || error.message);
        }

        console.log('\n🏁 数据采集测试完成!');
        
    } catch (error) {
        console.error('❌ 测试过程出错:', error.message);
    }
}

// 从标签页信息提取基本数据
function extractBasicData(tab) {
    const data = {
        timestamp: new Date().toISOString(),
        url: tab.url,
        title: tab.title,
        tabId: tab.id
    };

    // 从标题中提取用户名
    if (tab.title && tab.title.includes(' - 小红书')) {
        const titleParts = tab.title.split(' - 小红书');
        if (titleParts.length > 0) {
            data.nickname = titleParts[0].trim();
        }
    }

    // 从URL中提取用户ID
    if (tab.url) {
        const urlMatch = tab.url.match(/user\/profile\/([a-zA-Z0-9]+)/);
        if (urlMatch) {
            data.xiaohongshuId = urlMatch[1];
        }
    }

    // 设置数据来源
    data.dataSource = 'browser_tab_info';
    data.extractionMethod = 'direct_tab_analysis';

    return data;
}

// 运行测试
if (require.main === module) {
    testDataCollection().catch(console.error);
}

module.exports = { testDataCollection };
