#!/usr/bin/env node

/**
 * 🧪 测试桌面应用API
 */

const axios = require('axios');

async function testDesktopAPI() {
    console.log('🧪 测试桌面应用API...\n');

    try {
        // 1. 测试健康检查
        console.log('1️⃣ 测试健康检查...');
        const healthResponse = await axios.get('http://localhost:3000/health', {
            timeout: 5000
        });
        console.log('✅ 健康检查通过');
        console.log('📊 服务器状态:', healthResponse.data);

        // 2. 测试比特浏览器启动
        console.log('\n2️⃣ 测试比特浏览器启动...');
        const startResponse = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 30000
        });
        
        console.log('📊 启动响应:', JSON.stringify(startResponse.data, null, 2));
        
        if (startResponse.data.success) {
            console.log('✅ 通过桌面应用启动浏览器成功');
            
            const result = startResponse.data.data?.result;
            if (result) {
                console.log(`🔌 调试端口: ${result.debug_port || result.selenium_port || '未提供'}`);
                console.log(`🌐 HTTP端点: ${result.http || '未提供'}`);
                
                // 3. 测试调试端口
                const debugPort = result.debug_port || result.selenium_port;
                if (debugPort) {
                    await testDebugConnection(debugPort);
                }
            }
        } else {
            console.log('❌ 通过桌面应用启动浏览器失败');
        }

        // 4. 测试浏览器列表
        console.log('\n3️⃣ 测试获取浏览器列表...');
        const listResponse = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/list', {
            page: 1,
            pageSize: 10
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });
        
        if (listResponse.data.success) {
            console.log('✅ 获取浏览器列表成功');
            const browsers = listResponse.data.data?.list || [];
            console.log(`📋 找到 ${browsers.length} 个浏览器实例`);
            
            browsers.forEach((browser, index) => {
                console.log(`   ${index + 1}. ${browser.name} (ID: ${browser.id.substring(0, 8)}...)`);
            });
        } else {
            console.log('❌ 获取浏览器列表失败');
        }

    } catch (error) {
        console.log('❌ 测试失败:', error.message);
        if (error.response) {
            console.log('📋 错误详情:', error.response.data);
        }
    }
}

async function testDebugConnection(port) {
    console.log(`\n🔍 测试调试端口 ${port}...`);
    
    try {
        const response = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });
        
        const tabs = response.data;
        console.log(`✅ 调试端口连接成功，找到 ${tabs.length} 个标签页`);
        
        // 查找小红书标签页
        const xiaohongshuTabs = tabs.filter(tab => 
            tab.url && (
                tab.url.includes('xiaohongshu.com') || 
                tab.title.includes('小红书')
            )
        );
        
        if (xiaohongshuTabs.length > 0) {
            console.log(`🎯 找到 ${xiaohongshuTabs.length} 个小红书标签页`);
        } else {
            console.log('⚠️ 未找到小红书标签页');
            console.log('💡 请在浏览器中打开小红书网站进行测试');
        }
        
        return true;
    } catch (error) {
        console.log(`❌ 调试端口 ${port} 连接失败: ${error.message}`);
        return false;
    }
}

if (require.main === module) {
    testDesktopAPI().then(() => {
        console.log('\n🎉 桌面应用API测试完成！');
    }).catch(error => {
        console.error('❌ 测试过程出错:', error.message);
    });
}

module.exports = { testDesktopAPI };
