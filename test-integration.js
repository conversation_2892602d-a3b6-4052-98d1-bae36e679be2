// ===== 小红书管理集成测试脚本 =====

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testIntegration() {
    console.log('🧪 开始测试小红书管理集成...\n');
    
    try {
        // 1. 测试主页面是否正常
        console.log('📄 测试主页面访问...');
        const mainPageResponse = await axios.get(`${BASE_URL}/premium-index.html`);
        console.log('✅ 主页面访问正常，状态码:', mainPageResponse.status);
        console.log('');
        
        // 2. 测试小红书API是否正常
        console.log('🔌 测试小红书API接口...');
        const apiResponse = await axios.get(`${BASE_URL}/api/xiaohongshu/overview`);
        console.log('✅ 小红书API正常，数据:', {
            success: apiResponse.data.success,
            totalNotes: apiResponse.data.data.totalNotes,
            totalViews: apiResponse.data.data.totalViews
        });
        console.log('');
        
        // 3. 测试笔记列表API
        console.log('📝 测试笔记列表API...');
        const notesResponse = await axios.get(`${BASE_URL}/api/xiaohongshu/notes`);
        console.log('✅ 笔记列表API正常，笔记数量:', notesResponse.data.data.total);
        console.log('');
        
        // 4. 测试浏览器打开API
        console.log('🌐 测试浏览器打开API...');
        try {
            const browserResponse = await axios.post(`${BASE_URL}/api/xiaohongshu/browser/open`);
            console.log('✅ 浏览器API响应正常:', browserResponse.data.success);
        } catch (error) {
            console.log('⚠️  浏览器API可能需要比特浏览器运行:', error.response?.data?.message || error.message);
        }
        console.log('');
        
        // 5. 测试发布新笔记
        console.log('📤 测试发布笔记API...');
        const newNote = {
            title: '🧪 集成测试笔记 - ' + new Date().toLocaleString(),
            content: '这是一个集成测试笔记，验证小红书管理功能是否正常集成到主平台中。',
            tags: ['集成测试', '小红书管理', '自媒体矩阵'],
            privacy: '公开可见'
        };
        
        const publishResponse = await axios.post(`${BASE_URL}/api/xiaohongshu/notes`, newNote);
        console.log('✅ 发布笔记成功:', {
            success: publishResponse.data.success,
            noteId: publishResponse.data.data.id,
            title: publishResponse.data.data.title
        });
        console.log('');
        
        // 6. 验证数据更新
        console.log('🔄 验证数据更新...');
        const updatedOverview = await axios.get(`${BASE_URL}/api/xiaohongshu/overview`);
        console.log('✅ 数据已更新，新的笔记总数:', updatedOverview.data.data.totalNotes);
        console.log('');
        
        console.log('🎉 集成测试完成！');
        console.log('📊 测试结果总结:');
        console.log('   ✅ 主页面访问 - 正常');
        console.log('   ✅ 小红书API - 正常');
        console.log('   ✅ 笔记列表 - 正常');
        console.log('   ✅ 发布功能 - 正常');
        console.log('   ✅ 数据更新 - 正常');
        console.log('\n🚀 小红书管理已成功集成到账号管理模块中！');
        console.log('\n📱 使用方法:');
        console.log('   1. 访问主平台: http://localhost:3000/premium-index.html');
        console.log('   2. 点击左侧导航"账号管理"');
        console.log('   3. 选择"小红书管理"标签');
        console.log('   4. 开始使用小红书管理功能');
        
    } catch (error) {
        console.error('❌ 集成测试失败:', error.message);
        if (error.response) {
            console.error('   状态码:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testIntegration();
