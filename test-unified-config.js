#!/usr/bin/env node

/**
 * 🧪 测试统一配置
 * 验证所有组件使用相同的比特浏览器配置
 */

const axios = require('axios');
const { BITBROWSER_CONFIG, LOCAL_SERVER_CONFIG, ConfigUtils } = require('./bitbrowser-config');

async function testUnifiedConfig() {
    console.log('🧪 测试统一配置...\n');

    // 1. 显示当前配置
    console.log('📋 当前统一配置:');
    console.log(`   API地址: ${BITBROWSER_CONFIG.api_url}`);
    console.log(`   API Token: ${BITBROWSER_CONFIG.api_token}`);
    console.log(`   浏览器ID: ${BITBROWSER_CONFIG.browser_id}`);
    console.log(`   浏览器名称: ${BITBROWSER_CONFIG.browser_name}`);
    console.log(`   桌面应用: ${LOCAL_SERVER_CONFIG.url}\n`);

    // 2. 测试比特浏览器API连接
    console.log('1️⃣ 测试比特浏览器API连接...');
    try {
        const response = await axios.post(ConfigUtils.getApiUrl(BITBROWSER_CONFIG.endpoints.browser_list), {
            page: 1,
            pageSize: 10
        }, ConfigUtils.getRequestConfig());

        if (response.data.success) {
            const browsers = response.data.data?.list || [];
            console.log(`✅ API连接成功，找到 ${browsers.length} 个浏览器实例`);
            
            // 查找目标浏览器
            const targetBrowser = browsers.find(b => b.id === BITBROWSER_CONFIG.browser_id);
            if (targetBrowser) {
                console.log(`✅ 找到目标浏览器: ${targetBrowser.name}`);
                console.log(`   状态: ${targetBrowser.status === 2 ? '未运行' : '运行中'}`);
            } else {
                console.log(`❌ 未找到目标浏览器 (ID: ${BITBROWSER_CONFIG.browser_id})`);
            }
        } else {
            console.log('❌ API连接失败:', response.data.msg);
        }
    } catch (error) {
        console.log('❌ API连接失败:', error.message);
    }

    // 3. 测试桌面应用API
    console.log('\n2️⃣ 测试桌面应用API...');
    try {
        const healthResponse = await axios.get(`${LOCAL_SERVER_CONFIG.url}/health`, {
            timeout: 5000
        });
        console.log('✅ 桌面应用连接成功');
        console.log(`   运行时间: ${Math.round(healthResponse.data.uptime)}秒`);

        // 测试浏览器列表API
        const listResponse = await axios.post(ConfigUtils.getDesktopApiUrl('/bitbrowser/list'), {
            page: 1,
            pageSize: 10
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });

        if (listResponse.data.success) {
            console.log('✅ 桌面应用浏览器列表API正常');
            const browsers = listResponse.data.data?.browsers || [];
            console.log(`   通过桌面应用找到 ${browsers.length} 个浏览器实例`);
        } else {
            console.log('❌ 桌面应用浏览器列表API失败');
        }
    } catch (error) {
        console.log('❌ 桌面应用连接失败:', error.message);
    }

    // 4. 测试配置工具函数
    console.log('\n3️⃣ 测试配置工具函数...');
    console.log(`   getApiUrl('/browser/list'): ${ConfigUtils.getApiUrl('/browser/list')}`);
    console.log(`   getDesktopApiUrl('/test'): ${ConfigUtils.getDesktopApiUrl('/test')}`);
    
    const requestConfig = ConfigUtils.getRequestConfig();
    console.log(`   请求配置: ${JSON.stringify(requestConfig.headers, null, 2)}`);

    // 5. 测试小红书页面检测
    console.log('\n4️⃣ 测试小红书页面检测...');
    const testUrls = [
        'https://www.xiaohongshu.com/explore',
        'https://creator.xiaohongshu.com/home',
        'https://www.baidu.com',
        'https://www.google.com'
    ];

    testUrls.forEach(url => {
        const isXhs = ConfigUtils.isXiaohongshuPage(url);
        console.log(`   ${url}: ${isXhs ? '✅ 小红书页面' : '❌ 非小红书页面'}`);
    });

    // 6. 测试启动浏览器
    console.log('\n5️⃣ 测试启动浏览器...');
    try {
        const startResponse = await axios.post(ConfigUtils.getApiUrl(BITBROWSER_CONFIG.endpoints.browser_open), {
            id: BITBROWSER_CONFIG.browser_id
        }, ConfigUtils.getRequestConfig());

        if (startResponse.data.success) {
            console.log('✅ 浏览器启动成功');
            const result = startResponse.data.data;
            if (result) {
                console.log(`   调试端口: ${result.debug_port || result.selenium_port || '未提供'}`);
                console.log(`   HTTP端点: ${result.http || '未提供'}`);
            }
        } else {
            console.log('❌ 浏览器启动失败:', startResponse.data.msg);
        }
    } catch (error) {
        console.log('❌ 浏览器启动失败:', error.message);
    }

    console.log('\n🎉 统一配置测试完成！');
}

if (require.main === module) {
    testUnifiedConfig().catch(error => {
        console.error('❌ 测试过程出错:', error.message);
    });
}

module.exports = { testUnifiedConfig };
