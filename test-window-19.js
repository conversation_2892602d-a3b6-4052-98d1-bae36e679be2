#!/usr/bin/env node

/**
 * 🧪 测试19号窗口连接
 * 专门测试 "95362955272 ace1" 浏览器的启动和连接
 */

const axios = require('axios');
const { BITBROWSER_CONFIG, ConfigUtils } = require('./bitbrowser-config');

async function testWindow19() {
    console.log('🧪 测试19号窗口 (95362955272 ace1)...\n');

    // 显示配置信息
    console.log('📋 19号窗口配置:');
    console.log(`   浏览器名称: ${BITBROWSER_CONFIG.browser_name}`);
    console.log(`   浏览器ID: ${BITBROWSER_CONFIG.browser_id}`);
    console.log(`   API地址: ${BITBROWSER_CONFIG.api_url}`);
    console.log('');

    // 1. 检查浏览器状态
    console.log('1️⃣ 检查19号窗口状态...');
    try {
        const listResponse = await axios.post(
            ConfigUtils.getApiUrl('/browser/list'), 
            BITBROWSER_CONFIG.list_params, 
            ConfigUtils.getRequestConfig()
        );

        if (listResponse.data.success) {
            const browsers = listResponse.data.data?.list || [];
            const targetBrowser = browsers.find(b => b.id === BITBROWSER_CONFIG.browser_id);
            
            if (targetBrowser) {
                console.log(`✅ 找到19号窗口: ${targetBrowser.name}`);
                console.log(`   当前状态: ${targetBrowser.status === 2 ? '未运行' : '运行中'}`);
                console.log(`   最后操作: ${targetBrowser.operTime || '未知'}`);
                
                if (targetBrowser.status === 2) {
                    console.log('\n2️⃣ 尝试启动19号窗口...');
                    await attemptStartWindow19();
                } else {
                    console.log('\n2️⃣ 19号窗口已在运行，查找调试端口...');
                    await findDebugPort();
                }
            } else {
                console.log(`❌ 未找到19号窗口 (ID: ${BITBROWSER_CONFIG.browser_id})`);
                console.log('💡 请检查浏览器ID配置是否正确');
                return false;
            }
        } else {
            console.log('❌ 获取浏览器列表失败:', listResponse.data.msg);
            return false;
        }
    } catch (error) {
        console.log('❌ 检查浏览器状态失败:', error.message);
        return false;
    }

    return true;
}

async function attemptStartWindow19() {
    try {
        console.log('   🚀 发送启动请求...');
        const startResponse = await axios.post(
            ConfigUtils.getApiUrl('/browser/open'),
            { id: BITBROWSER_CONFIG.browser_id },
            {
                ...ConfigUtils.getRequestConfig(),
                timeout: 60000  // 增加超时时间到60秒
            }
        );

        console.log('   📊 启动响应:', JSON.stringify(startResponse.data, null, 2));

        if (startResponse.data.success) {
            console.log('   ✅ 19号窗口启动成功！');
            const result = startResponse.data.data;
            
            if (result) {
                const debugPort = result.debug_port || result.selenium_port;
                const wsEndpoint = result.ws_endpoint;
                const httpEndpoint = result.http;
                
                console.log(`   🔌 调试端口: ${debugPort || '未提供'}`);
                console.log(`   🌐 WebSocket: ${wsEndpoint || '未提供'}`);
                console.log(`   🔗 HTTP端点: ${httpEndpoint || '未提供'}`);
                
                if (debugPort) {
                    console.log('\n3️⃣ 测试调试端口连接...');
                    await testDebugConnection(debugPort);
                } else if (httpEndpoint) {
                    // 从HTTP端点提取端口号
                    const portMatch = httpEndpoint.match(/:(\d+)/);
                    if (portMatch) {
                        const port = parseInt(portMatch[1]);
                        console.log(`\n3️⃣ 从HTTP端点提取端口 ${port}，测试连接...`);
                        await testDebugConnection(port);
                    }
                } else {
                    console.log('\n3️⃣ 未提供调试端口，扫描常用端口...');
                    await findDebugPort();
                }
                
                return result;
            }
        } else {
            console.log('   ❌ 19号窗口启动失败:', startResponse.data.msg);
            
            if (startResponse.data.msg && startResponse.data.msg.includes('限制')) {
                console.log('   💡 窗口被限制启动，请手动操作:');
                console.log('   1. 打开比特浏览器界面');
                console.log('   2. 找到 "95362955272 ace1" 浏览器');
                console.log('   3. 点击启动按钮');
                console.log('   4. 重新运行此测试');
            }
            return null;
        }
    } catch (error) {
        console.log('   ❌ 启动请求失败:', error.message);
        if (error.code === 'ECONNABORTED') {
            console.log('   💡 启动超时，浏览器可能需要更长时间');
        }
        return null;
    }
}

async function findDebugPort() {
    console.log('   🔍 扫描调试端口...');
    
    // 扩展端口范围
    const portRanges = [
        { start: 53320, end: 53340 },  // 比特浏览器动态端口
        { start: 63520, end: 63540 },  // 常用端口
        { start: 51850, end: 51870 },  // 动态端口
        { start: 9220, end: 9230 }     // Chrome端口
    ];

    for (const range of portRanges) {
        for (let port = range.start; port <= range.end; port++) {
            try {
                const response = await axios.get(`http://127.0.0.1:${port}/json`, {
                    timeout: 1000
                });
                
                const tabs = response.data;
                console.log(`   ✅ 找到调试端口 ${port} - ${tabs.length} 个标签页`);
                
                // 检查小红书标签页
                const xiaohongshuTabs = tabs.filter(tab => 
                    tab.url && (
                        tab.url.includes('xiaohongshu.com') || 
                        tab.title.includes('小红书')
                    )
                );
                
                if (xiaohongshuTabs.length > 0) {
                    console.log(`   🎯 端口 ${port} 有 ${xiaohongshuTabs.length} 个小红书标签页！`);
                    return port;
                }
                
            } catch (error) {
                // 端口不可用，继续
            }
        }
    }
    
    console.log('   ⚠️ 未找到可用的调试端口');
    return null;
}

async function testDebugConnection(port) {
    try {
        console.log(`   🔍 连接调试端口 ${port}...`);
        const response = await axios.get(`http://127.0.0.1:${port}/json`, {
            timeout: 5000
        });
        
        const tabs = response.data;
        console.log(`   ✅ 调试端口 ${port} 连接成功！`);
        console.log(`   📋 找到 ${tabs.length} 个标签页`);
        
        if (tabs.length > 0) {
            console.log('   📄 标签页列表:');
            tabs.slice(0, 5).forEach((tab, index) => {
                const title = tab.title || '无标题';
                const url = (tab.url || '').substring(0, 60) + '...';
                console.log(`      ${index + 1}. ${title}`);
                console.log(`         ${url}`);
            });
            
            if (tabs.length > 5) {
                console.log(`      ... 还有 ${tabs.length - 5} 个标签页`);
            }
        }
        
        // 检查小红书标签页
        const xiaohongshuTabs = tabs.filter(tab => 
            tab.url && (
                tab.url.includes('xiaohongshu.com') || 
                tab.title.includes('小红书')
            )
        );
        
        if (xiaohongshuTabs.length > 0) {
            console.log(`   🎯 找到 ${xiaohongshuTabs.length} 个小红书标签页:`);
            xiaohongshuTabs.forEach((tab, index) => {
                console.log(`      ${index + 1}. ${tab.title}`);
            });
            console.log('   🎉 可以开始数据采集了！');
        } else {
            console.log('   💡 未找到小红书标签页');
            console.log('   📝 建议操作:');
            console.log('      1. 在浏览器中打开 https://www.xiaohongshu.com');
            console.log('      2. 或打开 https://creator.xiaohongshu.com');
            console.log('      3. 登录您的小红书账号');
        }
        
        return true;
    } catch (error) {
        console.log(`   ❌ 调试端口 ${port} 连接失败: ${error.message}`);
        return false;
    }
}

// 快速测试数据采集
async function testDataCollection() {
    console.log('\n4️⃣ 测试数据采集功能...');
    
    try {
        // 这里可以调用数据采集器进行测试
        console.log('   🔄 启动数据采集器...');
        
        // 模拟数据采集测试
        const { ClickNotesCollector } = require('./click-notes-collector');
        const collector = new ClickNotesCollector();
        
        // 测试端口获取
        await collector.getCurrentDebugPort();
        
        if (collector.debugPort) {
            console.log(`   ✅ 数据采集器已连接到端口 ${collector.debugPort}`);
            console.log('   🎉 19号窗口测试完全成功！');
            return true;
        } else {
            console.log('   ❌ 数据采集器无法连接');
            return false;
        }
        
    } catch (error) {
        console.log('   ❌ 数据采集测试失败:', error.message);
        return false;
    }
}

if (require.main === module) {
    testWindow19().then(success => {
        if (success) {
            console.log('\n🎉 19号窗口测试完成！');
            
            // 可选：测试数据采集
            const args = process.argv.slice(2);
            if (args.includes('--full')) {
                testDataCollection().then(collectionSuccess => {
                    if (collectionSuccess) {
                        console.log('\n✅ 完整测试成功！可以开始使用数据采集功能。');
                    } else {
                        console.log('\n⚠️ 数据采集测试失败，但浏览器连接正常。');
                    }
                });
            } else {
                console.log('\n💡 运行 "node test-window-19.js --full" 进行完整测试');
            }
        } else {
            console.log('\n❌ 19号窗口测试失败！');
        }
    }).catch(error => {
        console.error('❌ 测试过程出错:', error.message);
    });
}

module.exports = { testWindow19, testDebugConnection };
