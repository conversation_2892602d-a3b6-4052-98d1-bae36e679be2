// ===== 小红书API功能测试脚本 =====

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/xiaohongshu';

// 测试所有API功能
async function testAllAPIs() {
    console.log('🚀 开始测试小红书管理API...\n');
    
    try {
        // 1. 测试概览数据
        console.log('📊 测试概览数据API...');
        const overviewResponse = await axios.get(`${BASE_URL}/overview`);
        console.log('✅ 概览数据:', {
            success: overviewResponse.data.success,
            totalNotes: overviewResponse.data.data.totalNotes,
            totalViews: overviewResponse.data.data.totalViews,
            totalLikes: overviewResponse.data.data.totalLikes
        });
        console.log('');
        
        // 2. 测试笔记列表
        console.log('📝 测试笔记列表API...');
        const notesResponse = await axios.get(`${BASE_URL}/notes`);
        console.log('✅ 笔记列表:', {
            success: notesResponse.data.success,
            total: notesResponse.data.data.total,
            notesCount: notesResponse.data.data.notes.length,
            firstNoteTitle: notesResponse.data.data.notes[0]?.title
        });
        console.log('');
        
        // 3. 测试发布模板
        console.log('📋 测试发布模板API...');
        const templatesResponse = await axios.get(`${BASE_URL}/templates`);
        console.log('✅ 发布模板:', {
            success: templatesResponse.data.success,
            templatesCount: templatesResponse.data.data.length,
            firstTemplate: templatesResponse.data.data[0]?.name
        });
        console.log('');
        
        // 4. 测试数据分析
        console.log('📈 测试数据分析API...');
        const analyticsResponse = await axios.get(`${BASE_URL}/analytics?period=7d`);
        console.log('✅ 数据分析:', {
            success: analyticsResponse.data.success,
            period: analyticsResponse.data.data.period,
            metricsCount: Object.keys(analyticsResponse.data.data.metrics).length,
            topNotesCount: analyticsResponse.data.data.topNotes.length
        });
        console.log('');
        
        // 5. 测试发布新笔记
        console.log('📤 测试发布新笔记API...');
        const newNote = {
            title: '🚀 API测试笔记 - ' + new Date().toLocaleString(),
            content: `这是一个通过API自动发布的测试笔记！

✨ 测试内容：
• API功能验证
• 自动化发布测试
• 数据同步测试

🎯 发布时间：${new Date().toLocaleString()}

#API测试 #自动化 #小红书管理`,
            tags: ['API测试', '自动化', '小红书管理'],
            privacy: '公开可见'
        };
        
        const publishResponse = await axios.post(`${BASE_URL}/notes`, newNote);
        console.log('✅ 发布笔记:', {
            success: publishResponse.data.success,
            noteId: publishResponse.data.data.id,
            title: publishResponse.data.data.title,
            status: publishResponse.data.data.status
        });
        console.log('');
        
        // 6. 测试更新笔记（置顶）
        if (publishResponse.data.success) {
            console.log('📌 测试笔记置顶API...');
            const noteId = publishResponse.data.data.id;
            const updateResponse = await axios.put(`${BASE_URL}/notes/${noteId}`, {
                isTop: true,
                title: '📌【置顶】' + publishResponse.data.data.title
            });
            console.log('✅ 笔记置顶:', {
                success: updateResponse.data.success,
                isTop: updateResponse.data.data.isTop,
                newTitle: updateResponse.data.data.title
            });
            console.log('');
        }
        
        // 7. 测试批量更新
        console.log('🔄 测试批量更新API...');
        const batchResponse = await axios.put(`${BASE_URL}/notes/batch`, {
            noteIds: notesResponse.data.data.notes.slice(0, 2).map(note => note.id),
            updates: {
                privacy: '仅互关好友可见'
            }
        });
        console.log('✅ 批量更新:', {
            success: batchResponse.data.success,
            updatedCount: batchResponse.data.data.updatedCount
        });
        console.log('');
        
        // 8. 测试获取单个笔记
        if (notesResponse.data.data.notes.length > 0) {
            console.log('📄 测试单个笔记API...');
            const noteId = notesResponse.data.data.notes[0].id;
            const singleNoteResponse = await axios.get(`${BASE_URL}/notes/${noteId}`);
            console.log('✅ 单个笔记:', {
                success: singleNoteResponse.data.success,
                title: singleNoteResponse.data.data.title,
                privacy: singleNoteResponse.data.data.privacy,
                isTop: singleNoteResponse.data.data.isTop
            });
            console.log('');
        }
        
        console.log('🎉 所有API测试完成！');
        console.log('📊 测试结果总结:');
        console.log('   ✅ 概览数据 - 正常');
        console.log('   ✅ 笔记列表 - 正常');
        console.log('   ✅ 发布模板 - 正常');
        console.log('   ✅ 数据分析 - 正常');
        console.log('   ✅ 发布笔记 - 正常');
        console.log('   ✅ 笔记置顶 - 正常');
        console.log('   ✅ 批量更新 - 正常');
        console.log('   ✅ 单个笔记 - 正常');
        console.log('\n🚀 小红书管理系统API功能完全正常！');
        
    } catch (error) {
        console.error('❌ API测试失败:', error.message);
        if (error.response) {
            console.error('   状态码:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testAllAPIs();
