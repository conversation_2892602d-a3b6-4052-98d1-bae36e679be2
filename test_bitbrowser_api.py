#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 测试比特浏览器API连接
"""

import requests
import json

def test_api_connection():
    """测试API连接"""
    print("🔍 测试比特浏览器API连接...")
    
    # 可能的API地址
    api_urls = [
        "http://127.0.0.1:56906",
        "http://localhost:56906", 
        "http://127.0.0.1:3000",
        "http://localhost:3000",
        "http://127.0.0.1:8080",
        "http://localhost:8080"
    ]
    
    # 可能的端点
    endpoints = [
        "/browser/list",
        "/api/browser/list",
        "/api/v1/browser/list"
    ]
    
    # 不同的请求方式
    for api_url in api_urls:
        print(f"\n🔗 测试API地址: {api_url}")
        
        for endpoint in endpoints:
            print(f"   📡 测试端点: {endpoint}")
            
            # 测试不同的请求方式
            test_methods = [
                # 方法1: 无认证POST
                {
                    'method': 'POST',
                    'headers': {'Content-Type': 'application/json'},
                    'data': {"page": 0, "pageSize": 10}
                },
                # 方法2: 无认证GET
                {
                    'method': 'GET',
                    'headers': {},
                    'data': None
                },
                # 方法3: 带token的POST
                {
                    'method': 'POST', 
                    'headers': {
                        'Content-Type': 'application/json',
                        'X-API-KEY': 'ca28ee5ca6c4e4d209182a83aa16a2044'
                    },
                    'data': {"page": 0, "pageSize": 10}
                }
            ]
            
            for i, test_method in enumerate(test_methods):
                try:
                    url = f"{api_url}{endpoint}"
                    
                    if test_method['method'] == 'POST':
                        response = requests.post(
                            url,
                            json=test_method['data'],
                            headers=test_method['headers'],
                            timeout=3
                        )
                    else:
                        response = requests.get(
                            url,
                            headers=test_method['headers'],
                            timeout=3
                        )
                    
                    print(f"      方法{i+1}: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            print(f"      ✅ 成功! 返回数据: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
                            return api_url, endpoint, test_method
                        except:
                            print(f"      ✅ 成功! 返回文本: {response.text[:100]}...")
                            return api_url, endpoint, test_method
                    elif response.status_code == 403:
                        print(f"      ❌ 403 Forbidden - 需要认证")
                    elif response.status_code == 404:
                        print(f"      ❌ 404 Not Found - 端点不存在")
                    else:
                        print(f"      ❌ {response.status_code} - {response.text[:50]}...")
                        
                except requests.exceptions.ConnectionError:
                    print(f"      ❌ 连接失败 - 服务未运行")
                except requests.exceptions.Timeout:
                    print(f"      ❌ 超时")
                except Exception as e:
                    print(f"      ❌ 错误: {str(e)}")
    
    print("\n❌ 所有测试都失败了")
    return None, None, None

def test_direct_selenium():
    """测试直接Selenium连接"""
    print("\n🔍 测试直接Selenium连接...")
    
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    
    # 常见调试端口
    ports = [9222, 9223, 9224, 9225, 55276, 54345, 56906, 56907, 56908]
    
    for port in ports:
        try:
            print(f"   🔍 测试端口 {port}...")
            
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
            
            driver = webdriver.Chrome(options=chrome_options)
            
            print(f"   ✅ 端口 {port} 连接成功!")
            print(f"   📄 当前页面: {driver.current_url}")
            
            driver.quit()
            return port
            
        except Exception as e:
            print(f"   ❌ 端口 {port} 失败: {str(e)}")
    
    print("   ❌ 所有端口都失败了")
    return None

def main():
    """主函数"""
    print("🎯 比特浏览器连接测试工具")
    print("=" * 50)
    
    # 测试API连接
    api_url, endpoint, method = test_api_connection()
    
    if api_url:
        print(f"\n🎉 找到可用的API:")
        print(f"   地址: {api_url}")
        print(f"   端点: {endpoint}")
        print(f"   方法: {method['method']}")
        print(f"   头部: {method['headers']}")
    
    # 测试直接Selenium连接
    port = test_direct_selenium()
    
    if port:
        print(f"\n🎉 找到可用的调试端口: {port}")
    
    if not api_url and not port:
        print("\n💡 建议:")
        print("1. 确保比特浏览器正在运行")
        print("2. 检查比特浏览器是否启用了API服务")
        print("3. 检查比特浏览器是否启用了调试模式")
        print("4. 尝试手动打开一个浏览器窗口")

if __name__ == "__main__":
    main()
