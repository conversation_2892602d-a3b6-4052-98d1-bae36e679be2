#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 调试端口连接测试器
测试指定的调试端口是否可以连接
"""

import sys
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

def test_debug_port(port):
    """测试调试端口连接"""
    print(f"🔍 测试调试端口 {port}...")
    
    try:
        # 简化的Chrome选项
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
        
        # 基本选项
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        
        print(f"   🔗 尝试连接到 localhost:{port}...")
        
        # 尝试连接
        driver = webdriver.Chrome(options=chrome_options)
        
        print(f"   ✅ 端口 {port} 连接成功!")
        
        # 获取基本信息
        try:
            current_url = driver.current_url
            title = driver.title
            
            print(f"   📄 当前页面: {current_url}")
            print(f"   📝 页面标题: {title}")
            
            # 检查是否是小红书页面
            if 'xiaohongshu.com' in current_url:
                print("   🎉 检测到小红书页面!")
                
                # 简单测试页面操作
                try:
                    body = driver.find_element(By.TAG_NAME, "body")
                    page_text = body.text
                    
                    # 统计评论相关信息
                    comment_indicators = [
                        ('条评论', page_text.count('条评论')),
                        ('时间格式', len([line for line in page_text.split('\n') if any(char.isdigit() and '-' in line for char in line)])),
                        ('回复', page_text.count('回复')),
                        ('点赞', page_text.count('赞'))
                    ]
                    
                    print("   📊 页面内容分析:")
                    for indicator, count in comment_indicators:
                        if count > 0:
                            print(f"      {indicator}: {count}")
                    
                    # 检查是否有评论区域
                    if any(count > 0 for _, count in comment_indicators):
                        print("   ✅ 页面包含评论相关内容，适合爬取!")
                    else:
                        print("   ⚠️ 页面可能不包含评论内容")
                        
                except Exception as e:
                    print(f"   ⚠️ 页面内容分析失败: {e}")
            else:
                print("   💡 不是小红书页面")
                print("   💡 请在比特浏览器中导航到小红书评论页面")
            
            # 保持连接一段时间进行测试
            print("   ⏱️ 保持连接5秒进行稳定性测试...")
            time.sleep(5)
            
            # 再次检查连接
            try:
                current_url2 = driver.current_url
                print(f"   ✅ 连接稳定，当前页面: {current_url2}")
            except:
                print("   ⚠️ 连接不稳定")
            
        except Exception as e:
            print(f"   ⚠️ 获取页面信息失败: {e}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"   ❌ 端口 {port} 连接失败:")
        print(f"      错误: {str(e)}")
        
        # 分析错误类型
        error_str = str(e).lower()
        if "cannot connect to the service" in error_str:
            print("      💡 可能原因: ChromeDriver服务未启动")
        elif "invalid argument" in error_str:
            print("      💡 可能原因: Chrome选项不兼容")
        elif "chrome not reachable" in error_str:
            print("      💡 可能原因: 调试端口未开启或端口号错误")
        elif "session not created" in error_str:
            print("      💡 可能原因: Chrome版本与ChromeDriver不匹配")
        else:
            print("      💡 建议: 检查比特浏览器是否正常运行")
        
        return False

def main():
    if len(sys.argv) != 2:
        print("🎯 调试端口连接测试器")
        print("用法: python test_debug_port.py <端口号>")
        print("示例: python test_debug_port.py 64170")
        return
    
    try:
        port = int(sys.argv[1])
    except ValueError:
        print("❌ 端口号必须是数字")
        return
    
    print("🎯 调试端口连接测试器")
    print("=" * 50)
    
    success = test_debug_port(port)
    
    print("\n" + "=" * 50)
    if success:
        print(f"🎉 端口 {port} 测试成功!")
        print("✅ 可以使用此端口运行爬虫脚本")
        print(f"🚀 下一步: python scraper.py --port {port}")
    else:
        print(f"❌ 端口 {port} 测试失败")
        print("💡 建议:")
        print("   1. 确保比特浏览器窗口已打开")
        print("   2. 确保窗口启用了调试模式")
        print("   3. 尝试其他端口号")
        print("   4. 重启比特浏览器")

if __name__ == "__main__":
    main()
