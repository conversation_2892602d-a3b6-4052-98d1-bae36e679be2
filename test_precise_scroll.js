#!/usr/bin/env node

/**
 * 🧪 测试精确滚动
 * 专门测试评论区域的滚动控制
 */

const axios = require('axios');
const WebSocket = require('ws');

async function testPreciseScroll() {
    console.log('🧪 开始测试精确滚动...\n');

    try {
        // 1. 获取调试端口
        const debugPort = 63524;
        console.log(`✅ 使用端口: ${debugPort}`);

        // 2. 获取标签页
        const response = await axios.get(`http://127.0.0.1:${debugPort}/json`, {
            timeout: 5000
        });

        const tab = response.data.find(tab =>
            tab.url && tab.url.includes('xiaohongshu.com')
        );

        if (!tab) {
            throw new Error('未找到小红书标签页');
        }

        console.log(`🎯 找到标签页: ${tab.title}`);

        // 3. 执行精确滚动测试
        const ws = new WebSocket(tab.webSocketDebuggerUrl);

        ws.on('open', () => {
            console.log('✅ WebSocket连接成功');
            
            ws.send(JSON.stringify({
                id: 1,
                method: 'Runtime.enable'
            }));

            setTimeout(() => {
                const testScript = `
                    (async function() {
                        try {
                            console.log('🎯 开始精确滚动测试...');
                            
                            const results = {
                                initialStats: {},
                                scrollResults: [],
                                finalStats: {},
                                clickResults: []
                            };

                            // 初始统计
                            results.initialStats = {
                                avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                commentDivs: document.querySelectorAll('[class*="comment"]').length,
                                pageHeight: document.body.scrollHeight,
                                visibleHeight: window.innerHeight
                            };
                            
                            console.log('📊 初始统计:', results.initialStats);

                            // 查找评论容器
                            let commentContainer = null;
                            const containerSelectors = [
                                '.comments-container',
                                '.comment-list', 
                                '.interaction-container',
                                '[class*="comment-section"]'
                            ];

                            for (const selector of containerSelectors) {
                                const containers = document.querySelectorAll(selector);
                                for (const container of containers) {
                                    if (container.scrollHeight > container.clientHeight) {
                                        commentContainer = container;
                                        console.log(\`✅ 找到可滚动评论容器: \${selector}\`);
                                        break;
                                    }
                                }
                                if (commentContainer) break;
                            }

                            if (!commentContainer) {
                                console.log('⚠️ 未找到专门的评论容器，查找评论密集区域...');
                                const allDivs = document.querySelectorAll('div');
                                let maxComments = 0;
                                
                                allDivs.forEach(div => {
                                    const avatars = div.querySelectorAll('img[src*="avatar"]');
                                    if (avatars.length > maxComments) {
                                        maxComments = avatars.length;
                                        commentContainer = div;
                                    }
                                });
                                
                                if (commentContainer) {
                                    console.log(\`✅ 找到评论密集区域，包含 \${maxComments} 个头像\`);
                                }
                            }

                            if (!commentContainer) {
                                commentContainer = document.body;
                                console.log('⚠️ 使用整个页面作为滚动容器');
                            }

                            // 执行10轮精确滚动测试
                            for (let round = 0; round < 10; round++) {
                                console.log(\`🔄 第 \${round + 1} 轮滚动测试...\`);
                                
                                const beforeScroll = {
                                    avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                    scrollTop: commentContainer.scrollTop || window.pageYOffset,
                                    scrollHeight: commentContainer.scrollHeight || document.body.scrollHeight
                                };

                                // 滚动评论容器
                                if (commentContainer.scrollHeight > commentContainer.clientHeight) {
                                    commentContainer.scrollTop = commentContainer.scrollHeight;
                                } else {
                                    window.scrollTo(0, document.body.scrollHeight);
                                }
                                
                                // 等待内容加载
                                await new Promise(resolve => setTimeout(resolve, 1500));

                                const afterScroll = {
                                    avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                    scrollTop: commentContainer.scrollTop || window.pageYOffset,
                                    scrollHeight: commentContainer.scrollHeight || document.body.scrollHeight
                                };

                                const scrollResult = {
                                    round: round + 1,
                                    before: beforeScroll,
                                    after: afterScroll,
                                    avatarsAdded: afterScroll.avatars - beforeScroll.avatars,
                                    heightChanged: afterScroll.scrollHeight - beforeScroll.scrollHeight
                                };

                                results.scrollResults.push(scrollResult);
                                console.log(\`   📊 头像: \${beforeScroll.avatars} → \${afterScroll.avatars} (+\${scrollResult.avatarsAdded})\`);
                                console.log(\`   📏 高度: \${beforeScroll.scrollHeight} → \${afterScroll.scrollHeight} (+\${scrollResult.heightChanged})\`);

                                // 查找并点击"更多"按钮
                                const moreButtons = Array.from(commentContainer.querySelectorAll('*')).filter(el => {
                                    const text = el.textContent.trim();
                                    const isVisible = el.offsetParent !== null;
                                    const isClickable = el.tagName === 'BUTTON' || 
                                                      el.tagName === 'A' ||
                                                      el.onclick || 
                                                      getComputedStyle(el).cursor === 'pointer';
                                    
                                    return isVisible && isClickable && (
                                        text.includes('更多') || 
                                        text.includes('展开') ||
                                        text.includes('查看') ||
                                        /\\d+条回复/.test(text)
                                    );
                                });

                                let clickedInRound = 0;
                                for (let i = 0; i < Math.min(3, moreButtons.length); i++) {
                                    try {
                                        moreButtons[i].scrollIntoView({ behavior: 'smooth', block: 'center' });
                                        await new Promise(resolve => setTimeout(resolve, 300));
                                        moreButtons[i].click();
                                        clickedInRound++;
                                        await new Promise(resolve => setTimeout(resolve, 500));
                                    } catch (e) {
                                        // 忽略点击错误
                                    }
                                }

                                results.clickResults.push({
                                    round: round + 1,
                                    buttonsFound: moreButtons.length,
                                    buttonsClicked: clickedInRound
                                });

                                console.log(\`   🖱️ 找到 \${moreButtons.length} 个按钮，点击了 \${clickedInRound} 个\`);

                                // 如果没有新内容加载且没有按钮可点击，提前结束
                                if (scrollResult.avatarsAdded === 0 && scrollResult.heightChanged === 0 && clickedInRound === 0) {
                                    console.log('⚠️ 没有新内容加载，提前结束');
                                    break;
                                }
                            }

                            // 最终统计
                            results.finalStats = {
                                avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                commentDivs: document.querySelectorAll('[class*="comment"]').length,
                                pageHeight: document.body.scrollHeight,
                                visibleHeight: window.innerHeight
                            };

                            console.log('📊 最终统计:', results.finalStats);
                            console.log('🎉 精确滚动测试完成!');

                            return { success: true, data: results };
                        } catch (error) {
                            console.error('测试过程出错:', error);
                            return { success: false, error: error.message };
                        }
                    })();
                `;

                ws.send(JSON.stringify({
                    id: 2,
                    method: 'Runtime.evaluate',
                    params: {
                        expression: testScript,
                        returnByValue: true
                    }
                }));
            }, 1000);
        });

        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                if (message.id === 2 && message.result && message.result.result && message.result.result.value) {
                    const result = message.result.result.value;
                    
                    if (result.success) {
                        const data = result.data;
                        console.log('\n🎉 精确滚动测试结果:');
                        console.log('=' * 50);
                        
                        console.log('\n📊 初始 vs 最终统计:');
                        console.log(`👤 头像数量: ${data.initialStats.avatars} → ${data.finalStats.avatars} (+${data.finalStats.avatars - data.initialStats.avatars})`);
                        console.log(`💬 评论div: ${data.initialStats.commentDivs} → ${data.finalStats.commentDivs} (+${data.finalStats.commentDivs - data.initialStats.commentDivs})`);
                        console.log(`📏 页面高度: ${data.initialStats.pageHeight} → ${data.finalStats.pageHeight} (+${data.finalStats.pageHeight - data.initialStats.pageHeight})`);
                        
                        console.log('\n🔄 滚动轮次详情:');
                        data.scrollResults.forEach(scroll => {
                            console.log(`   第${scroll.round}轮: 头像+${scroll.avatarsAdded}, 高度+${scroll.heightChanged}`);
                        });
                        
                        console.log('\n🖱️ 按钮点击详情:');
                        const totalClicked = data.clickResults.reduce((sum, click) => sum + click.buttonsClicked, 0);
                        console.log(`   总共点击了 ${totalClicked} 个按钮`);
                        data.clickResults.forEach(click => {
                            if (click.buttonsClicked > 0) {
                                console.log(`   第${click.round}轮: 找到${click.buttonsFound}个，点击${click.buttonsClicked}个`);
                            }
                        });
                        
                        const progress = Math.round(data.finalStats.avatars / 1472 * 100);
                        console.log(`\n📈 采集进度: ${progress}% (${data.finalStats.avatars}/1472)`);
                        
                    } else {
                        console.log('❌ 测试失败:', result.error);
                    }
                    
                    ws.close();
                }
            } catch (error) {
                console.error('❌ 处理结果失败:', error.message);
                ws.close();
            }
        });

        ws.on('error', (error) => {
            console.error('❌ WebSocket错误:', error.message);
        });

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

if (require.main === module) {
    testPreciseScroll();
}

module.exports = testPreciseScroll;
