/**
 * 🎯 Puppeteer连接测试器
 * 测试连接到比特浏览器19号窗口
 */

const puppeteer = require('puppeteer-core');
const axios = require('axios');

class PuppeteerTester {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.targetWindowSeq = 19;
    }

    /**
     * 获取19号窗口信息
     */
    async getWindow19() {
        console.log('🔍 获取19号窗口信息...');
        
        try {
            const response = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            if (response.data.success) {
                const browsers = response.data.data.list;
                const window19 = browsers.find(browser => browser.seq === this.targetWindowSeq);
                
                if (window19) {
                    console.log(`✅ 找到19号窗口:`);
                    console.log(`   ID: ${window19.id}`);
                    console.log(`   序号: ${window19.seq}`);
                    console.log(`   名称: ${window19.name || '未命名'}`);
                    console.log(`   状态: ${window19.status} (1=运行中, 0=已关闭)`);
                    console.log(`   分组: ${window19.groupName}`);
                    return window19;
                } else {
                    throw new Error('未找到19号窗口');
                }
            } else {
                throw new Error(`API调用失败: ${response.data.msg}`);
            }
        } catch (error) {
            console.error('❌ 获取窗口信息失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取调试端口
     */
    async getDebugPorts() {
        console.log('🔌 获取调试端口...');
        
        try {
            const response = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            
            if (response.data.success) {
                const ports = response.data.data;
                console.log(`✅ 获取到 ${Object.keys(ports).length} 个调试端口:`);
                
                for (const [browserId, port] of Object.entries(ports)) {
                    console.log(`   浏览器 ${browserId.substring(0, 8)}... -> 端口 ${port}`);
                }
                
                return ports;
            } else {
                throw new Error(`获取端口失败: ${response.data.msg}`);
            }
        } catch (error) {
            console.error('❌ 获取调试端口失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取页面列表
     */
    async getPageList(debugPort) {
        console.log('📄 获取页面列表...');

        try {
            const response = await axios.get(`http://localhost:${debugPort}/json/list`);
            const pages = response.data;

            console.log(`✅ 找到 ${pages.length} 个页面:`);
            pages.forEach((page, index) => {
                console.log(`   ${index + 1}. ${page.title}`);
                console.log(`      URL: ${page.url}`);
                console.log(`      WebSocket: ${page.webSocketDebuggerUrl}`);
            });

            return pages;
        } catch (error) {
            console.error('❌ 获取页面列表失败:', error.message);
            throw error;
        }
    }

    /**
     * 测试Puppeteer连接
     */
    async testPuppeteerConnection() {
        console.log('\n🎯 开始Puppeteer连接测试...');

        try {
            // 1. 获取19号窗口
            const window19 = await this.getWindow19();

            // 2. 获取调试端口
            const ports = await this.getDebugPorts();
            const debugPort = ports[window19.id];

            if (!debugPort) {
                throw new Error('19号窗口未打开或未获取到调试端口');
            }

            console.log(`\n🎯 19号窗口调试端口: ${debugPort}`);

            // 3. 获取页面列表
            const pages = await this.getPageList(debugPort);

            // 4. 查找小红书页面
            const xiaohongshuPages = pages.filter(page =>
                page.url.includes('xiaohongshu.com') &&
                page.type === 'page'
            );

            if (xiaohongshuPages.length === 0) {
                throw new Error('未找到小红书页面');
            }

            console.log(`\n🎯 找到 ${xiaohongshuPages.length} 个小红书页面`);

            // 5. 连接到第一个小红书页面
            const targetPage = xiaohongshuPages[0];
            console.log(`🔗 连接到页面: ${targetPage.title}`);
            console.log(`   WebSocket: ${targetPage.webSocketDebuggerUrl}`);

            // 直接连接到页面的WebSocket
            const page = await puppeteer.connect({
                browserWSEndpoint: targetPage.webSocketDebuggerUrl,
                defaultViewport: null
            });

            console.log('✅ Puppeteer连接成功!');

            // 6. 获取页面信息
            const url = await page.url();
            const title = await page.title();

            console.log(`📝 页面信息:`);
            console.log(`   URL: ${url}`);
            console.log(`   标题: ${title}`);

            // 7. 页面内容分析
            console.log('🎉 检测到小红书页面!');

            try {
                const bodyText = await page.evaluate(() => document.body.textContent);
                const commentCount = (bodyText.match(/条评论/g) || []).length;
                const replyCount = (bodyText.match(/回复/g) || []).length;
                const timeCount = (bodyText.match(/[分小天月年]前/g) || []).length;

                console.log(`📊 页面内容分析:`);
                console.log(`   "条评论"出现次数: ${commentCount}`);
                console.log(`   "回复"出现次数: ${replyCount}`);
                console.log(`   时间标识出现次数: ${timeCount}`);

                if (commentCount > 0 || replyCount > 0 || timeCount > 0) {
                    console.log('✅ 页面包含评论相关内容，适合爬取!');
                } else {
                    console.log('⚠️ 页面可能不在评论区');
                }

                // 简单的元素检测
                const commentElements = await page.$$('[class*="comment"], [class*="Comment"]');
                console.log(`📝 找到 ${commentElements.length} 个可能的评论元素`);

            } catch (e) {
                console.log('⚠️ 页面内容分析失败:', e.message);
            }

            // 8. 断开连接
            await page.browser().disconnect();
            console.log('🔒 已断开连接');
            
            return true;
            
        } catch (error) {
            console.error('❌ Puppeteer连接失败:', error.message);
            
            // 错误分析
            if (error.message.includes('ECONNREFUSED')) {
                console.log('💡 可能原因: 调试端口未开启或端口号错误');
                console.log('💡 解决方案: 确保比特浏览器19号窗口正在运行');
            } else if (error.message.includes('WebSocket')) {
                console.log('💡 可能原因: WebSocket连接失败');
                console.log('💡 解决方案: 检查端口是否正确或重启浏览器');
            } else if (error.message.includes('未找到')) {
                console.log('💡 可能原因: 19号窗口未运行');
                console.log('💡 解决方案: 在比特浏览器中打开19号窗口');
            }
            
            return false;
        }
    }

    /**
     * 运行完整测试
     */
    async runTest() {
        console.log('🎯 Puppeteer连接测试器');
        console.log('='.repeat(50));
        
        console.log('📝 测试步骤:');
        console.log('   1. 连接比特浏览器API');
        console.log('   2. 获取19号窗口信息');
        console.log('   3. 获取调试端口');
        console.log('   4. 使用Puppeteer连接');
        console.log('   5. 验证页面信息');
        
        const success = await this.testPuppeteerConnection();
        
        console.log('\n' + '='.repeat(50));
        if (success) {
            console.log('🎉 Puppeteer连接测试成功!');
            console.log('🚀 现在可以运行完整的爬虫脚本:');
            console.log('   node puppeteer_scraper.js');
        } else {
            console.log('❌ Puppeteer连接测试失败');
            console.log('💡 请检查:');
            console.log('   1. 比特浏览器是否正在运行');
            console.log('   2. 19号窗口是否已打开');
            console.log('   3. 网络连接是否正常');
        }
    }
}

// 主函数
async function main() {
    const tester = new PuppeteerTester();
    await tester.runTest();
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = PuppeteerTester;
