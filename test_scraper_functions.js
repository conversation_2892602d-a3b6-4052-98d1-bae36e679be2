/**
 * 🧪 爬虫功能测试器
 * 全面测试小红书评论爬虫的各项功能
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

class ScraperFunctionTester {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.testResults = [];
    }

    /**
     * 记录测试结果
     */
    recordTest(testName, success, details = '') {
        const result = {
            test: testName,
            success,
            details,
            timestamp: new Date().toISOString()
        };
        this.testResults.push(result);
        
        const status = success ? '✅' : '❌';
        console.log(`${status} ${testName}: ${details}`);
    }

    /**
     * 测试1: 比特浏览器API连接
     */
    async testBitBrowserAPI() {
        console.log('\n🧪 测试1: 比特浏览器API连接');
        
        try {
            const response = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 10
            });

            if (response.data.success) {
                const browserCount = response.data.data.list.length;
                this.recordTest('API连接', true, `成功获取${browserCount}个浏览器窗口`);
                return true;
            } else {
                this.recordTest('API连接', false, `API返回错误: ${response.data.msg}`);
                return false;
            }
        } catch (error) {
            this.recordTest('API连接', false, `连接失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 测试2: 19号窗口检测
     */
    async test19WindowDetection() {
        console.log('\n🧪 测试2: 19号窗口检测');
        
        try {
            const response = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            const browsers = response.data.data.list;
            const window19 = browsers.find(browser => browser.seq === 19);
            
            if (window19) {
                this.recordTest('19号窗口检测', true, `找到窗口: ${window19.name || '未命名'}, 状态: ${window19.status}`);
                return window19;
            } else {
                this.recordTest('19号窗口检测', false, '未找到19号窗口');
                return null;
            }
        } catch (error) {
            this.recordTest('19号窗口检测', false, `检测失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 测试3: 调试端口获取
     */
    async testDebugPortRetrieval(window19) {
        console.log('\n🧪 测试3: 调试端口获取');
        
        if (!window19) {
            this.recordTest('调试端口获取', false, '19号窗口不存在');
            return null;
        }

        try {
            const response = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            
            if (response.data.success) {
                const ports = response.data.data;
                const debugPort = ports[window19.id];
                
                if (debugPort) {
                    this.recordTest('调试端口获取', true, `端口: ${debugPort}`);
                    return debugPort;
                } else {
                    this.recordTest('调试端口获取', false, '未获取到19号窗口的调试端口');
                    return null;
                }
            } else {
                this.recordTest('调试端口获取', false, `API错误: ${response.data.msg}`);
                return null;
            }
        } catch (error) {
            this.recordTest('调试端口获取', false, `获取失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 测试4: 调试端口连通性
     */
    async testDebugPortConnectivity(debugPort) {
        console.log('\n🧪 测试4: 调试端口连通性');
        
        if (!debugPort) {
            this.recordTest('调试端口连通性', false, '调试端口不存在');
            return false;
        }

        try {
            // 测试基本连接
            const response = await axios.get(`http://localhost:${debugPort}/json/version`, { timeout: 5000 });
            
            if (response.status === 200) {
                const browserInfo = response.data;
                this.recordTest('调试端口连通性', true, `Chrome版本: ${browserInfo.Browser}`);
                return true;
            } else {
                this.recordTest('调试端口连通性', false, `HTTP状态: ${response.status}`);
                return false;
            }
        } catch (error) {
            this.recordTest('调试端口连通性', false, `连接失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 测试5: 页面列表获取
     */
    async testPageListRetrieval(debugPort) {
        console.log('\n🧪 测试5: 页面列表获取');
        
        if (!debugPort) {
            this.recordTest('页面列表获取', false, '调试端口不存在');
            return [];
        }

        try {
            const response = await axios.get(`http://localhost:${debugPort}/json/list`, { timeout: 5000 });
            
            if (response.status === 200) {
                const pages = response.data;
                const xiaohongshuPages = pages.filter(page => page.url.includes('xiaohongshu.com'));
                
                this.recordTest('页面列表获取', true, `总页面: ${pages.length}, 小红书页面: ${xiaohongshuPages.length}`);
                return pages;
            } else {
                this.recordTest('页面列表获取', false, `HTTP状态: ${response.status}`);
                return [];
            }
        } catch (error) {
            this.recordTest('页面列表获取', false, `获取失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 测试6: 小红书页面识别
     */
    async testXiaohongshuPageDetection(pages) {
        console.log('\n🧪 测试6: 小红书页面识别');
        
        if (pages.length === 0) {
            this.recordTest('小红书页面识别', false, '没有页面可检测');
            return null;
        }

        const xiaohongshuPages = pages.filter(page => page.url.includes('xiaohongshu.com'));
        
        if (xiaohongshuPages.length === 0) {
            this.recordTest('小红书页面识别', false, '未找到小红书页面');
            return null;
        }

        // 检测页面类型
        const explorePage = xiaohongshuPages.find(page => page.url.includes('/explore/'));
        const profilePage = xiaohongshuPages.find(page => page.url.includes('/user/profile/'));
        
        let details = `找到${xiaohongshuPages.length}个小红书页面`;
        if (explorePage) details += ', 包含评论页面';
        if (profilePage) details += ', 包含用户资料页面';
        
        this.recordTest('小红书页面识别', true, details);
        return xiaohongshuPages;
    }

    /**
     * 测试7: 最新爬取结果验证
     */
    async testLatestScrapeResults() {
        console.log('\n🧪 测试7: 最新爬取结果验证');
        
        try {
            // 查找最新的爬取结果文件
            const files = fs.readdirSync('.');
            const jsonFiles = files.filter(file => 
                file.startsWith('xiaohongshu_comments_final_') && 
                file.endsWith('.json')
            );
            
            if (jsonFiles.length === 0) {
                this.recordTest('爬取结果验证', false, '未找到爬取结果文件');
                return false;
            }

            // 获取最新文件
            const latestFile = jsonFiles.sort().pop();
            const filePath = path.join('.', latestFile);
            
            // 读取并验证文件内容
            const content = fs.readFileSync(filePath, 'utf8');
            const data = JSON.parse(content);
            
            // 验证数据结构
            const hasValidStructure = (
                data.scrape_info &&
                data.scrape_info.url &&
                data.scrape_info.total_comments &&
                Array.isArray(data.comments)
            );
            
            if (hasValidStructure) {
                const commentCount = data.comments.length;
                const url = data.scrape_info.url;
                const scrapeTime = data.scrape_info.scrape_time;
                
                this.recordTest('爬取结果验证', true, 
                    `文件: ${latestFile}, 评论数: ${commentCount}, 时间: ${scrapeTime}`);
                
                // 验证评论内容质量
                const validComments = data.comments.filter(comment => 
                    comment.content && 
                    comment.content.length > 5 && 
                    comment.timestamp
                );
                
                this.recordTest('评论内容质量', true, 
                    `有效评论: ${validComments.length}/${commentCount}`);
                
                return true;
            } else {
                this.recordTest('爬取结果验证', false, '数据结构不完整');
                return false;
            }
            
        } catch (error) {
            this.recordTest('爬取结果验证', false, `验证失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 测试8: Puppeteer脚本可用性
     */
    async testPuppeteerScriptAvailability() {
        console.log('\n🧪 测试8: Puppeteer脚本可用性');
        
        try {
            const scriptPath = './puppeteer_final.js';
            
            if (fs.existsSync(scriptPath)) {
                const stats = fs.statSync(scriptPath);
                const sizeKB = Math.round(stats.size / 1024);
                
                this.recordTest('Puppeteer脚本可用性', true, 
                    `脚本存在, 大小: ${sizeKB}KB, 修改时间: ${stats.mtime.toISOString()}`);
                return true;
            } else {
                this.recordTest('Puppeteer脚本可用性', false, '脚本文件不存在');
                return false;
            }
        } catch (error) {
            this.recordTest('Puppeteer脚本可用性', false, `检查失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        console.log('\n📊 测试报告生成');
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(result => result.success).length;
        const failedTests = totalTests - passedTests;
        const successRate = Math.round((passedTests / totalTests) * 100);
        
        const report = {
            summary: {
                total_tests: totalTests,
                passed: passedTests,
                failed: failedTests,
                success_rate: `${successRate}%`,
                test_time: new Date().toISOString()
            },
            detailed_results: this.testResults
        };
        
        // 保存报告
        const reportFile = `scraper_test_report_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
        fs.writeFileSync(reportFile, JSON.stringify(report, null, 2), 'utf8');
        
        console.log(`📄 测试报告已保存: ${reportFile}`);
        console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过 (${successRate}%)`);
        
        return report;
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🧪 小红书评论爬虫功能测试');
        console.log('='.repeat(50));
        
        try {
            // 执行所有测试
            const apiSuccess = await this.testBitBrowserAPI();
            const window19 = await this.test19WindowDetection();
            const debugPort = await this.testDebugPortRetrieval(window19);
            const portConnected = await this.testDebugPortConnectivity(debugPort);
            const pages = await this.testPageListRetrieval(debugPort);
            const xiaohongshuPages = await this.testXiaohongshuPageDetection(pages);
            const resultsValid = await this.testLatestScrapeResults();
            const scriptAvailable = await this.testPuppeteerScriptAvailability();
            
            // 生成报告
            const report = this.generateTestReport();
            
            console.log('\n🎯 测试完成!');
            
            if (report.summary.success_rate === '100%') {
                console.log('🎉 所有功能测试通过！爬虫完全正常工作！');
            } else {
                console.log(`⚠️ 部分测试失败，成功率: ${report.summary.success_rate}`);
            }
            
            return report;
            
        } catch (error) {
            console.error('❌ 测试执行失败:', error.message);
            return null;
        }
    }
}

// 主函数
async function main() {
    const tester = new ScraperFunctionTester();
    await tester.runAllTests();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = ScraperFunctionTester;
