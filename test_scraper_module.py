#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 测试小红书评论爬取模块
演示如何集成到矩阵工具中
"""

from xiaohongshu_scraper_module import XiaohongshuScraperModule
import time

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试小红书评论爬取模块")
    print("=" * 50)
    
    # 初始化模块
    scraper = XiaohongshuScraperModule(
        api_url="http://127.0.0.1:56906",
        output_dir="./test_output"
    )
    
    try:
        # 1. 获取浏览器列表
        print("📋 获取浏览器列表...")
        browsers = scraper.list_browsers()
        
        if not browsers:
            print("❌ 未找到浏览器窗口")
            print("💡 请确保比特浏览器正在运行")
            return
        
        print(f"✅ 找到 {len(browsers)} 个浏览器窗口")
        
        # 显示前5个浏览器
        for i, browser in enumerate(browsers[:5]):
            seq = browser.get('seq', 'N/A')
            name = browser.get('name', '未命名')
            browser_id = browser.get('id', '')
            print(f"   {i+1}. 序号: {seq}, 名称: {name}, ID: {browser_id[:8]}...")
        
        # 2. 查找19号窗口
        print("\n🔍 查找19号窗口...")
        browser_19 = scraper.find_browser_by_seq(19)
        
        if not browser_19:
            print("❌ 未找到19号窗口")
            # 使用第一个窗口作为测试
            if browsers:
                browser_19 = browsers[0]
                print(f"💡 使用第一个窗口进行测试: 序号 {browser_19.get('seq')}")
            else:
                return
        else:
            print(f"✅ 找到19号窗口: {browser_19.get('name', '未命名')}")
        
        browser_id = browser_19.get('id')
        
        # 3. 打开浏览器窗口
        print(f"\n🚀 打开浏览器窗口...")
        open_result = scraper.open_browser(browser_id)
        
        if not open_result:
            print("❌ 打开浏览器失败")
            return
        
        debug_port = open_result.get('http', '').split(':')[-1]
        print(f"✅ 浏览器已打开，调试端口: {debug_port}")
        
        # 等待浏览器完全启动
        print("⏳ 等待浏览器启动...")
        time.sleep(5)
        
        # 4. 连接Selenium
        print(f"\n🔗 连接Selenium到端口 {debug_port}...")
        if not scraper.connect_selenium(debug_port):
            print("❌ Selenium连接失败")
            return
        
        print("✅ Selenium连接成功")
        
        # 5. 检查当前页面
        current_url = scraper.driver.current_url
        print(f"📄 当前页面: {current_url}")
        
        # 如果不在小红书页面，提示用户
        if 'xiaohongshu.com' not in current_url:
            print("💡 请手动导航到小红书评论页面")
            print("完成后按回车继续...")
            input()
        
        # 6. 开始爬取评论
        print("\n🎯 开始爬取评论...")
        result = scraper.scrape_comments()
        
        # 7. 保存结果
        print("\n💾 保存结果...")
        filepath = scraper.save_to_file(result)
        print(f"✅ 数据已保存到: {filepath}")
        
        # 8. 输出统计信息
        print("\n📊 爬取结果统计:")
        print(f"   📝 笔记ID: {result['noteInfo'].get('id', 'N/A')}")
        print(f"   📝 笔记标题: {result['noteInfo'].get('title', 'N/A')}")
        print(f"   👤 笔记作者: {result['noteInfo'].get('author', 'N/A')}")
        print(f"   🎯 目标评论数: {result['noteInfo'].get('totalCommentCount', 'N/A')}")
        print(f"   💬 实际提取数: {len(result['comments'])}")
        
        if result['comments']:
            print("\n👥 评论预览:")
            for i, comment in enumerate(result['comments'][:5]):
                username = comment.get('username', '匿名')
                time_str = comment.get('time', '未知时间')
                content = comment.get('content', '')[:60]
                print(f"   {i+1}. {username} ({time_str}): {content}...")
            
            # 兼职相关统计
            job_keywords = ['兼职', '工作', '聊天员', '陪玩', '求带', '日入', '赚钱']
            job_comments = [c for c in result['comments'] 
                          if any(keyword in c.get('content', '') for keyword in job_keywords)]
            
            if job_comments:
                job_percentage = round((len(job_comments) / len(result['comments'])) * 100)
                print(f"\n💼 兼职相关评论: {len(job_comments)}/{len(result['comments'])} ({job_percentage}%)")
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        scraper.cleanup()

def test_matrix_integration():
    """测试矩阵工具集成"""
    print("\n🔧 矩阵工具集成示例")
    print("=" * 30)
    
    # 模拟矩阵工具的配置
    matrix_config = {
        'bitbrowser_api': 'http://127.0.0.1:56906',
        'target_browser_seq': 19,
        'output_directory': './matrix_output',
        'scraping_targets': [
            {
                'platform': 'xiaohongshu',
                'url': 'https://www.xiaohongshu.com/explore/67af69ee000000002a003a157',
                'target_comments': 1472,
                'keywords': ['兼职', '聊天员', '陪玩', '求带']
            }
        ]
    }
    
    print("📋 矩阵工具配置:")
    print(f"   🔗 API地址: {matrix_config['bitbrowser_api']}")
    print(f"   🎯 目标浏览器: {matrix_config['target_browser_seq']}号窗口")
    print(f"   📁 输出目录: {matrix_config['output_directory']}")
    print(f"   🎯 爬取目标: {len(matrix_config['scraping_targets'])} 个")
    
    # 初始化爬取模块
    scraper = XiaohongshuScraperModule(
        api_url=matrix_config['bitbrowser_api'],
        output_dir=matrix_config['output_directory']
    )
    
    # 设置目标评论数
    target = matrix_config['scraping_targets'][0]
    scraper.config['target_comments'] = target['target_comments']
    
    print(f"\n🎯 配置完成，目标评论数: {target['target_comments']}")
    print("💡 这个模块可以轻松集成到你的矩阵工具中")
    
    # 显示集成方法
    print("\n🔧 集成方法:")
    print("1. 导入模块: from xiaohongshu_scraper_module import XiaohongshuScraperModule")
    print("2. 初始化: scraper = XiaohongshuScraperModule(api_url, output_dir)")
    print("3. 查找浏览器: browser = scraper.find_browser_by_seq(19)")
    print("4. 打开浏览器: result = scraper.open_browser(browser_id)")
    print("5. 连接Selenium: scraper.connect_selenium(debug_port)")
    print("6. 爬取评论: data = scraper.scrape_comments(target_url)")
    print("7. 保存数据: filepath = scraper.save_to_file(data)")
    print("8. 清理资源: scraper.cleanup()")

def main():
    """主函数"""
    print("🎯 小红书评论爬取模块测试")
    print("适用于集成到矩阵工具")
    print("=" * 50)
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 基本功能测试（实际爬取）")
    print("2. 矩阵工具集成示例（仅演示）")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == '1':
        test_basic_functionality()
    elif choice == '2':
        test_matrix_integration()
    else:
        print("无效选择，执行基本功能测试")
        test_basic_functionality()

if __name__ == "__main__":
    main()
