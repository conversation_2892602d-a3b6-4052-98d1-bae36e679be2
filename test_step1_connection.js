#!/usr/bin/env node

/**
 * 🔍 第一步：测试基础连接
 */

const axios = require('axios');

async function testConnection() {
    console.log('🔍 第一步：测试基础连接...\n');

    // 测试常见的Chrome调试端口
    const ports = [63524, 9222, 51859, 58222];
    
    for (const port of ports) {
        try {
            console.log(`🔌 测试端口 ${port}...`);
            
            const response = await axios.get(`http://127.0.0.1:${port}/json`, {
                timeout: 3000
            });
            
            console.log(`✅ 端口 ${port} 连接成功！`);
            console.log(`📋 找到 ${response.data.length} 个标签页`);
            
            // 查找小红书标签页
            const xiaohongshuTabs = response.data.filter(tab => 
                tab.url && (
                    tab.url.includes('xiaohongshu.com') || 
                    tab.title.includes('小红书')
                )
            );
            
            if (xiaohongshuTabs.length > 0) {
                console.log(`🎯 找到 ${xiaohongshuTabs.length} 个小红书标签页：`);
                xiaohongshuTabs.forEach((tab, index) => {
                    console.log(`   ${index + 1}. ${tab.title}`);
                    console.log(`      URL: ${tab.url.substring(0, 80)}...`);
                    console.log(`      WebSocket: ${tab.webSocketDebuggerUrl ? '可用' : '不可用'}`);
                });
                
                console.log('\n🎉 连接测试成功！可以进行下一步测试。');
                return { port, tabs: xiaohongshuTabs };
            } else {
                console.log('⚠️ 未找到小红书标签页');
                console.log('📋 当前打开的标签页：');
                response.data.slice(0, 3).forEach((tab, index) => {
                    console.log(`   ${index + 1}. ${tab.title || '无标题'}`);
                    console.log(`      URL: ${(tab.url || '无URL').substring(0, 60)}...`);
                });
            }
            
        } catch (error) {
            console.log(`❌ 端口 ${port} 连接失败: ${error.message}`);
        }
    }
    
    console.log('\n❌ 所有端口都连接失败！');
    console.log('💡 请确保：');
    console.log('   1. Chrome浏览器已启动');
    console.log('   2. 启动时使用了 --remote-debugging-port 参数');
    console.log('   3. 小红书页面已打开');
    
    return null;
}

if (require.main === module) {
    testConnection().catch(console.error);
}

module.exports = testConnection;
