#!/usr/bin/env node

/**
 * 🔍 第二步：测试WebSocket连接和基础脚本执行
 */

const axios = require('axios');
const WebSocket = require('ws');

async function testWebSocket() {
    console.log('🔍 第二步：测试WebSocket连接和基础脚本执行...\n');

    try {
        // 1. 获取标签页
        const response = await axios.get('http://127.0.0.1:63524/json', {
            timeout: 5000
        });

        const tab = response.data.find(tab =>
            tab.url && tab.url.includes('xiaohongshu.com/explore/')
        );

        if (!tab) {
            throw new Error('未找到小红书笔记页面');
        }

        console.log(`🎯 找到目标页面: ${tab.title}`);
        console.log(`🔗 URL: ${tab.url.substring(0, 80)}...`);

        // 2. 建立WebSocket连接
        console.log('\n🔌 建立WebSocket连接...');
        const ws = new WebSocket(tab.webSocketDebuggerUrl);

        return new Promise((resolve, reject) => {
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                // 启用Runtime
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    console.log('📝 执行基础测试脚本...');
                    
                    const testScript = `
                        (function() {
                            try {
                                const result = {
                                    url: window.location.href,
                                    title: document.title,
                                    pageHeight: document.body.scrollHeight,
                                    visibleHeight: window.innerHeight,
                                    avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                    commentDivs: document.querySelectorAll('[class*="comment"]').length,
                                    replyDivs: document.querySelectorAll('[class*="reply"]').length,
                                    moreButtons: Array.from(document.querySelectorAll('*')).filter(el => 
                                        el.textContent.includes('更多') || el.textContent.includes('展开')
                                    ).length
                                };
                                
                                console.log('脚本执行成功，结果:', result);
                                return { success: true, data: result };
                            } catch (error) {
                                console.error('脚本执行错误:', error);
                                return { success: false, error: error.message };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: testScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            const data = result.data;
                            console.log('\n📊 基础测试结果:');
                            console.log(`   📝 标题: ${data.title}`);
                            console.log(`   📏 页面高度: ${data.pageHeight}px`);
                            console.log(`   👀 可视高度: ${data.visibleHeight}px`);
                            console.log(`   👤 头像数量: ${data.avatars}`);
                            console.log(`   💬 评论div: ${data.commentDivs}`);
                            console.log(`   ↩️ 回复div: ${data.replyDivs}`);
                            console.log(`   🔘 更多按钮: ${data.moreButtons}`);
                            
                            console.log('\n✅ WebSocket测试成功！可以进行下一步测试。');
                            ws.close();
                            resolve(data);
                        } else {
                            console.log('❌ 脚本执行失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理消息失败:', error.message);
                    ws.close();
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('WebSocket测试超时'));
            }, 15000);
        });

    } catch (error) {
        console.error('❌ WebSocket测试失败:', error.message);
        throw error;
    }
}

if (require.main === module) {
    testWebSocket().catch(console.error);
}

module.exports = testWebSocket;
