#!/usr/bin/env node

/**
 * 🔍 第三步：测试单次滚动和按钮点击
 */

const axios = require('axios');
const WebSocket = require('ws');

async function testScrollAndClick() {
    console.log('🔍 第三步：测试单次滚动和按钮点击...\n');

    try {
        // 1. 获取标签页
        const response = await axios.get('http://127.0.0.1:63524/json', {
            timeout: 5000
        });

        const tab = response.data.find(tab =>
            tab.url && tab.url.includes('xiaohongshu.com/explore/')
        );

        if (!tab) {
            throw new Error('未找到小红书笔记页面');
        }

        console.log(`🎯 目标页面: ${tab.title}`);

        // 2. 建立WebSocket连接
        const ws = new WebSocket(tab.webSocketDebuggerUrl);

        return new Promise((resolve, reject) => {
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    console.log('🧪 执行滚动和点击测试...');
                    
                    const testScript = `
                        (async function() {
                            try {
                                console.log('开始滚动和点击测试...');
                                
                                const results = {
                                    before: {},
                                    after: {},
                                    clickResults: []
                                };

                                // 记录初始状态
                                results.before = {
                                    avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                    commentDivs: document.querySelectorAll('[class*="comment"]').length,
                                    pageHeight: document.body.scrollHeight,
                                    scrollTop: window.pageYOffset
                                };
                                
                                console.log('初始状态:', results.before);

                                // 1. 测试滚动
                                console.log('📜 测试滚动到底部...');
                                window.scrollTo(0, document.body.scrollHeight);
                                
                                // 等待内容加载
                                await new Promise(resolve => setTimeout(resolve, 2000));

                                // 2. 查找并点击"更多"按钮
                                console.log('🔘 查找"更多"按钮...');
                                const moreButtonTexts = ['更多', '展开', '查看', '条回复'];
                                let totalClicked = 0;
                                
                                for (const buttonText of moreButtonTexts) {
                                    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                                        const text = el.textContent.trim();
                                        const isVisible = el.offsetParent !== null;
                                        const isClickable = el.tagName === 'BUTTON' || 
                                                          el.tagName === 'A' ||
                                                          el.onclick || 
                                                          el.className.includes('btn') ||
                                                          getComputedStyle(el).cursor === 'pointer';
                                        
                                        return isVisible && isClickable && (
                                            text.includes(buttonText) ||
                                            (buttonText === '条回复' && /\\d+条回复/.test(text))
                                        );
                                    });
                                    
                                    console.log(\`找到 \${elements.length} 个"\${buttonText}"按钮\`);
                                    
                                    // 只点击前3个按钮进行测试
                                    for (let i = 0; i < Math.min(3, elements.length); i++) {
                                        try {
                                            const el = elements[i];
                                            const buttonText = el.textContent.trim().substring(0, 20);
                                            
                                            // 滚动到按钮可见
                                            el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                            await new Promise(resolve => setTimeout(resolve, 500));
                                            
                                            // 点击按钮
                                            el.click();
                                            totalClicked++;
                                            
                                            console.log(\`点击按钮: \${buttonText}\`);
                                            
                                            results.clickResults.push({
                                                buttonText: buttonText,
                                                success: true
                                            });
                                            
                                            // 等待内容加载
                                            await new Promise(resolve => setTimeout(resolve, 1000));
                                            
                                        } catch (e) {
                                            console.log(\`点击失败: \${e.message}\`);
                                            results.clickResults.push({
                                                buttonText: buttonText,
                                                success: false,
                                                error: e.message
                                            });
                                        }
                                    }
                                }

                                // 3. 再次滚动确保内容加载
                                console.log('📜 再次滚动确保内容加载...');
                                window.scrollTo(0, document.body.scrollHeight);
                                await new Promise(resolve => setTimeout(resolve, 2000));

                                // 记录最终状态
                                results.after = {
                                    avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                    commentDivs: document.querySelectorAll('[class*="comment"]').length,
                                    pageHeight: document.body.scrollHeight,
                                    scrollTop: window.pageYOffset
                                };
                                
                                console.log('最终状态:', results.after);

                                results.summary = {
                                    avatarsAdded: results.after.avatars - results.before.avatars,
                                    commentDivsAdded: results.after.commentDivs - results.before.commentDivs,
                                    heightChanged: results.after.pageHeight - results.before.pageHeight,
                                    totalClicked: totalClicked
                                };

                                console.log('测试完成，结果:', results.summary);
                                return { success: true, data: results };
                                
                            } catch (error) {
                                console.error('测试过程出错:', error);
                                return { success: false, error: error.message };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: testScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            const data = result.data;
                            console.log('\n📊 滚动和点击测试结果:');
                            console.log('=' * 50);
                            
                            console.log('\n📈 数量变化:');
                            console.log(`   👤 头像: ${data.before.avatars} → ${data.after.avatars} (+${data.summary.avatarsAdded})`);
                            console.log(`   💬 评论div: ${data.before.commentDivs} → ${data.after.commentDivs} (+${data.summary.commentDivsAdded})`);
                            console.log(`   📏 页面高度: ${data.before.pageHeight} → ${data.after.pageHeight} (+${data.summary.heightChanged}px)`);
                            console.log(`   🖱️ 点击按钮: ${data.summary.totalClicked}个`);
                            
                            console.log('\n🔘 按钮点击详情:');
                            data.clickResults.forEach((click, index) => {
                                const status = click.success ? '✅' : '❌';
                                console.log(`   ${index + 1}. ${status} ${click.buttonText}`);
                                if (!click.success) {
                                    console.log(`      错误: ${click.error}`);
                                }
                            });
                            
                            if (data.summary.avatarsAdded > 0 || data.summary.commentDivsAdded > 0) {
                                console.log('\n🎉 测试成功！发现新内容加载，可以进行批量采集！');
                            } else {
                                console.log('\n⚠️ 没有新内容加载，可能需要调整策略。');
                            }
                            
                            ws.close();
                            resolve(data);
                        } else {
                            console.log('❌ 测试失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理消息失败:', error.message);
                    ws.close();
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('滚动点击测试超时'));
            }, 30000);
        });

    } catch (error) {
        console.error('❌ 滚动点击测试失败:', error.message);
        throw error;
    }
}

if (require.main === module) {
    testScrollAndClick().catch(console.error);
}

module.exports = testScrollAndClick;
