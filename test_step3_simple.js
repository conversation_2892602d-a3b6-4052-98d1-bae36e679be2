#!/usr/bin/env node

/**
 * 🔍 第三步（简化版）：测试基础滚动
 */

const axios = require('axios');
const WebSocket = require('ws');

async function testSimpleScroll() {
    console.log('🔍 第三步（简化版）：测试基础滚动...\n');

    try {
        // 1. 获取标签页
        const response = await axios.get('http://127.0.0.1:63524/json', {
            timeout: 5000
        });

        const tab = response.data.find(tab =>
            tab.url && tab.url.includes('xiaohongshu.com/explore/')
        );

        if (!tab) {
            throw new Error('未找到小红书笔记页面');
        }

        console.log(`🎯 目标页面: ${tab.title}`);

        // 2. 建立WebSocket连接
        const ws = new WebSocket(tab.webSocketDebuggerUrl);

        return new Promise((resolve, reject) => {
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    console.log('📜 执行简单滚动测试...');
                    
                    const testScript = `
                        (function() {
                            try {
                                console.log('开始简单滚动测试...');
                                
                                // 记录初始状态
                                const before = {
                                    avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                    pageHeight: document.body.scrollHeight,
                                    scrollTop: window.pageYOffset
                                };
                                
                                console.log('滚动前:', before);

                                // 滚动到底部
                                window.scrollTo(0, document.body.scrollHeight);
                                
                                // 记录滚动后状态
                                const after = {
                                    avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                    pageHeight: document.body.scrollHeight,
                                    scrollTop: window.pageYOffset
                                };
                                
                                console.log('滚动后:', after);

                                const result = {
                                    before: before,
                                    after: after,
                                    avatarsAdded: after.avatars - before.avatars,
                                    heightChanged: after.pageHeight - before.pageHeight,
                                    scrolled: after.scrollTop > before.scrollTop
                                };

                                console.log('滚动测试完成:', result);
                                return { success: true, data: result };
                                
                            } catch (error) {
                                console.error('滚动测试出错:', error);
                                return { success: false, error: error.message };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: testScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            const data = result.data;
                            console.log('\n📊 简单滚动测试结果:');
                            console.log(`   👤 头像: ${data.before.avatars} → ${data.after.avatars} (+${data.avatarsAdded})`);
                            console.log(`   📏 页面高度: ${data.before.pageHeight} → ${data.after.pageHeight} (+${data.heightChanged}px)`);
                            console.log(`   📜 滚动位置: ${data.before.scrollTop} → ${data.after.scrollTop}`);
                            console.log(`   ✅ 滚动成功: ${data.scrolled ? '是' : '否'}`);
                            
                            if (data.avatarsAdded > 0) {
                                console.log('\n🎉 发现新头像加载！滚动有效果！');
                            } else if (data.heightChanged > 0) {
                                console.log('\n📈 页面高度增加！可能有新内容！');
                            } else {
                                console.log('\n⚠️ 没有明显变化，可能需要等待或点击按钮。');
                            }
                            
                            ws.close();
                            resolve(data);
                        } else {
                            console.log('❌ 滚动测试失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理消息失败:', error.message);
                    ws.close();
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('简单滚动测试超时'));
            }, 15000);
        });

    } catch (error) {
        console.error('❌ 简单滚动测试失败:', error.message);
        throw error;
    }
}

if (require.main === module) {
    testSimpleScroll().catch(console.error);
}

module.exports = testSimpleScroll;
