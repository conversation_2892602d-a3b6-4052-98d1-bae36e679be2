#!/usr/bin/env node

/**
 * 🔍 第四步：测试按钮点击
 */

const axios = require('axios');
const WebSocket = require('ws');

async function testButtonClick() {
    console.log('🔍 第四步：测试按钮点击...\n');

    try {
        // 1. 获取标签页
        const response = await axios.get('http://127.0.0.1:63524/json', {
            timeout: 5000
        });

        const tab = response.data.find(tab =>
            tab.url && tab.url.includes('xiaohongshu.com/explore/')
        );

        if (!tab) {
            throw new Error('未找到小红书笔记页面');
        }

        console.log(`🎯 目标页面: ${tab.title}`);

        // 2. 建立WebSocket连接
        const ws = new WebSocket(tab.webSocketDebuggerUrl);

        return new Promise((resolve, reject) => {
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    console.log('🔘 执行按钮点击测试...');
                    
                    const testScript = `
                        (function() {
                            try {
                                console.log('开始按钮点击测试...');
                                
                                const results = {
                                    before: {},
                                    buttons: [],
                                    clicks: [],
                                    after: {}
                                };

                                // 记录初始状态
                                results.before = {
                                    avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                    pageHeight: document.body.scrollHeight
                                };
                                
                                console.log('点击前状态:', results.before);

                                // 查找所有可能的"更多"按钮
                                const moreButtonTexts = ['更多', '展开', '查看', '条回复'];
                                
                                for (const buttonText of moreButtonTexts) {
                                    console.log(\`查找"\${buttonText}"按钮...\`);
                                    
                                    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                                        const text = el.textContent.trim();
                                        const isVisible = el.offsetParent !== null;
                                        
                                        return isVisible && text.length > 0 && text.length < 50 && (
                                            text.includes(buttonText) ||
                                            (buttonText === '条回复' && /\\d+条回复/.test(text))
                                        );
                                    });
                                    
                                    console.log(\`找到 \${elements.length} 个"\${buttonText}"相关元素\`);
                                    
                                    elements.forEach((el, index) => {
                                        const buttonInfo = {
                                            text: el.textContent.trim().substring(0, 30),
                                            tagName: el.tagName,
                                            className: el.className,
                                            isClickable: el.tagName === 'BUTTON' || 
                                                        el.tagName === 'A' ||
                                                        el.onclick || 
                                                        el.className.includes('btn') ||
                                                        getComputedStyle(el).cursor === 'pointer'
                                        };
                                        
                                        results.buttons.push(buttonInfo);
                                        
                                        if (index < 2 && buttonInfo.isClickable) {
                                            console.log(\`尝试点击: \${buttonInfo.text}\`);
                                            try {
                                                el.click();
                                                results.clicks.push({
                                                    text: buttonInfo.text,
                                                    success: true
                                                });
                                                console.log(\`点击成功: \${buttonInfo.text}\`);
                                            } catch (e) {
                                                results.clicks.push({
                                                    text: buttonInfo.text,
                                                    success: false,
                                                    error: e.message
                                                });
                                                console.log(\`点击失败: \${e.message}\`);
                                            }
                                        }
                                    });
                                }

                                // 记录点击后状态
                                results.after = {
                                    avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                    pageHeight: document.body.scrollHeight
                                };
                                
                                console.log('点击后状态:', results.after);

                                results.summary = {
                                    totalButtons: results.buttons.length,
                                    clickableButtons: results.buttons.filter(b => b.isClickable).length,
                                    totalClicks: results.clicks.length,
                                    successfulClicks: results.clicks.filter(c => c.success).length,
                                    avatarsAdded: results.after.avatars - results.before.avatars,
                                    heightChanged: results.after.pageHeight - results.before.pageHeight
                                };

                                console.log('按钮测试完成:', results.summary);
                                return { success: true, data: results };
                                
                            } catch (error) {
                                console.error('按钮测试出错:', error);
                                return { success: false, error: error.message };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: testScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            const data = result.data;
                            console.log('\n📊 按钮点击测试结果:');
                            console.log('=' * 50);
                            
                            console.log('\n🔘 按钮统计:');
                            console.log(`   总按钮数: ${data.summary.totalButtons}`);
                            console.log(`   可点击按钮: ${data.summary.clickableButtons}`);
                            console.log(`   尝试点击: ${data.summary.totalClicks}`);
                            console.log(`   成功点击: ${data.summary.successfulClicks}`);
                            
                            console.log('\n📈 内容变化:');
                            console.log(`   👤 头像: ${data.before.avatars} → ${data.after.avatars} (+${data.summary.avatarsAdded})`);
                            console.log(`   📏 页面高度: ${data.before.pageHeight} → ${data.after.pageHeight} (+${data.summary.heightChanged}px)`);
                            
                            console.log('\n🔘 发现的按钮:');
                            data.buttons.slice(0, 10).forEach((button, index) => {
                                const clickable = button.isClickable ? '✅' : '❌';
                                console.log(`   ${index + 1}. ${clickable} [${button.tagName}] ${button.text}`);
                            });
                            
                            if (data.buttons.length > 10) {
                                console.log(`   ... 还有 ${data.buttons.length - 10} 个按钮`);
                            }
                            
                            console.log('\n🖱️ 点击结果:');
                            data.clicks.forEach((click, index) => {
                                const status = click.success ? '✅' : '❌';
                                console.log(`   ${index + 1}. ${status} ${click.text}`);
                                if (!click.success) {
                                    console.log(`      错误: ${click.error}`);
                                }
                            });
                            
                            if (data.summary.avatarsAdded > 0 || data.summary.heightChanged > 0) {
                                console.log('\n🎉 按钮点击有效果！发现新内容！');
                            } else {
                                console.log('\n⚠️ 按钮点击没有明显效果，可能需要等待或尝试其他按钮。');
                            }
                            
                            ws.close();
                            resolve(data);
                        } else {
                            console.log('❌ 按钮测试失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理消息失败:', error.message);
                    ws.close();
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('按钮测试超时'));
            }, 20000);
        });

    } catch (error) {
        console.error('❌ 按钮测试失败:', error.message);
        throw error;
    }
}

if (require.main === module) {
    testButtonClick().catch(console.error);
}

module.exports = testButtonClick;
