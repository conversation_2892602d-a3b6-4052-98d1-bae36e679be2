#!/usr/bin/env node

/**
 * 🔍 第五步：测试等待后的效果
 */

const axios = require('axios');
const WebSocket = require('ws');

async function testWaitEffect() {
    console.log('🔍 第五步：测试等待后的效果...\n');

    try {
        // 1. 获取标签页
        const response = await axios.get('http://127.0.0.1:63524/json', {
            timeout: 5000
        });

        const tab = response.data.find(tab =>
            tab.url && tab.url.includes('xiaohongshu.com/explore/')
        );

        if (!tab) {
            throw new Error('未找到小红书笔记页面');
        }

        console.log(`🎯 目标页面: ${tab.title}`);

        // 2. 建立WebSocket连接
        const ws = new WebSocket(tab.webSocketDebuggerUrl);

        return new Promise((resolve, reject) => {
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');
                
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                setTimeout(() => {
                    console.log('⏱️ 执行等待效果测试...');
                    
                    const testScript = `
                        (async function() {
                            try {
                                console.log('开始等待效果测试...');
                                
                                const results = {
                                    rounds: []
                                };

                                // 执行5轮：点击按钮 -> 等待 -> 检查效果
                                for (let round = 1; round <= 5; round++) {
                                    console.log(\`第 \${round} 轮测试...\`);
                                    
                                    const roundResult = {
                                        round: round,
                                        before: {},
                                        clicks: [],
                                        after: {}
                                    };

                                    // 记录轮次开始状态
                                    roundResult.before = {
                                        avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                        commentDivs: document.querySelectorAll('[class*="comment"]').length,
                                        pageHeight: document.body.scrollHeight
                                    };
                                    
                                    console.log(\`轮次 \${round} 开始状态:, roundResult.before\`);

                                    // 查找并点击按钮
                                    const moreButtonTexts = ['更多', '展开', '查看', '条回复'];
                                    let clickedInRound = 0;
                                    
                                    for (const buttonText of moreButtonTexts) {
                                        const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                                            const text = el.textContent.trim();
                                            const isVisible = el.offsetParent !== null;
                                            const isClickable = el.tagName === 'BUTTON' || 
                                                              el.tagName === 'A' ||
                                                              el.onclick || 
                                                              el.className.includes('btn') ||
                                                              getComputedStyle(el).cursor === 'pointer';
                                            
                                            return isVisible && isClickable && text.length > 0 && text.length < 50 && (
                                                text.includes(buttonText) ||
                                                (buttonText === '条回复' && /\\d+条回复/.test(text))
                                            );
                                        });
                                        
                                        // 只点击前2个按钮
                                        for (let i = 0; i < Math.min(2, elements.length); i++) {
                                            try {
                                                const el = elements[i];
                                                const buttonText = el.textContent.trim().substring(0, 20);
                                                
                                                el.click();
                                                clickedInRound++;
                                                
                                                roundResult.clicks.push({
                                                    text: buttonText,
                                                    success: true
                                                });
                                                
                                                console.log(\`点击: \${buttonText}\`);
                                                
                                            } catch (e) {
                                                roundResult.clicks.push({
                                                    text: buttonText,
                                                    success: false,
                                                    error: e.message
                                                });
                                            }
                                        }
                                    }

                                    console.log(\`轮次 \${round} 点击了 \${clickedInRound} 个按钮\`);

                                    // 等待内容加载
                                    console.log(\`等待 3 秒让内容加载...\`);
                                    await new Promise(resolve => setTimeout(resolve, 3000));

                                    // 滚动一下确保内容加载
                                    window.scrollTo(0, document.body.scrollHeight);
                                    await new Promise(resolve => setTimeout(resolve, 1000));

                                    // 记录轮次结束状态
                                    roundResult.after = {
                                        avatars: document.querySelectorAll('img[src*="avatar"]').length,
                                        commentDivs: document.querySelectorAll('[class*="comment"]').length,
                                        pageHeight: document.body.scrollHeight
                                    };
                                    
                                    roundResult.changes = {
                                        avatarsAdded: roundResult.after.avatars - roundResult.before.avatars,
                                        commentDivsAdded: roundResult.after.commentDivs - roundResult.before.commentDivs,
                                        heightChanged: roundResult.after.pageHeight - roundResult.before.pageHeight,
                                        clicksAttempted: clickedInRound
                                    };

                                    console.log(\`轮次 \${round} 结果:, roundResult.changes\`);
                                    results.rounds.push(roundResult);

                                    // 如果没有新内容且没有按钮可点击，提前结束
                                    if (roundResult.changes.avatarsAdded === 0 && 
                                        roundResult.changes.commentDivsAdded === 0 && 
                                        roundResult.changes.heightChanged === 0 && 
                                        clickedInRound === 0) {
                                        console.log('没有新内容且无按钮可点击，提前结束');
                                        break;
                                    }
                                }

                                // 计算总体统计
                                const totalAvatarsAdded = results.rounds.reduce((sum, r) => sum + r.changes.avatarsAdded, 0);
                                const totalCommentDivsAdded = results.rounds.reduce((sum, r) => sum + r.changes.commentDivsAdded, 0);
                                const totalClicks = results.rounds.reduce((sum, r) => sum + r.changes.clicksAttempted, 0);

                                results.summary = {
                                    totalRounds: results.rounds.length,
                                    totalAvatarsAdded: totalAvatarsAdded,
                                    totalCommentDivsAdded: totalCommentDivsAdded,
                                    totalClicks: totalClicks,
                                    finalAvatars: results.rounds[results.rounds.length - 1].after.avatars,
                                    finalCommentDivs: results.rounds[results.rounds.length - 1].after.commentDivs
                                };

                                console.log('等待效果测试完成:', results.summary);
                                return { success: true, data: results };
                                
                            } catch (error) {
                                console.error('等待效果测试出错:', error);
                                return { success: false, error: error.message };
                            }
                        })();
                    `;

                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: testScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const result = message.result.result.value;
                        
                        if (result.success) {
                            const data = result.data;
                            console.log('\n📊 等待效果测试结果:');
                            console.log('=' * 50);
                            
                            console.log('\n📈 总体统计:');
                            console.log(`   🔄 测试轮次: ${data.summary.totalRounds}`);
                            console.log(`   🖱️ 总点击数: ${data.summary.totalClicks}`);
                            console.log(`   👤 新增头像: ${data.summary.totalAvatarsAdded}`);
                            console.log(`   💬 新增评论div: ${data.summary.totalCommentDivsAdded}`);
                            console.log(`   📊 最终头像数: ${data.summary.finalAvatars}`);
                            console.log(`   📊 最终评论div: ${data.summary.finalCommentDivs}`);
                            
                            console.log('\n🔄 各轮次详情:');
                            data.rounds.forEach((round, index) => {
                                console.log(`   第${round.round}轮: 点击${round.changes.clicksAttempted}个, 头像+${round.changes.avatarsAdded}, 评论+${round.changes.commentDivsAdded}`);
                            });
                            
                            if (data.summary.totalAvatarsAdded > 0 || data.summary.totalCommentDivsAdded > 0) {
                                console.log('\n🎉 测试成功！发现新内容加载！');
                                console.log(`📈 采集进度: ${Math.round(data.summary.finalAvatars/1472*100)}% (${data.summary.finalAvatars}/1472)`);
                            } else {
                                console.log('\n⚠️ 没有发现新内容，可能已经加载完毕或需要其他策略。');
                            }
                            
                            ws.close();
                            resolve(data);
                        } else {
                            console.log('❌ 等待效果测试失败:', result.error);
                            ws.close();
                            reject(new Error(result.error));
                        }
                    }
                } catch (error) {
                    console.error('❌ 处理消息失败:', error.message);
                    ws.close();
                    reject(error);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket错误:', error.message);
                reject(error);
            });

            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                reject(new Error('等待效果测试超时'));
            }, 60000); // 1分钟超时
        });

    } catch (error) {
        console.error('❌ 等待效果测试失败:', error.message);
        throw error;
    }
}

if (require.main === module) {
    testWaitEffect().catch(console.error);
}

module.exports = testWaitEffect;
