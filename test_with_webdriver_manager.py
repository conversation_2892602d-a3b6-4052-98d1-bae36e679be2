#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 使用webdriver-manager测试调试端口连接
自动管理ChromeDriver版本
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By

def test_connection_with_webdriver_manager(port):
    """使用webdriver-manager测试连接"""
    print(f"🔍 使用webdriver-manager测试端口 {port}...")
    
    try:
        # 使用webdriver-manager自动下载匹配的ChromeDriver
        print("   📥 自动下载/更新ChromeDriver...")
        service = Service(ChromeDriverManager().install())
        
        # Chrome选项
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        print(f"   🔗 尝试连接到 localhost:{port}...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 获取页面信息
        current_url = driver.current_url
        title = driver.title
        
        print(f"   ✅ 连接成功!")
        print(f"   📄 当前页面: {current_url}")
        print(f"   📝 页面标题: {title}")
        
        # 检查是否是小红书页面
        if 'xiaohongshu.com' in current_url:
            print("   🎉 检测到小红书页面!")
            
            # 简单测试页面操作
            try:
                body = driver.find_element(By.TAG_NAME, "body")
                page_text = body.text
                
                # 统计评论相关信息
                comment_indicators = [
                    ('条评论', page_text.count('条评论')),
                    ('回复', page_text.count('回复')),
                    ('点赞', page_text.count('赞')),
                    ('时间', len([line for line in page_text.split('\n') if any(t in line for t in ['分钟前', '小时前', '天前', '月前'])]))
                ]
                
                print("   📊 页面内容分析:")
                for indicator, count in comment_indicators:
                    if count > 0:
                        print(f"      {indicator}: {count}")
                
                # 检查是否有评论区域
                if any(count > 0 for _, count in comment_indicators):
                    print("   ✅ 页面包含评论相关内容，适合爬取!")
                else:
                    print("   ⚠️ 页面可能不包含评论内容")
                    
            except Exception as e:
                print(f"   ⚠️ 页面内容分析失败: {e}")
        else:
            print("   💡 不是小红书页面")
            print("   💡 请在比特浏览器中导航到小红书评论页面")
        
        # 保持连接一段时间进行稳定性测试
        print("   ⏱️ 保持连接5秒进行稳定性测试...")
        time.sleep(5)
        
        # 再次检查连接
        try:
            current_url2 = driver.current_url
            print(f"   ✅ 连接稳定，当前页面: {current_url2}")
        except:
            print("   ⚠️ 连接不稳定")
        
        driver.quit()
        return True
        
    except Exception as e:
        error_msg = str(e)
        print(f"   ❌ 连接失败:")
        print(f"      错误: {error_msg}")
        
        # 分析错误类型
        if "version" in error_msg.lower():
            print("      💡 可能原因: Chrome版本与ChromeDriver不匹配")
            print("      💡 解决方案: 更新比特浏览器或使用其他方案")
        elif "cannot connect" in error_msg.lower():
            print("      💡 可能原因: 调试端口未开启或端口号错误")
            print("      💡 解决方案: 确保比特浏览器19号窗口正在运行")
        elif "session not created" in error_msg.lower():
            print("      💡 可能原因: 浏览器会话创建失败")
            print("      💡 解决方案: 重启比特浏览器或检查调试设置")
        else:
            print("      💡 建议: 检查比特浏览器是否正常运行")
        
        return False

def test_multiple_approaches(port):
    """尝试多种连接方式"""
    print(f"🎯 多种方式测试端口 {port}")
    print("=" * 50)
    
    approaches = [
        {
            'name': '标准连接',
            'options': {
                'debuggerAddress': f'localhost:{port}',
                'args': ['--no-sandbox', '--disable-dev-shm-usage']
            }
        },
        {
            'name': '兼容模式',
            'options': {
                'debuggerAddress': f'localhost:{port}',
                'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--remote-debugging-port=0']
            }
        },
        {
            'name': '简化模式',
            'options': {
                'debuggerAddress': f'localhost:{port}',
                'args': ['--no-sandbox']
            }
        }
    ]
    
    for i, approach in enumerate(approaches, 1):
        print(f"\n🔍 方式 {i}: {approach['name']}")
        
        try:
            service = Service(ChromeDriverManager().install())
            chrome_options = Options()
            
            # 设置调试地址
            chrome_options.add_experimental_option("debuggerAddress", approach['options']['debuggerAddress'])
            
            # 添加参数
            for arg in approach['options']['args']:
                chrome_options.add_argument(arg)
            
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            current_url = driver.current_url
            title = driver.title
            
            print(f"   ✅ 方式 {i} 成功!")
            print(f"   📄 页面: {current_url}")
            print(f"   📝 标题: {title}")
            
            driver.quit()
            return True
            
        except Exception as e:
            print(f"   ❌ 方式 {i} 失败: {str(e)[:100]}...")
            continue
    
    print("\n❌ 所有连接方式都失败了")
    return False

def main():
    print("🎯 webdriver-manager调试端口测试器")
    print("=" * 50)
    
    port = 60811  # 我们获取到的19号窗口端口
    
    print(f"🎯 测试端口: {port}")
    print("📝 确保比特浏览器19号窗口正在运行")
    
    # 方法1: 标准测试
    print("\n🔍 方法1: 标准webdriver-manager测试")
    if test_connection_with_webdriver_manager(port):
        print("\n🎉 标准方法成功!")
        print("🚀 现在可以运行完整的爬虫脚本")
        return
    
    # 方法2: 多种方式测试
    print("\n🔍 方法2: 多种连接方式测试")
    if test_multiple_approaches(port):
        print("\n🎉 找到可用的连接方式!")
        print("🚀 现在可以运行完整的爬虫脚本")
        return
    
    print("\n❌ 所有测试都失败了")
    print("💡 可能的解决方案:")
    print("   1. 确保比特浏览器19号窗口正在运行")
    print("   2. 确保窗口启用了调试模式")
    print("   3. 重启比特浏览器")
    print("   4. 检查防火墙设置")
    print("   5. 使用浏览器控制台方案作为备选")

if __name__ == "__main__":
    main()
