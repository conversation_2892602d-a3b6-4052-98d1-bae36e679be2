#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 API Token详细测试
测试不同的Token格式和认证方式
"""

import requests
import json

def test_token_variations(base_token):
    """测试Token的不同变体"""
    api_base = "http://127.0.0.1:56906"
    
    # 不同的Token变体
    token_variations = [
        base_token,                           # 原始Token
        base_token.strip(),                   # 去除空格
        base_token.upper(),                   # 大写
        base_token.lower(),                   # 小写
        f"Bearer {base_token}",               # Bearer前缀
        f"Token {base_token}",                # Token前缀
    ]
    
    # 不同的Header格式
    header_formats = [
        "X-API-KEY",
        "X-Api-Key", 
        "x-api-key",
        "Authorization",
        "Token",
        "API-KEY"
    ]
    
    print("🔍 测试不同的Token和Header组合...")
    
    for i, token in enumerate(token_variations, 1):
        for j, header_name in enumerate(header_formats, 1):
            print(f"\n   测试组合 {i}-{j}: {header_name}: {token[:20]}...")
            
            headers = {
                'Content-Type': 'application/json',
                header_name: token
            }
            
            try:
                # 测试端口接口
                response = requests.post(f"{api_base}/browser/ports", headers=headers, timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        print(f"      ✅ 成功! Header: {header_name}, Token: {token[:20]}...")
                        return header_name, token, data
                    else:
                        print(f"      ❌ API错误: {data.get('msg', 'Unknown error')}")
                elif response.status_code == 403:
                    print(f"      ❌ 认证失败: {response.text[:50]}")
                else:
                    print(f"      ❌ 状态码 {response.status_code}: {response.text[:50]}")
                    
            except Exception as e:
                print(f"      ❌ 异常: {str(e)[:50]}")
    
    return None, None, None

def check_api_status():
    """检查API服务状态"""
    api_base = "http://127.0.0.1:56906"
    
    print("🔍 检查API服务状态...")
    
    try:
        # 检查根路径
        response = requests.get(f"{api_base}/", timeout=5)
        print(f"   根路径状态: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text[:200]
            print(f"   内容预览: {content}")
            
            # 检查是否包含API相关信息
            if 'api' in content.lower() or 'browser' in content.lower():
                print("   ✅ 看起来是正确的API服务")
            else:
                print("   ⚠️ 可能不是预期的API服务")
        
        return True
    except Exception as e:
        print(f"   ❌ API服务不可用: {e}")
        return False

def main():
    print("🎯 比特浏览器API Token详细测试")
    print("=" * 50)
    
    # 1. 检查API服务
    if not check_api_status():
        print("❌ API服务不可用，请检查比特浏览器是否运行")
        return
    
    # 2. 获取Token
    print("\n💡 请提供API Token进行测试:")
    print("   1. 在比特浏览器中打开Local API页面")
    print("   2. 如果有'重新生成'按钮，点击生成新Token")
    print("   3. 完整复制Token值（确保没有多余空格）")
    
    token = input("\n请输入API Token: ").strip()
    
    if not token:
        print("❌ Token不能为空")
        return
    
    print(f"\n🔍 收到Token: {token}")
    print(f"   长度: {len(token)} 字符")
    print(f"   格式: {'数字+字母' if token.isalnum() else '包含特殊字符'}")
    
    # 3. 测试Token变体
    header_name, working_token, data = test_token_variations(token)
    
    if working_token:
        print(f"\n🎉 找到有效的认证方式!")
        print(f"   Header: {header_name}")
        print(f"   Token: {working_token}")
        print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        # 保存有效配置
        config = {
            'api_base': 'http://127.0.0.1:56906',
            'header_name': header_name,
            'token': working_token,
            'test_response': data
        }
        
        with open('working_token_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"💾 有效配置已保存到 working_token_config.json")
        
        # 如果获取到端口数据，查找19号窗口
        if data.get('data'):
            ports_data = data['data']
            print(f"\n🎯 查找19号窗口...")
            
            for browser_id, port in ports_data.items():
                if '19' in browser_id:
                    print(f"   🎉 可能的19号窗口: {browser_id} -> 端口 {port}")
        
    else:
        print("\n❌ 所有Token变体都测试失败")
        print("💡 可能的解决方案:")
        print("   1. 在比特浏览器中重新生成Token")
        print("   2. 检查Local API是否正确启用")
        print("   3. 重启比特浏览器")
        print("   4. 检查Token是否完整复制")

if __name__ == "__main__":
    main()
