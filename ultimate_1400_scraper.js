/**
 * 🎯 终极1400条评论爬虫
 * 专门针对获取全部1472条评论的终极优化版本
 */

const puppeteer = require('puppeteer-core');
const axios = require('axios');
const fs = require('fs');

class Ultimate1400Scraper {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.targetWindowSeq = 19;
        this.browser = null;
        this.page = null;
        this.comments = [];
        this.targetCommentCount = 1472;
        this.maxScrolls = 2000; // 大幅增加滚动次数
    }

    /**
     * 连接到比特浏览器
     */
    async connectToBrowser() {
        console.log('🔗 连接到比特浏览器...');
        
        try {
            const windowResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            const browsers = windowResponse.data.data.list;
            const window19 = browsers.find(browser => browser.seq === this.targetWindowSeq);
            
            if (!window19) {
                throw new Error('未找到19号窗口');
            }

            const portsResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            const ports = portsResponse.data.data;
            const debugPort = ports[window19.id];
            
            if (!debugPort) {
                throw new Error('19号窗口未获取到调试端口');
            }

            const versionResponse = await axios.get(`http://localhost:${debugPort}/json/version`);
            const browserWSEndpoint = versionResponse.data.webSocketDebuggerUrl;
            
            this.browser = await puppeteer.connect({
                browserWSEndpoint,
                defaultViewport: null
            });
            
            const browserPages = await this.browser.pages();
            this.page = browserPages.find(page => page.url().includes('xiaohongshu.com/explore/'));
            
            if (!this.page) {
                this.page = browserPages.find(page => page.url().includes('xiaohongshu.com'));
            }
            
            if (!this.page) {
                throw new Error('未找到小红书页面');
            }
            
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            console.log('✅ 连接成功!');
            console.log(`📄 页面: ${title}`);
            
            return true;
            
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    /**
     * 闪电滑动 - 最极限的速度
     */
    async lightningScroll(distance = 1500) {
        await this.page.mouse.wheel({ deltaY: distance });
        await new Promise(resolve => setTimeout(resolve, 30)); // 极限30ms
    }

    /**
     * 疯狂点击所有可能的展开按钮
     */
    async crazyClickAll() {
        try {
            // 更全面的展开按钮选择器
            const allExpandSelectors = [
                'button:contains("展开")',
                'span:contains("展开")',
                'div:contains("展开")',
                'a:contains("展开")',
                'button:contains("更多")',
                'span:contains("更多")',
                'div:contains("更多")',
                'button:contains("加载更多")',
                'span:contains("加载更多")',
                '[class*="expand"]',
                '[class*="more"]',
                '[class*="load"]',
                '[class*="show-more"]',
                '[class*="unfold"]',
                '[data-testid*="expand"]',
                '[data-testid*="more"]',
                '.expand-btn',
                '.more-btn',
                '.load-more-btn',
                '.show-more-btn'
            ];

            let totalClicks = 0;
            
            for (const selector of allExpandSelectors) {
                try {
                    const elements = await this.page.$$(selector);
                    
                    for (const element of elements) {
                        try {
                            const isVisible = await element.isIntersectingViewport();
                            if (isVisible) {
                                await element.click();
                                totalClicks++;
                                await new Promise(resolve => setTimeout(resolve, 10)); // 极快点击
                            }
                        } catch (e) {
                            // 忽略单个元素点击错误
                        }
                    }
                } catch (e) {
                    // 忽略选择器错误
                }
            }
            
            return totalClicks;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 终极滚动策略 - 目标1400+条评论
     */
    async ultimateScroll() {
        console.log('🎯 开始终极滚动，目标1400+条评论...');
        
        let scrollCount = 0;
        let lastCommentCount = 0;
        let noProgressCount = 0;
        let totalClickCount = 0;
        let bestCommentCount = 0;
        
        while (scrollCount < this.maxScrolls && noProgressCount < 50) {
            const currentCommentCount = await this.page.evaluate(() => {
                const commentElements = document.querySelectorAll(
                    '[class*="comment"], [class*="Comment"], [class*="reply"], [class*="Reply"], [class*="interaction"]'
                );
                return commentElements.length;
            });
            
            // 记录最佳成绩
            if (currentCommentCount > bestCommentCount) {
                bestCommentCount = currentCommentCount;
            }
            
            // 每50次显示进度
            if (scrollCount % 50 === 0) {
                const progress = Math.round((currentCommentCount / this.targetCommentCount) * 100);
                console.log(`🎯 终极滚动 ${scrollCount} 次，评论元素: ${currentCommentCount}，进度: ${progress}%`);
            }
            
            // 疯狂点击展开按钮
            if (scrollCount % 2 === 0) { // 每2次滚动就点击
                const clickCount = await this.crazyClickAll();
                totalClickCount += clickCount;
            }
            
            // 多种滚动策略
            if (scrollCount % 10 === 0) {
                // 每10次进行大幅滚动
                await this.lightningScroll(2500);
            } else if (scrollCount % 5 === 0) {
                // 每5次进行中等滚动
                await this.lightningScroll(1800);
            } else {
                // 常规快速滚动
                await this.lightningScroll(1200 + Math.random() * 800);
            }
            
            // 检查进度
            if (currentCommentCount > lastCommentCount) {
                noProgressCount = 0;
                if (scrollCount % 50 === 0) {
                    console.log(`   🚀 新增 ${currentCommentCount - lastCommentCount} 个评论元素`);
                }
            } else {
                noProgressCount++;
            }
            
            lastCommentCount = currentCommentCount;
            scrollCount++;
            
            // 动态加速策略
            if (currentCommentCount > this.targetCommentCount * 0.2) {
                console.log(`   🔥 达到20%，启动加速模式！`);
                await this.lightningScroll(2000);
                await this.crazyClickAll();
            }
            
            if (currentCommentCount > this.targetCommentCount * 0.5) {
                console.log(`   ⚡ 达到50%，疯狂冲刺！`);
                await this.lightningScroll(3000);
                await this.crazyClickAll();
                await this.lightningScroll(3000);
            }
            
            if (currentCommentCount > this.targetCommentCount * 0.8) {
                console.log(`   🎯 达到80%，终极冲刺！`);
                await this.lightningScroll(4000);
                await this.crazyClickAll();
                await this.lightningScroll(4000);
                await this.crazyClickAll();
            }
            
            // 如果接近目标，继续努力
            if (currentCommentCount > this.targetCommentCount * 0.9) {
                console.log(`   🏆 达到90%，最后冲刺！`);
                for (let i = 0; i < 5; i++) {
                    await this.lightningScroll(5000);
                    await this.crazyClickAll();
                }
            }
        }
        
        console.log(`\n🏁 终极滚动完成！`);
        console.log(`   总滚动次数: ${scrollCount}`);
        console.log(`   总点击次数: ${totalClickCount}`);
        console.log(`   最终评论元素: ${lastCommentCount}`);
        console.log(`   最佳记录: ${bestCommentCount}`);
        console.log(`   完成度: ${Math.round((lastCommentCount / this.targetCommentCount) * 100)}%`);
        
        // 快速回顶部
        await this.page.evaluate(() => window.scrollTo(0, 0));
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    /**
     * 终极评论提取器
     */
    async ultimateExtractComments() {
        console.log('🔍 开始终极评论提取...');
        
        try {
            const comments = await this.page.evaluate(() => {
                const extractedComments = [];
                
                // 终极选择器集合
                const ultimateSelectors = [
                    // 评论容器
                    '[class*="comment-item"]',
                    '[class*="comment-container"]',
                    '[class*="comment-wrapper"]',
                    '[class*="comment-content"]',
                    '[class*="comment-text"]',
                    '[class*="comment-body"]',
                    '[class*="user-comment"]',
                    '[class*="note-comment"]',
                    '[class*="feed-comment"]',
                    
                    // 通用评论
                    '[class*="comment"]',
                    '[class*="Comment"]',
                    
                    // 回复相关
                    '[class*="reply"]',
                    '[class*="Reply"]',
                    '[class*="reply-item"]',
                    '[class*="reply-content"]',
                    
                    // 交互相关
                    '[class*="interaction"]',
                    '[class*="user-interaction"]',
                    '[class*="social-interaction"]',
                    
                    // 数据属性
                    '[data-testid*="comment"]',
                    '[data-testid*="reply"]',
                    '[data-testid*="interaction"]',
                    
                    // 角色属性
                    '[role="comment"]',
                    '[role="article"]',
                    
                    // 通用容器
                    '.note-item',
                    '.feed-item',
                    '.comment-list',
                    '.reply-list',
                    '.interaction-list'
                ];
                
                // 执行每种选择器
                ultimateSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        
                        elements.forEach((element, index) => {
                            const text = element.textContent?.trim();
                            if (text && text.length > 3) {
                                // 超级智能解析
                                const lines = text.split('\n').filter(line => line.trim());
                                
                                let username = '';
                                let content = '';
                                let time = '';
                                let likes = '';
                                
                                for (let i = 0; i < lines.length; i++) {
                                    const line = lines[i].trim();
                                    
                                    if (line.length < 1) continue;
                                    
                                    // 时间识别 - 超全面
                                    if (/\d{2}-\d{2}|\d+[分小天月年]前|\d{4}-\d{2}-\d{2}|\d{1,2}:\d{2}/.test(line)) {
                                        time = line;
                                        continue;
                                    }
                                    
                                    // 点赞数识别
                                    if (/^\d+$/.test(line) && parseInt(line) > 0 && parseInt(line) < 100000) {
                                        likes = line;
                                        continue;
                                    }
                                    
                                    // 跳过操作按钮和无用信息
                                    if (/^(赞|回复|展开|收起|点赞|分享|举报|删除|编辑|关注|取消关注)$/.test(line) ||
                                        line.includes('条评论') ||
                                        line.includes('条回复') ||
                                        line.includes('发送') ||
                                        line.includes('取消')) {
                                        continue;
                                    }
                                    
                                    // 用户名识别 - 更精确
                                    if (!username && line.length < 150 && 
                                        !line.includes('http') &&
                                        !line.includes('www') &&
                                        !line.includes('.com') &&
                                        !/[🥝😃🦁🥦🍟🐭🥪😁🥭🐡🥒😛🦊🥬🥞🍍🧁🍙😷🍉🐟🍊😏🍑💰🧱🍐🍧🍭💄]/.test(line)) {
                                        username = line;
                                    } else if (line.length > 2 && line.length < 5000) {
                                        // 评论内容 - 更宽松
                                        if (content) {
                                            content += ' ' + line;
                                        } else {
                                            content = line;
                                        }
                                    }
                                }
                                
                                // 超级清理内容
                                if (content) {
                                    content = content
                                        .replace(/展开\s*\d+\s*条回复/g, '')
                                        .replace(/\d+赞回复/g, '')
                                        .replace(/赞回复$/g, '')
                                        .replace(/回复$/g, '')
                                        .replace(/展开$/g, '')
                                        .replace(/收起$/g, '')
                                        .replace(/[🥝😃🦁🥦🍟🐭🥪😁🥭🐡🥒😛🦊🥬🥞🍍🧁🍙😷🍉🐟🍊😏🍑💰🧱🍐🍧🍭💄]/g, '')
                                        .replace(/\s+/g, ' ')
                                        .trim();
                                    
                                    if (content.length > 2 && content.length < 5000 &&
                                        !content.includes('undefined') &&
                                        !content.includes('null') &&
                                        !content.includes('NaN')) {
                                        extractedComments.push({
                                            username: username || '未知用户',
                                            content: content,
                                            time: time || '',
                                            likes: likes || '',
                                            method: `ultimate_${selector}`,
                                            element_index: index,
                                            timestamp: new Date().toISOString()
                                        });
                                    }
                                }
                            }
                        });
                    } catch (e) {
                        console.log(`选择器 ${selector} 处理失败:`, e.message);
                    }
                });
                
                // 终极文本分析
                const bodyText = document.body.textContent || '';
                const allLines = bodyText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
                
                // 寻找所有可能的评论模式
                for (let i = 0; i < allLines.length; i++) {
                    const line = allLines[i];
                    
                    // 超全面的时间模式
                    if (/\d{2}-\d{2}|\d+[分小天月年]前|\d{4}-\d{2}-\d{2}|\d{1,2}:\d{2}/.test(line) && line.length < 200) {
                        // 向前查找评论内容
                        for (let j = 1; j <= 8; j++) {
                            if (i - j >= 0) {
                                const contentLine = allLines[i - j];
                                if (contentLine.length > 3 && contentLine.length < 2000 &&
                                    !/(赞|回复|展开|收起|点赞|分享|举报|条评论|删除|编辑|关注)$/.test(contentLine) &&
                                    !contentLine.includes('http') &&
                                    !contentLine.includes('www') &&
                                    !contentLine.includes('.com') &&
                                    !/^[🥝😃🦁🥦🍟🐭🥪😁🥭🐡🥒😛🦊🥬🥞🍍🧁🍙😷🍉🐟🍊😏🍑💰🧱🍐🍧🍭💄]+$/.test(contentLine)) {
                                    
                                    // 查找用户名
                                    let username = '未知用户';
                                    for (let k = 1; k <= 3; k++) {
                                        if (i - j - k >= 0) {
                                            const userLine = allLines[i - j - k];
                                            if (userLine.length > 1 && userLine.length < 100 && 
                                                !userLine.includes('http') &&
                                                !userLine.includes('条评论') &&
                                                !userLine.includes('回复')) {
                                                username = userLine;
                                                break;
                                            }
                                        }
                                    }
                                    
                                    // 检查是否已存在
                                    const exists = extractedComments.some(comment => 
                                        comment.content.includes(contentLine.substring(0, 50))
                                    );
                                    
                                    if (!exists) {
                                        extractedComments.push({
                                            username: username,
                                            content: contentLine,
                                            time: line,
                                            likes: '',
                                            method: 'ultimate_text_analysis',
                                            timestamp: new Date().toISOString()
                                        });
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
                
                // 终极去重
                const uniqueComments = [];
                const seenContents = new Set();
                
                for (const comment of extractedComments) {
                    const contentKey = comment.content.substring(0, 150);
                    if (!seenContents.has(contentKey) && 
                        comment.content.length > 2 && 
                        comment.content.length < 5000 &&
                        !comment.content.includes('undefined') &&
                        !comment.content.includes('null') &&
                        !comment.content.includes('NaN') &&
                        comment.content !== comment.username) {
                        seenContents.add(contentKey);
                        uniqueComments.push(comment);
                    }
                }
                
                return uniqueComments;
            });
            
            this.comments = comments;
            console.log(`✅ 终极提取完成，共获得 ${this.comments.length} 条评论`);
            
            // 显示提取统计
            const methodStats = {};
            this.comments.forEach(comment => {
                const method = comment.method.split('_')[1] || comment.method;
                methodStats[method] = (methodStats[method] || 0) + 1;
            });
            
            console.log('📊 提取方式统计:');
            Object.entries(methodStats).forEach(([method, count]) => {
                console.log(`   ${method}: ${count} 条`);
            });
            
            return this.comments.length > 0;
            
        } catch (error) {
            console.error('❌ 终极提取失败:', error.message);
            return false;
        }
    }

    /**
     * 保存为终极TXT格式
     */
    async saveUltimateTXT() {
        if (this.comments.length === 0) {
            console.log('⚠️ 没有评论数据可保存');
            return;
        }
        
        try {
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            let txtContent = '';
            txtContent += '='.repeat(80) + '\n';
            txtContent += '小红书评论完整数据 (终极1400+版)\n';
            txtContent += '='.repeat(80) + '\n';
            txtContent += `页面标题: ${title}\n`;
            txtContent += `页面链接: ${currentUrl}\n`;
            txtContent += `爬取时间: ${new Date().toLocaleString('zh-CN')}\n`;
            txtContent += `评论总数: ${this.comments.length} 条\n`;
            txtContent += `目标数量: ${this.targetCommentCount} 条\n`;
            txtContent += `完成度: ${Math.round((this.comments.length / this.targetCommentCount) * 100)}%\n`;
            txtContent += `爬取方式: 终极滚动 + 超级提取\n`;
            txtContent += '='.repeat(80) + '\n\n';
            
            // 按时间排序
            const sortedComments = this.comments.sort((a, b) => {
                if (a.time && b.time) {
                    return a.time.localeCompare(b.time);
                }
                return 0;
            });
            
            // 添加评论内容
            sortedComments.forEach((comment, index) => {
                txtContent += `【评论 ${index + 1}】\n`;
                txtContent += `用户名: ${comment.username}\n`;
                if (comment.time) {
                    txtContent += `发布时间: ${comment.time}\n`;
                }
                if (comment.likes) {
                    txtContent += `点赞数: ${comment.likes}\n`;
                }
                txtContent += `评论内容: ${comment.content}\n`;
                txtContent += `提取方式: ${comment.method}\n`;
                txtContent += '-'.repeat(60) + '\n\n';
            });
            
            // 添加统计
            txtContent += '='.repeat(80) + '\n';
            txtContent += '详细统计信息\n';
            txtContent += '='.repeat(80) + '\n';
            
            const methodStats = {};
            this.comments.forEach(comment => {
                methodStats[comment.method] = (methodStats[comment.method] || 0) + 1;
            });
            
            txtContent += '按提取方式统计:\n';
            Object.entries(methodStats).sort((a, b) => b[1] - a[1]).forEach(([method, count]) => {
                const percentage = Math.round((count / this.comments.length) * 100);
                txtContent += `  ${method}: ${count} 条 (${percentage}%)\n`;
            });
            
            // 按时间统计
            const timeStats = {};
            this.comments.forEach(comment => {
                if (comment.time) {
                    const timeKey = comment.time.includes('-') ? comment.time.substring(0, 5) : 
                                   comment.time.includes('前') ? comment.time : '其他';
                    timeStats[timeKey] = (timeStats[timeKey] || 0) + 1;
                }
            });
            
            if (Object.keys(timeStats).length > 0) {
                txtContent += '\n按时间统计:\n';
                Object.entries(timeStats).sort().forEach(([time, count]) => {
                    txtContent += `  ${time}: ${count} 条\n`;
                });
            }
            
            // 内容长度统计
            const lengthRanges = {
                '短评论(1-30字)': 0,
                '中等评论(31-150字)': 0,
                '长评论(151-500字)': 0,
                '超长评论(500字以上)': 0
            };
            
            this.comments.forEach(comment => {
                const len = comment.content.length;
                if (len <= 30) lengthRanges['短评论(1-30字)']++;
                else if (len <= 150) lengthRanges['中等评论(31-150字)']++;
                else if (len <= 500) lengthRanges['长评论(151-500字)']++;
                else lengthRanges['超长评论(500字以上)']++;
            });
            
            txtContent += '\n按内容长度统计:\n';
            Object.entries(lengthRanges).forEach(([range, count]) => {
                if (count > 0) {
                    const percentage = Math.round((count / this.comments.length) * 100);
                    txtContent += `  ${range}: ${count} 条 (${percentage}%)\n`;
                }
            });
            
            txtContent += '\n' + '='.repeat(80) + '\n';
            txtContent += '终极1400+技术说明:\n';
            txtContent += '- 闪电滑动 (30ms极限延迟)\n';
            txtContent += '- 疯狂点击所有展开按钮\n';
            txtContent += '- 动态加速策略\n';
            txtContent += '- 终极选择器覆盖\n';
            txtContent += '- 超级智能解析和去重\n';
            txtContent += '- 最大2000次滚动\n';
            txtContent += '='.repeat(80) + '\n';
            
            // 保存文件
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const txtFilename = `xiaohongshu_ultimate_1400_comments_${timestamp}.txt`;
            
            fs.writeFileSync(txtFilename, txtContent, 'utf8');
            
            console.log(`💾 终极1400+版TXT文件已保存: ${txtFilename}`);
            console.log(`📊 总计 ${this.comments.length} 条评论 (目标: ${this.targetCommentCount} 条)`);
            
            return txtFilename;
            
        } catch (error) {
            console.error('❌ 保存文件失败:', error.message);
        }
    }

    /**
     * 运行终极1400+爬虫
     */
    async run() {
        console.log('🎯 终极1400+评论爬虫');
        console.log('='.repeat(80));
        
        try {
            // 1. 连接浏览器
            await this.connectToBrowser();
            
            // 2. 闪电等待
            console.log('⚡ 闪电等待页面稳定...');
            await new Promise(resolve => setTimeout(resolve, 800));
            
            // 3. 终极滚动
            await this.ultimateScroll();
            
            // 4. 终极提取
            const success = await this.ultimateExtractComments();
            
            if (!success) {
                console.log('❌ 未能提取到评论数据');
                return false;
            }
            
            // 5. 保存为终极TXT
            const txtFile = await this.saveUltimateTXT();
            
            console.log('\n🎉 终极1400+爬取完成!');
            console.log(`📁 请查看生成的TXT文件: ${txtFile}`);
            console.log(`📊 成功获取 ${this.comments.length} / ${this.targetCommentCount} 条评论`);
            
            const completionRate = Math.round((this.comments.length / this.targetCommentCount) * 100);
            console.log(`🎯 完成度: ${completionRate}%`);
            
            if (completionRate >= 95) {
                console.log('🏆 完美！几乎获取了所有1400+评论！');
            } else if (completionRate >= 80) {
                console.log('🥇 优秀！获取了大部分1400+评论！');
            } else if (completionRate >= 60) {
                console.log('🥈 良好！获取了超过一半的评论！');
            } else if (completionRate >= 40) {
                console.log('🥉 不错！获取了相当数量的评论！');
            } else {
                console.log('📈 有进步！继续优化可以获取更多！');
            }
            
            return true;
            
        } catch (error) {
            console.error('❌ 终极1400+爬虫运行失败:', error.message);
            return false;
        } finally {
            if (this.browser) {
                console.log('🔒 断开浏览器连接...');
                await this.browser.disconnect();
            }
        }
    }
}

// 主函数
async function main() {
    console.log('🎯 终极1400+评论爬虫启动器');
    console.log('='.repeat(80));
    
    console.log('🚀 终极优化特性:');
    console.log('   ⚡ 闪电滑动 (30ms极限延迟)');
    console.log('   📏 超大滚动距离 (最高5000px)');
    console.log('   🔥 疯狂点击策略');
    console.log('   🎯 动态加速算法');
    console.log('   🔍 终极选择器覆盖');
    console.log('   🧹 超级智能解析');
    console.log('   📊 最大2000次滚动');
    
    const scraper = new Ultimate1400Scraper();
    await scraper.run();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = Ultimate1400Scraper;
