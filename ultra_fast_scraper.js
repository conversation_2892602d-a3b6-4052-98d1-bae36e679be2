/**
 * ⚡ 超快速滑动爬虫
 * 极限优化版本，最大化滑动速度和频率
 */

const puppeteer = require('puppeteer-core');
const axios = require('axios');
const fs = require('fs');

class UltraFastScraper {
    constructor() {
        this.bitBrowserApiUrl = 'http://127.0.0.1:56906';
        this.targetWindowSeq = 19;
        this.browser = null;
        this.page = null;
        this.comments = [];
        this.targetCommentCount = 1472;
    }

    /**
     * 连接到比特浏览器
     */
    async connectToBrowser() {
        console.log('🔗 连接到比特浏览器...');
        
        try {
            const windowResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/list`, {
                page: 0,
                pageSize: 100
            });

            const browsers = windowResponse.data.data.list;
            const window19 = browsers.find(browser => browser.seq === this.targetWindowSeq);
            
            if (!window19) {
                throw new Error('未找到19号窗口');
            }

            const portsResponse = await axios.post(`${this.bitBrowserApiUrl}/browser/ports`);
            const ports = portsResponse.data.data;
            const debugPort = ports[window19.id];
            
            if (!debugPort) {
                throw new Error('19号窗口未获取到调试端口');
            }

            const versionResponse = await axios.get(`http://localhost:${debugPort}/json/version`);
            const browserWSEndpoint = versionResponse.data.webSocketDebuggerUrl;
            
            this.browser = await puppeteer.connect({
                browserWSEndpoint,
                defaultViewport: null
            });
            
            const browserPages = await this.browser.pages();
            this.page = browserPages.find(page => page.url().includes('xiaohongshu.com/explore/'));
            
            if (!this.page) {
                this.page = browserPages.find(page => page.url().includes('xiaohongshu.com'));
            }
            
            if (!this.page) {
                throw new Error('未找到小红书页面');
            }
            
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            console.log('✅ 连接成功!');
            console.log(`📄 页面: ${title}`);
            
            return true;
            
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    /**
     * 极速滑动 - 最小延迟
     */
    async ultraFastScroll(distance = 800) {
        await this.page.mouse.wheel({ deltaY: distance });
        await new Promise(resolve => setTimeout(resolve, 50)); // 极小延迟50ms
    }

    /**
     * 极速点击展开
     */
    async ultraFastClick() {
        try {
            const expandSelectors = [
                'button:contains("展开")',
                'span:contains("展开")',
                '[class*="expand"]',
                '[class*="more"]'
            ];

            let clickCount = 0;
            
            for (const selector of expandSelectors) {
                try {
                    const elements = await this.page.$$(selector);
                    
                    for (const element of elements) {
                        const isVisible = await element.isIntersectingViewport();
                        if (isVisible) {
                            await element.click();
                            clickCount++;
                            await new Promise(resolve => setTimeout(resolve, 20)); // 极快点击
                        }
                    }
                } catch (e) {
                    continue;
                }
            }
            
            return clickCount;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 极限滚动策略
     */
    async extremeScroll() {
        console.log('⚡ 开始极限滚动，全速加载评论...');
        
        let scrollCount = 0;
        let lastCommentCount = 0;
        let noProgressCount = 0;
        let totalClickCount = 0;
        
        // 极大增加滚动次数
        while (scrollCount < 1000 && noProgressCount < 30) {
            const currentCommentCount = await this.page.evaluate(() => {
                const commentElements = document.querySelectorAll(
                    '[class*="comment"], [class*="Comment"], [class*="reply"], [class*="Reply"]'
                );
                return commentElements.length;
            });
            
            // 每100次显示进度
            if (scrollCount % 100 === 0) {
                console.log(`⚡ 极速滚动 ${scrollCount} 次，评论元素: ${currentCommentCount}，目标: ${this.targetCommentCount}`);
            }
            
            // 更频繁的点击
            if (scrollCount % 3 === 0) {
                const clickCount = await this.ultraFastClick();
                totalClickCount += clickCount;
            }
            
            // 极速滚动 - 更大距离
            const scrollDistance = 1000 + Math.random() * 500; // 1000-1500px
            await this.ultraFastScroll(scrollDistance);
            
            // 检查进度
            if (currentCommentCount > lastCommentCount) {
                noProgressCount = 0;
                if (scrollCount % 100 === 0) {
                    console.log(`   ⚡ 新增 ${currentCommentCount - lastCommentCount} 个评论元素`);
                }
            } else {
                noProgressCount++;
            }
            
            lastCommentCount = currentCommentCount;
            scrollCount++;
            
            // 接近目标时疯狂加速
            if (currentCommentCount > this.targetCommentCount * 0.3) {
                console.log(`   🚀 达到30%，疯狂加速模式！`);
                await this.ultraFastScroll(1500);
                await this.ultraFastClick();
                await this.ultraFastScroll(1500);
            }
            
            if (currentCommentCount > this.targetCommentCount * 0.6) {
                console.log(`   🔥 达到60%，终极冲刺！`);
                await this.ultraFastScroll(2000);
                await this.ultraFastClick();
                await this.ultraFastScroll(2000);
                await this.ultraFastClick();
            }
        }
        
        console.log(`\n🏁 极限滚动完成！`);
        console.log(`   总滚动次数: ${scrollCount}`);
        console.log(`   总点击次数: ${totalClickCount}`);
        console.log(`   最终评论元素: ${lastCommentCount}`);
        console.log(`   完成度: ${Math.round((lastCommentCount / this.targetCommentCount) * 100)}%`);
        
        // 快速回顶部
        await this.page.evaluate(() => window.scrollTo(0, 0));
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    /**
     * 超级评论提取器
     */
    async superExtractComments() {
        console.log('🔍 开始超级评论提取...');
        
        try {
            const comments = await this.page.evaluate(() => {
                const extractedComments = [];
                
                // 超全面选择器
                const allSelectors = [
                    '[class*="comment-item"]',
                    '[class*="comment-container"]',
                    '[class*="comment-content"]',
                    '[class*="comment-text"]',
                    '[class*="user-comment"]',
                    '[class*="note-comment"]',
                    '[class*="feed-comment"]',
                    '[class*="comment"]',
                    '[class*="Comment"]',
                    '[class*="reply"]',
                    '[class*="Reply"]',
                    '[class*="interaction"]',
                    '[class*="user-interaction"]',
                    '[data-testid*="comment"]',
                    '[role="comment"]',
                    '.note-item',
                    '.feed-item',
                    '.comment-list',
                    '.reply-list'
                ];
                
                // 执行每种选择器
                allSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        
                        elements.forEach((element, index) => {
                            const text = element.textContent?.trim();
                            if (text && text.length > 5) {
                                // 更智能的解析
                                const lines = text.split('\n').filter(line => line.trim());
                                
                                let username = '';
                                let content = '';
                                let time = '';
                                let likes = '';
                                
                                for (let i = 0; i < lines.length; i++) {
                                    const line = lines[i].trim();
                                    
                                    if (line.length < 2) continue;
                                    
                                    // 时间识别 - 更全面
                                    if (/\d{2}-\d{2}|\d+[分小天月年]前|\d{4}-\d{2}-\d{2}/.test(line)) {
                                        time = line;
                                        continue;
                                    }
                                    
                                    // 点赞数识别
                                    if (/^\d+$/.test(line) && parseInt(line) > 0 && parseInt(line) < 50000) {
                                        likes = line;
                                        continue;
                                    }
                                    
                                    // 跳过操作按钮
                                    if (/^(赞|回复|展开|收起|点赞|分享|举报|删除|编辑)$/.test(line)) {
                                        continue;
                                    }
                                    
                                    // 用户名识别 - 更精确
                                    if (!username && line.length < 100 && 
                                        !line.includes('条评论') && 
                                        !line.includes('回复') &&
                                        !line.includes('展开') &&
                                        !line.includes('emoji') &&
                                        !line.includes('http') &&
                                        !/[🥝😃🦁🥦🍟🐭🥪😁🥭🐡🥒😛🦊🥬🥞🍍🧁🍙😷🍉🐟🍊😏🍑]/.test(line)) {
                                        username = line;
                                    } else if (line.length > 3 && line.length < 3000) {
                                        // 评论内容 - 更宽松
                                        if (content) {
                                            content += ' ' + line;
                                        } else {
                                            content = line;
                                        }
                                    }
                                }
                                
                                // 清理内容 - 更彻底
                                if (content) {
                                    content = content
                                        .replace(/展开\s*\d+\s*条回复/g, '')
                                        .replace(/\d+赞回复/g, '')
                                        .replace(/赞回复$/g, '')
                                        .replace(/回复$/g, '')
                                        .replace(/[🥝😃🦁🥦🍟🐭🥪😁🥭🐡🥒😛🦊🥬🥞🍍🧁🍙😷🍉🐟🍊😏🍑]/g, '')
                                        .replace(/\s+/g, ' ')
                                        .trim();
                                    
                                    if (content.length > 3 && content.length < 3000) {
                                        extractedComments.push({
                                            username: username || '未知用户',
                                            content: content,
                                            time: time || '',
                                            likes: likes || '',
                                            method: `ultra_${selector}`,
                                            element_index: index,
                                            timestamp: new Date().toISOString()
                                        });
                                    }
                                }
                            }
                        });
                    } catch (e) {
                        console.log(`选择器 ${selector} 处理失败:`, e.message);
                    }
                });
                
                // 超级文本分析
                const bodyText = document.body.textContent || '';
                const allLines = bodyText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
                
                // 寻找评论模式
                for (let i = 0; i < allLines.length; i++) {
                    const line = allLines[i];
                    
                    // 更全面的时间模式
                    if (/\d{2}-\d{2}|\d+[分小天月年]前|\d{4}-\d{2}-\d{2}/.test(line) && line.length < 100) {
                        // 向前查找评论内容
                        for (let j = 1; j <= 5; j++) {
                            if (i - j >= 0) {
                                const contentLine = allLines[i - j];
                                if (contentLine.length > 5 && contentLine.length < 1000 &&
                                    !/(赞|回复|展开|收起|点赞|分享|举报|条评论|删除|编辑)$/.test(contentLine) &&
                                    !contentLine.includes('http') &&
                                    !/^[🥝😃🦁🥦🍟🐭🥪😁🥭🐡🥒😛🦊🥬🥞🍍🧁🍙😷🍉🐟🍊😏🍑]+$/.test(contentLine)) {
                                    
                                    // 查找用户名
                                    let username = '未知用户';
                                    if (i - j - 1 >= 0) {
                                        const userLine = allLines[i - j - 1];
                                        if (userLine.length > 1 && userLine.length < 50 && 
                                            !userLine.includes('http')) {
                                            username = userLine;
                                        }
                                    }
                                    
                                    // 检查是否已存在
                                    const exists = extractedComments.some(comment => 
                                        comment.content.includes(contentLine.substring(0, 30))
                                    );
                                    
                                    if (!exists) {
                                        extractedComments.push({
                                            username: username,
                                            content: contentLine,
                                            time: line,
                                            likes: '',
                                            method: 'ultra_text_analysis',
                                            timestamp: new Date().toISOString()
                                        });
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
                
                // 超级去重
                const uniqueComments = [];
                const seenContents = new Set();
                
                for (const comment of extractedComments) {
                    const contentKey = comment.content.substring(0, 100);
                    if (!seenContents.has(contentKey) && 
                        comment.content.length > 3 && 
                        comment.content.length < 3000 &&
                        !comment.content.includes('undefined') &&
                        !comment.content.includes('null')) {
                        seenContents.add(contentKey);
                        uniqueComments.push(comment);
                    }
                }
                
                return uniqueComments;
            });
            
            this.comments = comments;
            console.log(`✅ 超级提取完成，共获得 ${this.comments.length} 条评论`);
            
            // 显示提取统计
            const methodStats = {};
            this.comments.forEach(comment => {
                const method = comment.method.split('_')[1] || comment.method;
                methodStats[method] = (methodStats[method] || 0) + 1;
            });
            
            console.log('📊 提取方式统计:');
            Object.entries(methodStats).forEach(([method, count]) => {
                console.log(`   ${method}: ${count} 条`);
            });
            
            return this.comments.length > 0;
            
        } catch (error) {
            console.error('❌ 超级提取失败:', error.message);
            return false;
        }
    }

    /**
     * 保存为超级TXT格式
     */
    async saveUltraTXT() {
        if (this.comments.length === 0) {
            console.log('⚠️ 没有评论数据可保存');
            return;
        }
        
        try {
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            let txtContent = '';
            txtContent += '='.repeat(80) + '\n';
            txtContent += '小红书评论完整数据 (极限滑动版)\n';
            txtContent += '='.repeat(80) + '\n';
            txtContent += `页面标题: ${title}\n`;
            txtContent += `页面链接: ${currentUrl}\n`;
            txtContent += `爬取时间: ${new Date().toLocaleString('zh-CN')}\n`;
            txtContent += `评论总数: ${this.comments.length} 条\n`;
            txtContent += `目标数量: ${this.targetCommentCount} 条\n`;
            txtContent += `完成度: ${Math.round((this.comments.length / this.targetCommentCount) * 100)}%\n`;
            txtContent += `爬取方式: 极限滑动 + 超级提取\n`;
            txtContent += '='.repeat(80) + '\n\n';
            
            // 按时间排序
            const sortedComments = this.comments.sort((a, b) => {
                if (a.time && b.time) {
                    return a.time.localeCompare(b.time);
                }
                return 0;
            });
            
            // 添加评论内容
            sortedComments.forEach((comment, index) => {
                txtContent += `【评论 ${index + 1}】\n`;
                txtContent += `用户名: ${comment.username}\n`;
                if (comment.time) {
                    txtContent += `发布时间: ${comment.time}\n`;
                }
                if (comment.likes) {
                    txtContent += `点赞数: ${comment.likes}\n`;
                }
                txtContent += `评论内容: ${comment.content}\n`;
                txtContent += `提取方式: ${comment.method}\n`;
                txtContent += '-'.repeat(60) + '\n\n';
            });
            
            // 添加统计
            txtContent += '='.repeat(80) + '\n';
            txtContent += '详细统计信息\n';
            txtContent += '='.repeat(80) + '\n';
            
            const methodStats = {};
            this.comments.forEach(comment => {
                methodStats[comment.method] = (methodStats[comment.method] || 0) + 1;
            });
            
            txtContent += '按提取方式统计:\n';
            Object.entries(methodStats).sort((a, b) => b[1] - a[1]).forEach(([method, count]) => {
                const percentage = Math.round((count / this.comments.length) * 100);
                txtContent += `  ${method}: ${count} 条 (${percentage}%)\n`;
            });
            
            txtContent += '\n' + '='.repeat(80) + '\n';
            txtContent += '极限滑动技术说明:\n';
            txtContent += '- 极小延迟时间 (50ms)\n';
            txtContent += '- 超大滚动距离 (1000-2000px)\n';
            txtContent += '- 疯狂加速策略\n';
            txtContent += '- 超全面选择器覆盖\n';
            txtContent += '- 智能内容清理和去重\n';
            txtContent += '='.repeat(80) + '\n';
            
            // 保存文件
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const txtFilename = `xiaohongshu_ultra_fast_comments_${timestamp}.txt`;
            
            fs.writeFileSync(txtFilename, txtContent, 'utf8');
            
            console.log(`💾 极限滑动版TXT文件已保存: ${txtFilename}`);
            console.log(`📊 总计 ${this.comments.length} 条评论 (目标: ${this.targetCommentCount} 条)`);
            
            return txtFilename;
            
        } catch (error) {
            console.error('❌ 保存文件失败:', error.message);
        }
    }

    /**
     * 运行极限滑动爬虫
     */
    async run() {
        console.log('⚡ 极限滑动爬虫 - 目标1472条评论');
        console.log('='.repeat(80));
        
        try {
            // 1. 连接浏览器
            await this.connectToBrowser();
            
            // 2. 极速等待
            console.log('⚡ 极速等待页面稳定...');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 3. 极限滚动
            await this.extremeScroll();
            
            // 4. 超级提取
            const success = await this.superExtractComments();
            
            if (!success) {
                console.log('❌ 未能提取到评论数据');
                return false;
            }
            
            // 5. 保存为超级TXT
            const txtFile = await this.saveUltraTXT();
            
            console.log('\n🎉 极限滑动爬取完成!');
            console.log(`📁 请查看生成的TXT文件: ${txtFile}`);
            console.log(`📊 成功获取 ${this.comments.length} / ${this.targetCommentCount} 条评论`);
            
            const completionRate = Math.round((this.comments.length / this.targetCommentCount) * 100);
            console.log(`🎯 完成度: ${completionRate}%`);
            
            if (completionRate >= 90) {
                console.log('🏆 完美！几乎获取了所有评论数据！');
            } else if (completionRate >= 70) {
                console.log('🥇 优秀！获取了大部分评论数据！');
            } else if (completionRate >= 50) {
                console.log('🥈 良好！获取了一半以上的评论数据！');
            } else if (completionRate >= 30) {
                console.log('🥉 不错！获取了相当数量的评论数据！');
            } else {
                console.log('📈 有进步！继续优化可以获取更多数据！');
            }
            
            return true;
            
        } catch (error) {
            console.error('❌ 极限滑动爬虫运行失败:', error.message);
            return false;
        } finally {
            if (this.browser) {
                console.log('🔒 断开浏览器连接...');
                await this.browser.disconnect();
            }
        }
    }
}

// 主函数
async function main() {
    console.log('⚡ 极限滑动爬虫启动器');
    console.log('='.repeat(80));
    
    console.log('🚀 极限优化:');
    console.log('   ⚡ 极小延迟 (50ms)');
    console.log('   📏 超大滚动距离 (1000-2000px)');
    console.log('   🔥 疯狂加速策略');
    console.log('   🎯 超全面选择器');
    console.log('   🧹 智能清理去重');
    
    const scraper = new UltraFastScraper();
    await scraper.run();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = UltraFastScraper;
