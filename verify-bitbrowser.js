#!/usr/bin/env node

/**
 * 🔧 比特浏览器配置验证脚本
 * 用于快速验证比特浏览器Local API配置和连接状态
 */

const axios = require('axios');

// 配置信息
const CONFIG = {
    localServer: 'http://localhost:3000',
    bitbrowserAPI: 'http://127.0.0.1:54345',
    apiToken: 'ca28ee5ca6de4d209182a83aa16a2044',
    targetBrowserId: '0d094596cb404282be3f814b98139c74'
};

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
    log(`\n${step} ${message}`, 'cyan');
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

// 验证步骤
async function verifyConfiguration() {
    log('\n🔧 比特浏览器配置验证工具', 'bright');
    log('=' * 50, 'cyan');
    
    // 步骤1: 检查本地服务器
    logStep('1️⃣', '检查本地服务器状态...');
    try {
        const response = await axios.post(`${CONFIG.localServer}/api/xiaohongshu/health/simple`, {}, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 5000
        });
        
        if (response.data.success) {
            logSuccess(`本地服务器运行正常 (运行时间: ${response.data.uptime}秒)`);
        } else {
            logError('本地服务器状态异常');
            return false;
        }
    } catch (error) {
        logError(`无法连接到本地服务器: ${error.message}`);
        logInfo('请确保服务器已启动: node server.js');
        return false;
    }

    // 步骤2: 检查比特浏览器API
    logStep('2️⃣', '检查比特浏览器API连接...');
    try {
        const response = await axios.post(`${CONFIG.bitbrowserAPI}/browser/list`, {
            page: 1,
            pageSize: 10
        }, {
            headers: { 
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${CONFIG.apiToken}`
            },
            timeout: 10000
        });
        
        if (response.data.success) {
            const browserCount = response.data.data?.list?.length || 0;
            logSuccess(`比特浏览器API连接正常 (找到 ${browserCount} 个浏览器实例)`);
        } else {
            logError(`比特浏览器API返回错误: ${response.data.msg || '未知错误'}`);
            return false;
        }
    } catch (error) {
        logError(`无法连接到比特浏览器API: ${error.message}`);
        logInfo('请确保比特浏览器已启动并且Local API功能已开启');
        return false;
    }

    // 步骤3: 通过本地服务器测试连接
    logStep('3️⃣', '通过本地服务器测试比特浏览器连接...');
    try {
        const response = await axios.post(`${CONFIG.localServer}/api/xiaohongshu/bitbrowser/list`, {
            page: 1,
            pageSize: 20
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 15000
        });
        
        if (response.data.success) {
            const browsers = response.data.data.browsers || [];
            const targetBrowser = response.data.data.targetBrowser;
            
            logSuccess(`通过本地服务器获取到 ${browsers.length} 个浏览器实例`);
            
            if (targetBrowser) {
                logSuccess(`找到目标浏览器: ${targetBrowser.name || targetBrowser.id}`);
                logInfo(`状态: ${targetBrowser.status || '未知'}`);
            } else {
                logWarning(`未找到目标浏览器 (ID: ${CONFIG.targetBrowserId})`);
                logInfo('可用的浏览器实例:');
                browsers.forEach((browser, index) => {
                    log(`   ${index + 1}. ${browser.name || browser.id} (${browser.status || '未知'})`, 'blue');
                });
            }
        } else {
            logError(`获取浏览器列表失败: ${response.data.message}`);
            return false;
        }
    } catch (error) {
        logError(`测试连接失败: ${error.message}`);
        return false;
    }

    // 步骤4: 测试完整连接
    logStep('4️⃣', '测试完整连接状态...');
    try {
        const response = await axios.post(`${CONFIG.localServer}/api/xiaohongshu/test-browser-connection`, {}, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 20000
        });
        
        if (response.data.success) {
            logSuccess('完整连接测试成功！');
            const summary = response.data.data.summary;
            
            log('\n📊 连接状态总结:', 'bright');
            log(`   API连接: ${summary.apiConnected ? '✅ 正常' : '❌ 失败'}`, summary.apiConnected ? 'green' : 'red');
            log(`   调试端口: ${summary.debugConnected ? '✅ 正常' : '❌ 失败'}`, summary.debugConnected ? 'green' : 'red');
            log(`   目标浏览器: ${summary.targetBrowserFound ? '✅ 已找到' : '❌ 未找到'}`, summary.targetBrowserFound ? 'green' : 'red');
            log(`   小红书页面: ${summary.xiaohongshuPageFound ? '✅ 已打开' : '❌ 未打开'}`, summary.xiaohongshuPageFound ? 'green' : 'red');
            
            if (summary.debugConnected && summary.xiaohongshuPageFound) {
                log('\n🎉 所有配置验证通过！可以开始数据采集。', 'green');
                return true;
            } else {
                log('\n⚠️  部分功能未就绪，请按照提示完成配置。', 'yellow');
            }
        } else {
            logWarning(`连接测试部分失败: ${response.data.message}`);
            logInfo(response.data.details?.suggestion || '请检查配置');
        }
    } catch (error) {
        logError(`完整连接测试失败: ${error.message}`);
    }

    return false;
}

// 显示配置信息
function showConfiguration() {
    log('\n⚙️  当前配置信息:', 'bright');
    log(`   本地服务器: ${CONFIG.localServer}`, 'blue');
    log(`   比特浏览器API: ${CONFIG.bitbrowserAPI}`, 'blue');
    log(`   API Token: ${CONFIG.apiToken}`, 'blue');
    log(`   目标浏览器ID: ${CONFIG.targetBrowserId}`, 'blue');
}

// 显示帮助信息
function showHelp() {
    log('\n💡 配置建议:', 'bright');
    log('   1. 确保比特浏览器已启动', 'yellow');
    log('   2. 检查Local API接口是否开启 (http://127.0.0.1:54345)', 'yellow');
    log('   3. 确认API Token配置正确', 'yellow');
    log('   4. 创建并启动目标浏览器实例', 'yellow');
    log('   5. 在浏览器中打开小红书网站', 'yellow');
    log('\n🌐 Web界面:', 'bright');
    log(`   配置验证页面: ${CONFIG.localServer}/bitbrowser-config.html`, 'cyan');
    log(`   浏览器管理页面: ${CONFIG.localServer}/bitbrowser-manager.html`, 'cyan');
    log(`   健康检查页面: ${CONFIG.localServer}/health-check.html`, 'cyan');
}

// 主函数
async function main() {
    try {
        showConfiguration();
        const success = await verifyConfiguration();
        
        if (!success) {
            showHelp();
            process.exit(1);
        }
        
        log('\n🚀 验证完成！', 'green');
        process.exit(0);
        
    } catch (error) {
        logError(`验证过程出错: ${error.message}`);
        showHelp();
        process.exit(1);
    }
}

// 运行验证
if (require.main === module) {
    main();
}

module.exports = { verifyConfiguration, CONFIG };
