#!/usr/bin/env node

/**
 * 🔍 小红书全量1472条评论采集器
 * 专门用于采集所有评论，确保不遗漏
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class XiaohongshuAll1472CommentsScraper {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        this.targetCommentCount = 1472; // 目标评论数
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`   💾 数据已保存到: ${filepath}`);
    }

    /**
     * 智能滚动加载所有评论
     */
    async loadAllCommentsCompletely(page) {
        console.log(`📜 智能滚动加载所有评论 (目标: ${this.targetCommentCount}条)...`);
        
        let scrollCount = 0;
        let lastCommentCount = 0;
        let stableCount = 0;
        const maxScrollAttempts = 100; // 增加最大滚动次数
        const maxStableAttempts = 5; // 连续5次没有新评论就停止
        
        while (scrollCount < maxScrollAttempts && stableCount < maxStableAttempts) {
            scrollCount++;
            
            // 滚动到页面底部
            await page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });
            
            console.log(`   🔄 滚动第 ${scrollCount} 次...`);
            
            // 等待内容加载
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 检查当前评论数量
            const currentCommentCount = await page.evaluate(() => {
                const commentSelectors = [
                    '.comment-item',
                    '.parent-comment',
                    '[class*="comment"]'
                ];
                
                let maxCount = 0;
                for (const selector of commentSelectors) {
                    const count = document.querySelectorAll(selector).length;
                    maxCount = Math.max(maxCount, count);
                }
                return maxCount;
            });
            
            console.log(`   📊 当前评论数: ${currentCommentCount}`);
            
            // 检查是否有新评论加载
            if (currentCommentCount > lastCommentCount) {
                lastCommentCount = currentCommentCount;
                stableCount = 0; // 重置稳定计数
                console.log(`   ✅ 新增 ${currentCommentCount - lastCommentCount} 条评论`);
            } else {
                stableCount++;
                console.log(`   ⏳ 没有新评论，稳定计数: ${stableCount}/${maxStableAttempts}`);
            }
            
            // 尝试点击"加载更多"按钮
            try {
                const loadMoreButtons = await page.$$('button, div, span');
                for (const button of loadMoreButtons) {
                    const buttonText = await button.evaluate(el => el.textContent?.trim() || '');
                    if (buttonText.includes('加载更多') || 
                        buttonText.includes('查看更多') ||
                        buttonText.includes('更多评论') ||
                        buttonText.includes('展开更多')) {
                        console.log(`   🎯 点击按钮: "${buttonText}"`);
                        await button.click();
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        stableCount = 0; // 重置稳定计数
                        break;
                    }
                }
            } catch (error) {
                // 忽略点击错误
            }
            
            // 如果已经接近目标数量，可以提前结束
            if (currentCommentCount >= this.targetCommentCount * 0.95) {
                console.log(`   🎯 已接近目标数量 (${currentCommentCount}/${this.targetCommentCount})`);
                break;
            }
        }
        
        console.log(`   ✅ 滚动完成，共滚动 ${scrollCount} 次，当前评论数: ${lastCommentCount}`);
        return lastCommentCount;
    }

    /**
     * 全面展开所有回复
     */
    async expandAllRepliesCompletely(page) {
        console.log('🔽 全面展开所有回复...');
        
        let totalExpanded = 0;
        let round = 0;
        const maxRounds = 20; // 增加展开轮数
        
        while (round < maxRounds) {
            round++;
            console.log(`   🔄 第 ${round} 轮展开操作...`);
            
            let expandedThisRound = 0;
            
            // 查找所有可能的展开元素
            const expandAttempts = await page.evaluate(() => {
                let clicked = 0;
                const expandTexts = [
                    '展开', '查看回复', '更多回复', '显示回复', 
                    '展开回复', '查看更多', '显示更多'
                ];
                
                // 查找所有元素
                const allElements = document.querySelectorAll('*');
                
                for (const el of allElements) {
                    const text = el.textContent?.trim() || '';
                    
                    // 检查是否包含展开相关文字
                    for (const expandText of expandTexts) {
                        if (text.includes(expandText) && text.includes('回复')) {
                            try {
                                // 尝试多种点击方式
                                el.click();
                                el.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                                el.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
                                el.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
                                
                                // 尝试点击父元素
                                if (el.parentElement) {
                                    el.parentElement.click();
                                }
                                
                                clicked++;
                                break;
                            } catch (e) {
                                // 忽略点击错误
                            }
                        }
                    }
                }
                
                return clicked;
            });
            
            expandedThisRound += expandAttempts;
            totalExpanded += expandedThisRound;
            
            console.log(`   ✅ 本轮展开 ${expandedThisRound} 个回复`);
            
            // 如果本轮没有展开任何回复，说明已经全部展开
            if (expandedThisRound === 0) {
                console.log(`   ℹ️ 没有更多回复可展开`);
                break;
            }
            
            // 等待页面更新
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        console.log(`   📊 总共展开 ${totalExpanded} 个回复区域`);
        return totalExpanded;
    }

    /**
     * 全量采集所有评论
     */
    async scrapeAll1472Comments(page) {
        console.log('💬 全量采集所有评论...');
        
        const commentsData = await page.evaluate(() => {
            const comments = [];
            const processedTexts = new Set(); // 用于去重
            
            try {
                console.log('开始全量采集评论...');
                
                // 多种策略采集评论
                const strategies = [
                    {
                        name: 'parent-comment',
                        selector: '.parent-comment',
                        description: '父评论容器'
                    },
                    {
                        name: 'comment-item',
                        selector: '.comment-item',
                        description: '评论项'
                    },
                    {
                        name: 'comment-container',
                        selector: '[class*="comment"]',
                        description: '包含comment的元素'
                    }
                ];
                
                for (const strategy of strategies) {
                    console.log(`使用策略: ${strategy.description}`);
                    
                    const elements = document.querySelectorAll(strategy.selector);
                    console.log(`找到 ${elements.length} 个 ${strategy.name} 元素`);
                    
                    elements.forEach((el, index) => {
                        try {
                            const rawText = el.textContent?.trim() || '';
                            
                            // 跳过已处理的内容（去重）
                            if (processedTexts.has(rawText) || rawText.length < 5) {
                                return;
                            }
                            
                            // 检查是否是有效的评论
                            const hasAvatar = el.querySelector('img[src*="avatar"]');
                            const hasUser = el.querySelector('[class*="user"], [class*="name"]');
                            
                            if (!hasAvatar || !hasUser) {
                                return;
                            }
                            
                            processedTexts.add(rawText);
                            
                            const comment = {
                                id: comments.length + 1,
                                type: 'primary',
                                user: '',
                                avatar: '',
                                content: '',
                                time: '',
                                likes: '',
                                replies: [],
                                debug: {
                                    rawText: rawText,
                                    className: el.className,
                                    strategy: strategy.name,
                                    elementIndex: index
                                }
                            };
                            
                            // 提取用户信息
                            const userEl = el.querySelector('[class*="user"], [class*="name"]');
                            if (userEl) {
                                comment.user = userEl.textContent.trim();
                            }
                            
                            // 提取头像
                            const avatarEl = el.querySelector('img[src*="avatar"]');
                            if (avatarEl) {
                                comment.avatar = avatarEl.src;
                            }
                            
                            // 提取评论内容
                            let contentText = rawText;
                            
                            // 清理内容
                            if (comment.user) {
                                contentText = contentText.replace(new RegExp(comment.user, 'g'), '').trim();
                            }
                            
                            // 移除常见的UI文字
                            contentText = contentText
                                .replace(/\d{2}-\d{2}/g, '') // 移除时间
                                .replace(/\d+回复$/g, '') // 移除回复数
                                .replace(/回复$/g, '')
                                .replace(/赞$/g, '')
                                .replace(/^\d+$/g, '') // 移除纯数字
                                .replace(/作者$/g, '')
                                .replace(/置顶评论$/g, '')
                                .replace(/\s+/g, ' ') // 合并空格
                                .trim();
                            
                            if (contentText.length > 3) {
                                comment.content = contentText;
                            }
                            
                            // 提取时间
                            const timeMatch = rawText.match(/(\d{2}-\d{2})/);
                            if (timeMatch) {
                                comment.time = timeMatch[1];
                            }
                            
                            // 提取点赞数
                            const likeMatch = rawText.match(/(\d+)(?=回复|$)/);
                            if (likeMatch) {
                                comment.likes = likeMatch[1];
                            }
                            
                            // 查找二级回复
                            const replyElements = el.querySelectorAll('[class*="reply"], .comment-item-sub');
                            
                            replyElements.forEach(replyEl => {
                                const replyText = replyEl.textContent?.trim() || '';
                                const replyHasAvatar = replyEl.querySelector('img[src*="avatar"]');
                                const replyHasUser = replyEl.querySelector('[class*="user"], [class*="name"]');
                                
                                if (replyHasAvatar && replyHasUser && replyText.length > 5 && 
                                    replyText.length < 500 && !processedTexts.has(replyText)) {
                                    
                                    processedTexts.add(replyText);
                                    
                                    const reply = {
                                        id: `${comment.id}-${comment.replies.length + 1}`,
                                        type: 'reply',
                                        user: '',
                                        content: '',
                                        time: '',
                                        likes: '',
                                        debug: {
                                            rawText: replyText,
                                            className: replyEl.className
                                        }
                                    };
                                    
                                    // 提取回复用户名
                                    const replyUserEl = replyEl.querySelector('[class*="user"], [class*="name"]');
                                    if (replyUserEl) {
                                        reply.user = replyUserEl.textContent.trim();
                                    }
                                    
                                    // 提取回复内容
                                    let replyContent = replyText;
                                    if (reply.user) {
                                        replyContent = replyContent.replace(new RegExp(reply.user, 'g'), '').trim();
                                    }
                                    
                                    replyContent = replyContent
                                        .replace(/\d{2}-\d{2}/g, '')
                                        .replace(/回复$/g, '')
                                        .replace(/赞$/g, '')
                                        .replace(/^\d+$/g, '')
                                        .replace(/\s+/g, ' ')
                                        .trim();
                                    
                                    if (replyContent.length > 2) {
                                        reply.content = replyContent;
                                        
                                        // 提取回复时间
                                        const replyTimeMatch = replyText.match(/(\d{2}-\d{2})/);
                                        if (replyTimeMatch) {
                                            reply.time = replyTimeMatch[1];
                                        }
                                        
                                        comment.replies.push(reply);
                                    }
                                }
                            });
                            
                            // 添加评论到结果中
                            if (comment.content || comment.user || comment.replies.length > 0) {
                                comments.push(comment);
                            }
                            
                        } catch (commentError) {
                            console.error('处理评论时出错:', commentError);
                        }
                    });
                    
                    console.log(`策略 ${strategy.name} 处理完成，当前总评论数: ${comments.length}`);
                }
                
                console.log(`最终处理了 ${comments.length} 个评论`);
                
                return {
                    totalComments: comments.length,
                    totalReplies: comments.reduce((sum, comment) => sum + comment.replies.length, 0),
                    comments: comments,
                    timestamp: new Date().toISOString(),
                    targetReached: comments.length >= 1400 // 检查是否接近目标
                };
                
            } catch (error) {
                console.error('采集评论时出错:', error);
                return { 
                    comments: [], 
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
            }
        });
        
        return commentsData;
    }

    async scrapeAll1472Main() {
        console.log('🕷️ 启动全量1472条评论采集器...');
        console.log(`🎯 目标: 采集 ${this.targetCommentCount} 条评论`);
        console.log('');

        let browser = null;

        try {
            console.log('🔗 连接到比特浏览器...');
            browser = await puppeteer.connect({
                browserURL: `http://127.0.0.1:${this.debugPort}`,
                defaultViewport: null
            });

            const pages = await browser.pages();
            const xiaohongshuPage = pages.find(page => 
                page.url().includes('xiaohongshu.com/explore/')
            );

            if (!xiaohongshuPage) {
                throw new Error('未找到小红书笔记页面');
            }

            console.log('✅ 找到小红书笔记页面');
            console.log('📄 当前URL:', xiaohongshuPage.url());
            console.log('');

            // 等待页面加载
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 1. 智能滚动加载所有评论
            const loadedCommentCount = await this.loadAllCommentsCompletely(xiaohongshuPage);

            // 2. 全面展开所有回复
            const expandedCount = await this.expandAllRepliesCompletely(xiaohongshuPage);

            // 3. 全量采集所有评论
            const commentsData = await this.scrapeAll1472Comments(xiaohongshuPage);
            
            console.log('✅ 全量评论采集完成');
            console.log('   页面加载评论数:', loadedCommentCount);
            console.log('   一级评论:', commentsData.totalComments || 0, '条');
            console.log('   二级回复:', commentsData.totalReplies || 0, '条');
            console.log('   展开操作:', expandedCount, '次');
            console.log('   目标完成度:', `${((commentsData.totalComments || 0) / this.targetCommentCount * 100).toFixed(1)}%`);

            // 4. 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const urlMatch = xiaohongshuPage.url().match(/explore\/([a-f0-9]+)/);
            const noteId = urlMatch ? urlMatch[1] : 'unknown';
            
            const fullData = {
                scrapeTime: new Date().toISOString(),
                noteId: noteId,
                pageUrl: xiaohongshuPage.url(),
                targetCommentCount: this.targetCommentCount,
                loadedCommentCount: loadedCommentCount,
                expandAttempts: expandedCount,
                commentsData: commentsData,
                summary: {
                    totalComments: commentsData.totalComments || 0,
                    totalReplies: commentsData.totalReplies || 0,
                    totalInteractions: (commentsData.totalComments || 0) + (commentsData.totalReplies || 0),
                    targetReached: commentsData.targetReached || false,
                    completionRate: ((commentsData.totalComments || 0) / this.targetCommentCount * 100).toFixed(1) + '%',
                    hasErrors: !!commentsData.error
                }
            };

            this.saveToFile(fullData, `all_1472_comments_${noteId}_${timestamp}.json`);

            console.log('');
            console.log('🎉 全量1472条评论采集完成！');
            console.log('📊 最终统计:');
            console.log(`   💬 一级评论: ${commentsData.totalComments || 0} 条`);
            console.log(`   🔄 二级回复: ${commentsData.totalReplies || 0} 条`);
            console.log(`   📈 总互动数: ${fullData.summary.totalInteractions} 条`);
            console.log(`   🎯 完成度: ${fullData.summary.completionRate}`);
            console.log(`   📁 数据文件: all_1472_comments_${noteId}_${timestamp}.json`);

            if (commentsData.totalComments >= this.targetCommentCount * 0.9) {
                console.log('   ✅ 成功采集到目标数量的90%以上！');
            } else {
                console.log('   ⚠️ 采集数量未达到预期，可能需要调整策略');
            }

            return fullData;

        } catch (error) {
            console.error('❌ 采集失败:', error.message);
            return null;
        } finally {
            if (browser) {
                await browser.disconnect();
            }
        }
    }
}

if (require.main === module) {
    const scraper = new XiaohongshuAll1472CommentsScraper();
    scraper.scrapeAll1472Main().catch(console.error);
}

module.exports = XiaohongshuAll1472CommentsScraper;
