#!/usr/bin/env node

/**
 * 🔍 小红书全量评论采集器
 * 完整采集页面上的所有评论和回复
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class XiaohongshuAllCommentsScraper {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`   💾 数据已保存到: ${filepath}`);
    }

    /**
     * 滚动到页面底部，加载所有评论
     */
    async loadAllComments(page) {
        console.log('📜 滚动加载所有评论...');
        
        let previousHeight = 0;
        let currentHeight = await page.evaluate(() => document.body.scrollHeight);
        let scrollAttempts = 0;
        const maxScrollAttempts = 20;
        
        while (previousHeight !== currentHeight && scrollAttempts < maxScrollAttempts) {
            previousHeight = currentHeight;
            
            // 滚动到底部
            await page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });
            
            console.log(`   🔄 滚动第 ${scrollAttempts + 1} 次...`);
            
            // 等待新内容加载
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 检查是否有"加载更多"按钮
            try {
                const loadMoreButtons = await page.$$('.load-more, .more-comments, [class*="load"], [class*="more"]');
                for (const button of loadMoreButtons) {
                    const buttonText = await button.evaluate(el => el.textContent);
                    if (buttonText && (buttonText.includes('更多') || buttonText.includes('加载'))) {
                        console.log(`   🎯 点击加载更多按钮: "${buttonText}"`);
                        await button.click();
                        await new Promise(resolve => setTimeout(resolve, 3000));
                    }
                }
            } catch (error) {
                // 忽略点击错误
            }
            
            currentHeight = await page.evaluate(() => document.body.scrollHeight);
            scrollAttempts++;
        }
        
        console.log(`   ✅ 滚动完成，共滚动 ${scrollAttempts} 次`);
    }

    /**
     * 全面展开所有回复
     */
    async expandAllReplies(page) {
        console.log('🔽 全面展开所有回复...');
        
        let totalExpanded = 0;
        let round = 0;
        const maxRounds = 10;
        
        while (round < maxRounds) {
            round++;
            console.log(`   🔄 第 ${round} 轮展开操作...`);
            
            let expandedThisRound = 0;
            
            // 方法1: 点击所有show-more元素
            const showMoreElements = await page.$$('.show-more');
            for (const element of showMoreElements) {
                try {
                    const text = await element.evaluate(el => el.textContent);
                    if (text && text.includes('展开') && text.includes('回复')) {
                        await element.click();
                        expandedThisRound++;
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                } catch (error) {
                    // 忽略点击错误
                }
            }
            
            // 方法2: 查找并点击所有包含"展开"的可点击元素
            const moreExpanded = await page.evaluate(() => {
                let clicked = 0;
                const allElements = document.querySelectorAll('*');
                
                for (const el of allElements) {
                    const text = el.textContent?.trim() || '';
                    
                    // 匹配各种展开回复的文字
                    if (text.match(/展开\s*\d*\s*条?回复/) || 
                        text.match(/查看\s*\d*\s*条?回复/) ||
                        text.match(/更多\s*\d*\s*条?回复/) ||
                        text === '展开回复' ||
                        text === '查看回复' ||
                        text === '更多回复') {
                        
                        // 查找可点击的元素（自身或父元素）
                        let clickableEl = el;
                        let attempts = 0;
                        
                        while (clickableEl && attempts < 5) {
                            try {
                                if (clickableEl.tagName === 'BUTTON' || 
                                    clickableEl.tagName === 'A' ||
                                    clickableEl.onclick ||
                                    clickableEl.getAttribute('role') === 'button' ||
                                    window.getComputedStyle(clickableEl).cursor === 'pointer') {
                                    
                                    // 尝试多种点击方式
                                    clickableEl.click();
                                    clickableEl.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                                    clickableEl.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
                                    clickableEl.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
                                    
                                    clicked++;
                                    break;
                                }
                            } catch (e) {
                                // 继续尝试父元素
                            }
                            
                            clickableEl = clickableEl.parentElement;
                            attempts++;
                        }
                    }
                }
                
                return clicked;
            });
            
            expandedThisRound += moreExpanded;
            totalExpanded += expandedThisRound;
            
            console.log(`   ✅ 本轮展开 ${expandedThisRound} 个回复`);
            
            // 如果本轮没有展开任何回复，说明已经全部展开
            if (expandedThisRound === 0) {
                console.log(`   ℹ️ 没有更多回复可展开`);
                break;
            }
            
            // 等待页面更新
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        console.log(`   📊 总共展开 ${totalExpanded} 个回复区域`);
        return totalExpanded;
    }

    /**
     * 全面采集所有评论
     */
    async scrapeAllComments(page) {
        console.log('💬 全面采集所有评论...');
        
        const commentsData = await page.evaluate(() => {
            const comments = [];
            
            try {
                // 获取页面基本信息
                const pageInfo = {
                    title: document.title,
                    url: window.location.href,
                    timestamp: new Date().toISOString()
                };
                
                // 多种策略查找评论元素
                const commentSelectors = [
                    '.comment-item',
                    '.parent-comment', 
                    '.comment',
                    '[class*="comment"]',
                    '.note-comment',
                    '.user-comment'
                ];
                
                let allCommentElements = [];
                
                // 尝试不同的选择器
                for (const selector of commentSelectors) {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        allCommentElements = Array.from(elements);
                        console.log(`使用选择器 ${selector} 找到 ${elements.length} 个评论`);
                        break;
                    }
                }
                
                // 如果没找到专门的评论容器，使用通用方法
                if (allCommentElements.length === 0) {
                    const allDivs = document.querySelectorAll('div');
                    allCommentElements = Array.from(allDivs).filter(div => {
                        const hasAvatar = div.querySelector('img[src*="avatar"]');
                        const hasText = div.textContent && div.textContent.trim().length > 10;
                        const hasUserInfo = div.querySelector('[class*="user"], [class*="name"]');
                        const notReplyContainer = !div.closest('.reply-container, .sub-comment');
                        return hasAvatar && hasText && hasUserInfo && notReplyContainer;
                    });
                    console.log(`通用方法找到 ${allCommentElements.length} 个可能的评论`);
                }
                
                // 处理每个评论
                allCommentElements.forEach((commentEl, index) => {
                    try {
                        const comment = {
                            id: index + 1,
                            type: 'primary',
                            user: '',
                            avatar: '',
                            content: '',
                            time: '',
                            likes: '',
                            replies: [],
                            rawText: commentEl.textContent?.trim() || ''
                        };
                        
                        // 提取用户信息
                        const userSelectors = ['.username', '.user-name', '[class*="user"]', '[class*="name"]'];
                        for (const selector of userSelectors) {
                            const userEl = commentEl.querySelector(selector);
                            if (userEl && userEl.textContent.trim()) {
                                comment.user = userEl.textContent.trim();
                                break;
                            }
                        }
                        
                        // 提取头像
                        const avatarEl = commentEl.querySelector('img[src*="avatar"], [class*="avatar"] img');
                        if (avatarEl && avatarEl.src) {
                            comment.avatar = avatarEl.src;
                        }
                        
                        // 提取评论内容
                        let contentText = commentEl.textContent.trim();
                        
                        // 清理内容
                        if (comment.user) {
                            contentText = contentText.replace(new RegExp(comment.user, 'g'), '').trim();
                        }
                        
                        // 移除常见的UI文字
                        contentText = contentText
                            .replace(/^\d+回复$/, '')
                            .replace(/^回复$/, '')
                            .replace(/^赞$/, '')
                            .replace(/^\d+$/, '')
                            .replace(/展开\s*\d*\s*条回复/g, '')
                            .replace(/查看\s*\d*\s*条回复/g, '')
                            .replace(/\d{2}-\d{2}/g, '')
                            .replace(/作者$/, '')
                            .replace(/置顶评论$/, '')
                            .replace(/\s+/g, ' ')
                            .trim();
                        
                        if (contentText.length > 3) {
                            comment.content = contentText;
                        }
                        
                        // 提取时间
                        const timeMatch = commentEl.textContent.match(/\d{2}-\d{2}/);
                        if (timeMatch) comment.time = timeMatch[0];
                        
                        // 提取点赞数
                        const likeElements = commentEl.querySelectorAll('[class*="like"], [class*="heart"]');
                        for (const likeEl of likeElements) {
                            const likeText = likeEl.textContent.trim();
                            if (/^\d+$/.test(likeText)) {
                                comment.likes = likeText;
                                break;
                            }
                        }
                        
                        // 查找二级回复
                        const replySelectors = [
                            '.reply-container',
                            '.sub-comment', 
                            '[class*="reply"]',
                            '[class*="sub"]'
                        ];
                        
                        for (const replySelector of replySelectors) {
                            const replyContainers = commentEl.querySelectorAll(replySelector);
                            
                            replyContainers.forEach(replyContainer => {
                                // 在回复容器中查找具体的回复元素
                                const replyElements = replyContainer.querySelectorAll('div');
                                
                                replyElements.forEach(replyEl => {
                                    const replyText = replyEl.textContent?.trim() || '';
                                    const hasAvatar = replyEl.querySelector('img[src*="avatar"]');
                                    const hasUser = replyEl.querySelector('[class*="user"], [class*="name"]');
                                    
                                    if (hasAvatar && hasUser && replyText.length > 3) {
                                        const reply = {
                                            id: `${comment.id}-${comment.replies.length + 1}`,
                                            type: 'reply',
                                            user: '',
                                            content: '',
                                            time: '',
                                            likes: '',
                                            rawText: replyText
                                        };
                                        
                                        // 提取回复用户名
                                        const replyUserEl = replyEl.querySelector('[class*="user"], [class*="name"]');
                                        if (replyUserEl) {
                                            reply.user = replyUserEl.textContent.trim();
                                        }
                                        
                                        // 提取回复内容
                                        let replyContent = replyText;
                                        if (reply.user) {
                                            replyContent = replyContent.replace(new RegExp(reply.user, 'g'), '').trim();
                                        }
                                        
                                        // 清理回复内容
                                        replyContent = replyContent
                                            .replace(/^回复$/, '')
                                            .replace(/^赞$/, '')
                                            .replace(/^\d+$/, '')
                                            .replace(/\d{2}-\d{2}/g, '')
                                            .replace(/\s+/g, ' ')
                                            .trim();
                                        
                                        if (replyContent.length > 2) {
                                            reply.content = replyContent;
                                            
                                            // 提取回复时间
                                            const replyTimeMatch = replyText.match(/\d{2}-\d{2}/);
                                            if (replyTimeMatch) reply.time = replyTimeMatch[0];
                                            
                                            comment.replies.push(reply);
                                        }
                                    }
                                });
                            });
                        }
                        
                        // 只添加有内容的评论
                        if (comment.content || comment.user || comment.replies.length > 0) {
                            comments.push(comment);
                        }
                        
                    } catch (commentError) {
                        console.error('处理评论时出错:', commentError);
                    }
                });
                
                return {
                    pageInfo: pageInfo,
                    totalComments: comments.length,
                    totalReplies: comments.reduce((sum, comment) => sum + comment.replies.length, 0),
                    comments: comments,
                    timestamp: new Date().toISOString()
                };
                
            } catch (error) {
                console.error('采集评论时出错:', error);
                return { 
                    comments: [], 
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
            }
        });
        
        return commentsData;
    }

    /**
     * 主要采集函数
     */
    async scrapeAllCommentsMain() {
        console.log('🕷️ 启动小红书全量评论采集器...');
        console.log('');

        let browser = null;

        try {
            console.log('🔗 连接到比特浏览器...');
            browser = await puppeteer.connect({
                browserURL: `http://127.0.0.1:${this.debugPort}`,
                defaultViewport: null
            });

            const pages = await browser.pages();
            const xiaohongshuPage = pages.find(page => 
                page.url().includes('xiaohongshu.com/explore/')
            );

            if (!xiaohongshuPage) {
                throw new Error('未找到小红书笔记页面');
            }

            console.log('✅ 找到小红书笔记页面');
            console.log('📄 当前URL:', xiaohongshuPage.url());
            console.log('');

            // 等待页面加载
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 1. 滚动加载所有评论
            await this.loadAllComments(xiaohongshuPage);

            // 2. 全面展开所有回复
            const expandedCount = await this.expandAllReplies(xiaohongshuPage);

            // 3. 采集所有评论
            const commentsData = await this.scrapeAllComments(xiaohongshuPage);
            
            console.log('✅ 全量评论采集完成');
            console.log('   页面标题:', commentsData.pageInfo?.title || '未知');
            console.log('   一级评论:', commentsData.totalComments || 0, '条');
            console.log('   二级回复:', commentsData.totalReplies || 0, '条');
            console.log('   展开操作:', expandedCount, '次');

            // 4. 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const urlMatch = xiaohongshuPage.url().match(/explore\/([a-f0-9]+)/);
            const noteId = urlMatch ? urlMatch[1] : 'unknown';
            
            const fullData = {
                scrapeTime: new Date().toISOString(),
                noteId: noteId,
                pageUrl: xiaohongshuPage.url(),
                expandAttempts: expandedCount,
                commentsData: commentsData,
                summary: {
                    totalComments: commentsData.totalComments || 0,
                    totalReplies: commentsData.totalReplies || 0,
                    totalInteractions: (commentsData.totalComments || 0) + (commentsData.totalReplies || 0),
                    hasErrors: !!commentsData.error
                }
            };

            this.saveToFile(fullData, `all_comments_${noteId}_${timestamp}.json`);

            console.log('');
            console.log('🎉 全量评论采集完成！');
            console.log('📊 最终统计:');
            console.log(`   💬 一级评论: ${commentsData.totalComments || 0} 条`);
            console.log(`   🔄 二级回复: ${commentsData.totalReplies || 0} 条`);
            console.log(`   📈 总互动数: ${fullData.summary.totalInteractions} 条`);
            console.log(`   🎯 展开操作: ${expandedCount} 次`);
            console.log(`   📁 数据文件: all_comments_${noteId}_${timestamp}.json`);

            return fullData;

        } catch (error) {
            console.error('❌ 采集失败:', error.message);
            return null;
        } finally {
            if (browser) {
                await browser.disconnect();
            }
        }
    }
}

if (require.main === module) {
    const scraper = new XiaohongshuAllCommentsScraper();
    scraper.scrapeAllCommentsMain().catch(console.error);
}

module.exports = XiaohongshuAllCommentsScraper;
