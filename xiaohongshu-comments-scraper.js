#!/usr/bin/env node

/**
 * 🔍 小红书笔记评论爬取器
 * 爬取笔记的一级评论和二级回复
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class XiaohongshuCommentsScraper {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
    }

    /**
     * 确保输出目录存在
     */
    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    /**
     * 保存数据到JSON文件
     */
    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`   💾 数据已保存到: ${filepath}`);
    }

    /**
     * 滚动页面加载更多评论
     */
    async scrollToLoadComments(page, maxScrolls = 5) {
        console.log('📜 滚动页面加载更多评论...');
        
        for (let i = 0; i < maxScrolls; i++) {
            await page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });
            
            // 等待新内容加载
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 检查是否有"加载更多"按钮
            try {
                const loadMoreBtn = await page.$('.load-more, .more-comments, [class*="load"], [class*="more"]');
                if (loadMoreBtn) {
                    console.log(`   🔄 点击加载更多按钮 (第${i + 1}次)`);
                    await loadMoreBtn.click();
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }
            } catch (error) {
                // 忽略点击错误
            }
        }
    }

    /**
     * 展开所有二级评论
     */
    async expandAllReplies(page) {
        console.log('🔽 展开所有二级评论...');

        try {
            let expandedCount = 0;
            let attempts = 0;
            const maxAttempts = 5;

            while (attempts < maxAttempts) {
                attempts++;
                console.log(`   🔄 第 ${attempts} 轮查找回复按钮...`);

                // 更精确的选择器
                const foundButtons = await page.evaluate(() => {
                    const buttons = [];

                    // 查找包含"回复"文字的所有元素
                    const allElements = document.querySelectorAll('*');

                    for (const el of allElements) {
                        const text = el.textContent?.trim() || '';

                        // 匹配各种回复相关的文字
                        if (text.match(/^\d+\s*条?回复$/) ||
                            text.match(/查看\s*\d*\s*回复/) ||
                            text.match(/展开\s*\d*\s*回复/) ||
                            text.match(/^\d+\s*回复$/) ||
                            text === '查看回复' ||
                            text === '展开回复' ||
                            text === '更多回复') {

                            // 确保是可点击的元素
                            if (el.tagName === 'BUTTON' ||
                                el.tagName === 'A' ||
                                el.onclick ||
                                el.style.cursor === 'pointer' ||
                                el.getAttribute('role') === 'button') {

                                buttons.push({
                                    text: text,
                                    tagName: el.tagName,
                                    className: el.className
                                });

                                // 尝试点击
                                try {
                                    el.click();
                                    return true; // 成功点击一个
                                } catch (e) {
                                    console.log('点击失败:', e.message);
                                }
                            }
                        }
                    }

                    return buttons.length > 0;
                });

                if (foundButtons) {
                    expandedCount++;
                    console.log(`   ✅ 成功点击回复按钮`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                } else {
                    console.log(`   ℹ️ 未找到更多回复按钮`);
                    break;
                }
            }

            console.log(`   📊 总共展开了 ${expandedCount} 个回复区域`);

        } catch (error) {
            console.log('   ⚠️ 展开回复时出错:', error.message);
        }
    }

    /**
     * 爬取笔记基本信息
     */
    async scrapeNoteInfo(page) {
        console.log('📝 爬取笔记基本信息...');
        
        const noteInfo = await page.evaluate(() => {
            const result = {
                title: '',
                author: '',
                content: '',
                likes: '',
                comments: '',
                shares: '',
                url: window.location.href,
                noteId: ''
            };

            try {
                // 笔记标题
                const titleSelectors = ['.note-title', '.title', 'h1', '[class*="title"]'];
                for (const selector of titleSelectors) {
                    const titleEl = document.querySelector(selector);
                    if (titleEl && titleEl.textContent.trim()) {
                        result.title = titleEl.textContent.trim();
                        break;
                    }
                }

                // 作者信息
                const authorSelectors = ['.author-name', '.username', '[class*="author"]', '[class*="user"]'];
                for (const selector of authorSelectors) {
                    const authorEl = document.querySelector(selector);
                    if (authorEl && authorEl.textContent.trim()) {
                        result.author = authorEl.textContent.trim();
                        break;
                    }
                }

                // 笔记内容
                const contentSelectors = ['.note-content', '.content', '[class*="content"]', '[class*="desc"]'];
                for (const selector of contentSelectors) {
                    const contentEl = document.querySelector(selector);
                    if (contentEl && contentEl.textContent.trim().length > 10) {
                        result.content = contentEl.textContent.trim();
                        break;
                    }
                }

                // 统计数据
                const statElements = document.querySelectorAll('[class*="count"], [class*="num"], [class*="stat"]');
                statElements.forEach(el => {
                    const text = el.textContent.trim();
                    const parent = el.parentElement;
                    const parentText = parent ? parent.textContent.trim() : '';
                    
                    if (parentText.includes('赞') || parentText.includes('like')) {
                        result.likes = text;
                    } else if (parentText.includes('评论') || parentText.includes('comment')) {
                        result.comments = text;
                    } else if (parentText.includes('分享') || parentText.includes('share')) {
                        result.shares = text;
                    }
                });

                // 从URL提取笔记ID
                const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
                if (urlMatch) {
                    result.noteId = urlMatch[1];
                }

                return result;
            } catch (error) {
                console.error('爬取笔记信息时出错:', error);
                return result;
            }
        });

        return noteInfo;
    }

    /**
     * 爬取评论数据
     */
    async scrapeComments(page) {
        console.log('💬 爬取评论数据...');
        
        const commentsData = await page.evaluate(() => {
            const comments = [];
            
            try {
                // 查找评论容器的多种可能选择器
                const commentSelectors = [
                    '.comment-item',
                    '.comment',
                    '[class*="comment"]',
                    '.reply-item',
                    '[class*="reply"]',
                    '.note-comment',
                    '.user-comment'
                ];

                let commentElements = [];
                
                // 尝试不同的选择器找到评论元素
                for (const selector of commentSelectors) {
                    commentElements = document.querySelectorAll(selector);
                    if (commentElements.length > 0) {
                        console.log(`找到评论选择器: ${selector}, 数量: ${commentElements.length}`);
                        break;
                    }
                }

                // 如果还是没找到，尝试更通用的方法
                if (commentElements.length === 0) {
                    // 查找包含用户头像和文本的元素
                    const allDivs = document.querySelectorAll('div');
                    commentElements = Array.from(allDivs).filter(div => {
                        const hasAvatar = div.querySelector('img[src*="avatar"]') || div.querySelector('[class*="avatar"]');
                        const hasText = div.textContent && div.textContent.trim().length > 5;
                        const hasUserInfo = div.querySelector('[class*="user"]') || div.querySelector('[class*="name"]');
                        return hasAvatar && hasText && hasUserInfo;
                    });
                }

                console.log(`总共找到 ${commentElements.length} 个可能的评论元素`);

                // 处理每个评论元素
                commentElements.forEach((commentEl, index) => {
                    try {
                        const comment = {
                            id: index + 1,
                            type: 'primary', // 一级评论
                            user: '',
                            avatar: '',
                            content: '',
                            time: '',
                            likes: '',
                            replies: []
                        };

                        // 用户名
                        const userSelectors = ['.username', '.user-name', '[class*="user"]', '[class*="name"]'];
                        for (const selector of userSelectors) {
                            const userEl = commentEl.querySelector(selector);
                            if (userEl && userEl.textContent.trim()) {
                                comment.user = userEl.textContent.trim();
                                break;
                            }
                        }

                        // 头像
                        const avatarEl = commentEl.querySelector('img[src*="avatar"], [class*="avatar"] img, img');
                        if (avatarEl && avatarEl.src) {
                            comment.avatar = avatarEl.src;
                        }

                        // 评论内容
                        const contentSelectors = ['.comment-content', '.content', '[class*="content"]', '[class*="text"]'];
                        for (const selector of contentSelectors) {
                            const contentEl = commentEl.querySelector(selector);
                            if (contentEl && contentEl.textContent.trim().length > 2) {
                                comment.content = contentEl.textContent.trim();
                                break;
                            }
                        }

                        // 如果没找到专门的内容元素，使用整个元素的文本（排除用户名）
                        if (!comment.content) {
                            let fullText = commentEl.textContent.trim();
                            if (comment.user) {
                                fullText = fullText.replace(comment.user, '').trim();
                            }
                            if (fullText.length > 5) {
                                comment.content = fullText;
                            }
                        }

                        // 时间
                        const timeSelectors = ['.time', '.date', '[class*="time"]', '[class*="date"]'];
                        for (const selector of timeSelectors) {
                            const timeEl = commentEl.querySelector(selector);
                            if (timeEl && timeEl.textContent.trim()) {
                                comment.time = timeEl.textContent.trim();
                                break;
                            }
                        }

                        // 点赞数
                        const likeSelectors = ['.like-count', '[class*="like"]', '[class*="heart"]'];
                        for (const selector of likeSelectors) {
                            const likeEl = commentEl.querySelector(selector);
                            if (likeEl && likeEl.textContent.trim()) {
                                const likeText = likeEl.textContent.trim();
                                if (/^\d+$/.test(likeText)) {
                                    comment.likes = likeText;
                                    break;
                                }
                            }
                        }

                        // 查找二级回复
                        const replySelectors = ['.reply', '.sub-comment', '[class*="reply"]', '[class*="sub"]'];
                        for (const selector of replySelectors) {
                            const replyElements = commentEl.querySelectorAll(selector);
                            
                            replyElements.forEach((replyEl, replyIndex) => {
                                try {
                                    const reply = {
                                        id: `${comment.id}-${replyIndex + 1}`,
                                        type: 'reply', // 二级回复
                                        user: '',
                                        content: '',
                                        time: '',
                                        likes: ''
                                    };

                                    // 回复用户名
                                    const replyUserEl = replyEl.querySelector('.username, .user-name, [class*="user"]');
                                    if (replyUserEl) {
                                        reply.user = replyUserEl.textContent.trim();
                                    }

                                    // 回复内容
                                    const replyContentEl = replyEl.querySelector('.content, [class*="content"], [class*="text"]');
                                    if (replyContentEl) {
                                        reply.content = replyContentEl.textContent.trim();
                                    } else {
                                        let replyText = replyEl.textContent.trim();
                                        if (reply.user) {
                                            replyText = replyText.replace(reply.user, '').trim();
                                        }
                                        if (replyText.length > 2) {
                                            reply.content = replyText;
                                        }
                                    }

                                    // 回复时间
                                    const replyTimeEl = replyEl.querySelector('.time, [class*="time"]');
                                    if (replyTimeEl) {
                                        reply.time = replyTimeEl.textContent.trim();
                                    }

                                    if (reply.content) {
                                        comment.replies.push(reply);
                                    }
                                } catch (replyError) {
                                    console.error('处理回复时出错:', replyError);
                                }
                            });
                        }

                        // 只添加有内容的评论
                        if (comment.content || comment.user) {
                            comments.push(comment);
                        }

                    } catch (commentError) {
                        console.error('处理评论时出错:', commentError);
                    }
                });

                return {
                    totalComments: comments.length,
                    totalReplies: comments.reduce((sum, comment) => sum + comment.replies.length, 0),
                    comments: comments,
                    timestamp: new Date().toISOString()
                };

            } catch (error) {
                console.error('爬取评论时出错:', error);
                return { comments: [], error: error.message };
            }
        });

        return commentsData;
    }

    /**
     * 主要爬取函数
     */
    async scrapeNoteComments() {
        console.log('🕷️ 启动小红书评论爬取器...');
        console.log('');

        let browser = null;

        try {
            // 连接到比特浏览器
            console.log('🔗 连接到比特浏览器...');
            browser = await puppeteer.connect({
                browserURL: `http://127.0.0.1:${this.debugPort}`,
                defaultViewport: null
            });

            // 获取小红书页面
            const pages = await browser.pages();
            const xiaohongshuPage = pages.find(page => 
                page.url().includes('xiaohongshu.com/explore/')
            );

            if (!xiaohongshuPage) {
                throw new Error('未找到小红书笔记页面');
            }

            console.log('✅ 找到小红书笔记页面');
            console.log('📄 当前URL:', xiaohongshuPage.url());
            console.log('');

            // 等待页面加载完成
            console.log('⏳ 等待页面加载完成...');
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 1. 爬取笔记基本信息
            const noteInfo = await this.scrapeNoteInfo(xiaohongshuPage);
            console.log('✅ 笔记信息爬取完成');
            console.log('   标题:', noteInfo.title || '未找到');
            console.log('   作者:', noteInfo.author || '未找到');
            console.log('   笔记ID:', noteInfo.noteId || '未找到');

            // 2. 滚动加载更多评论
            await this.scrollToLoadComments(xiaohongshuPage, 3);

            // 3. 展开所有二级回复
            await this.expandAllReplies(xiaohongshuPage);

            // 4. 爬取评论数据
            const commentsData = await this.scrapeComments(xiaohongshuPage);
            console.log('✅ 评论爬取完成');
            console.log('   一级评论:', commentsData.totalComments || 0, '条');
            console.log('   二级回复:', commentsData.totalReplies || 0, '条');

            // 5. 生成完整数据
            const fullData = {
                scrapeTime: new Date().toISOString(),
                noteInfo: noteInfo,
                commentsData: commentsData,
                summary: {
                    totalComments: commentsData.totalComments || 0,
                    totalReplies: commentsData.totalReplies || 0,
                    totalInteractions: (commentsData.totalComments || 0) + (commentsData.totalReplies || 0)
                }
            };

            // 6. 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const noteId = noteInfo.noteId || 'unknown';
            this.saveToFile(fullData, `comments_${noteId}_${timestamp}.json`);

            console.log('');
            console.log('🎉 评论爬取完成！');
            console.log('📁 输出目录:', this.outputDir);
            console.log('📊 爬取统计:');
            console.log(`   📝 笔记: ${noteInfo.title || '未知标题'}`);
            console.log(`   💬 一级评论: ${commentsData.totalComments || 0} 条`);
            console.log(`   🔄 二级回复: ${commentsData.totalReplies || 0} 条`);
            console.log(`   📈 总互动: ${fullData.summary.totalInteractions} 条`);

            return fullData;

        } catch (error) {
            console.error('❌ 爬取失败:', error.message);
            return null;
        } finally {
            if (browser) {
                await browser.disconnect();
            }
        }
    }
}

// 如果直接运行此文件
if (require.main === module) {
    const scraper = new XiaohongshuCommentsScraper();
    scraper.scrapeNoteComments().catch(console.error);
}

module.exports = XiaohongshuCommentsScraper;
