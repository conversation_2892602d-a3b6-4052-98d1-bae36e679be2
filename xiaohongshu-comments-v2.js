#!/usr/bin/env node

/**
 * 🔍 小红书评论爬取器 V2
 * 基于调试结果优化的版本，专门处理二级回复
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class XiaohongshuCommentsV2 {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`   💾 数据已保存到: ${filepath}`);
    }

    /**
     * 智能展开回复
     */
    async expandRepliesV2(page) {
        console.log('🔽 智能展开二级回复...');
        
        let expandedCount = 0;
        
        try {
            // 方法1: 点击 show-more 元素
            const showMoreElements = await page.$$('.show-more');
            console.log(`   🔍 找到 ${showMoreElements.length} 个 show-more 元素`);
            
            for (const element of showMoreElements) {
                try {
                    const text = await element.evaluate(el => el.textContent);
                    if (text && text.includes('展开') && text.includes('回复')) {
                        console.log(`   🎯 尝试点击: "${text}"`);
                        await element.click();
                        expandedCount++;
                        await new Promise(resolve => setTimeout(resolve, 1500));
                    }
                } catch (clickError) {
                    console.log(`   ⚠️ 点击失败: ${clickError.message}`);
                }
            }
            
            // 方法2: 查找包含"展开"文字的可点击父元素
            const expandElements = await page.evaluate(() => {
                const results = [];
                const allElements = document.querySelectorAll('*');
                
                for (const el of allElements) {
                    const text = el.textContent?.trim() || '';
                    if (text.match(/展开\s*\d+\s*条回复/)) {
                        // 查找可点击的父元素或自身
                        let clickableEl = el;
                        while (clickableEl && clickableEl !== document.body) {
                            if (clickableEl.tagName === 'BUTTON' || 
                                clickableEl.tagName === 'A' ||
                                clickableEl.onclick ||
                                clickableEl.getAttribute('role') === 'button' ||
                                window.getComputedStyle(clickableEl).cursor === 'pointer') {
                                
                                results.push({
                                    text: text,
                                    tagName: clickableEl.tagName,
                                    className: clickableEl.className
                                });
                                
                                try {
                                    clickableEl.click();
                                    return true;
                                } catch (e) {
                                    // 继续查找父元素
                                }
                            }
                            clickableEl = clickableEl.parentElement;
                        }
                    }
                }
                return results;
            });
            
            if (expandElements.length > 0) {
                console.log(`   ✅ 通过父元素点击展开了 ${expandElements.length} 个回复`);
                expandedCount += expandElements.length;
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            // 方法3: 模拟鼠标点击
            const moreExpandAttempts = await page.evaluate(() => {
                let clicked = 0;
                const expandTexts = ['展开', '查看回复', '更多回复'];
                
                for (const text of expandTexts) {
                    const elements = Array.from(document.querySelectorAll('*')).filter(el => 
                        el.textContent && el.textContent.includes(text) && el.textContent.includes('回复')
                    );
                    
                    for (const el of elements) {
                        try {
                            // 尝试触发各种事件
                            el.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                            el.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
                            el.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
                            clicked++;
                        } catch (e) {
                            // 忽略错误
                        }
                    }
                }
                return clicked;
            });
            
            if (moreExpandAttempts > 0) {
                console.log(`   ✅ 通过事件模拟展开了 ${moreExpandAttempts} 个回复`);
                expandedCount += moreExpandAttempts;
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            console.log(`   📊 总共尝试展开 ${expandedCount} 个回复区域`);
            
        } catch (error) {
            console.log('   ⚠️ 展开回复时出错:', error.message);
        }
        
        return expandedCount;
    }

    /**
     * 爬取评论和回复
     */
    async scrapeCommentsV2(page) {
        console.log('💬 爬取评论和回复数据...');
        
        const commentsData = await page.evaluate(() => {
            const comments = [];
            
            try {
                // 查找父评论容器
                const parentCommentSelectors = [
                    '.parent-comment',
                    '.comment-item',
                    '.comment',
                    '[class*="comment"]'
                ];
                
                let parentComments = [];
                
                for (const selector of parentCommentSelectors) {
                    parentComments = document.querySelectorAll(selector);
                    if (parentComments.length > 0) {
                        console.log(`使用选择器: ${selector}, 找到 ${parentComments.length} 个父评论`);
                        break;
                    }
                }
                
                // 如果没找到专门的评论容器，使用通用方法
                if (parentComments.length === 0) {
                    const allDivs = document.querySelectorAll('div');
                    parentComments = Array.from(allDivs).filter(div => {
                        const hasAvatar = div.querySelector('img[src*="avatar"]');
                        const hasText = div.textContent && div.textContent.trim().length > 10;
                        const hasUserInfo = div.querySelector('[class*="user"], [class*="name"]');
                        return hasAvatar && hasText && hasUserInfo;
                    });
                    console.log(`通用方法找到 ${parentComments.length} 个可能的评论`);
                }
                
                // 处理每个父评论
                parentComments.forEach((commentEl, index) => {
                    try {
                        const comment = {
                            id: index + 1,
                            type: 'primary',
                            user: '',
                            avatar: '',
                            content: '',
                            time: '',
                            likes: '',
                            replies: []
                        };
                        
                        // 提取用户信息
                        const userEl = commentEl.querySelector('.username, .user-name, [class*="user"], [class*="name"]');
                        if (userEl) comment.user = userEl.textContent.trim();
                        
                        const avatarEl = commentEl.querySelector('img[src*="avatar"], [class*="avatar"] img');
                        if (avatarEl) comment.avatar = avatarEl.src;
                        
                        // 提取评论内容
                        let contentText = commentEl.textContent.trim();
                        
                        // 清理内容：移除用户名、时间、点赞数等
                        if (comment.user) {
                            contentText = contentText.replace(comment.user, '').trim();
                        }
                        
                        // 移除常见的UI文字
                        contentText = contentText
                            .replace(/^\d+回复$/, '')
                            .replace(/^回复$/, '')
                            .replace(/^赞$/, '')
                            .replace(/^\d+$/, '')
                            .replace(/展开\s*\d+\s*条回复/, '')
                            .replace(/\d{2}-\d{2}/, '')
                            .replace(/作者$/, '')
                            .replace(/置顶评论$/, '')
                            .trim();
                        
                        if (contentText.length > 5) {
                            comment.content = contentText;
                        }
                        
                        // 查找时间
                        const timeMatch = commentEl.textContent.match(/\d{2}-\d{2}/);
                        if (timeMatch) comment.time = timeMatch[0];
                        
                        // 查找点赞数
                        const likeElements = commentEl.querySelectorAll('[class*="like"], [class*="heart"]');
                        for (const likeEl of likeElements) {
                            const likeText = likeEl.textContent.trim();
                            if (/^\d+$/.test(likeText)) {
                                comment.likes = likeText;
                                break;
                            }
                        }
                        
                        // 查找二级回复
                        const replyContainers = commentEl.querySelectorAll('.reply-container, .sub-comment, [class*="reply"]');
                        
                        replyContainers.forEach((replyContainer, replyIndex) => {
                            try {
                                // 查找回复容器内的所有可能的回复
                                const replyElements = replyContainer.querySelectorAll('div');
                                
                                replyElements.forEach(replyEl => {
                                    const replyText = replyEl.textContent?.trim() || '';
                                    const hasAvatar = replyEl.querySelector('img[src*="avatar"]');
                                    const hasUser = replyEl.querySelector('[class*="user"], [class*="name"]');
                                    
                                    if (hasAvatar && hasUser && replyText.length > 5) {
                                        const reply = {
                                            id: `${comment.id}-${comment.replies.length + 1}`,
                                            type: 'reply',
                                            user: '',
                                            content: '',
                                            time: '',
                                            likes: ''
                                        };
                                        
                                        // 提取回复用户名
                                        const replyUserEl = replyEl.querySelector('[class*="user"], [class*="name"]');
                                        if (replyUserEl) reply.user = replyUserEl.textContent.trim();
                                        
                                        // 提取回复内容
                                        let replyContent = replyText;
                                        if (reply.user) {
                                            replyContent = replyContent.replace(reply.user, '').trim();
                                        }
                                        
                                        // 清理回复内容
                                        replyContent = replyContent
                                            .replace(/^回复$/, '')
                                            .replace(/^赞$/, '')
                                            .replace(/^\d+$/, '')
                                            .replace(/\d{2}-\d{2}/, '')
                                            .trim();
                                        
                                        if (replyContent.length > 2) {
                                            reply.content = replyContent;
                                            
                                            // 提取回复时间
                                            const replyTimeMatch = replyText.match(/\d{2}-\d{2}/);
                                            if (replyTimeMatch) reply.time = replyTimeMatch[0];
                                            
                                            comment.replies.push(reply);
                                        }
                                    }
                                });
                                
                            } catch (replyError) {
                                console.error('处理回复时出错:', replyError);
                            }
                        });
                        
                        // 只添加有内容的评论
                        if (comment.content || comment.user) {
                            comments.push(comment);
                        }
                        
                    } catch (commentError) {
                        console.error('处理评论时出错:', commentError);
                    }
                });
                
                return {
                    totalComments: comments.length,
                    totalReplies: comments.reduce((sum, comment) => sum + comment.replies.length, 0),
                    comments: comments,
                    timestamp: new Date().toISOString()
                };
                
            } catch (error) {
                console.error('爬取评论时出错:', error);
                return { comments: [], error: error.message };
            }
        });
        
        return commentsData;
    }

    async scrapeNoteCommentsV2() {
        console.log('🕷️ 启动小红书评论爬取器 V2...');
        console.log('');

        let browser = null;

        try {
            console.log('🔗 连接到比特浏览器...');
            browser = await puppeteer.connect({
                browserURL: `http://127.0.0.1:${this.debugPort}`,
                defaultViewport: null
            });

            const pages = await browser.pages();
            const xiaohongshuPage = pages.find(page => 
                page.url().includes('xiaohongshu.com/explore/')
            );

            if (!xiaohongshuPage) {
                throw new Error('未找到小红书笔记页面');
            }

            console.log('✅ 找到小红书笔记页面');
            console.log('📄 当前URL:', xiaohongshuPage.url());
            console.log('');

            // 等待页面加载
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 1. 滚动加载更多评论
            console.log('📜 滚动加载更多评论...');
            for (let i = 0; i < 3; i++) {
                await xiaohongshuPage.evaluate(() => {
                    window.scrollTo(0, document.body.scrollHeight);
                });
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // 2. 智能展开回复
            const expandedCount = await this.expandRepliesV2(xiaohongshuPage);

            // 3. 爬取评论数据
            const commentsData = await this.scrapeCommentsV2(xiaohongshuPage);
            
            console.log('✅ 评论爬取完成');
            console.log('   一级评论:', commentsData.totalComments || 0, '条');
            console.log('   二级回复:', commentsData.totalReplies || 0, '条');
            console.log('   展开操作:', expandedCount, '次');

            // 4. 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const urlMatch = xiaohongshuPage.url().match(/explore\/([a-f0-9]+)/);
            const noteId = urlMatch ? urlMatch[1] : 'unknown';
            
            const fullData = {
                scrapeTime: new Date().toISOString(),
                noteId: noteId,
                pageUrl: xiaohongshuPage.url(),
                expandAttempts: expandedCount,
                commentsData: commentsData,
                summary: {
                    totalComments: commentsData.totalComments || 0,
                    totalReplies: commentsData.totalReplies || 0,
                    totalInteractions: (commentsData.totalComments || 0) + (commentsData.totalReplies || 0)
                }
            };

            this.saveToFile(fullData, `comments_v2_${noteId}_${timestamp}.json`);

            console.log('');
            console.log('🎉 评论爬取完成！');
            console.log('📊 爬取统计:');
            console.log(`   💬 一级评论: ${commentsData.totalComments || 0} 条`);
            console.log(`   🔄 二级回复: ${commentsData.totalReplies || 0} 条`);
            console.log(`   📈 总互动: ${fullData.summary.totalInteractions} 条`);
            console.log(`   🎯 展开尝试: ${expandedCount} 次`);

            return fullData;

        } catch (error) {
            console.error('❌ 爬取失败:', error.message);
            return null;
        } finally {
            if (browser) {
                await browser.disconnect();
            }
        }
    }
}

if (require.main === module) {
    const scraper = new XiaohongshuCommentsV2();
    scraper.scrapeNoteCommentsV2().catch(console.error);
}

module.exports = XiaohongshuCommentsV2;
