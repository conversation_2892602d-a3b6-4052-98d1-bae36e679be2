#!/usr/bin/env node

/**
 * 🔍 小红书最终评论爬取器
 * 基于页面状态检查结果的优化版本
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class XiaohongshuFinalScraper {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`   💾 数据已保存到: ${filepath}`);
    }

    /**
     * 最终版评论爬取
     */
    async scrapeFinalComments(page) {
        console.log('💬 执行最终版评论爬取...');
        
        const commentsData = await page.evaluate(() => {
            const comments = [];
            
            try {
                console.log('开始爬取评论...');
                
                // 1. 首先获取所有父评论
                const parentComments = document.querySelectorAll('.parent-comment');
                console.log(`找到 ${parentComments.length} 个父评论`);
                
                parentComments.forEach((parentEl, index) => {
                    try {
                        const comment = {
                            id: index + 1,
                            type: 'primary',
                            user: '',
                            avatar: '',
                            content: '',
                            time: '',
                            likes: '',
                            replies: [],
                            debug: {
                                rawText: parentEl.textContent?.trim() || '',
                                className: parentEl.className
                            }
                        };
                        
                        // 提取用户信息
                        const userEl = parentEl.querySelector('[class*="user"], [class*="name"]');
                        if (userEl) {
                            comment.user = userEl.textContent.trim();
                        }
                        
                        // 提取头像
                        const avatarEl = parentEl.querySelector('img[src*="avatar"]');
                        if (avatarEl) {
                            comment.avatar = avatarEl.src;
                        }
                        
                        // 提取评论内容 - 更精确的方法
                        let contentText = parentEl.textContent.trim();
                        
                        // 移除用户名
                        if (comment.user) {
                            contentText = contentText.replace(comment.user, '').trim();
                        }
                        
                        // 移除时间
                        contentText = contentText.replace(/\d{2}-\d{2}/g, '').trim();
                        
                        // 移除点赞数和回复数
                        contentText = contentText.replace(/^\d+$/, '').trim();
                        contentText = contentText.replace(/\d+回复$/, '').trim();
                        contentText = contentText.replace(/回复$/, '').trim();
                        contentText = contentText.replace(/赞$/, '').trim();
                        contentText = contentText.replace(/作者$/, '').trim();
                        contentText = contentText.replace(/置顶评论$/, '').trim();
                        
                        // 清理多余空格
                        contentText = contentText.replace(/\s+/g, ' ').trim();
                        
                        if (contentText.length > 3) {
                            comment.content = contentText;
                        }
                        
                        // 提取时间
                        const timeMatch = parentEl.textContent.match(/(\d{2}-\d{2})/);
                        if (timeMatch) {
                            comment.time = timeMatch[1];
                        }
                        
                        // 提取点赞数
                        const likeMatch = parentEl.textContent.match(/(\d+)(?=回复|$)/);
                        if (likeMatch) {
                            comment.likes = likeMatch[1];
                        }
                        
                        // 查找对应的回复容器
                        const replyContainers = document.querySelectorAll('.reply-container');
                        console.log(`找到 ${replyContainers.length} 个回复容器`);
                        
                        // 尝试找到属于这个评论的回复
                        replyContainers.forEach(replyContainer => {
                            // 检查回复容器是否在当前评论附近
                            const containerText = replyContainer.textContent || '';
                            
                            // 如果回复容器包含当前评论的用户名，可能是相关的
                            if (comment.user && containerText.includes(comment.user)) {
                                
                                // 在回复容器中查找具体的回复元素
                                const replyElements = replyContainer.querySelectorAll('div');
                                
                                replyElements.forEach(replyEl => {
                                    const replyText = replyEl.textContent?.trim() || '';
                                    const hasAvatar = replyEl.querySelector('img[src*="avatar"]');
                                    const hasUser = replyEl.querySelector('[class*="user"], [class*="name"]');
                                    
                                    // 确保这是一个有效的回复
                                    if (hasAvatar && hasUser && replyText.length > 5 && 
                                        !replyText.includes(comment.user) && // 不是原评论
                                        replyText.length < 500) { // 不是整个容器的文本
                                        
                                        const reply = {
                                            id: `${comment.id}-${comment.replies.length + 1}`,
                                            type: 'reply',
                                            user: '',
                                            content: '',
                                            time: '',
                                            likes: '',
                                            debug: {
                                                rawText: replyText,
                                                className: replyEl.className
                                            }
                                        };
                                        
                                        // 提取回复用户名
                                        const replyUserEl = replyEl.querySelector('[class*="user"], [class*="name"]');
                                        if (replyUserEl) {
                                            reply.user = replyUserEl.textContent.trim();
                                        }
                                        
                                        // 提取回复内容
                                        let replyContent = replyText;
                                        if (reply.user) {
                                            replyContent = replyContent.replace(reply.user, '').trim();
                                        }
                                        
                                        // 清理回复内容
                                        replyContent = replyContent
                                            .replace(/^回复$/, '')
                                            .replace(/^赞$/, '')
                                            .replace(/^\d+$/, '')
                                            .replace(/\d{2}-\d{2}/g, '')
                                            .replace(/\s+/g, ' ')
                                            .trim();
                                        
                                        if (replyContent.length > 2) {
                                            reply.content = replyContent;
                                            
                                            // 提取回复时间
                                            const replyTimeMatch = replyText.match(/(\d{2}-\d{2})/);
                                            if (replyTimeMatch) {
                                                reply.time = replyTimeMatch[1];
                                            }
                                            
                                            comment.replies.push(reply);
                                        }
                                    }
                                });
                            }
                        });
                        
                        // 添加评论到结果中
                        if (comment.content || comment.user || comment.replies.length > 0) {
                            comments.push(comment);
                        }
                        
                    } catch (commentError) {
                        console.error('处理评论时出错:', commentError);
                    }
                });
                
                // 2. 如果父评论数量不够，尝试使用comment-item
                if (comments.length < 10) {
                    console.log('父评论数量较少，尝试使用comment-item...');
                    
                    const commentItems = document.querySelectorAll('.comment-item');
                    console.log(`找到 ${commentItems.length} 个comment-item`);
                    
                    commentItems.forEach((itemEl, index) => {
                        // 避免重复添加
                        const existingComment = comments.find(c => 
                            c.debug && c.debug.rawText === itemEl.textContent?.trim()
                        );
                        
                        if (!existingComment) {
                            try {
                                const comment = {
                                    id: comments.length + 1,
                                    type: 'primary',
                                    user: '',
                                    avatar: '',
                                    content: '',
                                    time: '',
                                    likes: '',
                                    replies: [],
                                    debug: {
                                        rawText: itemEl.textContent?.trim() || '',
                                        className: itemEl.className,
                                        source: 'comment-item'
                                    }
                                };
                                
                                // 提取用户信息
                                const userEl = itemEl.querySelector('[class*="user"], [class*="name"]');
                                if (userEl) {
                                    comment.user = userEl.textContent.trim();
                                }
                                
                                // 提取头像
                                const avatarEl = itemEl.querySelector('img[src*="avatar"]');
                                if (avatarEl) {
                                    comment.avatar = avatarEl.src;
                                }
                                
                                // 提取内容
                                let contentText = itemEl.textContent.trim();
                                if (comment.user) {
                                    contentText = contentText.replace(comment.user, '').trim();
                                }
                                contentText = contentText.replace(/\d{2}-\d{2}/g, '').trim();
                                contentText = contentText.replace(/\d+回复$/, '').trim();
                                contentText = contentText.replace(/回复$/, '').trim();
                                contentText = contentText.replace(/赞$/, '').trim();
                                contentText = contentText.replace(/\s+/g, ' ').trim();
                                
                                if (contentText.length > 3) {
                                    comment.content = contentText;
                                }
                                
                                // 提取时间
                                const timeMatch = itemEl.textContent.match(/(\d{2}-\d{2})/);
                                if (timeMatch) {
                                    comment.time = timeMatch[1];
                                }
                                
                                // 查找这个评论项内的回复
                                const replyEls = itemEl.querySelectorAll('[class*="reply"]');
                                replyEls.forEach(replyEl => {
                                    const replyText = replyEl.textContent?.trim() || '';
                                    const hasAvatar = replyEl.querySelector('img[src*="avatar"]');
                                    const hasUser = replyEl.querySelector('[class*="user"], [class*="name"]');
                                    
                                    if (hasAvatar && hasUser && replyText.length > 5 && replyText.length < 200) {
                                        const reply = {
                                            id: `${comment.id}-${comment.replies.length + 1}`,
                                            type: 'reply',
                                            user: '',
                                            content: '',
                                            time: '',
                                            debug: {
                                                rawText: replyText,
                                                className: replyEl.className
                                            }
                                        };
                                        
                                        const replyUserEl = replyEl.querySelector('[class*="user"], [class*="name"]');
                                        if (replyUserEl) {
                                            reply.user = replyUserEl.textContent.trim();
                                        }
                                        
                                        let replyContent = replyText;
                                        if (reply.user) {
                                            replyContent = replyContent.replace(reply.user, '').trim();
                                        }
                                        replyContent = replyContent.replace(/\d{2}-\d{2}/g, '').trim();
                                        replyContent = replyContent.replace(/回复$/, '').trim();
                                        replyContent = replyContent.replace(/赞$/, '').trim();
                                        replyContent = replyContent.replace(/\s+/g, ' ').trim();
                                        
                                        if (replyContent.length > 2) {
                                            reply.content = replyContent;
                                            
                                            const replyTimeMatch = replyText.match(/(\d{2}-\d{2})/);
                                            if (replyTimeMatch) {
                                                reply.time = replyTimeMatch[1];
                                            }
                                            
                                            comment.replies.push(reply);
                                        }
                                    }
                                });
                                
                                if (comment.content || comment.user || comment.replies.length > 0) {
                                    comments.push(comment);
                                }
                                
                            } catch (itemError) {
                                console.error('处理comment-item时出错:', itemError);
                            }
                        }
                    });
                }
                
                console.log(`最终处理了 ${comments.length} 个评论`);
                
                return {
                    totalComments: comments.length,
                    totalReplies: comments.reduce((sum, comment) => sum + comment.replies.length, 0),
                    comments: comments,
                    timestamp: new Date().toISOString()
                };
                
            } catch (error) {
                console.error('爬取评论时出错:', error);
                return { 
                    comments: [], 
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
            }
        });
        
        return commentsData;
    }

    async scrapeFinalMain() {
        console.log('🕷️ 启动最终版评论爬取器...');
        console.log('');

        let browser = null;

        try {
            console.log('🔗 连接到比特浏览器...');
            browser = await puppeteer.connect({
                browserURL: `http://127.0.0.1:${this.debugPort}`,
                defaultViewport: null
            });

            const pages = await browser.pages();
            const xiaohongshuPage = pages.find(page => 
                page.url().includes('xiaohongshu.com/explore/')
            );

            if (!xiaohongshuPage) {
                throw new Error('未找到小红书笔记页面');
            }

            console.log('✅ 找到小红书笔记页面');
            console.log('📄 当前URL:', xiaohongshuPage.url());
            console.log('');

            // 等待页面加载
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 执行最终版爬取
            const commentsData = await this.scrapeFinalComments(xiaohongshuPage);
            
            console.log('✅ 最终版评论爬取完成');
            console.log('   一级评论:', commentsData.totalComments || 0, '条');
            console.log('   二级回复:', commentsData.totalReplies || 0, '条');

            // 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const urlMatch = xiaohongshuPage.url().match(/explore\/([a-f0-9]+)/);
            const noteId = urlMatch ? urlMatch[1] : 'unknown';
            
            const fullData = {
                scrapeTime: new Date().toISOString(),
                noteId: noteId,
                pageUrl: xiaohongshuPage.url(),
                commentsData: commentsData,
                summary: {
                    totalComments: commentsData.totalComments || 0,
                    totalReplies: commentsData.totalReplies || 0,
                    totalInteractions: (commentsData.totalComments || 0) + (commentsData.totalReplies || 0),
                    hasErrors: !!commentsData.error
                }
            };

            this.saveToFile(fullData, `final_comments_${noteId}_${timestamp}.json`);

            console.log('');
            console.log('🎉 最终版评论爬取完成！');
            console.log('📊 最终统计:');
            console.log(`   💬 一级评论: ${commentsData.totalComments || 0} 条`);
            console.log(`   🔄 二级回复: ${commentsData.totalReplies || 0} 条`);
            console.log(`   📈 总互动数: ${fullData.summary.totalInteractions} 条`);
            console.log(`   📁 数据文件: final_comments_${noteId}_${timestamp}.json`);

            return fullData;

        } catch (error) {
            console.error('❌ 爬取失败:', error.message);
            return null;
        } finally {
            if (browser) {
                await browser.disconnect();
            }
        }
    }
}

if (require.main === module) {
    const scraper = new XiaohongshuFinalScraper();
    scraper.scrapeFinalMain().catch(console.error);
}

module.exports = XiaohongshuFinalScraper;
