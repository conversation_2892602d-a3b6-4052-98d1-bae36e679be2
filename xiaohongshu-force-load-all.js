#!/usr/bin/env node

/**
 * 🔍 小红书强制加载全部评论器
 * 使用多种策略强制加载所有1472条评论
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class XiaohongshuForceLoadAllScraper {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
        this.targetCommentCount = 1472;
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`   💾 数据已保存到: ${filepath}`);
    }

    /**
     * 强制加载所有评论的多种策略
     */
    async forceLoadAllComments(page) {
        console.log(`🚀 强制加载所有评论 (目标: ${this.targetCommentCount}条)...`);
        
        let currentCommentCount = 0;
        let attempts = 0;
        const maxAttempts = 200;
        
        while (attempts < maxAttempts && currentCommentCount < this.targetCommentCount * 0.9) {
            attempts++;
            
            console.log(`   🔄 第 ${attempts} 次尝试...`);
            
            // 策略1: 滚动到底部
            await page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 策略2: 查找并点击所有可能的加载按钮
            const clickedButtons = await page.evaluate(() => {
                let clicked = 0;
                const buttonTexts = [
                    '加载更多', '查看更多', '更多评论', '展开更多', 
                    '显示更多', '加载中', '更多', '展开', '查看全部',
                    'Load more', 'Show more', 'View more'
                ];
                
                const allElements = document.querySelectorAll('*');
                
                for (const el of allElements) {
                    const text = el.textContent?.trim() || '';
                    
                    for (const buttonText of buttonTexts) {
                        if (text.includes(buttonText)) {
                            try {
                                // 多种点击方式
                                el.click();
                                el.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                                el.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
                                el.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
                                
                                // 尝试点击父元素
                                if (el.parentElement) {
                                    el.parentElement.click();
                                }
                                
                                clicked++;
                                break;
                            } catch (e) {
                                // 忽略错误
                            }
                        }
                    }
                }
                
                return clicked;
            });
            
            if (clickedButtons > 0) {
                console.log(`   🎯 点击了 ${clickedButtons} 个按钮`);
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
            
            // 策略3: 模拟键盘操作
            if (attempts % 10 === 0) {
                await page.keyboard.press('End'); // 按End键到底部
                await page.keyboard.press('PageDown'); // 按PageDown
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // 策略4: 触发滚动事件
            await page.evaluate(() => {
                // 触发各种滚动事件
                window.dispatchEvent(new Event('scroll'));
                window.dispatchEvent(new Event('resize'));
                
                // 尝试触发懒加载
                const scrollEvent = new Event('scroll', { bubbles: true });
                document.dispatchEvent(scrollEvent);
                
                // 模拟用户滚动行为
                for (let i = 0; i < 5; i++) {
                    window.scrollBy(0, 100);
                }
            });
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 策略5: 检查并点击分页或加载更多的特殊元素
            const specialClicks = await page.evaluate(() => {
                let clicked = 0;
                
                // 查找可能的分页元素
                const paginationSelectors = [
                    '.pagination', '.load-more', '.more-btn', '.next-page',
                    '[class*="page"]', '[class*="load"]', '[class*="more"]',
                    'button', 'a[href*="page"]'
                ];
                
                for (const selector of paginationSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (const el of elements) {
                        const text = el.textContent?.trim() || '';
                        if (text && (text.includes('更多') || text.includes('下一页') || 
                                   text.includes('加载') || text.includes('next') || 
                                   text.includes('more'))) {
                            try {
                                el.click();
                                clicked++;
                            } catch (e) {
                                // 忽略错误
                            }
                        }
                    }
                }
                
                return clicked;
            });
            
            if (specialClicks > 0) {
                console.log(`   🎯 点击了 ${specialClicks} 个特殊元素`);
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
            
            // 检查当前评论数量
            currentCommentCount = await page.evaluate(() => {
                const selectors = [
                    '.comment-item',
                    '.parent-comment',
                    '[class*="comment"]',
                    '.note-comment'
                ];
                
                let maxCount = 0;
                for (const selector of selectors) {
                    const count = document.querySelectorAll(selector).length;
                    maxCount = Math.max(maxCount, count);
                }
                return maxCount;
            });
            
            if (attempts % 5 === 0) {
                console.log(`   📊 当前评论数: ${currentCommentCount}`);
            }
            
            // 如果连续多次没有增加，尝试刷新策略
            if (attempts % 20 === 0) {
                console.log(`   🔄 尝试刷新策略...`);
                
                // 策略6: 尝试触发AJAX请求
                await page.evaluate(() => {
                    // 尝试触发可能的AJAX加载
                    if (window.fetch) {
                        // 这里可能需要根据实际的API调用进行调整
                        console.log('尝试触发AJAX请求...');
                    }
                    
                    // 尝试触发IntersectionObserver
                    const observer = new IntersectionObserver(() => {});
                    const elements = document.querySelectorAll('div');
                    elements.forEach(el => observer.observe(el));
                });
                
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            // 如果评论数量达到一定阈值，可以适当放宽条件
            if (currentCommentCount > 100) {
                console.log(`   ✅ 已加载 ${currentCommentCount} 条评论，继续尝试...`);
            }
        }
        
        console.log(`   ✅ 强制加载完成，共尝试 ${attempts} 次，最终评论数: ${currentCommentCount}`);
        return currentCommentCount;
    }

    /**
     * 深度采集评论
     */
    async deepScrapeComments(page) {
        console.log('💬 深度采集评论...');
        
        const commentsData = await page.evaluate(() => {
            const comments = [];
            const processedTexts = new Set();
            
            try {
                console.log('开始深度采集评论...');
                
                // 更全面的选择器策略
                const allSelectors = [
                    '.comment-item',
                    '.parent-comment',
                    '.note-comment',
                    '.user-comment',
                    '[class*="comment"]',
                    '[data-comment]',
                    '[id*="comment"]'
                ];
                
                let allCommentElements = [];
                
                // 收集所有可能的评论元素
                for (const selector of allSelectors) {
                    const elements = document.querySelectorAll(selector);
                    console.log(`选择器 ${selector}: ${elements.length} 个元素`);
                    
                    Array.from(elements).forEach(el => {
                        if (!allCommentElements.includes(el)) {
                            allCommentElements.push(el);
                        }
                    });
                }
                
                console.log(`总共收集到 ${allCommentElements.length} 个可能的评论元素`);
                
                // 处理每个元素
                allCommentElements.forEach((el, index) => {
                    try {
                        const rawText = el.textContent?.trim() || '';
                        
                        // 基本过滤
                        if (rawText.length < 5 || processedTexts.has(rawText)) {
                            return;
                        }
                        
                        // 检查是否包含用户信息
                        const hasAvatar = el.querySelector('img[src*="avatar"]') || 
                                         el.querySelector('img[src*="user"]') ||
                                         el.querySelector('[class*="avatar"]');
                        
                        const hasUser = el.querySelector('[class*="user"]') || 
                                       el.querySelector('[class*="name"]') ||
                                       el.querySelector('[data-user]');
                        
                        // 更宽松的条件
                        if (!hasAvatar && !hasUser && rawText.length < 20) {
                            return;
                        }
                        
                        processedTexts.add(rawText);
                        
                        const comment = {
                            id: comments.length + 1,
                            type: 'primary',
                            user: '',
                            avatar: '',
                            content: '',
                            time: '',
                            likes: '',
                            replies: [],
                            debug: {
                                rawText: rawText.substring(0, 200) + '...', // 限制长度
                                className: el.className,
                                tagName: el.tagName,
                                elementIndex: index
                            }
                        };
                        
                        // 提取用户信息
                        const userSelectors = [
                            '[class*="user"]', '[class*="name"]', '[data-user]',
                            '.username', '.user-name', '.author'
                        ];
                        
                        for (const userSelector of userSelectors) {
                            const userEl = el.querySelector(userSelector);
                            if (userEl && userEl.textContent.trim()) {
                                comment.user = userEl.textContent.trim();
                                break;
                            }
                        }
                        
                        // 提取头像
                        const avatarSelectors = [
                            'img[src*="avatar"]', 'img[src*="user"]', 
                            '[class*="avatar"] img', '.user-avatar img'
                        ];
                        
                        for (const avatarSelector of avatarSelectors) {
                            const avatarEl = el.querySelector(avatarSelector);
                            if (avatarEl && avatarEl.src) {
                                comment.avatar = avatarEl.src;
                                break;
                            }
                        }
                        
                        // 提取内容
                        let contentText = rawText;
                        
                        // 清理内容
                        if (comment.user) {
                            contentText = contentText.replace(new RegExp(comment.user, 'g'), '').trim();
                        }
                        
                        // 移除常见UI文字
                        contentText = contentText
                            .replace(/\d{2}-\d{2}/g, '')
                            .replace(/\d+回复$/g, '')
                            .replace(/回复$/g, '')
                            .replace(/赞$/g, '')
                            .replace(/^\d+$/g, '')
                            .replace(/作者$/g, '')
                            .replace(/置顶评论$/g, '')
                            .replace(/\s+/g, ' ')
                            .trim();
                        
                        if (contentText.length > 2) {
                            comment.content = contentText;
                        }
                        
                        // 提取时间
                        const timeMatch = rawText.match(/(\d{2}-\d{2})/);
                        if (timeMatch) {
                            comment.time = timeMatch[1];
                        }
                        
                        // 提取点赞数
                        const likeMatch = rawText.match(/(\d+)(?=回复|赞|$)/);
                        if (likeMatch) {
                            comment.likes = likeMatch[1];
                        }
                        
                        // 查找回复
                        const replySelectors = [
                            '[class*="reply"]', '.sub-comment', '.comment-sub',
                            '[class*="sub"]', '.nested-comment'
                        ];
                        
                        for (const replySelector of replySelectors) {
                            const replyElements = el.querySelectorAll(replySelector);
                            
                            replyElements.forEach(replyEl => {
                                const replyText = replyEl.textContent?.trim() || '';
                                
                                if (replyText.length > 3 && replyText.length < 500 && 
                                    !processedTexts.has(replyText)) {
                                    
                                    processedTexts.add(replyText);
                                    
                                    const reply = {
                                        id: `${comment.id}-${comment.replies.length + 1}`,
                                        type: 'reply',
                                        user: '',
                                        content: '',
                                        time: '',
                                        debug: {
                                            rawText: replyText.substring(0, 100) + '...',
                                            className: replyEl.className
                                        }
                                    };
                                    
                                    // 提取回复用户
                                    for (const userSelector of userSelectors) {
                                        const replyUserEl = replyEl.querySelector(userSelector);
                                        if (replyUserEl && replyUserEl.textContent.trim()) {
                                            reply.user = replyUserEl.textContent.trim();
                                            break;
                                        }
                                    }
                                    
                                    // 提取回复内容
                                    let replyContent = replyText;
                                    if (reply.user) {
                                        replyContent = replyContent.replace(new RegExp(reply.user, 'g'), '').trim();
                                    }
                                    
                                    replyContent = replyContent
                                        .replace(/\d{2}-\d{2}/g, '')
                                        .replace(/回复$/g, '')
                                        .replace(/赞$/g, '')
                                        .replace(/^\d+$/g, '')
                                        .replace(/\s+/g, ' ')
                                        .trim();
                                    
                                    if (replyContent.length > 1) {
                                        reply.content = replyContent;
                                        
                                        const replyTimeMatch = replyText.match(/(\d{2}-\d{2})/);
                                        if (replyTimeMatch) {
                                            reply.time = replyTimeMatch[1];
                                        }
                                        
                                        comment.replies.push(reply);
                                    }
                                }
                            });
                        }
                        
                        // 添加评论
                        if (comment.content || comment.user || comment.replies.length > 0) {
                            comments.push(comment);
                        }
                        
                    } catch (error) {
                        console.error('处理评论时出错:', error);
                    }
                });
                
                console.log(`深度采集完成，共处理 ${comments.length} 个评论`);
                
                return {
                    totalComments: comments.length,
                    totalReplies: comments.reduce((sum, comment) => sum + comment.replies.length, 0),
                    comments: comments,
                    timestamp: new Date().toISOString()
                };
                
            } catch (error) {
                console.error('深度采集时出错:', error);
                return { 
                    comments: [], 
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
            }
        });
        
        return commentsData;
    }

    async forceLoadMain() {
        console.log('🕷️ 启动强制加载全部评论器...');
        console.log(`🎯 目标: 强制加载 ${this.targetCommentCount} 条评论`);
        console.log('');

        let browser = null;

        try {
            console.log('🔗 连接到比特浏览器...');
            browser = await puppeteer.connect({
                browserURL: `http://127.0.0.1:${this.debugPort}`,
                defaultViewport: null
            });

            const pages = await browser.pages();
            const xiaohongshuPage = pages.find(page => 
                page.url().includes('xiaohongshu.com/explore/')
            );

            if (!xiaohongshuPage) {
                throw new Error('未找到小红书笔记页面');
            }

            console.log('✅ 找到小红书笔记页面');
            console.log('📄 当前URL:', xiaohongshuPage.url());
            console.log('');

            // 等待页面加载
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 1. 强制加载所有评论
            const loadedCount = await this.forceLoadAllComments(xiaohongshuPage);

            // 2. 深度采集评论
            const commentsData = await this.deepScrapeComments(xiaohongshuPage);
            
            console.log('✅ 强制加载和采集完成');
            console.log('   强制加载数:', loadedCount);
            console.log('   一级评论:', commentsData.totalComments || 0, '条');
            console.log('   二级回复:', commentsData.totalReplies || 0, '条');

            // 3. 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const urlMatch = xiaohongshuPage.url().match(/explore\/([a-f0-9]+)/);
            const noteId = urlMatch ? urlMatch[1] : 'unknown';
            
            const fullData = {
                scrapeTime: new Date().toISOString(),
                noteId: noteId,
                pageUrl: xiaohongshuPage.url(),
                targetCommentCount: this.targetCommentCount,
                loadedCount: loadedCount,
                commentsData: commentsData,
                summary: {
                    totalComments: commentsData.totalComments || 0,
                    totalReplies: commentsData.totalReplies || 0,
                    totalInteractions: (commentsData.totalComments || 0) + (commentsData.totalReplies || 0),
                    completionRate: ((commentsData.totalComments || 0) / this.targetCommentCount * 100).toFixed(1) + '%',
                    hasErrors: !!commentsData.error
                }
            };

            this.saveToFile(fullData, `force_load_all_${noteId}_${timestamp}.json`);

            console.log('');
            console.log('🎉 强制加载全部评论完成！');
            console.log('📊 最终统计:');
            console.log(`   💬 一级评论: ${commentsData.totalComments || 0} 条`);
            console.log(`   🔄 二级回复: ${commentsData.totalReplies || 0} 条`);
            console.log(`   📈 总互动数: ${fullData.summary.totalInteractions} 条`);
            console.log(`   🎯 完成度: ${fullData.summary.completionRate}`);
            console.log(`   📁 数据文件: force_load_all_${noteId}_${timestamp}.json`);

            return fullData;

        } catch (error) {
            console.error('❌ 强制加载失败:', error.message);
            return null;
        } finally {
            if (browser) {
                await browser.disconnect();
            }
        }
    }
}

if (require.main === module) {
    const scraper = new XiaohongshuForceLoadAllScraper();
    scraper.forceLoadMain().catch(console.error);
}

module.exports = XiaohongshuForceLoadAllScraper;
