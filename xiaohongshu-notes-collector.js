#!/usr/bin/env node

/**
 * 📝 小红书笔记采集器
 * 采集用户发布的所有笔记信息：标题、图片、点赞数、收藏数、评论数等
 */

const axios = require('axios');
const WebSocket = require('ws');

class XiaohongshuNotesCollector {
    constructor() {
        this.debugPort = null; // 动态获取
        this.currentTab = null;
    }

    // 🔍 主要采集方法
    async collectAllNotes() {
        console.log('📝 开始采集小红书笔记信息...\n');

        try {
            // 1. 获取当前调试端口
            await this.getCurrentDebugPort();

            // 2. 找到小红书标签页
            const tab = await this.findXiaohongshuTab();
            if (!tab) {
                throw new Error('未找到小红书标签页');
            }

            console.log(`🎯 目标页面: ${tab.title}`);
            console.log(`🔗 URL: ${tab.url}\n`);

            // 2. 通过WebSocket连接获取笔记数据
            const notesData = await this.extractNotesViaWebSocket(tab);
            
            console.log('✅ 笔记数据采集完成!');
            return notesData;

        } catch (error) {
            console.error('❌ 采集失败:', error.message);
            throw error;
        }
    }

    // � 获取当前调试端口
    async getCurrentDebugPort() {
        try {
            console.log('🔌 获取当前浏览器调试端口...');

            // 从本地服务器获取浏览器状态
            const response = await axios.post('http://localhost:3000/api/xiaohongshu/bitbrowser/start', {}, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 10000
            });

            if (response.data.success && response.data.data && response.data.data.result) {
                const httpInfo = response.data.data.result.http;
                if (httpInfo) {
                    this.debugPort = httpInfo.split(':')[1];
                    console.log(`✅ 获取到调试端口: ${this.debugPort}`);
                    return this.debugPort;
                }
            }

            // 如果API获取失败，尝试常见端口
            console.log('⚠️ API获取端口失败，尝试常见端口...');
            const commonPorts = [63524, 51859, 58222, 9222, 9223, 9224];

            for (const port of commonPorts) {
                try {
                    await axios.get(`http://127.0.0.1:${port}/json`, { timeout: 2000 });
                    this.debugPort = port;
                    console.log(`✅ 找到可用端口: ${port}`);
                    return port;
                } catch (error) {
                    // 继续尝试下一个端口
                }
            }

            throw new Error('未找到可用的调试端口');

        } catch (error) {
            console.error('❌ 获取调试端口失败:', error.message);
            throw error;
        }
    }

    // �🔍 查找小红书标签页
    async findXiaohongshuTab() {
        try {
            const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
                timeout: 5000
            });

            const tabs = response.data;
            return tabs.find(tab =>
                tab.url && (
                    tab.url.includes('xiaohongshu.com') ||
                    tab.url.includes('creator.xiaohongshu.com') ||
                    tab.title.includes('小红书')
                )
            );
        } catch (error) {
            console.error('❌ 无法获取标签页列表:', error.message);
            return null;
        }
    }

    // 🌐 通过WebSocket获取笔记数据
    async extractNotesViaWebSocket(tab) {
        return new Promise((resolve, reject) => {
            const wsUrl = tab.webSocketDebuggerUrl;
            console.log(`🔌 连接WebSocket: ${wsUrl}`);

            const ws = new WebSocket(wsUrl);
            let requestId = 1;

            ws.on('open', () => {
                console.log('✅ WebSocket连接成功');

                // 启用Runtime域
                ws.send(JSON.stringify({
                    id: requestId++,
                    method: 'Runtime.enable'
                }));

                // 执行笔记数据提取脚本
                setTimeout(() => {
                    const extractScript = this.buildNotesExtractionScript();
                    ws.send(JSON.stringify({
                        id: requestId++,
                        method: 'Runtime.evaluate',
                        params: {
                            expression: extractScript,
                            returnByValue: true
                        }
                    }));
                }, 1000);
            });

            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    
                    if (message.result && message.result.result && message.result.result.value) {
                        const extractedData = message.result.result.value;
                        console.log('📊 成功提取笔记数据');
                        
                        // 处理提取的数据
                        const processedData = this.processNotesData(extractedData, tab);
                        ws.close();
                        resolve(processedData);
                    }
                } catch (error) {
                    console.error('❌ 解析WebSocket消息失败:', error.message);
                }
            });

            ws.on('error', (error) => {
                console.error('❌ WebSocket连接错误:', error.message);
                reject(error);
            });

            ws.on('close', () => {
                console.log('🔌 WebSocket连接已关闭');
            });

            // 超时处理
            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                    reject(new Error('WebSocket连接超时'));
                }
            }, 20000);
        });
    }

    // 📝 构建笔记数据提取脚本
    buildNotesExtractionScript() {
        return `
            (function() {
                try {
                    const result = {
                        timestamp: new Date().toISOString(),
                        url: window.location.href,
                        title: document.title,
                        notes: []
                    };

                    // 查找笔记容器的多种可能选择器
                    const noteContainerSelectors = [
                        '.note-item',
                        '.feed-item',
                        '[class*="note"]',
                        '[class*="feed"]',
                        '[class*="card"]',
                        '.explore-feed .note-item',
                        '.user-posted-feeds .note-item'
                    ];

                    let noteElements = [];
                    
                    // 尝试不同的选择器找到笔记元素
                    for (const selector of noteContainerSelectors) {
                        noteElements = document.querySelectorAll(selector);
                        if (noteElements.length > 0) {
                            console.log('找到笔记元素，使用选择器:', selector);
                            break;
                        }
                    }

                    // 如果没找到特定的笔记容器，尝试通用方法
                    if (noteElements.length === 0) {
                        // 查找包含图片和文字的卡片式元素
                        const allElements = document.querySelectorAll('div');
                        noteElements = Array.from(allElements).filter(el => {
                            const hasImage = el.querySelector('img');
                            const hasText = el.textContent.trim().length > 10;
                            const hasInteraction = el.textContent.includes('👍') || 
                                                 el.textContent.includes('❤️') || 
                                                 el.textContent.includes('💬') ||
                                                 el.querySelector('[class*="like"]') ||
                                                 el.querySelector('[class*="heart"]');
                            return hasImage && hasText && hasInteraction;
                        });
                    }

                    console.log('找到', noteElements.length, '个笔记元素');

                    // 提取每个笔记的信息
                    noteElements.forEach((noteElement, index) => {
                        try {
                            const noteData = {
                                index: index + 1,
                                id: noteElement.id || 'note_' + index
                            };

                            // 提取笔记标题
                            const titleSelectors = [
                                '.title',
                                '.note-title',
                                'h1', 'h2', 'h3', 'h4',
                                '[class*="title"]',
                                '.content .text'
                            ];
                            
                            for (const selector of titleSelectors) {
                                const titleElement = noteElement.querySelector(selector);
                                if (titleElement && titleElement.textContent.trim()) {
                                    noteData.title = titleElement.textContent.trim();
                                    break;
                                }
                            }

                            // 如果没找到标题，使用文本内容的前50个字符
                            if (!noteData.title) {
                                const textContent = noteElement.textContent.trim();
                                if (textContent.length > 10) {
                                    noteData.title = textContent.substring(0, 50) + (textContent.length > 50 ? '...' : '');
                                }
                            }

                            // 提取笔记图片
                            const images = noteElement.querySelectorAll('img');
                            noteData.images = [];
                            images.forEach(img => {
                                if (img.src && !img.src.includes('data:') && !img.src.includes('avatar')) {
                                    noteData.images.push({
                                        src: img.src,
                                        alt: img.alt || ''
                                    });
                                }
                            });

                            // 提取互动数据 (点赞、收藏、评论)
                            const interactionElements = noteElement.querySelectorAll('*');
                            noteData.interactions = {
                                likes: 0,
                                collects: 0,
                                comments: 0
                            };

                            interactionElements.forEach(el => {
                                const text = el.textContent.trim();
                                const number = parseInt(text);
                                
                                if (!isNaN(number) && number >= 0) {
                                    const context = el.parentElement ? el.parentElement.textContent.toLowerCase() : '';
                                    const className = el.className.toLowerCase();
                                    
                                    if (context.includes('赞') || context.includes('like') || className.includes('like')) {
                                        noteData.interactions.likes = number;
                                    } else if (context.includes('收藏') || context.includes('collect') || className.includes('collect')) {
                                        noteData.interactions.collects = number;
                                    } else if (context.includes('评论') || context.includes('comment') || className.includes('comment')) {
                                        noteData.interactions.comments = number;
                                    }
                                }
                            });

                            // 提取笔记链接
                            const linkElement = noteElement.querySelector('a[href*="/explore/"]') || 
                                              noteElement.querySelector('a[href*="/discovery/"]') ||
                                              noteElement.querySelector('a');
                            if (linkElement && linkElement.href) {
                                noteData.link = linkElement.href;
                            }

                            // 提取发布时间
                            const timeElements = noteElement.querySelectorAll('*');
                            timeElements.forEach(el => {
                                const text = el.textContent.trim();
                                if (text.includes('天前') || text.includes('小时前') || text.includes('分钟前') || 
                                    text.includes('昨天') || text.includes('前天') || text.match(/\\d{2}-\\d{2}/)) {
                                    noteData.publishTime = text;
                                }
                            });

                            // 只添加有效的笔记数据
                            if (noteData.title || noteData.images.length > 0) {
                                result.notes.push(noteData);
                            }

                        } catch (error) {
                            console.error('处理笔记', index, '时出错:', error.message);
                        }
                    });

                    // 添加页面统计信息
                    result.summary = {
                        totalNotes: result.notes.length,
                        totalImages: result.notes.reduce((sum, note) => sum + note.images.length, 0),
                        totalLikes: result.notes.reduce((sum, note) => sum + (note.interactions.likes || 0), 0),
                        totalCollects: result.notes.reduce((sum, note) => sum + (note.interactions.collects || 0), 0),
                        totalComments: result.notes.reduce((sum, note) => sum + (note.interactions.comments || 0), 0)
                    };

                    return result;
                    
                } catch (error) {
                    return {
                        error: error.message,
                        timestamp: new Date().toISOString()
                    };
                }
            })();
        `;
    }

    // 📊 处理笔记数据
    processNotesData(rawData, tab) {
        const processedData = {
            ...rawData,
            tabId: tab.id,
            webSocketUrl: tab.webSocketDebuggerUrl,
            dataSource: 'browser_websocket_notes',
            extractionMethod: 'enhanced_notes_analysis'
        };

        return processedData;
    }
}

// 🧪 测试笔记采集
async function testNotesCollection() {
    const collector = new XiaohongshuNotesCollector();
    
    try {
        const data = await collector.collectAllNotes();
        
        console.log('\n📝 采集到的笔记数据:');
        console.log('=' * 60);
        console.log(`📊 总计: ${data.summary.totalNotes} 篇笔记`);
        console.log(`🖼️  总图片: ${data.summary.totalImages} 张`);
        console.log(`👍 总点赞: ${data.summary.totalLikes}`);
        console.log(`💖 总收藏: ${data.summary.totalCollects}`);
        console.log(`💬 总评论: ${data.summary.totalComments}\n`);
        
        // 显示每篇笔记的详细信息
        data.notes.forEach((note, index) => {
            console.log(`📝 笔记 ${index + 1}:`);
            console.log(`   标题: ${note.title || '无标题'}`);
            console.log(`   图片数量: ${note.images.length}`);
            console.log(`   👍 点赞: ${note.interactions.likes || 0}`);
            console.log(`   💖 收藏: ${note.interactions.collects || 0}`);
            console.log(`   💬 评论: ${note.interactions.comments || 0}`);
            if (note.publishTime) {
                console.log(`   📅 发布时间: ${note.publishTime}`);
            }
            if (note.link) {
                console.log(`   🔗 链接: ${note.link}`);
            }
            console.log('');
        });
        
        return data;
        
    } catch (error) {
        console.error('❌ 笔记采集失败:', error.message);
        console.log('💡 请确保19号浏览器已启动并打开了小红书用户页面');
    }
}

// 运行测试
if (require.main === module) {
    testNotesCollection().catch(console.error);
}

module.exports = { XiaohongshuNotesCollector, testNotesCollection };
