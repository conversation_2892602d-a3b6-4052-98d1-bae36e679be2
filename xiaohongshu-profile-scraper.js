#!/usr/bin/env node

/**
 * 🔍 小红书个人信息爬取器
 * 爬取用户个人资料、粉丝数据、笔记信息等
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class XiaohongshuProfileScraper {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
    }

    /**
     * 确保输出目录存在
     */
    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    /**
     * 保存数据到JSON文件
     */
    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`   💾 数据已保存到: ${filepath}`);
    }

    /**
     * 爬取用户个人信息
     */
    async scrapeUserProfile(page) {
        console.log('📋 爬取用户个人信息...');
        
        const profileData = await page.evaluate(() => {
            const result = {
                basicInfo: {},
                stats: {},
                description: {},
                tags: [],
                timestamp: new Date().toISOString()
            };

            try {
                // 基本信息
                const usernameEl = document.querySelector('.user-name, .username, [class*="name"]');
                if (usernameEl) result.basicInfo.username = usernameEl.textContent.trim();

                const userIdEl = document.querySelector('.user-id, [class*="id"]');
                if (userIdEl) result.basicInfo.userId = userIdEl.textContent.trim();

                const avatarEl = document.querySelector('.avatar img, .user-avatar img, [class*="avatar"] img');
                if (avatarEl) result.basicInfo.avatar = avatarEl.src;

                // 年龄和地区
                const ageLocationEl = document.querySelector('.age-location, [class*="age"], [class*="location"]');
                if (ageLocationEl) {
                    const text = ageLocationEl.textContent.trim();
                    const ageMatch = text.match(/(\d+)岁/);
                    const locationMatch = text.match(/岁\s*(.+)/) || text.match(/^(.+?)(?:\d+岁|$)/);
                    
                    if (ageMatch) result.basicInfo.age = ageMatch[1];
                    if (locationMatch) result.basicInfo.location = locationMatch[1].trim();
                }

                // 统计数据
                const statsElements = document.querySelectorAll('[class*="count"], [class*="num"], [class*="stat"]');
                statsElements.forEach(el => {
                    const text = el.textContent.trim();
                    const parent = el.parentElement;
                    const parentText = parent ? parent.textContent.trim() : '';
                    
                    if (parentText.includes('关注') || parentText.includes('follow')) {
                        result.stats.following = text;
                    } else if (parentText.includes('粉丝') || parentText.includes('fans')) {
                        result.stats.followers = text;
                    } else if (parentText.includes('获赞') || parentText.includes('like')) {
                        result.stats.likes = text;
                    }
                });

                // 从页面中提取数字统计
                const numberElements = document.querySelectorAll('*');
                numberElements.forEach(el => {
                    const text = el.textContent.trim();
                    if (/^\d+$/.test(text) && el.nextSibling) {
                        const nextText = el.nextSibling.textContent || '';
                        if (nextText.includes('关注')) result.stats.following = text;
                        if (nextText.includes('粉丝')) result.stats.followers = text;
                        if (nextText.includes('获赞')) result.stats.likes = text;
                    }
                });

                // 个人描述
                const descElements = document.querySelectorAll('.desc, .description, .bio, [class*="desc"], [class*="bio"]');
                descElements.forEach(el => {
                    const text = el.textContent.trim();
                    if (text && text.length > 5) {
                        result.description.text = text;
                    }
                });

                // 标签
                const tagElements = document.querySelectorAll('.tag, .label, [class*="tag"], [class*="label"]');
                tagElements.forEach(el => {
                    const text = el.textContent.trim();
                    if (text && text.length < 20) {
                        result.tags.push(text);
                    }
                });

                // 从当前页面URL获取用户ID
                const currentUrl = window.location.href;
                const userIdMatch = currentUrl.match(/user\/profile\/([a-f0-9]+)/);
                if (userIdMatch) {
                    result.basicInfo.profileId = userIdMatch[1];
                }

                return result;
            } catch (error) {
                console.error('爬取个人信息时出错:', error);
                return result;
            }
        });

        return profileData;
    }

    /**
     * 爬取用户笔记列表
     */
    async scrapeUserNotes(page, maxNotes = 20) {
        console.log(`📝 爬取用户笔记 (最多${maxNotes}条)...`);
        
        const notesData = await page.evaluate((maxNotes) => {
            const notes = [];
            
            try {
                // 查找笔记元素
                const noteSelectors = [
                    '.note-item',
                    '.post-item', 
                    '[class*="note"]',
                    '[class*="post"]',
                    '.feed-item'
                ];

                let noteElements = [];
                for (const selector of noteSelectors) {
                    noteElements = document.querySelectorAll(selector);
                    if (noteElements.length > 0) break;
                }

                // 如果没找到，尝试更通用的选择器
                if (noteElements.length === 0) {
                    noteElements = document.querySelectorAll('a[href*="/explore/"]');
                }

                const processedNotes = Math.min(noteElements.length, maxNotes);
                
                for (let i = 0; i < processedNotes; i++) {
                    const noteEl = noteElements[i];
                    const note = {
                        index: i + 1,
                        title: '',
                        link: '',
                        image: '',
                        likes: '',
                        comments: ''
                    };

                    // 标题
                    const titleEl = noteEl.querySelector('.title, .note-title, [class*="title"]');
                    if (titleEl) note.title = titleEl.textContent.trim();

                    // 链接
                    const linkEl = noteEl.querySelector('a') || noteEl;
                    if (linkEl && linkEl.href) note.link = linkEl.href;

                    // 图片
                    const imgEl = noteEl.querySelector('img');
                    if (imgEl) note.image = imgEl.src;

                    // 点赞数
                    const likeEl = noteEl.querySelector('[class*="like"], [class*="heart"]');
                    if (likeEl) note.likes = likeEl.textContent.trim();

                    // 评论数
                    const commentEl = noteEl.querySelector('[class*="comment"]');
                    if (commentEl) note.comments = commentEl.textContent.trim();

                    notes.push(note);
                }

                return {
                    totalFound: noteElements.length,
                    scraped: notes.length,
                    notes: notes,
                    timestamp: new Date().toISOString()
                };
            } catch (error) {
                console.error('爬取笔记时出错:', error);
                return { notes: [], error: error.message };
            }
        }, maxNotes);

        return notesData;
    }

    /**
     * 截取页面截图
     */
    async takeScreenshot(page, filename) {
        try {
            const screenshotPath = path.join(this.outputDir, filename);
            await page.screenshot({ 
                path: screenshotPath, 
                fullPage: true,
                quality: 80
            });
            console.log(`   📸 页面截图已保存: ${screenshotPath}`);
        } catch (error) {
            console.log('   ⚠️ 截图失败:', error.message);
        }
    }

    /**
     * 主要爬取函数
     */
    async scrapeProfile() {
        console.log('🕷️ 启动小红书个人信息爬取器...');
        console.log('');

        let browser = null;

        try {
            // 连接到比特浏览器
            console.log('🔗 连接到比特浏览器...');
            browser = await puppeteer.connect({
                browserURL: `http://127.0.0.1:${this.debugPort}`,
                defaultViewport: null
            });

            // 获取小红书页面
            const pages = await browser.pages();
            const xiaohongshuPage = pages.find(page => 
                page.url().includes('xiaohongshu.com')
            );

            if (!xiaohongshuPage) {
                throw new Error('未找到小红书页面');
            }

            console.log('✅ 找到小红书页面');
            console.log('📄 当前URL:', xiaohongshuPage.url());
            console.log('');

            // 等待页面加载完成
            console.log('⏳ 等待页面加载完成...');
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 1. 爬取个人信息
            const profileData = await this.scrapeUserProfile(xiaohongshuPage);
            console.log('✅ 个人信息爬取完成');
            console.log('   用户名:', profileData.basicInfo.username || '未找到');
            console.log('   用户ID:', profileData.basicInfo.userId || '未找到');
            console.log('   年龄:', profileData.basicInfo.age || '未找到');
            console.log('   地区:', profileData.basicInfo.location || '未找到');
            console.log('   关注数:', profileData.stats.following || '未找到');
            console.log('   粉丝数:', profileData.stats.followers || '未找到');
            console.log('   获赞数:', profileData.stats.likes || '未找到');

            // 保存个人信息
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            this.saveToFile(profileData, `profile_${timestamp}.json`);

            // 2. 爬取笔记信息
            console.log('');
            const notesData = await this.scrapeUserNotes(xiaohongshuPage, 20);
            console.log('✅ 笔记信息爬取完成');
            console.log('   找到笔记:', notesData.totalFound || 0, '条');
            console.log('   成功爬取:', notesData.scraped || 0, '条');

            // 保存笔记信息
            this.saveToFile(notesData, `notes_${timestamp}.json`);

            // 3. 截取页面截图
            console.log('');
            console.log('📸 截取页面截图...');
            await this.takeScreenshot(xiaohongshuPage, `screenshot_${timestamp}.png`);

            // 4. 生成汇总报告
            const summary = {
                scrapeTime: new Date().toISOString(),
                pageUrl: xiaohongshuPage.url(),
                profile: profileData,
                notes: {
                    totalFound: notesData.totalFound,
                    scraped: notesData.scraped,
                    sampleNotes: notesData.notes.slice(0, 5) // 只保存前5条作为示例
                },
                files: [
                    `profile_${timestamp}.json`,
                    `notes_${timestamp}.json`,
                    `screenshot_${timestamp}.png`
                ]
            };

            this.saveToFile(summary, `summary_${timestamp}.json`);

            console.log('');
            console.log('🎉 爬取完成！');
            console.log('📁 输出目录:', this.outputDir);
            console.log('📋 生成文件:');
            summary.files.forEach(file => {
                console.log(`   - ${file}`);
            });

            return summary;

        } catch (error) {
            console.error('❌ 爬取失败:', error.message);
            return null;
        } finally {
            if (browser) {
                await browser.disconnect();
            }
        }
    }
}

// 如果直接运行此文件
if (require.main === module) {
    const scraper = new XiaohongshuProfileScraper();
    scraper.scrapeProfile().catch(console.error);
}

module.exports = XiaohongshuProfileScraper;
