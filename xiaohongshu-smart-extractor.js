#!/usr/bin/env node

/**
 * 🧠 小红书智能提取器
 * 专门针对小红书页面结构优化的评论提取器
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class XiaohongshuSmartExtractor {
    constructor() {
        this.debugPort = 55276;
        this.outputDir = './scraped_data';
        this.ensureOutputDir();
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    async connectToBrowser() {
        try {
            console.log('🔗 连接到比特浏览器...');
            const browser = await puppeteer.connect({
                browserURL: `http://localhost:${this.debugPort}`,
                defaultViewport: null
            });
            
            const pages = await browser.pages();
            const page = pages.find(p => p.url().includes('xiaohongshu.com/explore/'));
            
            if (!page) {
                throw new Error('未找到小红书笔记页面');
            }
            
            console.log('✅ 找到小红书笔记页面');
            console.log(`📄 当前URL: ${page.url()}`);
            
            return { browser, page };
        } catch (error) {
            console.error('❌ 连接失败:', error.message);
            throw error;
        }
    }

    // 温和滚动加载更多评论
    async gentleScrollAndLoad(page) {
        console.log('🔄 开始温和滚动加载评论...');
        
        for (let i = 0; i < 3; i++) {
            console.log(`   📜 滚动 ${i + 1}/3`);
            
            await page.evaluate(() => {
                window.scrollBy(0, 400);
            });
            
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        console.log('✅ 滚动完成');
    }

    // 智能提取评论
    async smartExtractComments(page) {
        console.log('🧠 开始智能提取评论...');
        
        const result = await page.evaluate(() => {
            const data = {
                noteInfo: {
                    title: '',
                    id: '',
                    totalCommentCount: 0
                },
                comments: [],
                debug: {
                    pageText: '',
                    extractMethod: 'smart-text-parsing'
                }
            };
            
            try {
                // 获取页面文本
                const pageText = document.body.textContent;
                data.debug.pageText = pageText.substring(0, 2000); // 保存前2000字符用于调试
                
                // 提取笔记ID
                const urlMatch = window.location.href.match(/explore\/([a-f0-9]+)/);
                if (urlMatch) {
                    data.noteInfo.id = urlMatch[1];
                }
                
                // 提取评论总数
                const countMatch = pageText.match(/共\s*(\d+)\s*条评论/);
                if (countMatch) {
                    data.noteInfo.totalCommentCount = parseInt(countMatch[1]);
                }
                
                // 查找评论区域
                console.log('🔍 查找评论区域...');
                const commentSectionMatch = pageText.match(/共\s*\d+\s*条评论([\s\S]*?)(?:回到顶部|发现|发布|创作中心|$)/);
                
                if (!commentSectionMatch) {
                    console.log('❌ 未找到评论区域');
                    return data;
                }
                
                const commentText = commentSectionMatch[1];
                console.log(`📝 评论区域文本长度: ${commentText.length}`);
                
                // 智能分割评论
                const comments = this.parseCommentsFromText(commentText);
                data.comments = comments;
                
                console.log(`✅ 提取到 ${comments.length} 条评论`);
                
            } catch (error) {
                console.error('❌ 提取过程出错:', error);
                data.error = error.message;
            }
            
            return data;
        });
        
        return result;
    }

    // 在页面中注入评论解析函数
    async injectParsingFunctions(page) {
        await page.evaluateOnNewDocument(() => {
            // 解析评论文本的核心函数
            window.parseCommentsFromText = function(commentText) {
                const comments = [];
                
                // 按行分割
                const lines = commentText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
                
                let currentComment = null;
                let commentIndex = 0;
                
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    
                    // 跳过无用行
                    if (this.shouldSkipLine(line)) {
                        continue;
                    }
                    
                    // 检查是否是新评论的开始
                    if (this.isCommentStart(line)) {
                        // 保存前一个评论
                        if (currentComment && currentComment.content.length > 5) {
                            this.finalizeComment(currentComment);
                            comments.push(currentComment);
                        }
                        
                        // 创建新评论
                        currentComment = this.createComment(++commentIndex, line);
                    } else if (currentComment) {
                        // 继续当前评论
                        currentComment.rawLines.push(line);
                        if (line.length > 3 && !this.isMetaInfo(line)) {
                            currentComment.content += ' ' + line;
                        }
                    }
                }
                
                // 保存最后一个评论
                if (currentComment && currentComment.content.length > 5) {
                    this.finalizeComment(currentComment);
                    comments.push(currentComment);
                }
                
                return comments;
            };
            
            // 判断是否应该跳过这一行
            window.shouldSkipLine = function(line) {
                const skipPatterns = [
                    '加载中', '你还没有', '温馨提示', '广告屏蔽', '我知道了',
                    '活动', 'window.', 'function', 'console.log', 'document.',
                    '创作服务', '直播管理', '专业号', '商家入驻', 'MCN入驻'
                ];
                
                return skipPatterns.some(pattern => line.includes(pattern)) || 
                       line.length < 2 || 
                       /^[0-9\s\-:\.]+$/.test(line) ||
                       /^[^\w\u4e00-\u9fff]+$/.test(line);
            };
            
            // 判断是否是评论开始
            window.isCommentStart = function(line) {
                // 包含作者标识
                if (line.includes('作者') && line.length < 100) return true;
                
                // 包含置顶标识
                if (line.includes('置顶评论')) return true;
                
                // 时间格式开头
                if (line.match(/^\d{2}-\d{2}/)) return true;
                
                // 用户名+时间格式
                if (line.match(/^[^\d\s]{2,15}\s*\d{2}-\d{2}/)) return true;
                
                // 表情符号开头（小红书常见）
                if (line.match(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/)) return true;
                
                return false;
            };
            
            // 判断是否是元信息（点赞、回复数等）
            window.isMetaInfo = function(line) {
                return line.match(/^\d+$/) || 
                       line.match(/^\d+回复$/) || 
                       line.match(/^回复$/) || 
                       line.match(/^赞$/) ||
                       line.includes('展开') ||
                       line.includes('条回复');
            };
            
            // 创建评论对象
            window.createComment = function(index, firstLine) {
                const comment = {
                    id: index,
                    userId: '',
                    username: '',
                    content: firstLine,
                    time: '',
                    likes: 0,
                    replyCount: 0,
                    isAuthor: firstLine.includes('作者'),
                    isPinned: firstLine.includes('置顶'),
                    rawLines: [firstLine]
                };
                
                // 提取时间
                const timeMatch = firstLine.match(/(\d{2}-\d{2})/);
                if (timeMatch) {
                    comment.time = timeMatch[1];
                }
                
                // 提取用户名
                const userMatch = firstLine.match(/^([^\d\s]{2,15})\s*\d{2}-\d{2}/);
                if (userMatch) {
                    comment.username = userMatch[1].replace(/[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]/g, '').trim();
                }
                
                return comment;
            };
            
            // 完善评论信息
            window.finalizeComment = function(comment) {
                // 清理内容
                let content = comment.content;
                
                // 移除用户名
                if (comment.username) {
                    content = content.replace(comment.username, '');
                }
                
                // 移除时间
                content = content.replace(/\d{2}-\d{2}/g, '');
                
                // 移除表情符号开头
                content = content.replace(/^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+/, '');
                
                // 移除常见标识
                content = content.replace(/作者|置顶评论|回复|赞/g, '');
                
                // 清理空格
                content = content.replace(/\s+/g, ' ').trim();
                
                // 提取点赞数和回复数
                comment.rawLines.forEach(line => {
                    const likeMatch = line.match(/^(\d+)$/);
                    if (likeMatch) {
                        comment.likes = parseInt(likeMatch[1]);
                    }
                    
                    const replyMatch = line.match(/(\d+)回复/);
                    if (replyMatch) {
                        comment.replyCount = parseInt(replyMatch[1]);
                    }
                });
                
                comment.content = content;
                delete comment.rawLines; // 清理临时数据
            };
        });
    }

    saveToFile(data, filename) {
        const filepath = path.join(this.outputDir, filename);
        fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async run() {
        try {
            console.log('🧠 启动小红书智能提取器...');
            
            const { browser, page } = await this.connectToBrowser();
            
            // 注入解析函数
            await this.injectParsingFunctions(page);
            
            // 温和滚动
            await this.gentleScrollAndLoad(page);
            
            // 智能提取
            const result = await this.smartExtractComments(page);
            
            // 保存数据
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `smart_comments_${result.noteInfo.id}_${timestamp}.json`;
            this.saveToFile(result, filename);
            
            // 输出结果
            console.log('\n🎉 智能提取完成！');
            console.log('📊 提取统计:');
            console.log(`   🆔 笔记ID: ${result.noteInfo.id}`);
            console.log(`   🎯 目标评论数: ${result.noteInfo.totalCommentCount}`);
            console.log(`   💬 实际提取数: ${result.comments.length}`);
            console.log(`   📁 保存文件: ${filename}`);
            
            if (result.comments.length > 0) {
                console.log('\n👥 评论预览:');
                result.comments.slice(0, 5).forEach((comment, index) => {
                    console.log(`   ${index + 1}. 用户: ${comment.username || '未知'}`);
                    console.log(`      时间: ${comment.time || '未知'}`);
                    console.log(`      内容: ${comment.content.substring(0, 40)}...`);
                    console.log(`      点赞: ${comment.likes} | 回复: ${comment.replyCount}`);
                    console.log('');
                });
            }
            
            await browser.disconnect();
            
        } catch (error) {
            console.error('❌ 提取失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行提取器
if (require.main === module) {
    const extractor = new XiaohongshuSmartExtractor();
    extractor.run();
}

module.exports = XiaohongshuSmartExtractor;
