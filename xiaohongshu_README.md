# 小红书创作者中心数据收集器 - Selenium版

## 🎯 功能特点

- ✅ **支持比特浏览器** - 自动连接现有的比特浏览器配置
- ✅ **支持Chrome浏览器** - 备用方案，自动启动Chrome
- ✅ **自动化数据收集** - 自动提取页面数据和关键指标
- ✅ **智能数据分析** - 识别粉丝数、点赞数、阅读量等关键数据
- ✅ **数据导出** - 支持JSON格式导出
- ✅ **交互式操作** - 友好的命令行界面

## 🚀 快速开始

### 方法1: 一键运行 (推荐)
```bash
# 双击运行
install_and_run.bat
```

### 方法2: 手动安装
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 运行程序
python xiaohongshu_selenium.py
```

## 📋 使用步骤

1. **选择浏览器类型**
   - 比特浏览器 (推荐) - 使用现有配置
   - Chrome浏览器 - 自动启动新浏览器

2. **选择比特浏览器配置** (如果选择比特浏览器)
   - 程序会自动列出所有可用的浏览器配置
   - 输入编号选择要使用的浏览器

3. **手动登录**
   - 程序会自动打开小红书网站
   - 请手动登录您的账号
   - 登录完成后按回车键继续

4. **自动收集数据**
   - 程序会自动进入创作者中心
   - 自动收集页面数据
   - 显示收集结果

5. **导出数据**
   - 选择是否导出数据到JSON文件
   - 文件会保存在当前目录

## 📊 收集的数据类型

- 📈 **基础指标**: 粉丝数、点赞数、收藏数、分享数
- 📝 **内容数据**: 阅读量、曝光量、互动数据
- 💰 **收益信息**: 创作收益、佣金收入
- 🔢 **页面数字**: 所有页面中的数字数据
- 📋 **关键词数据**: 包含关键词的文本行

## ⚙️ 配置说明

### 比特浏览器配置
- API地址: `http://127.0.0.1:54345`
- API Token: `ca28ee5ca6de4d209182a83aa16a2044`

如需修改配置，请编辑 `xiaohongshu_selenium.py` 文件中的相关变量。

## 🔧 依赖要求

- Python 3.7+
- Chrome浏览器 (如果不使用比特浏览器)
- ChromeDriver (自动下载)

## 📝 注意事项

1. **登录要求**: 需要手动登录小红书账号
2. **网络要求**: 需要稳定的网络连接
3. **浏览器要求**: 确保Chrome浏览器已安装
4. **比特浏览器**: 如使用比特浏览器，确保API服务正在运行

## 🐛 常见问题

### Q: 比特浏览器连接失败？
A: 请确保比特浏览器的API服务正在运行，端口为54345

### Q: Chrome浏览器启动失败？
A: 请确保已安装Chrome浏览器，程序会自动下载ChromeDriver

### Q: 数据收集不完整？
A: 请确保已登录账号且页面完全加载后再收集数据

### Q: 导出的数据为空？
A: 可能是页面结构变化，请检查是否在正确的创作者中心页面

## 📄 输出文件格式

```json
{
  "timestamp": "2024-01-01T12:00:00",
  "page_title": "创作者中心",
  "current_url": "https://creator.xiaohongshu.com/creator/data",
  "numbers": ["1000", "500", "200"],
  "keyword_data": ["粉丝 1000", "点赞 500"],
  "specific_data": {
    "粉丝数": "1000",
    "点赞数": "500"
  }
}
```
