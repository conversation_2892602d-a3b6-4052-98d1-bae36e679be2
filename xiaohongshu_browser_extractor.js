// ===== 🔍 小红书浏览器数据提取器 【核心数据采集引擎】 =====
// 📝 功能说明：专门负责从比特浏览器中实时提取小红书账号数据和内容信息
// 🎯 核心技术：Chrome DevTools Protocol + 页面DOM解析 + 数据清洗处理
// 🌐 支持平台：小红书创作中心、个人主页、笔记详情页
// 💾 数据类型：用户信息、粉丝数据、内容统计、互动数据

const axios = require('axios'); // 🌐 HTTP客户端 - 与浏览器调试接口通信

// 比特浏览器配置
const BITBROWSER_CONFIG = {
    api_url: "http://127.0.0.1:54345",
    api_token: "ca28ee5ca6de4d209182a83aa16a2044",
    browser_19_id: "0d094596cb404282be3f814b98139c74"
};

// 小红书数据提取器类
class XiaohongshuBrowserExtractor {
    constructor() {
        this.debugPort = 9222;
    }

    // 主要提取方法
    async extractAccountData() {
        try {
            console.log('🚀 开始从比特浏览器提取小红书账号数据...');

            // 1. 尝试找到可用的调试端口和小红书标签页
            const debugInfo = await this.findAvailableDebugPort();
            if (!debugInfo) {
                throw new Error('未找到可用的浏览器调试端口或小红书标签页');
            }

            console.log(`✅ 使用调试端口: ${debugInfo.port}`);
            this.debugPort = debugInfo.port;

            // 2. 提取账号数据
            const accountData = await this.extractDataFromPage(debugInfo.xiaohongshuTab);

            console.log('✅ 数据提取完成');
            return accountData;

        } catch (error) {
            console.error('❌ 提取失败:', error.message);
            throw error;
        }
    }

    // 查找可用的调试端口和小红书标签页
    async findAvailableDebugPort() {
        console.log('🔍 搜索可用的Chrome调试端口...');

        // 1. 首先尝试从比特浏览器API获取实际调试端口
        const actualPort = await this.getActualDebugPort();
        if (actualPort) {
            console.log(`🎯 从比特浏览器API获取到调试端口: ${actualPort}`);
            const result = await this.checkPortForXiaohongshu(actualPort);
            if (result) return result;
        }

        // 2. 如果API获取失败，则搜索常见端口范围
        const ports = [
            9222, 9223, 9224, 9225, 9226, 9227, 9228, 9229, 9230, 9231, 9232, 9233, 9234, 9235, 9236, 9237, 9238, 9239, 9240,
            // 比特浏览器动态端口范围
            51859, 58222, 50000, 51000, 52000, 53000, 54000, 55000, 56000, 57000, 58000, 59000
        ];

        for (const port of ports) {
            const result = await this.checkPortForXiaohongshu(port);
            if (result) return result;
        }

        console.log('❌ 未找到可用的调试端口或小红书标签页');
        console.log('💡 请确保：');
        console.log('   1. 比特浏览器已启动');
        console.log('   2. 已创建并启动浏览器实例');
        console.log('   3. 在浏览器中打开了小红书网站');

        return null;
    }

    // 从比特浏览器API获取实际调试端口
    async getActualDebugPort() {
        try {
            const response = await axios.post(`${BITBROWSER_CONFIG.api_url}/browser/list`, {
                page: 0,
                pageSize: 100
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${BITBROWSER_CONFIG.api_token}`
                },
                timeout: 5000
            });

            if (response.data?.success && response.data.data?.list) {
                const targetBrowser = response.data.data.list.find(b =>
                    b.id === BITBROWSER_CONFIG.browser_19_id
                );

                if (targetBrowser && targetBrowser.status && targetBrowser.debug_port) {
                    return targetBrowser.debug_port;
                }
            }
        } catch (error) {
            console.log('⚠️ 无法从API获取调试端口:', error.message);
        }
        return null;
    }

    // 检查指定端口是否有小红书标签页
    async checkPortForXiaohongshu(port) {
        try {
            console.log(`🔌 尝试端口 ${port}...`);

            const response = await axios.get(`http://127.0.0.1:${port}/json`, {
                timeout: 2000
            });

            const tabs = response.data;
            console.log(`📋 端口 ${port} 找到 ${tabs.length} 个标签页`);

            // 查找小红书标签页
            const xiaohongshuTab = tabs.find(tab =>
                tab.url && (
                    tab.url.includes('xiaohongshu.com') ||
                    tab.url.includes('creator.xiaohongshu.com') ||
                    tab.title.includes('小红书')
                )
            );

            if (xiaohongshuTab) {
                console.log(`🎉 在端口 ${port} 找到小红书标签页: ${xiaohongshuTab.title}`);
                return {
                    port: port,
                    xiaohongshuTab: xiaohongshuTab
                };
            }

        } catch (error) {
            console.log(`❌ 端口 ${port} 不可用`);
        }
        return null;
    }

    // 获取浏览器信息
    async getBrowserInfo() {
        try {
            const response = await axios.get(`${BITBROWSER_CONFIG.api_url}/local_api/browser/list`, {
                headers: {
                    'Authorization': `Bearer ${BITBROWSER_CONFIG.api_token}`
                },
                timeout: 5000
            });
            
            if (response.data?.success) {
                const browsers = response.data.data || [];
                const targetBrowser = browsers.find(b => b.id === BITBROWSER_CONFIG.browser_19_id);
                
                if (targetBrowser?.status === 'running') {
                    console.log(`✅ 浏览器运行中: ${targetBrowser.name}`);
                    this.debugPort = targetBrowser.debug_port || 9222;
                    return targetBrowser;
                }
            }
            
            return null;
        } catch (error) {
            console.error('❌ 获取浏览器信息失败:', error.message);
            return null;
        }
    }

    // 查找小红书标签页
    async findXiaohongshuTab() {
        try {
            const response = await axios.get(`http://127.0.0.1:${this.debugPort}/json`, {
                timeout: 5000
            });
            
            const tabs = response.data;
            console.log(`📋 找到 ${tabs.length} 个标签页`);
            
            // 查找小红书相关标签页
            const xiaohongshuTab = tabs.find(tab => 
                tab.url && (
                    tab.url.includes('xiaohongshu.com') || 
                    tab.url.includes('creator.xiaohongshu.com') ||
                    tab.title.includes('小红书')
                )
            );
            
            if (xiaohongshuTab) {
                console.log(`🎯 找到小红书标签页: ${xiaohongshuTab.title}`);
                console.log(`🔗 URL: ${xiaohongshuTab.url}`);
            }
            
            return xiaohongshuTab;
        } catch (error) {
            console.error('❌ 查找标签页失败:', error.message);
            return null;
        }
    }

    // 从页面提取数据
    async extractDataFromPage(tab) {
        try {
            console.log('📊 开始提取页面数据...');
            
            // 构建数据提取脚本
            const extractScript = this.buildExtractionScript();
            
            // 通过Chrome DevTools Protocol执行脚本
            // 使用标签页的webSocketDebuggerUrl进行连接
            console.log(`🔗 连接到标签页: ${tab.title}`);
            console.log(`🌐 WebSocket URL: ${tab.webSocketDebuggerUrl}`);

            // 简化方法：直接解析页面URL和标题获取基本信息
            const basicData = this.extractBasicDataFromTab(tab);
            console.log('✅ 基本数据提取成功');

            return this.processExtractedData(basicData);
            
            if (response.data?.result?.value) {
                const extractedData = response.data.result.value;
                console.log('✅ 页面数据提取成功');
                
                // 处理和清理数据
                return this.processExtractedData(extractedData);
            }
            
            throw new Error('未能提取到有效数据');
        } catch (error) {
            console.error('❌ 页面数据提取失败:', error.message);
            throw error;
        }
    }

    // 从标签页基本信息提取数据
    extractBasicDataFromTab(tab) {
        const data = {
            timestamp: new Date().toISOString(),
            url: tab.url,
            title: tab.title,
            tabId: tab.id
        };

        // 从标题中提取用户名
        if (tab.title && tab.title.includes(' - 小红书')) {
            const titleParts = tab.title.split(' - 小红书');
            if (titleParts.length > 0) {
                data.nickname = titleParts[0].trim();
            }
        }

        // 从URL中提取用户ID
        if (tab.url) {
            const urlMatch = tab.url.match(/user\/profile\/([a-zA-Z0-9]+)/);
            if (urlMatch) {
                data.xiaohongshuId = urlMatch[1];
            }
        }

        // 设置数据来源
        data.dataSource = 'browser_tab_info';
        data.extractionMethod = 'tab_metadata';

        // 添加提示信息，说明需要更详细的数据采集
        data.note = '基础数据已采集，如需获取粉丝数、关注数等详细信息，需要通过页面DOM解析';

        return data;
    }

    // 构建数据提取脚本
    buildExtractionScript() {
        return `
            (function() {
                try {
                    const result = {
                        timestamp: new Date().toISOString(),
                        url: window.location.href,
                        title: document.title
                    };
                    
                    // 提取头像
                    const avatarSelectors = [
                        'img[alt*="头像"]',
                        '.avatar img',
                        '.user-avatar img',
                        '[class*="avatar"] img',
                        '.profile-avatar img',
                        '.user-info img'
                    ];
                    
                    for (const selector of avatarSelectors) {
                        const img = document.querySelector(selector);
                        if (img && img.src && !img.src.includes('data:')) {
                            result.avatar = img.src;
                            break;
                        }
                    }
                    
                    // 提取昵称
                    const nicknameSelectors = [
                        '.user-name',
                        '.nickname',
                        '[class*="user-name"]',
                        '[class*="nickname"]',
                        '.profile-name',
                        '.account-name',
                        'h1',
                        '.title'
                    ];
                    
                    for (const selector of nicknameSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim() && 
                            !element.textContent.includes('小红书') &&
                            element.textContent.length < 50) {
                            result.nickname = element.textContent.trim();
                            break;
                        }
                    }
                    
                    // 提取简介/个人描述
                    const bioSelectors = [
                        '.user-desc',
                        '.bio',
                        '.description',
                        '[class*="desc"]',
                        '[class*="bio"]',
                        '.intro',
                        '.profile-desc',
                        '.user-intro'
                    ];
                    
                    for (const selector of bioSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim() && element.textContent.length > 10) {
                            result.bio = element.textContent.trim();
                            break;
                        }
                    }
                    
                    // 提取小红书ID
                    const bodyText = document.body.textContent;
                    const idMatches = [
                        bodyText.match(/小红书账号[：:]\\s*(\\d+)/),
                        bodyText.match(/ID[：:]\\s*(\\d+)/),
                        bodyText.match(/用户ID[：:]\\s*(\\d+)/)
                    ];
                    
                    for (const match of idMatches) {
                        if (match && match[1]) {
                            result.xiaohongshuId = match[1];
                            break;
                        }
                    }
                    
                    // 提取统计数据
                    const numbers = [];
                    const numberSelectors = [
                        '[class*="count"]',
                        '[class*="number"]',
                        '.stat-value',
                        '[class*="stat"]',
                        '.data-value'
                    ];
                    
                    numberSelectors.forEach(selector => {
                        document.querySelectorAll(selector).forEach(el => {
                            const text = el.textContent.replace(/[^\\d]/g, '');
                            const num = parseInt(text);
                            if (!isNaN(num) && num >= 0) {
                                numbers.push(num);
                            }
                        });
                    });
                    
                    result.numbers = [...new Set(numbers)].sort((a, b) => b - a);
                    
                    // 提取特定数据
                    const followMatch = bodyText.match(/(\\d+)\\s*关注数?/);
                    const fansMatch = bodyText.match(/(\\d+)\\s*粉丝数?/);
                    const likesMatch = bodyText.match(/(\\d+)\\s*获赞与?收藏/);
                    
                    if (followMatch) result.followCount = parseInt(followMatch[1]);
                    if (fansMatch) result.fansCount = parseInt(fansMatch[1]);
                    if (likesMatch) result.likesAndCollects = parseInt(likesMatch[1]);
                    
                    // 提取页面文本（用于备用解析）
                    result.bodyText = bodyText;
                    
                    return result;
                } catch (e) {
                    return { error: e.message, stack: e.stack };
                }
            })()
        `;
    }

    // 处理提取的数据
    processExtractedData(rawData) {
        if (rawData.error) {
            throw new Error(`页面脚本执行错误: ${rawData.error}`);
        }
        
        // 数据清理和验证
        const processedData = {
            nickname: rawData.nickname || '未知用户',
            xiaohongshuId: rawData.xiaohongshuId || 'unknown',
            avatar: rawData.avatar || 'https://sns-avatar-qc.xhscdn.com/avatar/default.jpg',
            bio: rawData.bio || '暂无简介',
            followCount: rawData.followCount || 0,
            fansCount: rawData.fansCount || 0,
            likesAndCollects: rawData.likesAndCollects || 0,
            url: rawData.url,
            title: rawData.title,
            numbers: rawData.numbers || [],
            bodyText: rawData.bodyText || '',
            extractTime: rawData.timestamp
        };
        
        // 如果没有直接提取到统计数据，尝试从numbers数组推断
        if (!processedData.followCount && processedData.numbers.length > 0) {
            processedData.followCount = processedData.numbers[0] || 0;
        }
        if (!processedData.fansCount && processedData.numbers.length > 1) {
            processedData.fansCount = processedData.numbers[1] || 0;
        }
        if (!processedData.likesAndCollects && processedData.numbers.length > 2) {
            processedData.likesAndCollects = processedData.numbers[2] || 0;
        }
        
        console.log(`📊 处理后的数据:`);
        console.log(`   昵称: ${processedData.nickname}`);
        console.log(`   ID: ${processedData.xiaohongshuId}`);
        console.log(`   粉丝数: ${processedData.fansCount}`);
        console.log(`   关注数: ${processedData.followCount}`);
        console.log(`   获赞收藏: ${processedData.likesAndCollects}`);
        
        return processedData;
    }
}

module.exports = XiaohongshuBrowserExtractor;
