#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书笔记管理器 V2 - 改进版
更精确地识别笔记并实现置顶、权限设置功能
"""

import requests
import json
import time
import asyncio
import random
from datetime import datetime

class XiaohongshuNoteManagerV2:
    def __init__(self):
        self.api_url = "http://127.0.0.1:54345"
        self.api_token = "ca28ee5ca6de4d209182a83aa16a2044"
        self.browser_19_id = "0d094596cb404282be3f814b98139c74"
        self.debug_url = None
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def open_browser_19(self):
        """打开19号浏览器"""
        try:
            self.log("🚀 打开19号比特浏览器...")
            
            response = requests.post(
                f"{self.api_url}/browser/open",
                headers={
                    'Content-Type': 'application/json',
                    'X-API-Key': self.api_token
                },
                json={
                    "id": self.browser_19_id,
                    "args": [
                        "--disable-blink-features=AutomationControlled",
                        "--exclude-switches=enable-automation"
                    ],
                    "loadExtensions": False,
                    "extractIp": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('data'):
                    browser_info = result['data']
                    self.debug_url = browser_info.get('http')
                    
                    if self.debug_url:
                        self.log(f"✅ 浏览器已打开")
                        return True
                    else:
                        self.log("❌ 未获取到调试地址", "ERROR")
                        return False
                else:
                    self.log(f"❌ 打开失败: {result}", "ERROR")
                    return False
            else:
                self.log(f"❌ API调用失败: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ 打开浏览器异常: {str(e)}", "ERROR")
            return False
    
    async def navigate_to_note_manager(self):
        """导航到笔记管理页面"""
        try:
            from pyppeteer import connect
            
            self.log("🔗 连接到浏览器...")
            
            # 获取WebSocket端点
            port = self.debug_url.split(':')[-1]
            version_response = requests.get(f"http://127.0.0.1:{port}/json/version", timeout=10)
            
            if version_response.status_code != 200:
                self.log("❌ 无法获取浏览器版本信息", "ERROR")
                return None
            
            version_info = version_response.json()
            ws_endpoint = version_info.get('webSocketDebuggerUrl')
            
            if not ws_endpoint:
                self.log("❌ 无法获取WebSocket端点", "ERROR")
                return None
            
            # 连接到浏览器
            browser = await connect(browserWSEndpoint=ws_endpoint)
            self.log("✅ 已连接到浏览器")
            
            # 获取所有页面
            pages = await browser.pages()
            
            xiaohongshu_page = None
            
            # 查找小红书页面
            for page in pages:
                url = page.url
                if 'xiaohongshu.com' in url:
                    xiaohongshu_page = page
                    self.log("🎯 找到小红书页面")
                    break
            
            if not xiaohongshu_page:
                self.log("🌐 创建新页面并导航到笔记管理...")
                xiaohongshu_page = await browser.newPage()
                
                # 设置反检测
                await xiaohongshu_page.evaluateOnNewDocument('''() => {
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                    window.chrome = { runtime: {} };
                }''')
                
                await xiaohongshu_page.goto('https://creator.xiaohongshu.com/creator/note-manager', {
                    'waitUntil': 'networkidle2',
                    'timeout': 30000
                })
            else:
                # 如果不在笔记管理页面，导航过去
                current_url = xiaohongshu_page.url
                if 'note-manager' not in current_url:
                    self.log("🔄 导航到笔记管理页面...")
                    await xiaohongshu_page.goto('https://creator.xiaohongshu.com/creator/note-manager', {
                        'waitUntil': 'networkidle2',
                        'timeout': 30000
                    })
            
            # 等待页面加载
            self.log("⏳ 等待笔记管理页面加载...")
            await asyncio.sleep(5)
            
            return xiaohongshu_page, browser
            
        except Exception as e:
            self.log(f"❌ 导航到笔记管理页面失败: {str(e)}", "ERROR")
            return None, None
    
    async def analyze_page_structure(self, page):
        """分析页面结构，找到真正的笔记元素"""
        try:
            self.log("🔍 分析页面结构...")
            
            page_analysis = await page.evaluate('''() => {
                const analysis = {
                    page_info: {
                        title: document.title,
                        url: window.location.href,
                        body_classes: document.body.className
                    },
                    potential_note_containers: [],
                    all_elements_sample: []
                };
                
                // 查找可能包含笔记的容器
                const containerSelectors = [
                    '[class*="note"]',
                    '[class*="item"]', 
                    '[class*="card"]',
                    '[class*="list"]',
                    '[class*="content"]',
                    '[data-testid*="note"]',
                    '[role="listitem"]'
                ];
                
                containerSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            analysis.potential_note_containers.push({
                                selector: selector,
                                count: elements.length,
                                sample_html: elements[0] ? elements[0].outerHTML.substring(0, 300) : '',
                                sample_text: elements[0] ? elements[0].textContent.substring(0, 100) : ''
                            });
                        }
                    } catch(e) {}
                });
                
                // 查找包含文本内容的元素（可能是笔记标题）
                const textElements = Array.from(document.querySelectorAll('*')).filter(el => {
                    const text = el.textContent.trim();
                    return text.length > 5 && text.length < 100 && 
                           !text.includes('笔记管理') && 
                           !text.includes('发布笔记') &&
                           el.children.length === 0; // 叶子节点
                });
                
                analysis.all_elements_sample = textElements.slice(0, 10).map(el => ({
                    tag: el.tagName,
                    className: el.className,
                    text: el.textContent.trim(),
                    parent_class: el.parentElement ? el.parentElement.className : ''
                }));
                
                return analysis;
            }''')
            
            self.log("✅ 页面结构分析完成")
            return page_analysis
            
        except Exception as e:
            self.log(f"❌ 页面结构分析失败: {str(e)}", "ERROR")
            return None
    
    async def find_real_notes(self, page):
        """基于页面分析结果找到真正的笔记"""
        try:
            self.log("📋 查找真正的笔记元素...")
            
            notes_data = await page.evaluate('''() => {
                const notes = [];
                
                // 方法1: 查找包含笔记标题的元素
                const titleElements = Array.from(document.querySelectorAll('*')).filter(el => {
                    const text = el.textContent.trim();
                    // 过滤掉导航和系统文本
                    const excludeTexts = ['笔记管理', '发布笔记', '数据看板', '账号概览', '内容分析', '粉丝数据', '活动中心'];
                    const isExcluded = excludeTexts.some(exclude => text.includes(exclude));
                    
                    return text.length > 3 && text.length < 50 && 
                           !isExcluded &&
                           el.children.length <= 2 && // 不是复杂容器
                           !text.match(/^\\d+$/) && // 不是纯数字
                           !text.includes('点击') &&
                           !text.includes('查看');
                });
                
                // 方法2: 查找具有特定结构的笔记卡片
                const cardElements = document.querySelectorAll('[class*="card"], [class*="item"], .note-item');
                
                // 合并结果
                const allPotentialNotes = [...titleElements, ...cardElements];
                
                allPotentialNotes.forEach((element, index) => {
                    if (index >= 20) return; // 限制数量
                    
                    const noteData = {
                        index: index,
                        title: '',
                        element_info: {
                            tag: element.tagName,
                            className: element.className,
                            text: element.textContent.trim().substring(0, 100),
                            html_snippet: element.outerHTML.substring(0, 200)
                        },
                        parent_info: {
                            tag: element.parentElement ? element.parentElement.tagName : '',
                            className: element.parentElement ? element.parentElement.className : ''
                        },
                        actions_found: []
                    };
                    
                    // 提取标题
                    noteData.title = element.textContent.trim().substring(0, 50);
                    
                    // 查找可能的操作按钮
                    const parent = element.parentElement;
                    if (parent) {
                        const actionButtons = parent.querySelectorAll('button, [role="button"], [class*="btn"], [class*="action"]');
                        actionButtons.forEach(btn => {
                            const btnText = btn.textContent.trim();
                            const btnClass = btn.className;
                            if (btnText || btnClass) {
                                noteData.actions_found.push({
                                    text: btnText,
                                    className: btnClass,
                                    type: btn.tagName
                                });
                            }
                        });
                    }
                    
                    notes.push(noteData);
                });
                
                return {
                    total_found: notes.length,
                    notes: notes,
                    search_methods: ['title_elements', 'card_elements']
                };
            }''')
            
            self.log(f"✅ 找到 {notes_data['total_found']} 个潜在笔记元素")
            return notes_data
            
        except Exception as e:
            self.log(f"❌ 查找笔记失败: {str(e)}", "ERROR")
            return None
    
    async def try_note_actions(self, page, note_index=0):
        """尝试对指定笔记执行操作"""
        try:
            self.log(f"🔧 尝试对笔记 {note_index + 1} 执行操作...")
            
            # 首先尝试右键菜单
            right_click_result = await page.evaluate(f'''(noteIndex) => {{
                const allElements = Array.from(document.querySelectorAll('*')).filter(el => {{
                    const text = el.textContent.trim();
                    return text.length > 3 && text.length < 50 && 
                           !text.includes('笔记管理') && 
                           !text.includes('发布笔记');
                }});
                
                if (noteIndex >= allElements.length) return false;
                
                const targetElement = allElements[noteIndex];
                
                // 模拟右键点击
                const event = new MouseEvent('contextmenu', {{
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    button: 2
                }});
                
                targetElement.dispatchEvent(event);
                return true;
            }}''', note_index)
            
            if right_click_result:
                await asyncio.sleep(2)
                
                # 查找右键菜单中的选项
                menu_result = await page.evaluate('''() => {
                    const menuItems = document.querySelectorAll('[class*="menu"] li, [class*="context"] li, [role="menuitem"]');
                    const foundActions = [];
                    
                    menuItems.forEach(item => {
                        const text = item.textContent.trim();
                        if (text.includes('置顶') || text.includes('权限') || text.includes('设置') || text.includes('编辑')) {
                            foundActions.push({
                                text: text,
                                className: item.className
                            });
                        }
                    });
                    
                    return foundActions;
                }''')
                
                if menu_result:
                    self.log(f"✅ 找到右键菜单选项: {[item['text'] for item in menu_result]}")
                    return menu_result
            
            # 尝试查找更多操作按钮
            more_button_result = await page.evaluate(f'''(noteIndex) => {{
                const allElements = Array.from(document.querySelectorAll('*')).filter(el => {{
                    const text = el.textContent.trim();
                    return text.length > 3 && text.length < 50;
                }});
                
                if (noteIndex >= allElements.length) return false;
                
                const targetElement = allElements[noteIndex];
                const parent = targetElement.parentElement;
                
                if (parent) {{
                    const moreButtons = parent.querySelectorAll('button, [role="button"]');
                    for (let btn of moreButtons) {{
                        const text = btn.textContent.trim();
                        if (text.includes('更多') || text.includes('...') || text.includes('操作')) {{
                            btn.click();
                            return true;
                        }}
                    }}
                }}
                
                return false;
            }}''', note_index)
            
            if more_button_result:
                await asyncio.sleep(2)
                self.log("✅ 点击了更多操作按钮")
                
                # 查找弹出的菜单
                dropdown_result = await page.evaluate('''() => {
                    const dropdownItems = document.querySelectorAll('[class*="dropdown"] li, [class*="menu"] li, .menu-item');
                    const foundActions = [];
                    
                    dropdownItems.forEach(item => {
                        const text = item.textContent.trim();
                        if (text.includes('置顶') || text.includes('权限') || text.includes('设置')) {
                            foundActions.push({
                                text: text,
                                className: item.className
                            });
                        }
                    });
                    
                    return foundActions;
                }''')
                
                if dropdown_result:
                    self.log(f"✅ 找到下拉菜单选项: {[item['text'] for item in dropdown_result]}")
                    return dropdown_result
            
            self.log("⚠️ 未找到可用的操作选项", "WARNING")
            return []
            
        except Exception as e:
            self.log(f"❌ 尝试笔记操作失败: {str(e)}", "ERROR")
            return []
    
    def display_analysis_results(self, page_analysis, notes_data, action_results):
        """显示分析结果"""
        print(f"\n🔍 页面分析结果")
        print("="*60)
        
        if page_analysis:
            print(f"📄 页面: {page_analysis['page_info']['title']}")
            print(f"🔗 URL: {page_analysis['page_info']['url']}")
            
            print(f"\n📦 潜在笔记容器:")
            for container in page_analysis['potential_note_containers'][:5]:
                print(f"  {container['selector']}: {container['count']} 个元素")
            
            print(f"\n📝 文本元素样本:")
            for element in page_analysis['all_elements_sample'][:5]:
                print(f"  {element['tag']}: {element['text'][:30]}...")
        
        if notes_data:
            print(f"\n📋 找到的笔记:")
            print(f"总数: {notes_data['total_found']}")
            
            for note in notes_data['notes'][:5]:
                print(f"  {note['index']+1}. {note['title'][:30]}...")
                if note['actions_found']:
                    print(f"     操作按钮: {[action['text'] for action in note['actions_found'][:3]]}")
        
        if action_results:
            print(f"\n🔧 操作测试结果:")
            for i, result in enumerate(action_results):
                if result:
                    print(f"  笔记 {i+1}: 找到 {len(result)} 个操作选项")
                    for action in result[:3]:
                        print(f"    - {action['text']}")
                else:
                    print(f"  笔记 {i+1}: 未找到操作选项")
        
        print("="*60)
    
    def run_advanced_analysis(self):
        """运行高级分析"""
        print("🔍 小红书笔记管理器 V2 - 高级分析")
        print("="*60)
        print("📋 深度分析页面结构，精确定位功能")
        print("="*60)
        
        try:
            # 检查pyppeteer
            try:
                import pyppeteer
            except ImportError:
                self.log("正在安装pyppeteer...")
                import subprocess
                import sys
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyppeteer'], 
                             capture_output=True)
            
            # 打开浏览器
            if not self.open_browser_19():
                return
            
            # 等待启动
            self.log("⏳ 等待浏览器启动...")
            time.sleep(5)
            
            # 导航到笔记管理页面
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                page, browser = loop.run_until_complete(
                    self.navigate_to_note_manager()
                )
                
                if not page:
                    print("❌ 无法访问笔记管理页面")
                    return
                
                # 分析页面结构
                page_analysis = loop.run_until_complete(
                    self.analyze_page_structure(page)
                )
                
                # 查找真正的笔记
                notes_data = loop.run_until_complete(
                    self.find_real_notes(page)
                )
                
                # 测试前3个笔记的操作
                action_results = []
                for i in range(min(3, notes_data['total_found'] if notes_data else 0)):
                    result = loop.run_until_complete(
                        self.try_note_actions(page, i)
                    )
                    action_results.append(result)
                    time.sleep(1)
                
                # 显示分析结果
                self.display_analysis_results(page_analysis, notes_data, action_results)
                
                # 保存详细分析结果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"note_analysis_v2_{timestamp}.json"
                
                final_data = {
                    'timestamp': datetime.now().isoformat(),
                    'page_analysis': page_analysis,
                    'notes_data': notes_data,
                    'action_results': action_results
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(final_data, f, ensure_ascii=False, indent=2)
                
                print(f"\n🎉 高级分析完成!")
                print(f"📁 分析文件: {filename}")
                
                loop.run_until_complete(browser.disconnect())
                
            finally:
                loop.close()
            
            print(f"\n✅ 浏览器保持打开状态")
            
        except Exception as e:
            self.log(f"程序异常: {str(e)}", "ERROR")
        
        print(f"\n👋 程序结束")

if __name__ == "__main__":
    manager = XiaohongshuNoteManagerV2()
    manager.run_advanced_analysis()
