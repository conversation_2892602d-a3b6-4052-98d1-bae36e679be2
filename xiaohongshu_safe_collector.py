#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书数据收集器 - 安全版本
增加反检测措施，降低被平台发现的风险
"""

import requests
import json
import time
import asyncio
import random
from datetime import datetime

class SafeXiaohongshuCollector:
    def __init__(self):
        self.api_url = "http://127.0.0.1:54345"
        self.api_token = "ca28ee5ca6de4d209182a83aa16a2044"
        self.browser_19_id = "0d094596cb404282be3f814b98139c74"
        self.debug_url = None
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def random_delay(self, min_seconds=1, max_seconds=3):
        """随机延迟，模拟人工操作"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def open_browser_19_safe(self):
        """安全地打开19号浏览器"""
        try:
            self.log("🚀 安全打开19号比特浏览器...")
            
            # 使用比特浏览器的反检测参数
            response = requests.post(
                f"{self.api_url}/browser/open",
                headers={
                    'Content-Type': 'application/json',
                    'X-API-Key': self.api_token
                },
                json={
                    "id": self.browser_19_id,
                    "args": [
                        "--disable-blink-features=AutomationControlled",
                        "--exclude-switches=enable-automation",
                        "--disable-extensions-except",
                        "--disable-plugins-discovery",
                        "--no-first-run",
                        "--no-service-autorun",
                        "--password-store=basic"
                    ],
                    "loadExtensions": True,  # 加载扩展增加真实性
                    "extractIp": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('data'):
                    browser_info = result['data']
                    self.debug_url = browser_info.get('http')
                    
                    if self.debug_url:
                        self.log(f"✅ 浏览器已安全打开")
                        self.log(f"🔗 调试地址: {self.debug_url}")
                        return True
                    else:
                        self.log("❌ 未获取到调试地址", "ERROR")
                        return False
                else:
                    self.log(f"❌ 打开失败: {result}", "ERROR")
                    return False
            else:
                self.log(f"❌ API调用失败: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ 打开浏览器异常: {str(e)}", "ERROR")
            return False
    
    async def safe_data_collection(self):
        """安全的数据收集方法"""
        try:
            from pyppeteer import connect
            
            self.log("🔗 安全连接到浏览器...")
            
            # 获取WebSocket端点
            port = self.debug_url.split(':')[-1]
            version_response = requests.get(f"http://127.0.0.1:{port}/json/version", timeout=10)
            
            if version_response.status_code != 200:
                self.log("❌ 无法获取浏览器版本信息", "ERROR")
                return None
            
            version_info = version_response.json()
            ws_endpoint = version_info.get('webSocketDebuggerUrl')
            
            if not ws_endpoint:
                self.log("❌ 无法获取WebSocket端点", "ERROR")
                return None
            
            # 连接到浏览器
            browser = await connect(browserWSEndpoint=ws_endpoint)
            self.log("✅ 已安全连接到浏览器")
            
            # 获取所有页面
            pages = await browser.pages()
            self.log(f"📋 找到 {len(pages)} 个页面")
            
            xiaohongshu_page = None
            
            # 查找小红书页面
            for page in pages:
                url = page.url
                title = await page.title()
                
                if 'xiaohongshu.com' in url:
                    xiaohongshu_page = page
                    self.log("🎯 找到小红书页面")
                    break
            
            if not xiaohongshu_page:
                self.log("🌐 手动导航到创作者中心...")
                xiaohongshu_page = await browser.newPage()
                
                # 设置反检测属性
                await xiaohongshu_page.evaluateOnNewDocument('''() => {
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                    
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5],
                    });
                    
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en'],
                    });
                    
                    window.chrome = {
                        runtime: {},
                    };
                    
                    Object.defineProperty(navigator, 'permissions', {
                        get: () => ({
                            query: () => Promise.resolve({ state: 'granted' }),
                        }),
                    });
                }''')
                
                # 模拟人工导航
                await xiaohongshu_page.goto('https://www.xiaohongshu.com', {
                    'waitUntil': 'networkidle2',
                    'timeout': 30000
                })
                
                # 随机等待，模拟用户浏览
                await asyncio.sleep(random.uniform(3, 6))
                
                # 导航到创作者中心
                await xiaohongshu_page.goto('https://creator.xiaohongshu.com/creator/data', {
                    'waitUntil': 'networkidle2',
                    'timeout': 30000
                })
            
            # 模拟人工操作 - 随机等待
            self.log("⏳ 模拟用户浏览行为...")
            await asyncio.sleep(random.uniform(5, 10))
            
            # 模拟鼠标移动
            await xiaohongshu_page.mouse.move(
                random.randint(100, 800), 
                random.randint(100, 600)
            )
            await asyncio.sleep(random.uniform(1, 2))
            
            # 模拟滚动
            await xiaohongshu_page.evaluate('''() => {
                window.scrollTo(0, Math.random() * 500);
            }''')
            await asyncio.sleep(random.uniform(2, 4))
            
            # 安全地收集数据
            self.log("📊 开始安全收集数据...")
            
            # 分步收集，避免一次性执行大量代码
            basic_info = await xiaohongshu_page.evaluate('''() => {
                return {
                    title: document.title,
                    url: window.location.href,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent
                };
            }''')
            
            await asyncio.sleep(random.uniform(1, 2))
            
            # 收集可见文本
            visible_text = await xiaohongshu_page.evaluate('''() => {
                const elements = document.querySelectorAll('*');
                let visibleText = '';
                
                for (let el of elements) {
                    if (el.offsetParent !== null && el.textContent && el.textContent.trim()) {
                        const style = window.getComputedStyle(el);
                        if (style.display !== 'none' && style.visibility !== 'hidden') {
                            visibleText += el.textContent.trim() + '\\n';
                        }
                    }
                }
                
                return visibleText.substring(0, 10000); // 限制长度
            }''')
            
            await asyncio.sleep(random.uniform(1, 2))
            
            # 安全地查找数据元素
            data_elements = await xiaohongshu_page.evaluate('''() => {
                const keywords = ['粉丝', '点赞', '收藏', '分享', '阅读', '曝光', '互动'];
                const foundData = [];
                
                // 只查找明显的数据元素
                const textNodes = document.evaluate(
                    "//text()[contains(., '粉丝') or contains(., '点赞') or contains(., '收藏')]",
                    document,
                    null,
                    XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE,
                    null
                );
                
                for (let i = 0; i < Math.min(textNodes.snapshotLength, 20); i++) {
                    const node = textNodes.snapshotItem(i);
                    if (node.textContent && node.textContent.trim().length < 100) {
                        foundData.push(node.textContent.trim());
                    }
                }
                
                return foundData;
            }''');
            
            # 组合数据
            collected_data = {
                'basic_info': basic_info,
                'visible_text': visible_text,
                'data_elements': data_elements,
                'collection_method': 'Safe Pyppeteer',
                'safety_measures': [
                    'Random delays',
                    'Mouse simulation',
                    'Scroll simulation',
                    'Anti-detection scripts',
                    'Gradual data collection'
                ]
            }
            
            self.log("✅ 安全数据收集完成")
            
            # 模拟用户继续浏览
            await asyncio.sleep(random.uniform(2, 5))
            
            await browser.disconnect()
            
            return collected_data
            
        except Exception as e:
            self.log(f"❌ 安全数据收集失败: {str(e)}", "ERROR")
            return None
    
    def analyze_safe_data(self, data):
        """分析安全收集的数据"""
        if not data:
            return None
        
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'page_info': data['basic_info'],
            'extracted_keywords': [],
            'numbers': [],
            'safety_score': 'HIGH'  # 安全评分
        }
        
        # 从可见文本中提取关键信息
        text = data['visible_text']
        
        # 提取数字
        import re
        numbers = re.findall(r'\d+', text)
        analysis['numbers'] = numbers[:30]  # 限制数量
        
        # 查找关键词
        keywords = ['粉丝', '点赞', '收藏', '分享', '阅读', '曝光', '互动', '收益']
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if line and len(line) < 150:
                for keyword in keywords:
                    if keyword in line:
                        analysis['extracted_keywords'].append({
                            'keyword': keyword,
                            'context': line
                        })
                        break
        
        # 去重
        seen = set()
        unique_keywords = []
        for item in analysis['extracted_keywords']:
            key = item['context']
            if key not in seen:
                seen.add(key)
                unique_keywords.append(item)
        
        analysis['extracted_keywords'] = unique_keywords[:20]
        
        return analysis
    
    def display_safe_results(self, analysis):
        """显示安全收集结果"""
        if not analysis:
            print("❌ 没有数据可显示")
            return
        
        print(f"\n🛡️ 安全数据收集结果")
        print("="*50)
        
        page_info = analysis['page_info']
        print(f"📄 页面: {page_info['title']}")
        print(f"🔗 URL: {page_info['url'][:60]}...")
        print(f"🛡️ 安全等级: {analysis['safety_score']}")
        print(f"🕒 收集时间: {analysis['timestamp']}")
        
        print(f"\n📊 数据统计:")
        print(f"  🔍 关键词数据: {len(analysis['extracted_keywords'])} 条")
        print(f"  🔢 提取数字: {len(analysis['numbers'])} 个")
        
        if analysis['extracted_keywords']:
            print(f"\n🔍 关键数据 (前10条):")
            for i, item in enumerate(analysis['extracted_keywords'][:10], 1):
                print(f"  {i:2d}. [{item['keyword']}] {item['context'][:50]}...")
        
        if analysis['numbers']:
            print(f"\n🔢 数字数据 (前15个):")
            print(f"     {', '.join(analysis['numbers'][:15])}")
        
        print("="*50)
    
    def run_safe_collection(self):
        """运行安全收集流程"""
        print("🛡️ 小红书数据收集器 - 安全版本")
        print("="*60)
        print("📋 增强反检测措施，降低被发现风险")
        print("="*60)
        
        try:
            # 检查pyppeteer
            try:
                import pyppeteer
            except ImportError:
                self.log("正在安装pyppeteer...")
                import subprocess
                import sys
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyppeteer'], 
                             capture_output=True)
            
            # 安全打开浏览器
            if not self.open_browser_19_safe():
                return
            
            # 随机等待
            self.log("⏳ 随机等待浏览器启动...")
            time.sleep(random.uniform(3, 8))
            
            # 安全收集数据
            self.log("🛡️ 开始安全数据收集...")
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                collected_data = loop.run_until_complete(
                    self.safe_data_collection()
                )
                
                if collected_data:
                    # 分析数据
                    analysis = self.analyze_safe_data(collected_data)
                    
                    if analysis:
                        # 显示结果
                        self.display_safe_results(analysis)
                        
                        # 保存数据
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"xiaohongshu_safe_data_{timestamp}.json"
                        
                        final_data = {
                            'raw_data': collected_data,
                            'analysis': analysis,
                            'safety_notes': [
                                '使用比特浏览器反检测环境',
                                '模拟真实用户行为',
                                '随机延迟和操作',
                                '分步数据收集',
                                '限制数据量避免异常'
                            ]
                        }
                        
                        with open(filename, 'w', encoding='utf-8') as f:
                            json.dump(final_data, f, ensure_ascii=False, indent=2)
                        
                        print(f"\n🎉 安全数据收集成功!")
                        print(f"📁 数据文件: {filename}")
                        print(f"🛡️ 已采用多重反检测措施")
                    else:
                        print(f"\n⚠️ 数据分析失败")
                else:
                    print(f"\n⚠️ 安全数据收集失败")
                
            finally:
                loop.close()
            
            print(f"\n✅ 浏览器保持打开状态")
            print(f"💡 建议：不要频繁运行，避免触发风控")
            
        except Exception as e:
            self.log(f"程序异常: {str(e)}", "ERROR")
        
        print(f"\n👋 程序结束")

if __name__ == "__main__":
    collector = SafeXiaohongshuCollector()
    collector.run_safe_collection()
