#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 小红书评论爬取模块 - 集成到矩阵工具
基于比特浏览器API + Selenium的评论爬取模块
"""

import time
import json
import re
import os
import requests
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from typing import Dict, List, Optional, Tuple

class XiaohongshuScraperModule:
    """小红书评论爬取模块"""
    
    def __init__(self, api_url: str = "http://127.0.0.1:56906", output_dir: str = "./scraped_data"):
        """
        初始化爬取模块
        
        Args:
            api_url: 比特浏览器API地址
            output_dir: 输出目录
        """
        self.api_url = api_url
        self.output_dir = output_dir
        self.ensure_output_dir()
        
        # 默认配置
        self.config = {
            'target_comments': 1472,
            'max_scroll_attempts': 1000,
            'scroll_delay': 0.6,
            'click_delay': 0.4,
            'wait_timeout': 10
        }
        
        self.driver = None
        self.browser_id = None
        self.debug_port = None
        
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def get_api_headers(self) -> Dict[str, str]:
        """获取API请求头"""
        return {'Content-Type': 'application/json'}
    
    def list_browsers(self, page: int = 0, page_size: int = 100) -> List[Dict]:
        """
        获取浏览器窗口列表
        
        Args:
            page: 页码，从0开始
            page_size: 每页数量
            
        Returns:
            浏览器窗口列表
        """
        try:
            response = requests.post(
                f"{self.api_url}/browser/list",
                json={"page": page, "pageSize": page_size},
                headers=self.get_api_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return data.get('data', [])
            
            return []
            
        except Exception as e:
            print(f"❌ 获取浏览器列表失败: {str(e)}")
            return []
    
    def find_browser_by_seq(self, seq: int) -> Optional[Dict]:
        """
        根据序号查找浏览器窗口
        
        Args:
            seq: 浏览器序号
            
        Returns:
            浏览器窗口信息
        """
        browsers = self.list_browsers()
        
        for browser in browsers:
            if browser.get('seq') == seq:
                return browser
        
        return None
    
    def open_browser(self, browser_id: str, args: List[str] = None) -> Optional[Dict]:
        """
        打开浏览器窗口
        
        Args:
            browser_id: 浏览器ID
            args: 启动参数
            
        Returns:
            打开结果，包含调试端口等信息
        """
        try:
            if args is None:
                args = ["--remote-debugging-address=0.0.0.0"]
            
            response = requests.post(
                f"{self.api_url}/browser/open",
                json={
                    "id": browser_id,
                    "args": args,
                    "queue": True
                },
                headers=self.get_api_headers(),
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return data.get('data')
            
            return None
            
        except Exception as e:
            print(f"❌ 打开浏览器失败: {str(e)}")
            return None
    
    def close_browser(self, browser_id: str) -> bool:
        """
        关闭浏览器窗口
        
        Args:
            browser_id: 浏览器ID
            
        Returns:
            是否成功关闭
        """
        try:
            response = requests.post(
                f"{self.api_url}/browser/close",
                json={"id": browser_id},
                headers=self.get_api_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('success', False)
            
            return False
            
        except Exception as e:
            print(f"❌ 关闭浏览器失败: {str(e)}")
            return False
    
    def connect_selenium(self, debug_port: str) -> bool:
        """
        连接Selenium到调试端口
        
        Args:
            debug_port: 调试端口
            
        Returns:
            是否连接成功
        """
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{debug_port}")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.debug_port = debug_port
            
            return True
            
        except Exception as e:
            print(f"❌ Selenium连接失败: {str(e)}")
            return False
    
    def scrape_comments(self, target_url: str = None) -> Dict:
        """
        爬取评论主方法
        
        Args:
            target_url: 目标URL，如果提供则导航到该URL
            
        Returns:
            爬取结果
        """
        if not self.driver:
            raise Exception("Selenium未连接，请先调用connect_selenium")
        
        # 如果提供了目标URL，导航到该页面
        if target_url:
            self.driver.get(target_url)
            time.sleep(3)
        
        # 检查是否在小红书页面
        current_url = self.driver.current_url
        if 'xiaohongshu.com' not in current_url:
            raise Exception(f"当前不在小红书页面: {current_url}")
        
        print(f"🎯 开始爬取评论，目标: {self.config['target_comments']} 条")
        
        # 滚动到评论区域
        self._scroll_to_comments()
        
        # 执行滚动加载
        loaded_count = self._perform_scroll_loading()
        
        # 提取评论
        result = self._extract_comments()
        
        print(f"✅ 爬取完成，提取到 {len(result['comments'])} 条评论")
        
        return result
    
    def _scroll_to_comments(self):
        """滚动到评论区域"""
        try:
            comment_selectors = [
                "//span[contains(text(), '条评论')]",
                "//div[contains(text(), '评论')]",
                "//*[contains(@class, 'comment')]"
            ]
            
            for selector in comment_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    print("✅ 定位到评论区域")
                    time.sleep(2)
                    return
                except:
                    continue
            
            # 如果没找到，滚动到页面中部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.6);")
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ 滚动到评论区域失败: {str(e)}")
    
    def _perform_scroll_loading(self) -> int:
        """执行滚动加载"""
        current_count = 0
        previous_count = 0
        stable_count = 0
        total_scrolls = 0
        total_clicks = 0
        
        for i in range(self.config['max_scroll_attempts']):
            # 统计评论数量
            current_count = self._count_comments()
            
            # 检查是否达到目标
            if current_count >= self.config['target_comments'] * 0.95:
                print(f"🎉 接近目标！{current_count}/{self.config['target_comments']}")
                break
            
            # 检查进度
            if current_count == previous_count:
                stable_count += 1
            else:
                stable_count = 0
                previous_count = current_count
            
            # 点击加载更多
            if stable_count >= 3:
                if self._click_load_more():
                    total_clicks += 1
                    stable_count = 0
            
            # 执行滚动
            self._perform_scroll(i)
            
            # 如果长时间稳定，停止
            if stable_count >= 20:
                break
            
            time.sleep(self.config['scroll_delay'])
            total_scrolls += 1
            
            # 每100次输出进度
            if i % 100 == 0 and i > 0:
                progress = round((current_count / self.config['target_comments']) * 100)
                print(f"📊 进度: {progress}% ({current_count}/{self.config['target_comments']})")
        
        return current_count
    
    def _count_comments(self) -> int:
        """统计评论数量"""
        try:
            page_text = self.driver.execute_script("return document.body.textContent;")
            
            # 多种统计方法
            time_count = len(re.findall(r'\d{2}-\d{2}', page_text))
            reply_count = len(re.findall(r'\d+回复', page_text))
            keyword_count = len(re.findall(r'求带|宝子|学姐|兼职|聊天员', page_text)) // 2
            
            try:
                dom_count = len(self.driver.find_elements(By.CSS_SELECTOR, '[class*="comment"], [class*="Comment"]'))
            except:
                dom_count = 0
            
            return max(time_count, reply_count, keyword_count, dom_count)
            
        except Exception as e:
            return 0
    
    def _click_load_more(self) -> bool:
        """点击加载更多按钮"""
        try:
            load_more_texts = ['加载更多', '展开更多', '查看更多', '更多评论', '展开', '更多']
            
            for text in load_more_texts:
                try:
                    elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{text}')]")
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            time.sleep(self.config['click_delay'])
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            return False
    
    def _perform_scroll(self, index: int):
        """执行滚动"""
        strategies = [
            lambda: self.driver.execute_script("window.scrollBy(0, 300);"),
            lambda: self.driver.execute_script("window.scrollBy(0, 500);"),
            lambda: self.driver.execute_script("window.scrollBy(0, 800);"),
            lambda: self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);"),
            lambda: self.driver.execute_script("""
                const comments = document.querySelectorAll('[class*="comment"], [class*="Comment"]');
                if (comments.length > 0) {
                    comments[comments.length - 1].scrollIntoView();
                }
            """)
        ]
        
        strategy_index = index % len(strategies)
        strategies[strategy_index]()

    def _extract_comments(self) -> Dict:
        """提取评论"""
        try:
            page_text = self.driver.execute_script("return document.body.textContent;")
            current_url = self.driver.current_url
            title = self.driver.title

            # 提取笔记基本信息
            note_info = {
                'id': '',
                'title': title.replace(' - 小红书', ''),
                'author': '',
                'totalCommentCount': 0,
                'url': current_url
            }

            # 提取笔记ID
            url_match = re.search(r'explore/([a-f0-9]+)', current_url)
            if url_match:
                note_info['id'] = url_match.group(1)

            # 提取评论总数
            comment_count_match = re.search(r'(\d+)\s*条评论', page_text)
            if comment_count_match:
                note_info['totalCommentCount'] = int(comment_count_match.group(1))

            # 提取作者
            if '漫娴学姐' in page_text:
                note_info['author'] = '漫娴学姐 招暑假工版'

            # 解析评论
            comments = self._parse_comments(page_text)

            return {
                'noteInfo': note_info,
                'comments': comments,
                'extractStats': {
                    'totalTextLength': len(page_text),
                    'successfulExtractions': len(comments),
                    'extractionMethods': ['bitbrowser-api-selenium']
                },
                'extractTime': datetime.now().isoformat()
            }

        except Exception as e:
            print(f"❌ 提取评论失败: {str(e)}")
            return {
                'noteInfo': {},
                'comments': [],
                'extractStats': {},
                'extractTime': datetime.now().isoformat()
            }

    def _parse_comments(self, page_text: str) -> List[Dict]:
        """解析评论"""
        all_comments = []

        # 时间模式解析
        time_pattern = r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)'
        time_matches = list(re.finditer(time_pattern, page_text))

        for i, match in enumerate(time_matches):
            time_str = match.group(1)
            time_index = match.start()

            start_index = max(0, time_index - 200)
            end_index = time_matches[i + 1].start() if i + 1 < len(time_matches) else time_index + 1000
            end_index = min(len(page_text), end_index)

            comment_text = page_text[start_index:end_index].strip()

            if 20 < len(comment_text) < 2000:
                comment = self._create_comment(comment_text, len(all_comments) + 1, 'time')
                if comment:
                    all_comments.append(comment)

        # 关键词模式解析
        keywords = ['求带', '宝子', '学姐', '兼职', '聊天员', '陪玩', '小红薯', 'Carina', '广州']

        for keyword in keywords:
            for match in re.finditer(keyword, page_text):
                start_index = max(0, match.start() - 150)
                end_index = min(len(page_text), match.start() + 800)

                comment_text = page_text[start_index:end_index].strip()

                if 15 < len(comment_text) < 1500:
                    comment = self._create_comment(comment_text, len(all_comments) + 1, 'keyword')
                    if comment:
                        all_comments.append(comment)

        # 去重
        unique_comments = self._deduplicate_comments(all_comments)

        return unique_comments

    def _create_comment(self, text: str, comment_id: int, source: str) -> Optional[Dict]:
        """创建评论对象"""
        comment = {
            'id': comment_id,
            'userId': '',
            'username': '',
            'content': '',
            'time': '',
            'likes': 0,
            'replyCount': 0,
            'isAuthor': '作者' in text,
            'isPinned': '置顶' in text,
            'extractedInfo': {
                'source': source,
                'originalLength': len(text)
            }
        }

        # 提取时间
        time_match = re.search(r'(\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前)', text)
        if time_match:
            comment['time'] = time_match.group(1)

        # 提取用户名
        user_patterns = [
            r'^([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*\d{2}-\d{2}',
            r'([A-Za-z0-9_\u4e00-\u9fff]{2,20})\s*求带',
            r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+([A-Za-z0-9_\u4e00-\u9fff]{2,20})'
        ]

        for pattern in user_patterns:
            match = re.search(pattern, text)
            if match:
                comment['username'] = match.group(1).strip()
                break

        # 清理内容
        content = text
        content = re.sub(r'\d{2}-\d{2}|\d+小时前|\d+分钟前|\d+天前', '', content)
        content = re.sub(r'^[🍉🐟🍊😏🍑🐟🦁🥕🍇🥞🍋🐵🥝😃🦁🥦🍟🐭🦊🥬🍍]+', '', content)
        content = re.sub(r'作者|置顶评论|回复|赞|仅自己可见', '', content)
        if comment['username']:
            content = content.replace(comment['username'], '')
        content = re.sub(r'\s+', ' ', content).strip()

        comment['content'] = content

        # 提取数字信息
        like_match = re.search(r'(\d+)\s*赞', text)
        if like_match:
            comment['likes'] = int(like_match.group(1))

        reply_match = re.search(r'(\d+)\s*回复', text)
        if reply_match:
            comment['replyCount'] = int(reply_match.group(1))

        # 提取用户ID
        user_id_match = re.search(r'小红薯([A-F0-9]{8,})', text)
        if user_id_match:
            comment['userId'] = user_id_match.group(1)

        return comment if len(comment['content']) >= 5 else None

    def _deduplicate_comments(self, comments: List[Dict]) -> List[Dict]:
        """去重评论"""
        unique = []
        seen = set()

        for comment in comments:
            key = comment['content'][:30]
            if key not in seen:
                seen.add(key)
                unique.append(comment)

        # 重新分配ID
        for i, comment in enumerate(unique):
            comment['id'] = i + 1

        return unique

    def save_to_file(self, data: Dict, filename: str = None) -> str:
        """
        保存数据到文件

        Args:
            data: 要保存的数据
            filename: 文件名，如果不提供则自动生成

        Returns:
            保存的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            note_id = data.get('noteInfo', {}).get('id', 'unknown')
            filename = f"xiaohongshu_comments_{note_id}_{timestamp}.json"

        filepath = os.path.join(self.output_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        return filepath

    def cleanup(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None

        self.debug_port = None
        self.browser_id = None
