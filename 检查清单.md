# 比特浏览器"获取不到窗口"问题检查清单

## 🔍 第一步：确认比特浏览器基本状态

### ✅ 检查项目1：比特浏览器客户端
- [ ] 比特浏览器客户端已启动
- [ ] 客户端界面正常显示
- [ ] 没有错误提示或异常状态
- [ ] 版本是否为最新版本

### ✅ 检查项目2：Local API设置
- [ ] 进入比特浏览器 → 设置 → Local API
- [ ] "Local API接收控制" 开关已开启
- [ ] API端口显示为 54345
- [ ] 已生成API Token
- [ ] Token已正确复制（没有多余空格）

### ✅ 检查项目3：浏览器实例
- [ ] 在比特浏览器主界面能看到浏览器列表
- [ ] 至少创建了一个浏览器实例
- [ ] 浏览器实例状态正常（不是错误状态）
- [ ] 浏览器实例有正确的名称和配置

## 🔍 第二步：网络连接测试

### 方法1：使用最简单的测试脚本
```bash
node simple-test.js YOUR_API_TOKEN
```

### 方法2：使用curl命令测试
```bash
# 测试健康检查
curl -X POST http://127.0.0.1:54345/health \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: YOUR_TOKEN" \
  -d "{}"

# 测试浏览器列表
curl -X POST http://127.0.0.1:54345/browser/list \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: YOUR_TOKEN" \
  -d '{"page": 0, "pageSize": 10}'
```

### 方法3：检查端口占用
```bash
# Windows
netstat -an | findstr :54345

# 应该看到类似这样的输出：
# TCP    127.0.0.1:54345        0.0.0.0:0              LISTENING
```

## 🔍 第三步：常见问题排查

### 问题1：连接被拒绝
**现象**: `ECONNREFUSED` 或 `连接失败`
**原因**: 比特浏览器未启动或端口问题
**解决**:
1. 重启比特浏览器客户端
2. 检查端口是否为54345
3. 确认Local API已开启

### 问题2：认证失败
**现象**: `401 Unauthorized` 或认证错误
**原因**: API Token问题
**解决**:
1. 重新生成API Token
2. 检查Token复制是否完整
3. 确认请求头使用 `X-API-KEY`

### 问题3：返回空列表
**现象**: 连接成功但浏览器列表为空
**原因**: 没有浏览器实例
**解决**:
1. 在比特浏览器中创建浏览器实例
2. 检查浏览器实例状态
3. 确认实例没有被删除

### 问题4：超时错误
**现象**: 请求超时
**原因**: 网络或防火墙问题
**解决**:
1. 检查防火墙设置
2. 临时关闭杀毒软件测试
3. 检查网络连接

## 🔍 第四步：详细诊断

### 运行完整诊断
```bash
node bitbrowser-diagnosis.js YOUR_API_TOKEN
```

### 查看详细日志
1. 打开比特浏览器客户端
2. 查看是否有API相关的日志或错误信息
3. 检查客户端状态栏是否有异常提示

## 🔍 第五步：手动验证步骤

### 1. 验证比特浏览器状态
- 打开比特浏览器客户端
- 确认主界面显示正常
- 检查是否有浏览器实例列表

### 2. 验证Local API设置
- 进入设置 → Local API
- 确认开关状态为"开启"
- 记录API Token（完整复制）

### 3. 验证网络连接
- 在浏览器中访问 http://127.0.0.1:54345
- 应该看到连接响应（可能是错误页面，但不应该是连接失败）

### 4. 验证API调用
- 使用simple-test.js脚本测试
- 观察详细的请求和响应信息

## 📞 如果问题仍然存在

### 收集信息
请提供以下信息：
1. 比特浏览器版本号
2. 操作系统版本
3. simple-test.js的完整输出
4. 比特浏览器客户端截图
5. Local API设置截图

### 可能的解决方案
1. **重装比特浏览器**: 卸载后重新安装最新版本
2. **更换端口**: 在Local API设置中尝试更换端口
3. **重置配置**: 重置比特浏览器的所有设置
4. **联系技术支持**: 向比特浏览器官方寻求帮助

## 💡 预防措施

### 定期检查
- 定期更新比特浏览器版本
- 定期重新生成API Token
- 定期检查防火墙设置

### 备用方案
- 准备多个API Token
- 记录正确的配置步骤
- 建立测试流程

---

**重要提醒**: 请按照此清单逐项检查，每完成一项就标记 ✅，这样可以系统性地找出问题所在。
