# 比特浏览器配置指南

## 📋 概述

本系统支持从比特浏览器中实时采集小红书账号的真实数据，包括：
- 🖼️ **头像** - 用户真实头像图片
- 👤 **昵称** - 用户显示名称
- 📝 **简介** - 个人描述信息
- 📊 **统计数据** - 粉丝数、关注数、获赞收藏数
- 🆔 **账号ID** - 小红书唯一标识

## 🔧 配置步骤

### 1. 启动比特浏览器
**重要**: 必须先启动比特浏览器应用程序并开启API功能

#### 1.1 启动比特浏览器应用
- 双击比特浏览器桌面图标
- 等待应用程序完全加载

#### 1.2 开启Local API功能
- 在比特浏览器界面中找到"设置"或"API"选项
- 开启"Local API"功能
- 确认API端口为 `54345` (默认端口)
- 记录API Token: `ca28ee5ca6de4d209182a83aa16a2044`

#### 1.3 验证API连接
运行测试命令验证连接：
```bash
node find_debug_port.js
```

### 2. 检查API配置
在 `routes/xiaohongshu.js` 中确认以下配置：
```javascript
const BITBROWSER_CONFIG = {
    api_url: "http://127.0.0.1:54345",
    api_token: "ca28ee5ca6de4d209182a83aa16a2044",
    browser_19_id: "0d094596cb404282be3f814b98139c74"
};
```

### 3. 创建或启动浏览器实例
- 在比特浏览器中创建一个新的浏览器实例
- 记录浏览器实例的ID，更新到 `browser_19_id` 配置中
- 启动该浏览器实例

### 4. 打开小红书页面
在启动的浏览器实例中：
- 访问 `https://creator.xiaohongshu.com/new/home` (创作中心)
- 或访问 `https://www.xiaohongshu.com` (主站)
- 确保已登录您的小红书账号
- 等待页面完全加载

### 5. 测试数据采集
运行测试脚本验证配置：
```bash
node test_browser_extractor.js
```

## 🎯 数据采集原理

### 采集优先级
1. **比特浏览器实时数据** (最优先)
   - 直接从浏览器页面提取最新数据
   - 包含完整的头像、昵称、简介等信息

2. **本地数据文件** (备用方案)
   - 使用之前保存的JSON数据文件
   - 数据可能不是最新的

3. **错误提示** (最后方案)
   - 如果以上都失败，返回明确的错误信息

### 数据提取技术
- 使用 Chrome DevTools Protocol
- 通过JavaScript在页面中执行数据提取脚本
- 智能识别页面元素和数据结构

## 🔍 支持的页面类型

### 小红书创作中心
- URL: `https://creator.xiaohongshu.com/*`
- 可提取：账号统计、创作数据、粉丝信息

### 小红书主站
- URL: `https://www.xiaohongshu.com/*`
- 可提取：个人主页信息、基础统计数据

### 个人主页
- URL: `https://www.xiaohongshu.com/user/profile/*`
- 可提取：完整的个人资料信息

## 🛠️ 故障排除

### 问题1：无法连接比特浏览器API
**症状**：`Request failed with status code 404`
**解决方案**：
1. 确认比特浏览器已启动
2. 检查API端口是否为 54345
3. 验证API Token是否正确

### 问题2：找不到小红书标签页
**症状**：`未找到小红书标签页`
**解决方案**：
1. 在浏览器中打开小红书网站
2. 确保页面URL包含 `xiaohongshu.com`
3. 刷新页面确保完全加载

### 问题3：数据提取不完整
**症状**：某些字段为空或默认值
**解决方案**：
1. 确保已登录小红书账号
2. 等待页面完全加载后再采集
3. 检查页面是否显示完整的用户信息

### 问题4：头像无法获取
**症状**：头像显示为默认图片
**解决方案**：
1. 确保用户已设置头像
2. 检查图片加载是否完成
3. 验证图片URL是否有效

## 📊 数据字段说明

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| nickname | String | 用户昵称 | "漫娴学姐 招暑假工版" |
| xiaohongshuId | String | 小红书ID | "***********" |
| avatar | String | 头像URL | "https://sns-avatar-qc.xhscdn.com/..." |
| bio | String | 个人简介 | "📍 深耕广佛高校圈的活动适配师" |
| followCount | Number | 关注数 | 698 |
| fansCount | Number | 粉丝数 | 245 |
| likesAndCollects | Number | 获赞收藏数 | 933 |

## 🚀 使用示例

### 通过API采集
```bash
curl -X POST http://localhost:3000/api/xiaohongshu/accounts/collect
```

### 通过前端界面
1. 打开 `http://localhost:3000/premium-index.html`
2. 进入"账号管理"页面
3. 点击"采集账号"按钮
4. 查看采集结果

## 📝 注意事项

1. **隐私保护**：仅采集公开可见的账号信息
2. **频率限制**：避免过于频繁的数据采集
3. **数据准确性**：实时数据比本地文件更准确
4. **网络稳定性**：确保网络连接稳定
5. **浏览器兼容性**：建议使用最新版本的比特浏览器

## 🔄 更新配置

如需修改浏览器配置，请编辑以下文件：
- `routes/xiaohongshu.js` - 后端API配置
- `xiaohongshu_browser_extractor.js` - 数据提取器配置

修改后需要重启服务器：
```bash
node server.js
```
