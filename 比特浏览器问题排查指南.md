# 比特浏览器"获取不到窗口"问题排查指南

## 🚨 问题现象
- 测试连接成功，但获取浏览器列表失败
- 返回空列表或错误信息
- API调用超时或连接被拒绝

## 🔍 快速诊断工具

### 方法1：使用命令行诊断工具
```bash
# 安装依赖（如果还没安装）
npm install axios

# 运行诊断（替换YOUR_API_TOKEN为实际Token）
node bitbrowser-diagnosis.js YOUR_API_TOKEN
```

### 方法2：使用Web测试工具
1. 访问 http://localhost:3002/bitbrowser-test-app.html
2. 配置API信息
3. 点击"诊断问题"按钮

## 📋 手动排查步骤

### 第1步：检查比特浏览器客户端
- [ ] 确认比特浏览器客户端已启动
- [ ] 检查客户端版本（建议使用最新版本）
- [ ] 确认客户端运行正常，没有错误提示

### 第2步：检查Local API设置
- [ ] 进入比特浏览器 → 设置 → Local API
- [ ] 确认"Local API接收控制"已开启
- [ ] 检查API端口是否为54345
- [ ] 重新生成API Token并记录

### 第3步：检查浏览器实例
- [ ] 在比特浏览器中创建至少一个浏览器实例
- [ ] 确认浏览器实例状态正常
- [ ] 检查浏览器分组设置

### 第4步：检查网络连接
- [ ] 确认防火墙没有阻止54345端口
- [ ] 检查杀毒软件是否拦截了连接
- [ ] 尝试关闭VPN或代理

### 第5步：测试API连接
```bash
# 测试基础连接
curl http://127.0.0.1:54345

# 测试健康检查（替换YOUR_TOKEN）
curl -X POST http://127.0.0.1:54345/health \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: YOUR_TOKEN" \
  -d "{}"

# 测试浏览器列表（替换YOUR_TOKEN）
curl -X POST http://127.0.0.1:54345/browser/list \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: YOUR_TOKEN" \
  -d '{"page": 0, "pageSize": 10}'
```

## 🔧 常见问题解决方案

### 问题1：连接被拒绝 (ECONNREFUSED)
**原因**: 比特浏览器未启动或端口错误
**解决方案**:
1. 启动比特浏览器客户端
2. 检查API端口设置
3. 确认Local API功能已开启

### 问题2：API Token无效
**原因**: Token过期或输入错误
**解决方案**:
1. 重新生成API Token
2. 检查Token复制是否完整
3. 确认没有多余的空格或字符

### 问题3：返回空列表
**原因**: 比特浏览器中没有浏览器实例
**解决方案**:
1. 在比特浏览器中创建新的浏览器实例
2. 确认浏览器实例状态正常
3. 检查分组过滤条件

### 问题4：请求超时
**原因**: 网络问题或防火墙阻止
**解决方案**:
1. 检查防火墙设置
2. 添加比特浏览器到白名单
3. 尝试使用不同的网络

### 问题5：API版本不兼容
**原因**: 比特浏览器版本过旧
**解决方案**:
1. 更新比特浏览器到最新版本
2. 检查API文档确认支持的端点
3. 尝试不同的API端点

## 📞 获取帮助

### 自助诊断
1. 运行诊断工具获取详细报告
2. 查看比特浏览器官方文档
3. 检查比特浏览器客户端日志

### 联系支持
如果问题仍然存在，请提供以下信息：
- 比特浏览器版本号
- 操作系统版本
- 诊断工具输出结果
- 错误截图或日志

## 💡 预防措施

### 定期维护
- 定期更新比特浏览器客户端
- 定期重新生成API Token
- 定期清理无用的浏览器实例

### 监控设置
- 设置API连接监控
- 记录API调用日志
- 建立错误告警机制

### 备用方案
- 准备多个API Token
- 配置备用端口
- 建立手动操作流程

---

**注意**: 此指南适用于比特浏览器Local API功能。如果使用的是其他版本或功能，请参考相应的官方文档。
