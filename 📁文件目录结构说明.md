# 📁 项目文件目录结构说明 【中文标签版】

## 🏗️ **核心服务文件**
```
📁 项目根目录/
├── 🚀 server.js                    # 主服务器文件 - Express应用入口
├── 📋 package.json                 # 项目配置文件 - 依赖管理
├── 🔧 package-lock.json           # 依赖锁定文件 - 版本控制
└── ⚙️ preload.js                  # 预加载脚本 - Electron配置
```

## 🛣️ **API路由模块**
```
📁 routes/                          # API路由目录
├── 📱 xiaohongshu.js              # 小红书管理API - 核心业务逻辑
├── 👥 accounts.js                 # 账号管理API - 用户账号操作
├── 💬 chat.js                     # 聊天功能API - 实时通信
├── 📊 overview.js                 # 数据总览API - 统计分析
├── 📈 monitor.js                  # 监控功能API - 系统监控
├── 📝 messages.js                 # 消息管理API - 消息处理
└── 📋 records.js                  # 记录管理API - 操作记录
```

## 🌐 **前端静态资源**
```
📁 public/                         # 静态文件目录
├── 🎨 premium-index.html          # 主页面 - 桌面端界面
├── ⚡ premium-app.js              # 主应用脚本 - 前端逻辑
├── 🎨 premium-ui.css              # 主样式文件 - 界面美化
├── 📱 xiaohongshu.html            # 小红书管理页面
├── 🔧 xiaohongshu-manager.js      # 小红书管理脚本
├── 🎨 xiaohongshu-styles.css      # 小红书样式文件
├── 🎨 modern-design.css           # 现代化设计样式
├── 🎨 settings-styles.css         # 设置页面样式
├── 🎨 styles.css                  # 通用样式文件
└── 🔧 config-manager.js           # 配置管理脚本
```

## 🔍 **数据采集引擎**
```
📁 数据采集模块/
├── 🔍 xiaohongshu_browser_extractor.js    # 浏览器数据提取器 - 核心采集
├── 🌐 check_bitbrowser.js                 # 比特浏览器检查工具
├── 🔧 debug_browser_connection.js         # 调试连接工具
└── 🧪 test-xiaohongshu-api.js            # 小红书API测试
```

## 🐍 **Python数据处理**
```
📁 Python脚本/
├──  xiaohongshu_safe_collector.py       # 安全数据采集器 - 主要采集工具
├── 📝 xiaohongshu_note_manager_v2.py      # 笔记管理器v2 - 最新版本
└── � requirements.txt                    # Python依赖配置
```

## 📊 **数据文件**
```
📁 数据存储/
└── 💾 xiaohongshu_safe_data_20250729_110029.json    # 安全采集数据 - 主要数据源
```

## 🔧 **工具脚本**
```
📁 工具集/
├── 🚀 start.bat                           # 主启动脚本 - 一键启动服务
├── 🚀 start-desktop.bat                   # 桌面端启动脚本
├── 🐳 docker-compose.yml                  # Docker容器编排
├── 🐳 docker-start.bat                    # Docker启动脚本
├── 🔧 install_and_run.bat                 # 安装运行脚本
├── ⚙️ healthcheck.js                      # 健康检查脚本
└── 🧪 test-integration.js                 # 集成测试脚本
```

## 📚 **文档说明**
```
📁 文档目录/
├── 📖 README.md                           # 项目说明文档
├── 📋 API_DOCUMENTATION.md                # API接口文档
├── 🏗️ CODE_STRUCTURE.md                  # 代码结构说明
├── 🖥️ DESKTOP-README.md                  # 桌面端说明
├── 💼 COMMERCIAL-DEPLOYMENT.md            # 商业部署指南
├── 📊 DATA-OVERVIEW-GUIDE.md              # 数据总览指南
└── 🔧 DEVELOPMENT_GUIDE.md                # 开发指南
```

## 🎨 **资源文件**
```
📁 assets/                         # 资源目录
├── 🎨 icon.png                    # 应用图标
├── 🎨 icon.svg                    # 矢量图标
├── 🎨 favicon.svg                 # 网站图标
└── 📄 create-icon.html            # 图标创建页面
```

## 📦 **依赖模块**
```
📁 node_modules/                   # Node.js依赖包目录
├── 🌐 express/                    # Web框架
├── ⚡ socket.io/                  # 实时通信
├── 🌐 axios/                      # HTTP客户端
├── 🆔 uuid/                       # 唯一标识符
├── 🔓 cors/                       # 跨域处理
└── 📋 body-parser/                # 请求解析
```

## 🏷️ **文件标签说明**

### 📁 **目录标签**
- 🏗️ 核心架构文件
- 🛣️ 路由模块
- 🌐 前端资源
- 🔍 数据采集
- 🐍 Python脚本
- 📊 数据文件
- 🔧 工具脚本
- 📚 文档说明
- 🎨 资源文件
- 📦 依赖模块

### 🏷️ **功能标签**
- 🚀 启动/主要文件
- 📱 小红书相关
- 👥 账号管理
- 💬 聊天通信
- 📊 数据分析
- 📈 监控统计
- 📝 内容管理
- 🔧 配置工具
- 🧪 测试调试
- 🎨 界面样式
- 💾 数据存储
- 🌐 网络通信
- ⚡ 实时功能
- 🔍 数据采集
- 🤖 自动化
- 📋 管理功能

## 🎯 **使用说明**

1. **🚀 启动服务**: 运行 `node server.js` 启动主服务器
2. **🌐 访问界面**: 打开 `http://localhost:3000` 访问管理界面
3. **📱 小红书功能**: 通过 `/api/xiaohongshu/*` 接口操作小红书数据
4. **🔍 数据采集**: 使用浏览器提取器实时采集数据
5. **📊 数据分析**: 查看各种报告和统计数据

## 🔧 **开发维护**

- **📝 添加新功能**: 在对应的路由文件中添加API端点
- **🎨 修改界面**: 编辑 `public/` 目录下的HTML/CSS/JS文件
- **🔍 数据采集**: 修改 `xiaohongshu_browser_extractor.js` 提取逻辑
- **📊 数据处理**: 使用Python脚本进行数据分析和处理
- **🧪 测试调试**: 运行各种test_*.js文件进行功能测试
