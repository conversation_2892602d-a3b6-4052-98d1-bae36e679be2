# 📋 API接口中文说明文档 【完整接口指南】

## 🚀 **服务器基础信息**
- **🌐 服务地址**: `http://localhost:3000`
- **📡 协议支持**: HTTP/HTTPS + WebSocket
- **🔓 跨域策略**: 允许所有来源访问
- **📋 数据格式**: JSON

## 📱 **小红书管理API** `/api/xiaohongshu`

### 🔍 **数据采集接口**

#### `POST /api/xiaohongshu/accounts/collect`
**📝 功能**: 自动采集小红书账号信息
```json
// 📤 请求参数: 无需参数
// 📥 响应示例:
{
  "success": true,
  "message": "成功采集到 1 个小红书账号",
  "data": {
    "accounts": [
      {
        "id": "uuid-string",                    // 🆔 账号唯一标识
        "platform": "xiaohongshu",             // 📱 平台名称
        "nickname": "漫娴学姐 招暑假工版",        // 👤 用户昵称
        "xiaohongshuId": "***********",         // 🔢 小红书ID
        "avatar": "头像URL",                    // 🖼️ 头像链接
        "followCount": 698,                     // 👥 关注数
        "fansCount": 245,                       // 👥 粉丝数
        "likesAndCollects": 933,                // ❤️ 获赞与收藏
        "bio": "个人简介",                       // 📝 个人简介
        "status": "正常",                       // 📊 账号状态
        "loginStatus": "已登录",                // 🔐 登录状态
        "dataSource": "browser_realtime"       // 📊 数据来源
      }
    ],
    "total": 1,
    "totalCollected": 1
  }
}
```

#### `GET /api/xiaohongshu/accounts/list`
**📝 功能**: 获取已采集的账号列表
```json
// 📤 查询参数:
// ?platform=xiaohongshu&status=正常&page=1&limit=20

// 📥 响应示例:
{
  "success": true,
  "data": {
    "accounts": [...],                          // 📋 账号列表
    "pagination": {
      "current": 1,                             // 📄 当前页码
      "total": 5,                               // 📄 总页数
      "pageSize": 20,                           // 📄 每页数量
      "totalItems": 100                         // 📊 总记录数
    }
  }
}
```

#### `POST /api/xiaohongshu/test-browser-connection`
**📝 功能**: 测试比特浏览器连接状态
```json
// 📥 响应示例:
{
  "success": true,
  "message": "比特浏览器连接成功，已找到小红书标签页",
  "data": {
    "debugPort": 9222,                          // 🔌 调试端口
    "xiaohongshuTab": {
      "title": "小红书 - 你的生活兴趣社区",      // 📄 标签页标题
      "url": "https://www.xiaohongshu.com/explore", // 🔗 页面URL
      "id": "tab-id-string"                     // 🆔 标签页ID
    }
  }
}
```

### 📊 **数据分析接口**

#### `GET /api/xiaohongshu/overview`
**📝 功能**: 获取小红书数据概览
```json
// 📥 响应示例:
{
  "success": true,
  "data": {
    "totalNotes": 3,                            // 📝 总笔记数
    "totalViews": 11362,                        // 👀 总浏览量
    "totalLikes": 307,                          // ❤️ 总点赞数
    "totalComments": 86,                        // 💬 总评论数
    "avgEngagement": 3.8,                       // 📊 平均互动率
    "recentGrowth": {
      "views": "+12.5%",                        // 📈 浏览量增长
      "likes": "+8.3%",                         // 📈 点赞增长
      "followers": "+5.2%"                      // 📈 粉丝增长
    }
  }
}
```

#### `GET /api/xiaohongshu/analytics`
**📝 功能**: 获取详细分析数据
```json
// 📤 查询参数: ?period=7d (时间段: 7d/30d/90d)
// 📥 响应示例:
{
  "success": true,
  "data": {
    "period": "7d",                             // 📅 分析周期
    "metrics": {
      "views": {
        "current": 11362,                       // 📊 当前数值
        "previous": 9876,                       // 📊 对比数值
        "growth": "+15.1%"                      // 📈 增长率
      }
    },
    "topNotes": [...],                          // 🏆 热门笔记
    "engagementTrend": [...]                    // 📈 互动趋势
  }
}
```

### 📝 **内容管理接口**

#### `GET /api/xiaohongshu/notes`
**📝 功能**: 获取笔记列表
```json
// 📤 查询参数: ?page=1&limit=10&status=已发布&privacy=公开可见
// 📥 响应示例:
{
  "success": true,
  "data": {
    "notes": [
      {
        "id": "note-uuid",                      // 🆔 笔记ID
        "title": "笔记标题",                    // 📝 笔记标题
        "content": "笔记内容",                  // 📄 笔记内容
        "status": "已发布",                     // 📊 发布状态
        "views": 3774,                          // 👀 浏览量
        "likes": 84,                            // ❤️ 点赞数
        "comments": 23,                         // 💬 评论数
        "isTop": false,                         // 📌 是否置顶
        "privacy": "公开可见",                  // 🔐 隐私设置
        "tags": ["标签1", "标签2"],             // 🏷️ 标签列表
        "publishTime": "2025-01-15T10:30:00Z"  // 📅 发布时间
      }
    ],
    "total": 50,
    "page": 1,
    "totalPages": 5
  }
}
```

#### `POST /api/xiaohongshu/notes`
**📝 功能**: 发布新笔记
```json
// 📤 请求参数:
{
  "title": "笔记标题",                          // 📝 必填：标题
  "content": "笔记内容",                        // 📄 必填：内容
  "tags": ["标签1", "标签2"],                  // 🏷️ 可选：标签
  "privacy": "公开可见",                        // 🔐 可选：隐私设置
  "scheduleTime": "2025-01-20T10:00:00Z"       // ⏰ 可选：定时发布
}

// 📥 响应示例:
{
  "success": true,
  "data": {
    "id": "new-note-uuid",
    "title": "笔记标题",
    "status": "已发布",                         // 或 "定时发布"
    "publishTime": "2025-01-15T10:30:00Z"
  },
  "message": "笔记发布成功"
}
```

## 👥 **账号管理API** `/api/accounts`

#### `GET /api/accounts`
**📝 功能**: 获取账号列表
```json
// 📤 查询参数: ?page=1&limit=20&status=online&platform=小红书&search=关键词
// 📥 响应示例:
{
  "success": true,
  "data": {
    "accounts": [
      {
        "id": "account-uuid",                   // 🆔 账号ID
        "username": "测试账号001",              // 👤 用户名
        "platform": "小红书",                  // 📱 平台
        "status": "online",                     // 📊 状态
        "followers": 1250,                      // 👥 粉丝数
        "following": 156,                       // 👥 关注数
        "posts": 45,                            // 📝 发布数
        "likes": 2800,                          // ❤️ 获赞数
        "createdAt": "2023-04-10T16:46:11Z",   // 📅 创建时间
        "lastLogin": "2023-04-10T16:46:11Z"    // 📅 最后登录
      }
    ],
    "total": 100,
    "page": 1,
    "totalPages": 5
  }
}
```

#### `POST /api/accounts`
**📝 功能**: 创建新账号
```json
// 📤 请求参数:
{
  "username": "新账号名",                      // 👤 必填：用户名
  "platform": "小红书",                       // 📱 必填：平台
  "group": "default"                           // 👥 可选：分组
}

// 📥 响应示例:
{
  "success": true,
  "data": {
    "id": "new-account-uuid",
    "username": "新账号名",
    "platform": "小红书",
    "status": "offline",
    "createdAt": "2025-01-15T10:30:00Z"
  },
  "message": "账号创建成功"
}
```

## 📊 **数据总览API** `/api/overview`

#### `GET /api/overview/stats`
**📝 功能**: 获取系统统计数据
```json
// 📥 响应示例:
{
  "success": true,
  "data": {
    "accounts": {
      "total": 150,                             // 📊 总账号数
      "active": 120,                            // 📊 活跃账号
      "online": 45                              // 📊 在线账号
    },
    "content": {
      "totalPosts": 1250,                       // 📝 总发布数
      "todayPosts": 25,                         // 📝 今日发布
      "totalViews": 125000,                     // 👀 总浏览量
      "totalLikes": 8500                        // ❤️ 总点赞数
    },
    "growth": {
      "followers": "+12.5%",                    // 📈 粉丝增长
      "engagement": "+8.3%",                    // 📈 互动增长
      "content": "+15.2%"                       // 📈 内容增长
    }
  }
}
```

## 🔧 **系统管理API**

#### `GET /health`
**📝 功能**: 系统健康检查
```json
// 📥 响应示例:
{
  "status": "healthy",                          // 📊 系统状态
  "timestamp": "2025-01-15T10:30:00Z",         // ⏰ 检查时间
  "uptime": 3600,                               // ⏱️ 运行时间(秒)
  "version": "1.0.0"                            // 📋 版本号
}
```

## 🚨 **错误响应格式**
```json
{
  "success": false,
  "message": "错误描述信息",                     // 📝 错误说明
  "error": "详细错误信息",                       // 🔍 技术细节
  "code": 400                                    // 📊 HTTP状态码
}
```

## 📡 **WebSocket实时通信**
- **🔗 连接地址**: `ws://localhost:3000`
- **📝 事件类型**: `chat-message`, `data-update`, `system-notification`
- **🔄 实时功能**: 聊天消息、数据更新推送、系统通知

## 🔐 **认证说明**
- **🔓 当前状态**: 无需认证（开发环境）
- **🔒 生产环境**: 建议添加JWT或API Key认证
- **🛡️ 安全建议**: 启用HTTPS、限制访问来源、添加请求频率限制
